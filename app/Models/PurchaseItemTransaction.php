<?php

namespace App\Models;

use App\Models\Master\ItemMaster;
use Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Support\Carbon;

/**
 * App\Models\PurchaseItemTransaction
 *
 * @property int $id
 * @property int $purchase_transaction_id
 * @property int $item_id
 * @property string|null $additional_description
 * @property int|null $unit_id
 * @property string|null $consolidating_items_to_invoice
 * @property float|null $quantity
 * @property float|null $free_quantity
 * @property float|null $rpu_with_gst
 * @property float|null $rpu_without_gst
 * @property int $discount_type
 * @property float|null $discount_value
 * @property float|null $total_discount_amount
 * @property int|null $gst_id
 * @property float|null $gst_tax_percentage
 * @property float|null $total
 * @property int|null $ledger_id
 * @property float|null $classification_igst_tax
 * @property float|null $classification_cgst_tax
 * @property float|null $classification_sgst_tax
 * @property float|null $classification_cess_tax
 * @property float $classification_is_rcm_applicable
 * @property float|null $classification_nature_type
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property float $cess_rate
 * @property float $cess_amount
 * @property float $taxable_value
 * @property-read TaxClassificationDetails|null $classificationNatureType
 * @property-read Ledger|null $ledger
 *
 * @method static Builder|PurchaseItemTransaction newModelQuery()
 * @method static Builder|PurchaseItemTransaction newQuery()
 * @method static Builder|PurchaseItemTransaction query()
 * @method static Builder|PurchaseItemTransaction whereAdditionalDescription($value)
 * @method static Builder|PurchaseItemTransaction whereCessAmount($value)
 * @method static Builder|PurchaseItemTransaction whereCessRate($value)
 * @method static Builder|PurchaseItemTransaction whereClassificationCessTax($value)
 * @method static Builder|PurchaseItemTransaction whereClassificationCgstTax($value)
 * @method static Builder|PurchaseItemTransaction whereClassificationIgstTax($value)
 * @method static Builder|PurchaseItemTransaction whereClassificationIsRcmApplicable($value)
 * @method static Builder|PurchaseItemTransaction whereClassificationNatureType($value)
 * @method static Builder|PurchaseItemTransaction whereClassificationSgstTax($value)
 * @method static Builder|PurchaseItemTransaction whereConsolidatingItemsToInvoice($value)
 * @method static Builder|PurchaseItemTransaction whereCreatedAt($value)
 * @method static Builder|PurchaseItemTransaction whereDiscountType($value)
 * @method static Builder|PurchaseItemTransaction whereDiscountValue($value)
 * @method static Builder|PurchaseItemTransaction whereGstId($value)
 * @method static Builder|PurchaseItemTransaction whereGstTaxPercentage($value)
 * @method static Builder|PurchaseItemTransaction whereId($value)
 * @method static Builder|PurchaseItemTransaction whereItemId($value)
 * @method static Builder|PurchaseItemTransaction whereLedgerId($value)
 * @method static Builder|PurchaseItemTransaction wherePurchaseTransactionId($value)
 * @method static Builder|PurchaseItemTransaction whereQuantity($value)
 * @method static Builder|PurchaseItemTransaction whereFreeQuantity($value)
 * @method static Builder|PurchaseItemTransaction whereRpuWithGst($value)
 * @method static Builder|PurchaseItemTransaction whereRpuWithoutGst($value)
 * @method static Builder|PurchaseItemTransaction whereTotal($value)
 * @method static Builder|PurchaseItemTransaction whereTotalDiscountAmount($value)
 * @method static Builder|PurchaseItemTransaction whereUnitId($value)
 * @method static Builder|PurchaseItemTransaction whereUpdatedAt($value)
 *
 * @mixin Eloquent
 *
 * @property float $classification_is_itc_applicable
 * @property float $taxable_amount
 * @property-read ItemMaster $items
 * @property-read PurchaseTransaction $purchaseTransaction
 *
 * @method static Builder|PurchaseItemTransaction whereClassificationIsItcApplicable($value)
 * @method static Builder|PurchaseItemTransaction whereTaxableAmount($value)
 *
 * @property-read \App\Models\UnitOfMeasurement|null $unit
 */
class PurchaseItemTransaction extends Model
{
    public $table = 'purchase_transaction_items';

    public $fillable = [
        'purchase_transaction_id',
        'item_id',
        'additional_description',
        'unit_id',
        'hsn_code',
        'quantity',
        'free_quantity',
        'mrp',
        'with_tax',
        'rpu_with_gst',
        'rpu_without_gst',
        'discount_type',
        'discount_value',
        'discount_type_2',
        'discount_value_2',
        'total_discount_amount',
        'gst_id',
        'gst_tax_percentage',
        'total',
        'ledger_id',
        'classification_nature_type',
        'classification_is_rcm_applicable',
        'classification_is_itc_applicable',
        'classification_igst_tax',
        'classification_cgst_tax',
        'classification_sgst_tax',
        'classification_cess_tax',
        'consolidating_items_to_invoice',
        'cess_rate',
        'cess_amount',
        'taxable_amount',
        'decimal_places_for_quantity',
        'decimal_places_for_rate',
    ];

    public $casts = [
        'hsn_code' => 'string',
        'quantity' => 'double',
        'free_quantity' => 'double',
        'rpu_with_gst' => 'double',
        'rpu_without_gst' => 'double',
        'discount_type' => 'integer',
        'discount_value' => 'double',
        'discount_type_2' => 'integer',
        'discount_value_2' => 'double',
        'total_discount_amount' => 'double',
        'gst_tax_percentage' => 'double',
        'total' => 'double',
        'additional_description' => 'string',
        'classification_nature_type' => 'integer',
        'classification_is_rcm_applicable' => 'double',
        'classification_is_itc_applicable' => 'double',
        'classification_igst_tax' => 'double',
        'classification_cgst_tax' => 'double',
        'classification_sgst_tax' => 'double',
        'classification_cess_tax' => 'double',
        'consolidating_items_to_invoice' => 'string',
        'cess_rate' => 'double',
        'cess_amount' => 'double',
        'with_tax' => 'boolean',
    ];

    public $append = ['taxable_value'];

    public function classificationNatureType(): BelongsTo
    {
        return $this->belongsTo(TaxClassificationDetails::class, 'classification_nature_type', 'id');
    }

    public function ledger(): BelongsTo
    {
        return $this->belongsTo(Ledger::class, 'ledger_id', 'id');
    }

    public function items(): BelongsTo
    {
        return $this->belongsTo(ItemMaster::class, 'item_id', 'id');
    }

    public function purchaseTransaction(): BelongsTo
    {
        return $this->belongsTo(PurchaseTransaction::class, 'purchase_transaction_id', 'id');
    }

    public function unit(): BelongsTo
    {
        return $this->belongsTo(UnitOfMeasurement::class, 'unit_id', 'id');
    }

    public function gst(): BelongsTo
    {
        return $this->belongsTo(GstTax::class, 'gst_id', 'id');
    }

    public function customFieldTransactionItemsValues(): MorphMany
    {
        return $this->morphMany(ItemCustomFieldValue::class, 'model');
    }

    public function customFieldTransactionItemsInventoryValues(): MorphMany
    {
        return $this->morphMany(ItemCustomFieldCombinationInventory::class, 'model');
    }

    protected function taxableValue(): Attribute
    {
        return Attribute::make(
            get: fn ($value
            ) => ((float) $this->quantity * (float) $this->rpu_without_gst) - (float) $this->total_discount_amount,
        );
    }

    public function getLedgerReport()
    {
        $transaction = $this->purchaseTransaction;

        return [
            'transaction_id' => $transaction->id,
            'date' => $transaction->voucher_date->format('d-m-y'),
            'ledger_name' => $transaction->supplier->name,
            'transaction_type' => 'Purchase',
            'voucher_no' => $transaction->voucher_number,
            'invoice_no' => '',
            'narration' => $transaction->narration ?? null,
            'debit_amount' => $this->taxable_value,
            'credit_amount' => 0,
            'balance' => 0,
        ];
    }
}
