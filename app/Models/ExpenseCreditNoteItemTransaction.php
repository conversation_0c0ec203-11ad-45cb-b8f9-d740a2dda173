<?php

namespace App\Models;

use App\Models\Master\ItemMaster;
use Barryvdh\LaravelIdeHelper\Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Support\Carbon;

/**
 * App\Models\ExpenseCreditNoteItemTransaction
 *
 * @property int $id
 * @property int $expense_cn_id
 * @property int $item_id
 * @property string|null $additional_description
 * @property int|null $unit_id
 * @property float|null $quantity
 * @property float|null $rpu_with_gst
 * @property float|null $rpu_without_gst
 * @property int $discount_type
 * @property float|null $discount_value
 * @property float|null $total_discount_amount
 * @property int|null $gst_id
 * @property float|null $gst_tax_percentage
 * @property float|null $total
 * @property int|null $ledger_id
 * @property float|null $classification_igst_tax
 * @property float|null $classification_cgst_tax
 * @property float|null $classification_sgst_tax
 * @property float|null $classification_cess_tax
 * @property float $classification_is_rcm_applicable
 * @property float|null $classification_nature_type
 * @property string|null $consolidating_items_to_invoice
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property float $cess_rate
 * @property float $cess_amount
 * @property float $taxable_value
 * @property-read TaxClassificationDetails|null $classificationNatureType
 * @property-read Ledger|null $ledger
 *
 * @method static Builder|ExpenseCreditNoteItemTransaction newModelQuery()
 * @method static Builder|ExpenseCreditNoteItemTransaction newQuery()
 * @method static Builder|ExpenseCreditNoteItemTransaction query()
 * @method static Builder|ExpenseCreditNoteItemTransaction whereAdditionalDescription($value)
 * @method static Builder|ExpenseCreditNoteItemTransaction whereCessAmount($value)
 * @method static Builder|ExpenseCreditNoteItemTransaction whereCessRate($value)
 * @method static Builder|ExpenseCreditNoteItemTransaction whereClassificationCessTax($value)
 * @method static Builder|ExpenseCreditNoteItemTransaction whereClassificationCgstTax($value)
 * @method static Builder|ExpenseCreditNoteItemTransaction whereClassificationIgstTax($value)
 * @method static Builder|ExpenseCreditNoteItemTransaction whereClassificationIsRcmApplicable($value)
 * @method static Builder|ExpenseCreditNoteItemTransaction whereClassificationNatureType($value)
 * @method static Builder|ExpenseCreditNoteItemTransaction whereClassificationSgstTax($value)
 * @method static Builder|ExpenseCreditNoteItemTransaction whereConsolidatingItemsToInvoice($value)
 * @method static Builder|ExpenseCreditNoteItemTransaction whereCreatedAt($value)
 * @method static Builder|ExpenseCreditNoteItemTransaction whereDiscountType($value)
 * @method static Builder|ExpenseCreditNoteItemTransaction whereDiscountValue($value)
 * @method static Builder|ExpenseCreditNoteItemTransaction whereExpenseCnId($value)
 * @method static Builder|ExpenseCreditNoteItemTransaction whereGstId($value)
 * @method static Builder|ExpenseCreditNoteItemTransaction whereGstTaxPercentage($value)
 * @method static Builder|ExpenseCreditNoteItemTransaction whereId($value)
 * @method static Builder|ExpenseCreditNoteItemTransaction whereItemId($value)
 * @method static Builder|ExpenseCreditNoteItemTransaction whereLedgerId($value)
 * @method static Builder|ExpenseCreditNoteItemTransaction whereQuantity($value)
 * @method static Builder|ExpenseCreditNoteItemTransaction whereRpuWithGst($value)
 * @method static Builder|ExpenseCreditNoteItemTransaction whereRpuWithoutGst($value)
 * @method static Builder|ExpenseCreditNoteItemTransaction whereTotal($value)
 * @method static Builder|ExpenseCreditNoteItemTransaction whereTotalDiscountAmount($value)
 * @method static Builder|ExpenseCreditNoteItemTransaction whereUnitId($value)
 * @method static Builder|ExpenseCreditNoteItemTransaction whereUpdatedAt($value)
 *
 * @mixin Eloquent
 *
 * @property float $classification_is_itc_applicable
 * @property float $taxable_amount
 * @property-read ExpenseCreditNoteTransaction $expenseCreditNotesTransaction
 * @property-read ItemMaster $items
 *
 * @method static Builder|ExpenseCreditNoteItemTransaction whereClassificationIsItcApplicable($value)
 * @method static Builder|ExpenseCreditNoteItemTransaction whereTaxableAmount($value)
 *
 * @property-read \App\Models\UnitOfMeasurement|null $unit
 */
class ExpenseCreditNoteItemTransaction extends Model
{
    use HasFactory;

    protected $table = 'expense_credit_note_item_transactions';

    public $fillable = [
        'expense_cn_id',
        'item_id',
        'additional_description',
        'unit_id',
        'hsn_code',
        'mrp',
        'quantity',
        'with_tax',
        'rpu_with_gst',
        'rpu_without_gst',
        'discount_type',
        'discount_value',
        'discount_type_2',
        'discount_value_2',
        'total_discount_amount',
        'gst_id',
        'gst_tax_percentage',
        'total',
        'ledger_id',
        'classification_nature_type',
        'classification_is_rcm_applicable',
        'classification_is_itc_applicable',
        'classification_igst_tax',
        'classification_cgst_tax',
        'classification_sgst_tax',
        'classification_cess_tax',
        'consolidating_items_to_invoice',
        'cess_rate',
        'cess_amount',
        'taxable_amount',
        'decimal_places_for_quantity',
        'decimal_places_for_rate',
    ];

    public $casts = [
        'hsn_code' => 'string',
        'quantity' => 'double',
        'rpu_with_gst' => 'double',
        'rpu_without_gst' => 'double',
        'discount_type' => 'integer',
        'discount_value' => 'double',
        'discount_type_2' => 'integer',
        'discount_value_2' => 'double',
        'total_discount_amount' => 'double',
        'gst_tax_percentage' => 'double',
        'total' => 'double',
        'classification_nature_type' => 'integer',
        'classification_is_rcm_applicable' => 'double',
        'classification_is_itc_applicable' => 'double',
        'classification_igst_tax' => 'double',
        'classification_cgst_tax' => 'double',
        'classification_sgst_tax' => 'double',
        'classification_cess_tax' => 'double',
        'consolidating_items_to_invoice' => 'string',
        'cess_rate' => 'double',
        'cess_amount' => 'double',
        'with_tax' => 'boolean',
    ];

    public $append = ['taxable_value'];

    public function classificationNatureType(): BelongsTo
    {
        return $this->belongsTo(TaxClassificationDetails::class, 'classification_nature_type', 'id');
    }

    public function items(): BelongsTo
    {
        return $this->belongsTo(ItemMaster::class, 'item_id', 'id');
    }

    public function ledger(): BelongsTo
    {
        return $this->belongsTo(Ledger::class, 'ledger_id', 'id');
    }

    public function expenseCreditNotesTransaction(): BelongsTo
    {
        return $this->belongsTo(ExpenseCreditNoteTransaction::class, 'expense_cn_id', 'id');
    }

    public function unit(): BelongsTo
    {
        return $this->belongsTo(UnitOfMeasurement::class, 'unit_id', 'id');
    }

    public function gst(): BelongsTo
    {
        return $this->belongsTo(GstTax::class, 'gst_id', 'id');
    }

    public function customFieldTransactionItemsValues(): MorphMany
    {
        return $this->morphMany(ItemCustomFieldValue::class, 'model');
    }

    public function customFieldTransactionItemsInventoryValues(): MorphMany
    {
        return $this->morphMany(ItemCustomFieldCombinationInventory::class, 'model');
    }

    protected function taxableValue(): Attribute
    {
        return Attribute::make(
            get: fn ($value
            ) => ((float) $this->quantity * (float) $this->rpu_without_gst) - (float) $this->total_discount_amount,
        );
    }

    public function getLedgerReport()
    {
        $transaction = $this->expenseCreditNotesTransaction;

        return [
            'transaction_id' => $transaction->id,
            'date' => $transaction->voucher_date->format('d-m-y'),
            'ledger_name' => $transaction->supplier->name,
            'transaction_type' => 'Expense Cr. Note',
            'voucher_no' => $transaction->voucher_number,
            'invoice_no' => '',
            'narration' => $transaction->narration ?? null,
            'debit_amount' => $this->taxable_value,
            'credit_amount' => 0,
            'balance' => 0,
        ];
    }
}
