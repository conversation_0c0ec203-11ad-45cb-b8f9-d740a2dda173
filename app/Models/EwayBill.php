<?php

namespace App\Models;

use App\Traits\HasCompany;
use Barryvdh\LaravelIdeHelper\Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

/**
 * Class EwayBill
 *
 * @property int $id
 * @property int $company_id
 * @property string $eway_bill_no
 * @property int $bill_type
 * @property int $supply_type
 * @property int $sub_type
 * @property int $transaction_id
 * @property string $transaction_type
 * @property int $is_canceled
 * @property string|null $meta
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 *
 * @method static Builder|EwayBill newModelQuery()
 * @method static Builder|EwayBill newQuery()
 * @method static Builder|EwayBill query()
 * @method static Builder|EwayBill whereBillType($value)
 * @method static Builder|EwayBill whereCompanyId($value)
 * @method static Builder|EwayBill whereCreatedAt($value)
 * @method static Builder|EwayBill whereEwayBillNo($value)
 * @method static Builder|EwayBill whereId($value)
 * @method static Builder|EwayBill whereIsCanceled($value)
 * @method static Builder|EwayBill whereMeta($value)
 * @method static Builder|EwayBill whereSubType($value)
 * @method static Builder|EwayBill whereSupplyType($value)
 * @method static Builder|EwayBill whereTransactionId($value)
 * @method static Builder|EwayBill whereTransactionType($value)
 * @method static Builder|EwayBill whereUpdatedAt($value)
 *
 * @mixin Eloquent
 *
 * @property string|null $eway_bill_date
 * @property string|null $valid_upto
 * @property-read Company $company
 * @property-read SaleTransaction|null $saleTransaction
 * @property-read PurchaseReturnTransaction|null $purchaseReturnTransaction
 *
 * @method static Builder|EwayBill whereEwayBillDate($value)
 * @method static Builder|EwayBill whereValidUpto($value)
 * @method static Builder|EwayBill whereDateBetween($fieldName, $fromDate, $toDate)
 * @method static \Database\Factories\EwayBillFactory factory($count = null, $state = [])
 *
 * @mixin \Eloquent
 */
class EwayBill extends Model
{
    use HasCompany;
    use HasFactory;

    public $table = 'eway_bills';

    public $fillable = [
        'company_id',
        'eway_bill_no',
        'eway_bill_date',
        'valid_upto',
        'bill_type',
        'supply_type',
        'sub_type',
        'transaction_id',
        'transaction_type',
        'is_canceled',
        'meta',
    ];

    public const PURCHASE_RETURN = 'purchase-return';

    public const DELIVERY_CHALLAN = 'delivery-challan';

    public const SALE_RETURN = 'sale-return';

    public const SALE = 'sale';

    public const SELECT_REASON = 0;

    public const DUPLICATE = 1;

    public const ORDER_CANCELLED = 2;

    public const DATA_ENTRY_MISTAKE = 3;

    public const OTHERS = 4;

    public const REASON = [
        self::SELECT_REASON => 'Select Reason',
        self::DUPLICATE => 'Duplicate',
        self::ORDER_CANCELLED => 'Order Cancelled',
        self::DATA_ENTRY_MISTAKE => 'Data Entry Mistake',
        self::OTHERS => 'Others',
    ];

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    public function saleTransaction(): BelongsTo
    {
        return $this->belongsTo(SaleTransaction::class, 'transaction_id', 'id');
    }

    public function purchaseReturnTransaction(): BelongsTo
    {
        return $this->belongsTo(PurchaseReturnTransaction::class, 'transaction_id', 'id');
    }

    public function deliveryChallanTransaction(): BelongsTo
    {
        return $this->belongsTo(DeliveryChallanTransaction::class, 'transaction_id', 'id');
    }

    public function saleReturnTransaction(): BelongsTo
    {
        return $this->belongsTo(SaleReturnTransaction::class, 'transaction_id', 'id');
    }

    /**
     * Scope a query to only include the last n days records
     */
    public function scopeWhereDateBetween(Builder $query, $fieldName, $fromDate, $toDate): Builder
    {
        return $query->whereDate($fieldName, '>=', $fromDate)->whereDate($fieldName, '<=', $toDate);
    }
}
