<?php

namespace App\Models;

use App\Models\Master\ItemMaster;
use Barryvdh\LaravelIdeHelper\Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Support\Carbon;

/**
 * App\Models\SaleReturnItemTransaction
 *
 * @property int $id
 * @property int $srt_id
 * @property int $item_id
 * @property int|null $ledger_id
 * @property string|null $additional_description
 * @property int|null $unit_id
 * @property float|null $quantity
 * @property float|null $free_quantity
 * @property float|null $rpu_with_gst
 * @property float|null $rpu_without_gst
 * @property int $discount_type
 * @property float|null $discount_value
 * @property float|null $total_discount_amount
 * @property int|null $gst_id
 * @property float|null $gst_tax_percentage
 * @property float|null $total
 * @property float|null $classification_igst_tax
 * @property float|null $classification_cgst_tax
 * @property float|null $classification_sgst_tax
 * @property float|null $classification_cess_tax
 * @property float $classification_is_rcm_applicable
 * @property float|null $classification_nature_type
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property string|null $consolidating_items_to_invoice
 * @property float $cess_rate
 * @property float $cess_amount
 * @property float $taxable_value
 * @property-read TaxClassificationDetails|null $classificationNatureType
 * @property-read Ledger|null $ledger
 * @property-read SaleReturnTransaction $saleReturnTransaction
 *
 * @method static Builder|SaleReturnItemTransaction newModelQuery()
 * @method static Builder|SaleReturnItemTransaction newQuery()
 * @method static Builder|SaleReturnItemTransaction query()
 * @method static Builder|SaleReturnItemTransaction whereAdditionalDescription($value)
 * @method static Builder|SaleReturnItemTransaction whereCessAmount($value)
 * @method static Builder|SaleReturnItemTransaction whereCessRate($value)
 * @method static Builder|SaleReturnItemTransaction whereClassificationCessTax($value)
 * @method static Builder|SaleReturnItemTransaction whereClassificationCgstTax($value)
 * @method static Builder|SaleReturnItemTransaction whereClassificationIgstTax($value)
 * @method static Builder|SaleReturnItemTransaction whereClassificationIsRcmApplicable($value)
 * @method static Builder|SaleReturnItemTransaction whereClassificationNatureType($value)
 * @method static Builder|SaleReturnItemTransaction whereClassificationSgstTax($value)
 * @method static Builder|SaleReturnItemTransaction whereConsolidatingItemsToInvoice($value)
 * @method static Builder|SaleReturnItemTransaction whereCreatedAt($value)
 * @method static Builder|SaleReturnItemTransaction whereDiscountType($value)
 * @method static Builder|SaleReturnItemTransaction whereDiscountValue($value)
 * @method static Builder|SaleReturnItemTransaction whereGstId($value)
 * @method static Builder|SaleReturnItemTransaction whereGstTaxPercentage($value)
 * @method static Builder|SaleReturnItemTransaction whereId($value)
 * @method static Builder|SaleReturnItemTransaction whereItemId($value)
 * @method static Builder|SaleReturnItemTransaction whereLedgerId($value)
 * @method static Builder|SaleReturnItemTransaction whereQuantity($value)
 * @method static Builder|SaleReturnItemTransaction whereFreeQuantity($value)
 * @method static Builder|SaleReturnItemTransaction whereRpuWithGst($value)
 * @method static Builder|SaleReturnItemTransaction whereRpuWithoutGst($value)
 * @method static Builder|SaleReturnItemTransaction whereSrtId($value)
 * @method static Builder|SaleReturnItemTransaction whereTotal($value)
 * @method static Builder|SaleReturnItemTransaction whereTotalDiscountAmount($value)
 * @method static Builder|SaleReturnItemTransaction whereUnitId($value)
 * @method static Builder|SaleReturnItemTransaction whereUpdatedAt($value)
 *
 * @mixin Eloquent
 *
 * @property float|null $taxable_amount
 * @property-read ItemMaster $items
 * @property-read \App\Models\UnitOfMeasurement|null $unit
 *
 * @method static Builder|SaleReturnItemTransaction whereTaxableAmount($value)
 */
class SaleReturnItemTransaction extends Model
{
    use HasFactory;

    public $table = 'sale_return_item_transactions';

    public $fillable = [
        'srt_id',
        'item_id',
        'additional_description',
        'unit_id',
        'hsn_code',
        'mrp',
        'quantity',
        'free_quantity',
        'with_tax',
        'rpu_with_gst',
        'rpu_without_gst',
        'discount_type',
        'discount_value',
        'discount_type_2',
        'discount_value_2',
        'total_discount_amount',
        'gst_id',
        'gst_tax_percentage',
        'total',
        'ledger_id',
        'classification_nature_type',
        'classification_is_rcm_applicable',
        'classification_igst_tax',
        'classification_cgst_tax',
        'classification_sgst_tax',
        'classification_cess_tax',
        'consolidating_items_to_invoice',
        'cess_rate',
        'cess_amount',
        'taxable_amount',
        'decimal_places_for_quantity',
        'decimal_places_for_rate',
    ];

    public $casts = [
        'hsn_code' => 'string',
        'quantity' => 'double',
        'free_quantity' => 'double',
        'rpu_with_gst' => 'double',
        'rpu_without_gst' => 'double',
        'discount_type' => 'integer',
        'discount_value' => 'double',
        'discount_type_2' => 'integer',
        'discount_value_2' => 'double',
        'total_discount_amount' => 'double',
        'gst_tax_percentage' => 'double',
        'total' => 'double',
        'classification_nature_type' => 'integer',
        'classification_is_rcm_applicable' => 'double',
        'classification_igst_tax' => 'double',
        'classification_cgst_tax' => 'double',
        'classification_sgst_tax' => 'double',
        'classification_cess_tax' => 'double',
        'consolidating_items_to_invoice' => 'string',
        'cess_rate' => 'double',
        'cess_amount' => 'double',
        'with_tax' => 'boolean',
    ];

    public function classificationNatureType(): BelongsTo
    {
        return $this->belongsTo(TaxClassificationDetails::class, 'classification_nature_type', 'id');
    }

    public function items(): BelongsTo
    {
        return $this->belongsTo(ItemMaster::class, 'item_id', 'id');
    }

    public function unit(): BelongsTo
    {
        return $this->belongsTo(UnitOfMeasurement::class, 'unit_id', 'id');
    }

    public function ledger(): BelongsTo
    {
        return $this->belongsTo(Ledger::class, 'ledger_id', 'id');
    }

    public function saleReturnTransaction(): BelongsTo
    {
        return $this->belongsTo(SaleReturnTransaction::class, 'srt_id', 'id');
    }

    public function gst(): BelongsTo
    {
        return $this->belongsTo(GstTax::class, 'gst_id', 'id');
    }

    public function customFieldTransactionItemsValues(): MorphMany
    {
        return $this->morphMany(ItemCustomFieldValue::class, 'model');
    }

    public function customFieldTransactionItemsInventoryValues(): MorphMany
    {
        return $this->morphMany(ItemCustomFieldCombinationInventory::class, 'model');
    }

    protected function taxableValue(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => ((float) $this->quantity * (float) $this->rpu_without_gst) - (float) $this->total_discount_amount,
        );
    }

    public function getLedgerReport()
    {
        $saleTransaction = $this->saleReturnTransaction;

        return [
            'transaction_id' => $saleTransaction->id,
            'date' => $saleTransaction->date->format('d-m-y'),
            'ledger_name' => $saleTransaction->customer->name,
            'transaction_type' => 'Sale Return',
            'voucher_no' => $saleTransaction->full_invoice_number,
            'invoice_no' => '',
            'debit_amount' => $this->taxable_value,
            'narration' => $saleTransaction->narration ?? null,
            'credit_amount' => 0,
            'balance' => 0,
        ];
    }
}
