<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ItemCustomFieldCombinationInventory extends Model
{
    use HasFactory;

    protected $table = 'item_custom_field_combination_inventory';

    protected $fillable = [
        'item_custom_field_combination_id',
        'model_id',
        'model_type',
        'quantity',
    ];

    public function itemCustomFieldCombination()
    {
        return $this->belongsTo(ItemCustomFieldCombination::class);
    }

    public function model()
    {
        return $this->morphTo();
    }

}
