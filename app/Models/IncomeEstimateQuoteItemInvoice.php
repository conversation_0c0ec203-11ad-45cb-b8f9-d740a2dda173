<?php

namespace App\Models;

use App\Models\Master\ItemMaster;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;

/**
 * App\Models\IncomeEstimateQuoteItemInvoice
 *
 * @property int $id
 * @property int $transactions_id
 * @property int $item_id
 * @property int|null $ledger_id
 * @property string|null $additional_description
 * @property int|null $unit_id
 * @property float|null $quantity
 * @property float|null $rpu_with_gst
 * @property float|null $rpu_without_gst
 * @property int $discount_type
 * @property float|null $discount_value
 * @property float|null $total_discount_amount
 * @property int|null $gst_id
 * @property float|null $gst_tax_percentage
 * @property float|null $total
 * @property float $cess_rate
 * @property float $cess_amount
 * @property float|null $classification_igst_tax
 * @property float|null $classification_cgst_tax
 * @property float|null $classification_sgst_tax
 * @property float|null $classification_cess_tax
 * @property int $classification_is_rcm_applicable
 * @property int|null $classification_nature_type
 * @property string|null $consolidating_items_to_invoice
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder|IncomeEstimateQuoteItemInvoice newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|IncomeEstimateQuoteItemInvoice newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|IncomeEstimateQuoteItemInvoice query()
 * @method static \Illuminate\Database\Eloquent\Builder|IncomeEstimateQuoteItemInvoice whereAdditionalDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|IncomeEstimateQuoteItemInvoice whereCessAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|IncomeEstimateQuoteItemInvoice whereCessRate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|IncomeEstimateQuoteItemInvoice whereClassificationCessTax($value)
 * @method static \Illuminate\Database\Eloquent\Builder|IncomeEstimateQuoteItemInvoice whereClassificationCgstTax($value)
 * @method static \Illuminate\Database\Eloquent\Builder|IncomeEstimateQuoteItemInvoice whereClassificationIgstTax($value)
 * @method static \Illuminate\Database\Eloquent\Builder|IncomeEstimateQuoteItemInvoice whereClassificationIsRcmApplicable($value)
 * @method static \Illuminate\Database\Eloquent\Builder|IncomeEstimateQuoteItemInvoice whereClassificationNatureType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|IncomeEstimateQuoteItemInvoice whereClassificationSgstTax($value)
 * @method static \Illuminate\Database\Eloquent\Builder|IncomeEstimateQuoteItemInvoice whereConsolidatingItemsToInvoice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|IncomeEstimateQuoteItemInvoice whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|IncomeEstimateQuoteItemInvoice whereDiscountType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|IncomeEstimateQuoteItemInvoice whereDiscountValue($value)
 * @method static \Illuminate\Database\Eloquent\Builder|IncomeEstimateQuoteItemInvoice whereGstId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|IncomeEstimateQuoteItemInvoice whereGstTaxPercentage($value)
 * @method static \Illuminate\Database\Eloquent\Builder|IncomeEstimateQuoteItemInvoice whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|IncomeEstimateQuoteItemInvoice whereItemId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|IncomeEstimateQuoteItemInvoice whereLedgerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|IncomeEstimateQuoteItemInvoice whereQuantity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|IncomeEstimateQuoteItemInvoice whereRpuWithGst($value)
 * @method static \Illuminate\Database\Eloquent\Builder|IncomeEstimateQuoteItemInvoice whereRpuWithoutGst($value)
 * @method static \Illuminate\Database\Eloquent\Builder|IncomeEstimateQuoteItemInvoice whereTotal($value)
 * @method static \Illuminate\Database\Eloquent\Builder|IncomeEstimateQuoteItemInvoice whereTotalDiscountAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|IncomeEstimateQuoteItemInvoice whereTransactionsId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|IncomeEstimateQuoteItemInvoice whereUnitId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|IncomeEstimateQuoteItemInvoice whereUpdatedAt($value)
 * 
 * @property-read float $taxable_value
 *
 * @mixin \Eloquent
 */
class IncomeEstimateQuoteItemInvoice extends Model
{
    use HasFactory;

    public $table = 'income_estimate_quote_item_invoice';

    public $fillable = [
        'transactions_id',
        'item_id',
        'ledger_id',
        'additional_description',
        'unit_id',
        'hsn_code',
        'quantity',
        'mrp',
        'with_tax',
        'rpu_with_gst',
        'rpu_without_gst',
        'discount_type',
        'discount_value',
        'discount_type_2',
        'discount_value_2',
        'total_discount_amount',
        'gst_id',
        'gst_tax_percentage',
        'total',
        'cess_rate',
        'cess_amount',
        'classification_igst_tax',
        'classification_cgst_tax',
        'classification_sgst_tax',
        'classification_cess_tax',
        'classification_is_rcm_applicable',
        'classification_nature_type',
        'consolidating_items_to_invoice',
        'decimal_places_for_quantity',
        'decimal_places_for_rate',
    ];

    public $appends = ['taxable_value'];

    public $casts = [
        'hsn_code' => 'string',
        'quantity' => 'double',
        'rpu_with_gst' => 'double',
        'rpu_without_gst' => 'double',
        'discount_type' => 'integer',
        'discount_value' => 'double',
        'discount_type_2' => 'integer',
        'discount_value_2' => 'double',
        'total_discount_amount' => 'double',
        'gst_tax_percentage' => 'double',
        'total' => 'double',
        'classification_nature_type' => 'integer',
        'classification_is_rcm_applicable' => 'double',
        'classification_igst_tax' => 'double',
        'classification_cgst_tax' => 'double',
        'classification_sgst_tax' => 'double',
        'classification_cess_tax' => 'double',
        'consolidating_items_to_invoice' => 'string',
        'cess_rate' => 'double',
        'cess_amount' => 'double',
        'with_tax' => 'boolean',
    ];

    public function items(): BelongsTo
    {
        return $this->belongsTo(ItemMaster::class, 'item_id', 'id');
    }

    public function ledgers(): BelongsTo
    {
        return $this->belongsTo(Ledger::class, 'ledger_id', 'id');
    }

    public function classificationNatureType(): BelongsTo
    {
        return $this->belongsTo(TaxClassificationDetails::class, 'classification_nature_type', 'id');
    }

    public function unit(): BelongsTo
    {
        return $this->belongsTo(UnitOfMeasurement::class, 'unit_id', 'id');
    }

    public function estimateTransaction(): BelongsTo
    {
        return $this->belongsTo(IncomeEstimateQuoteTransaction::class, 'transactions_id', 'id');
    }

    public function gst(): BelongsTo
    {
        return $this->belongsTo(GstTax::class, 'gst_id', 'id');
    }

    public function customFieldTransactionItemsValues(): MorphMany
    {
        return $this->morphMany(ItemCustomFieldValue::class, 'model');
    }

    public function customFieldTransactionItemsInventoryValues(): MorphMany
    {
        return $this->morphMany(ItemCustomFieldCombinationInventory::class, 'model');
    }

    protected function taxableValue(): Attribute
    {
        return Attribute::make(
            get: fn ($value
            ) => ((float) $this->quantity * (float) $this->rpu_without_gst) - (float) $this->total_discount_amount,
        );
    }
}
