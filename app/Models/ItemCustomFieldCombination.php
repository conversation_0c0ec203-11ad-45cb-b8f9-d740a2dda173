<?php

namespace App\Models;

use App\Models\Master\ItemMaster;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ItemCustomFieldCombination extends Model
{
    use HasFactory;

    protected $table = 'item_custom_field_combinations';

    protected $fillable = [
        'item_id',
        'available_quantity',
        'cf_id_1',
        'cf_value_1',
        'cf_id_2',
        'cf_value_2',
        'cf_id_3',
        'cf_value_3',
        'cf_id_4',
        'cf_value_4',
        'cf_id_5',
        'cf_value_5',
        'purchase_rate',
        'purchase_date',
    ];

    public function combinationInventory()
    {
        return $this->hasMany(ItemCustomFieldCombinationInventory::class, 'item_custom_field_combination_id', 'id');
    }

    public function item()
    {
        return $this->belongsTo(ItemMaster::class, 'item_id');
    }

    public function customField1()
    {
        return $this->belongsTo(ItemCustomField::class, 'cf_id_1');
    }

    public function customField2()
    {
        return $this->belongsTo(ItemCustomField::class, 'cf_id_2');
    }

    public function customField3()
    {
        return $this->belongsTo(ItemCustomField::class, 'cf_id_3');
    }

    public function customField4()
    {
        return $this->belongsTo(ItemCustomField::class, 'cf_id_4');
    }

    public function customField5()
    {
        return $this->belongsTo(ItemCustomField::class, 'cf_id_5');
    }

    protected function purchaseDate(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => $value ? date('d-m-Y', strtotime($value)) : null,
            set: fn ($value) => $value ? date('Y-m-d', strtotime($value)) : null,
        );
    }
}
