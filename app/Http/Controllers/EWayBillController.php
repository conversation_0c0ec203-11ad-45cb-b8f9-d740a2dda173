<?php

namespace App\Http\Controllers;

use App\Actions\EwayBill\CancelEwayBill;
use App\Actions\EwayBill\GenerateEwayBill;
use App\Actions\EwayBill\GetEwayBillData;
use App\Http\Requests\EwayBillRequest;
use App\Models\DeliveryChallanTransaction;
use App\Models\EwayBill;
use App\Models\PurchaseReturnTransaction;
use App\Models\SaleReturnTransaction;
use App\Models\SaleTransaction;
use App\Repositories\EWayBillRepository;
use Exception;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Laracasts\Flash\Flash;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

class EWayBillController extends AppBaseController
{
    public EWayBillRepository $ewayBillRepo;

    public function __construct(EWayBillRepository $ewayBillRepo)
    {
        $this->ewayBillRepo = $ewayBillRepo;
    }

    public function index(Request $request, $transactionId, $transactionType)
    {
        $company = getCurrentCompany();
        $fromGstIn = $company->companyTax->gstin ?? '';
        if (empty($fromGstIn) || $fromGstIn == '') {
            if ($request->ajax()) {
                throw new UnprocessableEntityHttpException('From GSTIN is compulsory for generate E-Way Bill.');
            }
            Flash::error('From GSTIN is compulsory for generate E-Way Bill.');

            return redirect()->back();
        }

        if ($transactionType == EwayBill::PURCHASE_RETURN) {
            $transaction = PurchaseReturnTransaction::whereId($transactionId)->firstOrFail();
            $invoiceType = $transaction->pr_item_type;
        } elseif ($transactionType == EwayBill::DELIVERY_CHALLAN) {
            $transaction = DeliveryChallanTransaction::whereId($transactionId)->firstOrFail();
            $invoiceType = 2;
            if ($transaction->invoice_type != DeliveryChallanTransaction::WITH_AMOUNT) {
                Flash::error('E-Way bill is not generate in without tax invoice');

                return redirect()->back();
            }
        } elseif ($transactionType == EwayBill::SALE_RETURN) {
            $transaction = SaleReturnTransaction::whereId($transactionId)->firstOrFail();
            $invoiceType = $transaction->sr_item_type;
        } else {
            $transaction = SaleTransaction::whereId($transactionId)->firstOrFail();
            $invoiceType = $transaction->sales_item_type;
        }

        if ($invoiceType == SaleTransaction::ACCOUNTING_INVOICE) {
            Flash::error('E-Way bill is not generate in accounting invoice');

            return redirect()->back();
        }

        $data = GetEwayBillData::run($transactionId, $transactionType);

        return view('company.e-way-bill.index', compact('data', 'transactionType'));
    }

    /**
     * @param  $transactionType
     * @return JsonResponse|void
     *
     * @throws Exception
     */
    public function generateEWayBill(EwayBillRequest $request, $transactionId)
    {
        $data = GenerateEwayBill::run($request, $transactionId);

        return $this->sendResponse($data, 'Generate E-Way Bill Successfully');
    }

    /**
     * @return View
     */
    public function eWayBillTableIndex(): \Illuminate\View\View
    {
        return view('company.e-way-bill.table.index');
    }

    public function eWayBillCancel(Request $request, $id): JsonResponse
    {
        $data = CancelEwayBill::run($request, $id);

        return $this->sendResponse($data, 'E-Way Bill Cancel Successfully');
    }

    public function getDistanceFromPincode(Request $request)
    {
        $data = $this->ewayBillRepo->getDistanceFromPincode($request);

        return $this->sendResponse($data, 'Distance retrieved from pincode successfully.');
    }

    public function getEwaybillPdf($ewayBillId)
    {
        return $this->ewayBillRepo->getEwaybillPdf($ewayBillId);

    }

    public function getEwaybillDeatilsPdf($ewayBillId)
    {
        return $this->ewayBillRepo->getEwaybillDeatilsPdf($ewayBillId);
    }
}
