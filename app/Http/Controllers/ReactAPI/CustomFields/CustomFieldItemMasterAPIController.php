<?php

namespace App\Http\Controllers\ReactAPI\CustomFields;

use App\Actions\CustomFieldsItemMaster\DeleteCustomFieldOfItemMaster;
use App\Actions\CustomFieldsItemMaster\DeleteFormulaAction;
use App\Actions\CustomFieldsItemMaster\EditCustomFieldItemMaster;
use App\Actions\CustomFieldsItemMaster\GetAllItemMasterCustomFields;
use App\Actions\CustomFieldsItemMaster\OrderingCustomFieldsAction;
use App\Actions\CustomFieldsItemMaster\StoreCustomFieldItemMaster;
use App\Actions\CustomFieldsItemMaster\StoreUpdateCustomFieldDefaultFormula;
use App\Actions\CustomFieldsItemMaster\UpdateCustomFieldItemMaster;
use App\Actions\CustomFieldsItemMaster\UpdateStatusOfItemCustomFieldAction;
use App\Actions\CustomFieldsItemMaster\UpdateTransactionStatusOfItemCustomFieldAction;
use App\Http\Controllers\API\v1\AppBaseAPIController;
use App\Http\Requests\ReactAPI\CustomFieldsItemMasterAPIRequest;
use App\Models\ItemCustomField;
use Illuminate\Http\Request;

class CustomFieldItemMasterAPIController extends AppBaseAPIController
{
    public function getCustomFieldTypes()
    {
        $customFieldsTypes = ItemCustomField::CUSTOM_FIELD_ARRAY_WITH_INPUT_TYPE;

        $response = collect($customFieldsTypes)->values()->toArray();

        return $this->sendResponse($response, 'Get Custom Field Types successfully.');
    }

    public function index(Request $request)
    {
        $input = $request->all();
        $itemId = $input['item_id'] ?? null;

        $response = GetAllItemMasterCustomFields::run($itemId);

        return $this->sendResponse($response, 'Get all Custom Field of Item Master successfully.');
    }

    public function store(CustomFieldsItemMasterAPIRequest $request)
    {
        $input = $request->all();

        // this condition is temporary for inventory custom field
        if (isset($input['open_in_popup']) && $input['open_in_popup'] == 1) {
            $conflictingType = $input['custom_field_type'] == 1 ? 2 : 1;
            $conflictExists = ItemCustomField::where('custom_field_type', $conflictingType)->where('open_in_popup', true)->exists();
            if ($conflictExists) {
                return $this->sendError('You can not add single qty and multiple qty custom field at same time.');
            }

            $maxFive = ItemCustomField::where('open_in_popup', true)->count();
            if ($maxFive >= 5) {
                return $this->sendError('You can not add more than 5 custom fields.');
            }
        }

        $response = StoreCustomFieldItemMaster::run($input);

        return $this->sendResponse($response, 'Custom Field of Item Master created successfully.');
    }

    public function edit($id)
    {
        $response = EditCustomFieldItemMaster::run($id);

        return $this->sendResponse($response, 'Get Custom Field of Item Master successfully.');
    }

    public function update(CustomFieldsItemMasterAPIRequest $request, $id)
    {
        $input = $request->all();

        $response = UpdateCustomFieldItemMaster::run($input, $id);

        return $this->sendResponse($response, 'Custom Field of Item Master updated successfully.');
    }

    public function delete($id)
    {
        DeleteCustomFieldOfItemMaster::run($id);

        return $this->sendSuccess('Custom Field of Item Master deleted successfully.');
    }

    public function updateStatus(Request $request)
    {
        $request->validate([
            'custom_field_id' => 'required|integer|exists:item_custom_field,id',
            'status' => 'required|boolean',
        ]);

        $input = $request->all();

        $response = UpdateStatusOfItemCustomFieldAction::run($input);

        return $this->sendResponse($response, 'Custom Field Status Updated successfully.');
    }

    public function updateTransactionStatus(Request $request)
    {
        $request->validate([
            'custom_field_id' => 'required|integer|exists:item_custom_field,id',
            'is_enabled' => 'required',
            'transaction_type' => 'required',
        ]);

        $input = $request->all();

        UpdateTransactionStatusOfItemCustomFieldAction::run($input);

        return $this->sendSuccess('Custom Field Status Updated successfully.');
    }

    public function storeUpdateFormula(Request $request)
    {
        $request->validate([
            'formula' => 'required',
            'is_system_field' => 'required|in:0,1',
            'custom_field_id' => 'nullable|required_if:is_system_field,0|integer|exists:item_custom_field,id',
            'system_field_name' => 'nullable|string|required_if:is_system_field,1',
            'used_cf_ids_for_formula' => 'nullable|string',
        ], [
            'formula.required' => 'Please enter formula.',
            'is_system_field.required' => 'Please select custom field or system key.',
            'custom_field_id.required_if' => 'Please select custom field.',
            'system_field_name.required_if' => 'Please select system key.',
            'used_cf_ids_for_formula.required' => 'Please select at least one custom field.',
        ]);

        $input = $request->all();

        $response = StoreUpdateCustomFieldDefaultFormula::run($input);

        return $this->sendResponse($response, 'Custom Field Formula created or updated successfully.');
    }

    public function deleteFormula(Request $request, $id)
    {
        DeleteFormulaAction::run($id);

        return $this->sendSuccess('Custom Field Formula Deleted successfully.');
    }

    public function orderingCustomFields(Request $request)
    {
        $request->validate([
            'ordering' => 'required|array',
        ]);

        $input = $request->all();

        $response = OrderingCustomFieldsAction::run($input);

        return $this->sendResponse($response, 'Custom Field Formula ordered successfully.');
    }
}
