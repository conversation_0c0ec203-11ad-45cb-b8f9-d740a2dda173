<?php

namespace App\Http\Livewire;

use App\Models\EwayBill;
use App\Repositories\EWayBillRepository;
use Carbon\Carbon;
use Illuminate\Support\Facades\App;

class EWayBillTable extends CustomLivewireTableComponent
{
    protected $listeners = ['refresh' => '$refresh', 'searchTable', 'searchColumn', 'sortColumn', 'dateFilter'];

    // public $defaultSorting = 'created_at';

    public $startDate = '';

    public $endDate = '';

    public function render()
    {
        $companyFilter = getCompanyFilters();
        $dates = isset($companyFilter['eway_bill_date']) ? explode(' - ', $companyFilter['eway_bill_date']) : [];
        $sDate = ! empty($dates) ? $dates[0] : Carbon::now()->startOfMonth()->format('Y-m-d');
        $eDate = ! empty($dates) ? $dates[1] : Carbon::now()->endOfMonth()->format('Y-m-d');

        $this->startDate = empty($this->startDate) ? $sDate : $this->startDate;
        $this->endDate = empty($this->endDate) ? $eDate : $this->endDate;

        $query = EwayBill::with(['purchaseReturnTransaction.supplier', 'saleTransaction.customer', 'deliveryChallanTransaction.party', 'saleReturnTransaction.customer'])
            ->whereBetween('eway_bill_date', [$this->startDate, $this->endDate])->orderBy('created_at', 'desc');
        $data = $query->get();

        /** @var EWayBillRepository $eWayBillRepository */
        $eWayBillRepository = App::make(EWayBillRepository::class);
        $data = $eWayBillRepository->prepareDataForTable($data);
        $data = $this->prepareAllDataWithSearchingAndSorting($data);

        $rows['data'] = $data;
        $rows['fields'] = $this->getFields();
        $rows['show_fields'] = $this->getShowFields();
        $rows['startDate'] = $this->startDate;
        $rows['endDate'] = $this->endDate;
        $this->dispatchBrowserEvent('initSelect2');

        return view('livewire.e-way-bill-table', compact('rows'));
    }

    public function dateFilter($date): void
    {
        $date = explode(' - ', $date);
        $this->startDate = $date[0];
        $this->endDate = $date[1];
    }

    public function getFields(): array
    {
        return [
            [
                'name' => 'Transaction Type',
                'column_name' => 'transaction_type',
                'is_sortable' => true,
                'is_sorting' => array_key_exists('transaction_type', $this->sortColumn),
                'sorting_direction' => $this->sortColumn['transaction_type'] ?? '',
                'is_searchable' => true,
                'is_show' => true,
                'show_total' => false,
                'is_number' => false,
            ],
            [
                'name' => 'Invoice Number',
                'column_name' => 'invoice_number',
                'is_sortable' => true,
                'is_sorting' => array_key_exists('invoice_number', $this->sortColumn),
                'sorting_direction' => $this->sortColumn['invoice_number'] ?? '',
                'is_searchable' => true,
                'is_show' => true,
                'show_total' => false,
                'is_number' => false,
            ],
            [
                'name' => 'Invoice Date',
                'column_name' => 'invoice_date',
                'is_sortable' => true,
                'is_sorting' => array_key_exists('invoice_date', $this->sortColumn),
                'sorting_direction' => $this->sortColumn['invoice_date'] ?? '',
                'is_searchable' => true,
                'is_show' => true,
                'show_total' => false,
                'is_number' => false,
            ],
            [
                'name' => 'Party Name',
                'column_name' => 'party_name',
                'is_sortable' => true,
                'is_sorting' => array_key_exists('party_name', $this->sortColumn),
                'sorting_direction' => $this->sortColumn['party_name'] ?? '',
                'is_searchable' => true,
                'is_show' => true,
                'show_total' => false,
                'is_number' => false,
            ],
            [
                'name' => 'Invoice Value',
                'column_name' => 'invoice_value',
                'is_sortable' => true,
                'is_sorting' => array_key_exists('invoice_value', $this->sortColumn),
                'sorting_direction' => $this->sortColumn['invoice_value'] ?? '',
                'is_searchable' => true,
                'is_show' => true,
                'show_total' => false,
                'is_number' => false,
            ],
            [
                'name' => 'Supply Type',
                'column_name' => 'supply_type',
                'is_sortable' => true,
                'is_sorting' => array_key_exists('supply_type', $this->sortColumn),
                'sorting_direction' => $this->sortColumn['supply_type'] ?? '',
                'is_searchable' => true,
                'is_show' => true,
                'show_total' => false,
                'is_number' => false,
            ],
            [
                'name' => 'EWay Bill Number',
                'column_name' => 'eway_bill_number',
                'is_sortable' => true,
                'is_sorting' => array_key_exists('eway_bill_number', $this->sortColumn),
                'sorting_direction' => $this->sortColumn['eway_bill_number'] ?? '',
                'is_searchable' => true,
                'is_show' => true,
                'show_total' => false,
                'is_number' => false,
            ],
            [
                'name' => 'EWay Bill Date',
                'column_name' => 'eway_bill_date',
                'is_sortable' => true,
                'is_sorting' => array_key_exists('eway_bill_date', $this->sortColumn),
                'sorting_direction' => $this->sortColumn['eway_bill_date'] ?? '',
                'is_searchable' => true,
                'is_show' => true,
                'show_total' => false,
                'is_number' => false,
            ],
            [
                'name' => 'Valid Upto',
                'column_name' => 'valid_upto_date',
                'is_sortable' => true,
                'is_sorting' => array_key_exists('valid_upto_date', $this->sortColumn),
                'sorting_direction' => $this->sortColumn['valid_upto_date'] ?? '',
                'is_searchable' => true,
                'is_show' => true,
                'show_total' => false,
                'is_number' => false,
            ],
            [
                'name' => 'Action',
                'column_name' => 'action',
                'is_sortable' => false,
                'is_sorting' => array_key_exists('action', $this->sortColumn),
                'sorting_direction' => $this->sortColumn['action'] ?? '',
                'is_searchable' => false,
                'is_show' => true,
                'show_total' => false,
                'is_number' => false,
            ],
        ];
    }
}
