<?php

namespace App\Actions\Expense\DebitNote;

use App\Actions\CustomFieldsItemMaster\Inventory\CalculateAvailableQTY;
use App\Models\ExpenseDebitNoteItemTransaction;
use App\Models\ExpenseDebitNoteLedgerTransaction;
use App\Models\ExpenseDebitNoteTransaction;
use App\Models\ItemCustomFieldCombinationInventory;
use App\Models\ItemCustomFieldValue;
use App\Models\ReceiptTransaction;
use Lorisleiva\Actions\Concerns\AsAction;

class HardDeleteExpenseDebitNoteTransaction
{
    use AsAction;

    public function handle($id, $allTransaction = false, $isDirectHardDelete = false)
    {
        $query = $isDirectHardDelete ? ExpenseDebitNoteTransaction::withTrashed() : ExpenseDebitNoteTransaction::onlyTrashed();
        if ($allTransaction) {
            $expenseDebitNoteTransactions = $query->get();
        } else {
            $expenseDebitNoteTransactions = $query->whereId($id)->get();
        }

        foreach ($expenseDebitNoteTransactions as $expenseDebitNote) {
            ReceiptTransaction::withTrashed()
                ->where('company_id', $expenseDebitNote->company_id)
                ->whereReceiptNumber('expense-debit-note/'.$expenseDebitNote->voucher_number)->financialYearDate()?->forceDelete();

            $itemsIds = $expenseDebitNote->debitNoteItems->pluck('id')->toArray();
            $ledgersIds = $expenseDebitNote->debitNoteLedgers->pluck('id')->toArray();
            $allCFIds = array_merge($itemsIds, $ledgersIds);
            ItemCustomFieldValue::whereIn('model_id', $allCFIds)->whereIn('model_type', [ExpenseDebitNoteItemTransaction::class, ExpenseDebitNoteLedgerTransaction::class])?->delete();

            // Delete Inventory
            $inventoryItems = ItemCustomFieldCombinationInventory::whereIn('model_id', $allCFIds)->whereIn('model_type', [ExpenseDebitNoteItemTransaction::class, ExpenseDebitNoteLedgerTransaction::class])->get();
            foreach ($inventoryItems as $inventoryItem) {
                $combination = $inventoryItem->itemCustomFieldCombination;
                $inventoryItem->delete();
                CalculateAvailableQTY::run($combination);
            }

            $expenseDebitNote->customFieldValues()->delete();
            $expenseDebitNote->addresses()->delete();
            $expenseDebitNote->forceDelete();
        }

        return true;
    }
}
