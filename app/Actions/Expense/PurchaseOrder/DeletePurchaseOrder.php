<?php

namespace App\Actions\Expense\PurchaseOrder;

use App\Actions\CustomFieldsItemMaster\Inventory\CalculateAvailableQTY;
use App\Models\ItemCustomFieldCombination;
use App\Models\ItemCustomFieldCombinationInventory;
use App\Models\ItemCustomFieldValue;
use App\Models\PurchaseOrderAccountingInvoice;
use App\Models\PurchaseOrderItemInvoice;
use Lorisleiva\Actions\Concerns\AsAction;

class DeletePurchaseOrder
{
    use AsAction;

    public function handle($purchaseOrderTransaction)
    {
        $itemsIds = $purchaseOrderTransaction->transactionItems->pluck('id')->toArray();
        $ledgersIds = $purchaseOrderTransaction->transactionLedgers->pluck('id')->toArray();
        $allCFIds = array_merge($itemsIds, $ledgersIds);
        ItemCustomFieldValue::whereIn('model_id', $allCFIds)->whereIn('model_type', [PurchaseOrderItemInvoice::class, PurchaseOrderAccountingInvoice::class])?->delete();

        // Start Delete Inventory
        $removeCombinationIds = [];
        $inventoryItems = ItemCustomFieldCombinationInventory::whereIn('model_id', $allCFIds)->whereIn('model_type', [PurchaseOrderItemInvoice::class, PurchaseOrderAccountingInvoice::class])->get();
        foreach ($inventoryItems as $inventoryItem) {
            $combination = $inventoryItem->itemCustomFieldCombination;
            $removeCombinationIds[] = $combination->id;
            $inventoryItem->delete();
            CalculateAvailableQTY::run($combination);
        }
        // Manage if combination has no any inventory then delete
        if (count($removeCombinationIds) > 0) {
            foreach ($removeCombinationIds as $combinationId) {
                $check = ItemCustomFieldCombinationInventory::where('item_custom_field_combination_id', $combinationId)->count();
                if ($check == 0) {
                    ItemCustomFieldCombination::whereId($combinationId)->where('available_quantity', 0)->delete();
                }
            }
        }
        // End Delete Inventory

        $purchaseOrderTransaction->customFieldValues()->delete();

        $transaction = $purchaseOrderTransaction->delete();

        return $transaction;
    }
}
