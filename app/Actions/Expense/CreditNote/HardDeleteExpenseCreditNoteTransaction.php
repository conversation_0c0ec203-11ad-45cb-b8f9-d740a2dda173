<?php

namespace App\Actions\Expense\CreditNote;

use App\Actions\CustomFieldsItemMaster\Inventory\CalculateAvailableQTY;
use App\Models\ExpenseCreditNoteItemTransaction;
use App\Models\ExpenseCreditNoteLedgerTransaction;
use App\Models\ExpenseCreditNoteTransaction;
use App\Models\ItemCustomFieldCombination;
use App\Models\ItemCustomFieldCombinationInventory;
use App\Models\ItemCustomFieldValue;
use App\Models\PaymentTransaction;
use Lorisleiva\Actions\Concerns\AsAction;

class HardDeleteExpenseCreditNoteTransaction
{
    use AsAction;

    public function handle($id, $allTransaction = false, $isDirectHardDelete = false)
    {
        $query = $isDirectHardDelete ? ExpenseCreditNoteTransaction::withTrashed() : ExpenseCreditNoteTransaction::onlyTrashed();
        if ($allTransaction) {
            $expenseCreditNoteTransactions = $query->get();
        } else {
            $expenseCreditNoteTransactions = $query->whereId($id)->get();
        }

        foreach ($expenseCreditNoteTransactions as $expenseCreditNote) {
            PaymentTransaction::withTrashed()
                ->where('company_id', $expenseCreditNote->company_id)
                ->wherePaymentVoucherNumber('expense-credit-note/'.$expenseCreditNote->voucher_number)->financialYearDate()?->forceDelete();

            $itemsIds = $expenseCreditNote->expenseCreditNoteItems->pluck('id')->toArray();
            $ledgersIds = $expenseCreditNote->expenseCreditNoteLedgers->pluck('id')->toArray();
            $allCFIds = array_merge($itemsIds, $ledgersIds);
            ItemCustomFieldValue::whereIn('model_id', $allCFIds)->whereIn('model_type', [ExpenseCreditNoteItemTransaction::class, ExpenseCreditNoteLedgerTransaction::class])?->delete();

            // Start Delete Inventory
            $removeCombinationIds = [];
            $inventoryItems = ItemCustomFieldCombinationInventory::whereIn('model_id', $allCFIds)->whereIn('model_type', [ExpenseCreditNoteItemTransaction::class, ExpenseCreditNoteLedgerTransaction::class])->get();
            foreach ($inventoryItems as $inventoryItem) {
                $combination = $inventoryItem->itemCustomFieldCombination;
                $removeCombinationIds[] = $combination->id;
                $inventoryItem->delete();
                CalculateAvailableQTY::run($combination);
            }
            // Manage if combination has no any inventory then delete
            if (count($removeCombinationIds) > 0) {
                foreach ($removeCombinationIds as $combinationId) {
                    $check = ItemCustomFieldCombinationInventory::where('item_custom_field_combination_id', $combinationId)->count();
                    if ($check == 0) {
                        ItemCustomFieldCombination::whereId($combinationId)->where('available_quantity', 0)->delete();
                    }
                }
            }
            // End Delete Inventory

            $expenseCreditNote->customFieldValues()->delete();
            $expenseCreditNote->addresses()->delete();
            $expenseCreditNote->forceDelete();
        }

        return true;
    }
}
