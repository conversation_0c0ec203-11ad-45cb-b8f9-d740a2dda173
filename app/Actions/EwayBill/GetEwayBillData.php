<?php

namespace App\Actions\EwayBill;

use App\Http\Controllers\CompanyController;
use App\Models\Address;
use App\Models\DeliveryChallanTransaction;
use App\Models\DeliveryChallanTransactionItem;
use App\Models\EwayBill;
use App\Models\PurchaseReturnItemTransaction;
use App\Models\PurchaseReturnTransaction;
use App\Models\SaleReturnTransaction;
use App\Models\SaleTransaction;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\App;
use Lorisleiva\Actions\Concerns\AsAction;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

class GetEwayBillData
{
    use AsAction;

    public function handle($transactionId, $transactionType)
    {
        $company = getCurrentCompany();
        $fromGstIn = $company->companyTax->gstin ?? '';
        if (empty($fromGstIn) || $fromGstIn == '') {
            if (request()->ajax()) {
                throw new UnprocessableEntityHttpException('From GSTIN is compulsory for generate E-Way Bill.');
            }
        }

        if ($transactionType == EwayBill::PURCHASE_RETURN) {
            $transaction = PurchaseReturnTransaction::whereId($transactionId)->firstOrFail();
            /** @var PurchaseReturnItemTransaction $items */
            $items = $transaction->purchaseReturnItems;
            $customerSupplier = $transaction->supplier;
        } elseif ($transactionType == EwayBill::DELIVERY_CHALLAN) {
            $transaction = DeliveryChallanTransaction::whereId($transactionId)->firstOrFail();
            /** @var DeliveryChallanTransactionItem $items */
            $items = $transaction->transactionItems;
            $customerSupplier = $transaction->party;
        } elseif ($transactionType == EwayBill::SALE_RETURN) {
            $transaction = SaleReturnTransaction::whereId($transactionId)->firstOrFail();
            /** @var SaleReturnItemTransaction $items */
            $items = $transaction->saleReturnItems;
            $customerSupplier = $transaction->customer;
        } else {
            $transaction = SaleTransaction::whereId($transactionId)->firstOrFail();
            /** @var SaleTransactionItem $items */
            $items = $transaction->saleItems;
            $customerSupplier = $transaction->customer;
        }

        $data = $this->getEwayBillData($transaction, $items, $customerSupplier, $transactionType);

        return $data;
    }

    public function getEwayBillData($transaction, $items, $customerSupplier, $transactionTypeForCheck)
    {
        $company = getCurrentCompany();
        $fixedDigit = getCompanyFixedDigitNumber();

        $itemData = [];
        if (! empty($items)) {
            foreach ($items as $item) {
                $itemData[] = [
                    'itemId' => $item->id ?? '',
                    'productName' => $item->items->item_name ?? '',
                    'productDesc' => $item->additional_description ?? '',
                    'hsnCode' => $item->hsn_code ?? $item->items->model->hsn_sac_code ?? '',
                    'quantity' => $item->quantity ?? 0.00,
                    'qtyUnitId' => $item->unit->id ?? 0,
                    'qtyUnit' => $item->unit->code ?? '',
                    'taxableAmount' => $item->taxable_value ?? 0.00,
                    'cgstRate' => ! empty($item->classification_cgst_tax) ? (float) $item->gst_tax_percentage / 2 : 0.00,
                    'sgstRate' => ! empty($item->classification_sgst_tax) ? (float) $item->gst_tax_percentage / 2 : 0.00,
                    'igstRate' => ! empty($item->classification_igst_tax) ? (float) $item->gst_tax_percentage : 0.00,
                    'cessRate' => $item->cess_rate ?? 0.00,
                ];
            }
        }

        $transactionAdditionalCharges = $transaction->prepareAdditionalCharges() ?? [];

        if (! empty($transactionAdditionalCharges)) {
            foreach ($transactionAdditionalCharges as $item) {
                if (abs($item['taxable_value']) != 0) {
                    $itemData[] = [
                        'itemId' => '',
                        'productName' => $item['ledger_name'] ?? '',
                        'productDesc' => '',
                        'hsnCode' => $item['hsn_code'] ?? '',
                        'quantity' => 1,
                        'qtyUnitId' => $item->unit->id ?? 0,
                        'qtyUnit' => '',
                        'taxableAmount' => abs(round($item['taxable_value'], $fixedDigit)),
                        'cgstRate' => ! empty($item['cgst']) && $item['cgst'] != 0 ? (float) ($item['rate_of_gst'] / 2) : 0.00,
                        'sgstRate' => ! empty($item['sgst']) && $item['sgst'] != 0 ? (float) ($item['rate_of_gst'] / 2) : 0.00,
                        'igstRate' => ! empty($item['igst']) && $item['igst'] != 0 ? (float) $item['rate_of_gst'] : 0.00,
                        'cessRate' => round($item['cess_amount'], $fixedDigit),
                    ];
                }
            }
        }

        $totalAdditionalCharges = 0;
        if (count($transaction->additionalCharges) > 0 && empty($transactionAdditionalCharges)) {
            foreach ($transaction->additionalCharges as $additionalCharge) {
                $totalAdditionalCharges += $additionalCharge->total_without_tax;
            }
        }

        $transaction->load('addLess');

        $totalAddLessCharges = 0;
        if (count($transaction->addLess) > 0) {
            foreach ($transaction->addLess as $addLess) {
                $totalAddLessCharges += $addLess->total;
            }
        }

        if ($transactionTypeForCheck == EwayBill::PURCHASE_RETURN) {
            $docNo = $transaction->voucher_number;
            $docDate = $transaction->voucher_date;
        } elseif ($transactionTypeForCheck == EwayBill::DELIVERY_CHALLAN) {
            $docNo = $transaction->challan_number;
            $docDate = $transaction->challan_date;
        } else {
            $docNo = $transaction->full_invoice_number;
            $docDate = $transaction->date;
        }

        $supplyType = SaleTransaction::OUTWARD;
        if ($transactionTypeForCheck == EwayBill::DELIVERY_CHALLAN) {
            $subType = SaleTransaction::SUB_SUPPLY_TYPE_OTHER;
            $documentType = SaleTransaction::DELIVERY_CHALLAN;
            $specify = 'Delivery Challan';
        } elseif ($transactionTypeForCheck == EwayBill::SALE_RETURN) {
            $supplyType = SaleTransaction::INWARD;
            $subType = SaleTransaction::SUB_SUPPLY_TYPE_SALE_RETURN;
            $documentType = SaleTransaction::DELIVERY_CHALLAN;
        } else {
            $subType = SaleTransaction::SUB_SUPPLY_TYPE_SUPPLY;
            $documentType = SaleTransaction::TAX_INVOICE;
        }
        $transactionMode = SaleTransaction::TRANSPORTER_MODE_ROAD;
        $vehicleType = SaleTransaction::VEHICLE_TYPE_REGULAR;

        if ($transactionTypeForCheck == EwayBill::SALE_RETURN) {
            $billFrom = $customerSupplier->billingAddress->state_id ?? $customerSupplier->model->billingAddress->state_id ?? null;
            $dispatchFrom = $customerSupplier->shippingAddress->state_id ?? $customerSupplier->model->shippingAddress->state_id ?? null;
            $billTo = $company->billingAddress->state_id ?? $company->dispatchAddress->state_id ?? null;
            $shipTo = $company->dispatchAddress->state_id ?? null;
        } else {
            $billFrom = $transaction->dispatchAddress->state_id ?? $company->dispatchAddress->state_id ?? null;
            $dispatchFrom = $transaction->dispatchAddress->state_id ?? $company->dispatchAddress->state_id ?? null;
            $billTo = $transaction->billingAddress->state_id ?? $customerSupplier->model->billingAddress->state_id ?? $transaction->shippingAddress->state_id ?? null;
            $shipTo = $transaction->shippingAddress->state_id ?? $customerSupplier->model->shippingAddress->state_id ?? null;
        }

        // Client give changes for bill type on 24-03-2025;
        // old condition for billTo ShipTo => $billFrom == $dispatchFrom && $billTo != $shipTo replace with ! $transaction->same_as_billing.

        $transactionType = SaleTransaction::E_BILL_TRANS_TYPE_REGULAR;
        if (! $transaction->same_as_billing) {
            $transactionType = SaleTransaction::E_BILL_TRANS_TYPE_BILL_TO_SHIP_TO;
        } elseif ($billFrom != $dispatchFrom && $billTo == $shipTo) {
            $transactionType = SaleTransaction::E_BILL_TRANS_TYPE_BILL_FROM_DISPATCH_TO;
        } elseif ($billFrom != $dispatchFrom && $billTo != $shipTo) {
            $transactionType = SaleTransaction::E_BILL_TRANS_TYPE_COMBINATION;
        }

        /** @var CompanyController $companyController */
        $companyController = App::make(CompanyController::class);
        $gstResponse = $companyController->getGstInformation($transaction->gstin);
        $gstInformation = $gstResponse->getData();

        if ($gstInformation->success) {
            $decodedData = json_decode($gstInformation->data);
            $result = $decodedData->result ?? null;
        }

        if (! empty($transaction->shipping_address_id)) {
            $shippingAddressForUse = Address::where('id', $transaction->shipping_address_id)->first() ?? null;
        } else {
            $shippingAddressForUse = $transaction->shippingAddress ?? null;
        }

        if ($transactionTypeForCheck == EwayBill::SALE_RETURN) {
            $toAddr1 = $company->dispatchAddress->address_1 ?? '';
            $toAddr2 = $company->dispatchAddress->address_2 ?? '';
            $toPlace = getCityName($company->dispatchAddress->city_id ?? null);
            $toPincode = $company->dispatchAddress->pin_code ?? '';
            $toStateName = getStateName($company->dispatchAddress->state_id ?? null);
            $toCityId = $company->dispatchAddress->city_id ?? null;
            $toStateId = $company->dispatchAddress->state_id ?? null;
            $toCountryId = $company->dispatchAddress->country_id ?? null;
            $toTrdId = $company->id;
            $toTrdName = $company->trade_name;
            $toGstin = ! empty($company->companyTax->gstin) ? $company->companyTax->gstin : 'URP';

            $fromTrdId = $customerSupplier->id;
            $fromTrdName = $customerSupplier->name;
            $fromGstin = ! empty($customerSupplier->model->gstin) ? $customerSupplier->model->gstin : 'URP';
            $fromAddr1 = $customerSupplier->model->billingAddress->address_1 ?? '';
            $fromAddr2 = $customerSupplier->model->billingAddress->address_2 ?? '';
            $fromPlace = getCityName($customerSupplier->model->billingAddress->city_id ?? null);
            $fromPincode = $customerSupplier->model->billingAddress->pin_code ?? '';
            $fromCityId = $customerSupplier->model->billingAddress->city_id ?? null;
            $fromStateId = $customerSupplier->model->billingAddress->state_id ?? null;
            $fromCountryId = $customerSupplier->model->billingAddress->country_id ?? null;
            $fromStateCode = getStateCode($customerSupplier->model->billingAddress->state_id ?? null);
            $fromStateName = getStateName($customerSupplier->model->billingAddress->state_id ?? null);
        } else {
            $toAddr1 = $shippingAddressForUse->address_1 ?? $transaction->billingAddress->address_1 ?? '';
            $toAddr2 = $shippingAddressForUse->address_2 ?? $transaction->billingAddress->address_2 ?? '';
            $toPlace = getCityName($shippingAddressForUse->city_id ?? $transaction->billingAddress->city_id ?? null);
            $toPincode = $shippingAddressForUse->pin_code ?? $transaction->billingAddress->pin_code ?? '';
            $toStateName = getStateName($shippingAddressForUse->state_id ?? $transaction->billingAddress->state_id ?? null);
            $toCityId = $shippingAddressForUse->city_id ?? $transaction->billingAddress->city_id ?? null;
            $toStateId = $shippingAddressForUse->state_id ?? $transaction->billingAddress->state_id ?? null;
            $toCountryId = $shippingAddressForUse->country_id ?? $transaction->billingAddress->country_id ?? null;
            $toTrdId = $customerSupplier->id;
            $toTrdName = $customerSupplier->name;
            $toGstin = ! empty($transaction->gstin) ? $transaction->gstin : 'URP';

            $fromTrdId = $company->id;
            $fromTrdName = $company->trade_name;
            $fromGstin = $company->companyTax->gstin;
            $fromAddr1 = $transaction->dispatchAddress->address_1 ?? $company->dispatchAddress->address_1 ?? '';
            $fromAddr2 = $transaction->dispatchAddress->address_2 ?? $company->dispatchAddress->address_2 ?? '';
            $fromPlace = getCityName($transaction->dispatchAddress->city_id ?? $company->dispatchAddress->city_id ?? '');
            $fromPincode = $transaction->dispatchAddress->pin_code ?? $company->dispatchAddress->pin_code ?? '';
            $fromCityId = $transaction->dispatchAddress->city_id ?? $company->dispatchAddress->city_id ?? null;
            $fromStateId = $transaction->dispatchAddress->state_id ?? $company->dispatchAddress->state_id ?? null;
            $fromCountryId = $transaction->dispatchAddress->country_id ?? $company->dispatchAddress->country_id ?? null;
            $fromStateCode = getStateCode($transaction->dispatchAddress->state_id ?? $company->dispatchAddress->state_id ?? null);
            $fromStateName = getStateName($transaction->dispatchAddress->state_id ?? $company->dispatchAddress->state_id ?? null);
        }

        if (isset($result->dty) && $result->dty === 'SEZ Unit') {
            $billToStateName = 'OTHER COUNTRY';
            $toStateCode = 96;
        } else {
            if ($transactionTypeForCheck == EwayBill::SALE_RETURN) {
                $billToStateName = getStateName($company->billingAddress->state_id ?? null);
                $toStateCode = getStateCode($company->dispatchAddress->state_id ?? null);
            } else {
                $billToStateName = getStateName($transaction->billingAddress->state_id ?? $customerSupplier->model->billingAddress->state_id ?? $transaction->shippingAddress->state_id);
                $toStateCode = getStateCode($shippingAddressForUse->state_id ?? $transaction->billingAddress->state_id ?? null);
            }
        }

        return [
            'transactionId' => $transaction->id,
            'supply_type' => $supplyType,
            'sub_type' => $subType,
            'specify' => $specify ?? '',
            'document_type' => $documentType,
            'docNo' => $docNo,
            'docDate' => Carbon::parse($docDate)->format('d/m/Y') ?? '',
            'transaction_type' => $transactionType,
            'fromTrdId' => $fromTrdId,
            'fromTrdName' => $fromTrdName,
            'fromGstin' => $fromGstin,
            'fromAddr1' => $fromAddr1,
            'fromAddr2' => $fromAddr2,
            'fromPlace' => $fromPlace,
            'fromPincode' => $fromPincode,
            'fromCityId' => $fromCityId,
            'fromStateId' => $fromStateId,
            'fromCountryId' => $fromCountryId,
            'fromStateCode' => $fromStateCode,
            'fromStateName' => $fromStateName,
            'toTrdId' => $toTrdId,
            'toTrdName' => $toTrdName,
            'toGstin' => $toGstin,
            'toAddr1' => $toAddr1,
            'toAddr2' => $toAddr2,
            'toPlace' => $toPlace,
            'toPincode' => $toPincode,
            'toStateCode' => $toStateCode,
            'toStateName' => $toStateName,
            'billToStateName' => $billToStateName,
            'toCityId' => $toCityId,
            'toStateId' => $toStateId,
            'toCountryId' => $toCountryId,
            'itemList' => $itemData,
            'totalTaxValue' => $items->sum('total') + array_sum(Arr::pluck($transactionAdditionalCharges, 'taxable_value')),
            'cgstValue' => $transaction->cgst ?? 0,
            'sgstValue' => $transaction->sgst ?? 0,
            'igstValue' => $transaction->igst ?? 0,
            'cessValue' => $transaction->cess ?? 0,
            'otherAmount' => $transaction->rounding_amount + $transaction->tcs_amount + $totalAdditionalCharges + $totalAddLessCharges,
            'totInvValue' => $transaction->grand_total ?? 0,
            'transporterId' => $transaction->transport->gstin ?? $customerSupplier->model->transporter->gstin ?? '',
            'transporterName' => $transaction->transport->transporter_name ?? $customerSupplier->model->transporter->transporter_name ?? '',
            'transporter_mode' => $transactionMode,
            'vehicle_type' => $vehicleType,
            'vehicle_no' => $transaction->transporter_vehicle_number ?? '',
            'transDocNo' => $transaction->transporter_document_number ?? '',
            'transDocDate' => Carbon::parse($transaction->transporter_document_date)->format('d-m-Y') ?? '',
            'isDocumentTypeDisabled' => $transactionTypeForCheck == EwayBill::SALE_RETURN ? true : false,
        ];
    }
}
