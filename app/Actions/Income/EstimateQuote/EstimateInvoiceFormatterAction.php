<?php

namespace App\Actions\Income\EstimateQuote;

use App\Actions\v1\RearrangeItems\GetRearrangeItemsAction;
use App\Models\CompanySetting;
use App\Models\IncomeEstimateQuoteTransaction;
use App\Models\ItemCustomField;
use App\Models\RearrangeItem;
use App\Models\SaleTransaction;
use InvalidArgumentException;
use Lorisleiva\Actions\Concerns\AsAction;

class EstimateInvoiceFormatterAction
{
    use AsAction;

    public $items;

    public $isItemInvoice;

    public $pdfSymbol;

    public $invoiceSetting;

    public $showPrintSettings;

    public function handle(array &$data)
    {
        $this->pdfSymbol = getPrintPdfCurrencySymbol($data['currentCompany']->id);
        $requiredKeys = ['itemType', 'transaction', 'showPrintSettings', 'changeLabel'];
        $this->invoiceSetting = $data['invoiceSetting'];
        $this->showPrintSettings = $data['showPrintSettings'];

        foreach ($requiredKeys as $key) {
            if (! isset($data[$key])) {
                throw new InvalidArgumentException("Missing required key: $key");
            }
        }

        $data['rearrangeItems'] = ['headings' => [], 'detail' => [], 'footer' => []];
        $this->isItemInvoice = $data['itemType'] === IncomeEstimateQuoteTransaction::ITEM_INVOICE;
        $this->items = $this->isItemInvoice ? ($data['transactionItems'] ?? []) : ($data['transactionLedgers'] ?? []);

        $commonLabels = [
            'Discount 1' => [
                'name' => $data['changeLabel']['estimate_discount'] ?? 'Dis.',
                'show_in_print' => (($data['showPrintSettings']['show_estimate_discount'] ?? true) && ($this->items->sum('discount_value') != 0)) ? true : false,
                'key' => 'discount_value',
                'class' => 'text-center w-80px',
            ],
            'Discount 2' => [
                'name' => $data['changeLabel']['estimate_dis_2_label'] ?? 'Dis. 2',
                'show_in_print' => (($data['showPrintSettings']['estimate_dis_2_enable'] ?? true) && ($this->items->sum('discount_value_2') != 0)) ? true : false,
                'key' => 'discount_value_2',
                'class' => 'text-center w-80px',
            ],
            'GST' => [
                'name' => $data['changeLabel']['estimate_gst'] ?? 'GST (%)',
                'show_in_print' => (($data['isCompanyGstApplicable'] ?? false) && ($data['showPrintSettings']['show_estimate_gst'] ?? true)) ? true : false,
                'key' => 'gst_tax_percentage',
                'class' => 'text-center w-80px',
            ],
        ];

        if ($data['itemType'] === SaleTransaction::ITEM_INVOICE) {
            $rearrange = GetRearrangeItemsAction::run(RearrangeItem::INCOME_ESTIMATE_QUOTE, RearrangeItem::ITEM_INVOICE);
            $rearrangeItems = $rearrange['items'] ?? [];

            $data['label'] = array_merge($commonLabels, [
                'HSN-SAC' => [
                    'name' => $data['changeLabel']['estimate_hsn_sac'] ?? 'HSN/SAC',
                    'show_in_print' => ($data['isCompanyGstApplicable'] ?? false) && ($data['showPrintSettings']['show_estimate_hsn_sac'] ?? true),
                    'key' => 'hsn_code',
                    'class' => 'text-center',
                ],
                'MRP' => [
                    'name' => $data['changeLabel']['estimate_mrp'] ?? 'MRP',
                    'show_in_print' => (($data['showPrintSettings']['show_estimate_mrp'] ?? true) && ($this->items->sum('mrp') != 0)) ? true : false,
                    'key' => 'mrp',
                    'class' => 'text-center',
                ],

                'Unit Price' => [
                    'name' => $data['changeLabel']['estimate_rate_with_gst'] ?? 'Rate With GST',
                    'show_in_print' => ($data['isCompanyGstApplicable'] ?? false) && ($data['showPrintSettings']['show_estimate_rate_with_gst'] ?? false),
                    'key' => 'rpu_with_gst',
                    'class' => 'text-center',
                ],
                'Quantity' => [
                    'name' => $data['changeLabel']['estimate_qty'] ?? 'Qty',
                    'show_in_print' => $data['showPrintSettings']['show_estimate_qty'] ?? true,
                    'key' => 'primary_quantity',
                    'class' => 'text-center',
                ],
                'UOM' => [
                    'name' => 'Unit',
                    'show_in_print' => false,
                    'key' => 'primary_quantity_1',
                    'class' => 'text-center',
                ],
            ]);
        } elseif ($data['itemType'] === SaleTransaction::ACCOUNTING_INVOICE) {
            $rearrange = GetRearrangeItemsAction::run(RearrangeItem::INCOME_ESTIMATE_QUOTE, RearrangeItem::ACCOUNT_INVOICE);
            $rearrangeItems = $rearrange['items'] ?? [];
            $data['label'] = array_merge($commonLabels, [
                'Amount' => [
                    'name' => $data['changeLabel']['estimate_rate_with_gst'] ?? 'Rate With GST',
                    'show_in_print' => ($data['isCompanyGstApplicable'] ?? false) && ($data['showPrintSettings']['show_estimate_rate_with_gst'] ?? true),
                    'key' => 'rpu_with_gst',
                    'class' => 'text-center',
                ],
            ]);
        } else {
            throw new InvalidArgumentException("Invalid itemType: {$data['itemType']}");
        }

        // Build headings
        $data['rearrangeItems']['headings'] = [
            [
                'header' => 'SN',
                'name' => $data['changeLabel']['estimate_sn'] ?? 'SN',
                'is_show_in_print' => true,
                'is_custom_field' => false,
                'key' => 'sn',
                'class' => 'w-25px text-center',
            ],
            [
                'header' => $data['itemType'] === SaleTransaction::ITEM_INVOICE ? 'Item Name' : 'Particulars',
                'name' => $data['changeLabel']['estimate_item_name'] ?? ($data['itemType'] === SaleTransaction::ITEM_INVOICE ? 'Item Name' : 'Particulars'),
                'is_show_in_print' => true,
                'is_custom_field' => false,
                'key' => 'item_name',
                'class' => 'w-100 text-start',
            ],
        ];

        // Add dynamic headings from rearrangeItems
        foreach ($rearrangeItems as $item) {
            $label = $data['label'][$item['name']] ?? [
                'name' => $item['name'],
                'show_in_print' => ($item['is_show_in_print'] && $item['is_enabled']) ? true : false,
                'key' => $item['name'],
                'class' => 'text-center',
            ];

            $data['rearrangeItems']['headings'][] = [
                'header' => $item['name'],
                'name' => $label['name'],
                'is_show_in_print' => $label['show_in_print'],
                'is_custom_field' => ! empty($item->custom_field_item_id),
                'key' => $label['key'] ?? $item['name'],
                'class' => $label['class'] ?? '',
            ];

            // Add extra headings for specific fields
            $extraHeadings = $this->getExtraHeadings($item, $data);
            $data['rearrangeItems']['headings'] = array_merge($data['rearrangeItems']['headings'], $extraHeadings);
        }

        // Add Taxable Value heading
        $data['rearrangeItems']['headings'][] = [
            'header' => 'Taxable Value',
            'name' => $data['changeLabel']['estimate_taxable_value'] ?? (($data['isCompanyGstApplicable'] ?? false) ? 'Taxable Value' : 'Amount'),
            'is_show_in_print' => true,
            'is_custom_field' => false,
            'key' => 'total',
            'class' => 'text-center w-100px',
        ];

        $data['rearrangeItems']['headings'][] = [
            'header' => 'Total Amount',
            'name' => $data['changeLabel']['estimate_total_amount'] ?? 'Total Amount',
            'is_show_in_print' => (isset($this->invoiceSetting['pdf_format']) && $this->invoiceSetting['pdf_format'] == CompanySetting::LANDSCAPE_A5 && (($data['isCompanyGstApplicable'] ?? false) && ($data['showPrintSettings']['show_estimate_total_amount'] ?? true))) ? true : false,
            'is_custom_field' => false,
            'key' => 'total_amount',
            'class' => 'text-center w-100px',
        ];

        // Build detail rows
        $validKeys = array_column($data['rearrangeItems']['headings'], 'is_show_in_print', 'key');

        $uniqueId = 1;
        foreach ($this->items as $item) {
            $row = [];
            foreach ($validKeys as $key => $showInPrint) {
                $row[$key] = $this->getRowValue($key, $item, $showInPrint, $uniqueId);
            }
            $data['rearrangeItems']['detail'][] = $row;
            $uniqueId++;
        }

        // Build footer
        $data['rearrangeItems']['footer'] = $this->buildFooter($validKeys, $data);

        return $data;
    }

    /**
     * Get extra headings for specific fields.
     *
     * @param  mixed  $item Rearrange item
     * @param  array  $data Input data
     * @return array Extra headings
     */
    private function getExtraHeadings($item, array $data): array
    {
        $extraHeadings = [];

        if ($item['name'] === 'Unit Price') {
            $extraHeadings[] = [
                'header' => 'Sale Rate',
                'name' => $data['changeLabel']['estimate_rate'] ?? 'Rate',
                'is_show_in_print' => $data['showPrintSettings']['show_estimate_rate'] ?? true,
                'is_custom_field' => ! empty($item->custom_field_item_id),
                'key' => 'rpu_without_gst',
                'class' => 'text-center w-100px',
            ];
        }

        if ($item['name'] === 'Amount') {
            $extraHeadings[] = [
                'header' => 'Rate',
                'name' => $data['changeLabel']['estimate_rate'] ?? 'Rate',
                'is_show_in_print' => $data['showPrintSettings']['show_estimate_rate'] ?? true,
                'is_custom_field' => ! empty($item->custom_field_item_id),
                'key' => 'rpu_without_gst',
                'class' => 'text-center w-100px',
            ];
        }

        if ($item['name'] === 'Discount 2') {
            $extraHeadings[] = [
                'header' => 'Total Discount',
                'name' => $data['changeLabel']['estimate_total_discount'] ?? 'Total Dis.',
                'is_show_in_print' => (($data['showPrintSettings']['show_estimate_total_discount'] ?? true) && ($this->items->sum('total_discount_amount') != 0)) ? true : false,
                'is_custom_field' => ! empty($item->custom_field_item_id),
                'key' => 'total_discount_amount',
                'class' => 'text-center w-80px',
            ];
        }

        if ($item['name'] === 'Quantity') {
            $extraHeadings[] = [
                'header' => 'Sec UOM',
                'name' => $data['changeLabel']['estimate_uom_label'] ?? 'Sec(UOM)',
                'is_show_in_print' => (($data['showPrintSettings']['estimate_uom_enable'] ?? false) && ($this->items->sum('secondary_quantity') != 0)) ? true : false,
                'is_custom_field' => ! empty($item->custom_field_item_id),
                'key' => 'secondary_quantity',
                'class' => 'text-center w-80px',
            ];
        }
        if ($item['name'] === 'GST') {
            $extraHeadings[] = [
                'header' => 'GST Amount',
                'name' => $data['changeLabel']['estimate_gst_amount'] ?? 'GST Amt',
                'is_show_in_print' => (isset($this->invoiceSetting['pdf_format']) && $this->invoiceSetting['pdf_format'] == CompanySetting::LANDSCAPE_A5 && (($data['isCompanyGstApplicable'] ?? false) && ($data['showPrintSettings']['show_estimate_gst_amount'] ?? true))) ? true : false,
                'is_custom_field' => false,
                'key' => 'gst_amount',
                'class' => 'text-center',
            ];
        }

        return $extraHeadings;
    }

    /**
     * Get value for a row based on key.
     *
     * @param  string  $key Column key
     * @param  mixed  $item Transaction item or ledger
     * @param  bool  $showInPrint Whether to show in print
     * @param  int  $uniqueId Serial number
     * @return array Value and print setting
     */
    private function getRowValue(string $key, $item, bool $showInPrint, int $uniqueId): array
    {
        $primaryUnitName = ($this->showPrintSettings['show_estimate_unit'] ?? true) ? ($item->primary_unit_name ?? null) : null;
        $secondaryUnitName = ($this->showPrintSettings['show_estimate_unit'] ?? true) ? ($item->secondary_unit_name ?? null) : null;
        $gstAmount = ($item->classification_igst_tax + $item->classification_cgst_tax + $item->classification_sgst_tax) ?? 0;
        $total = $item->total ?? 0;
        $totalAmount = $gstAmount + $total;

        return match ($key) {
            'sn' => ['value' => $uniqueId, 'is_show_in_print' => $showInPrint],
            'item_name' => $this->getFullItemDetails($item, $showInPrint),
            'hsn_code' => ['value' => $item->hsn_code ?? ($item->items->model->hsn_sac_code ?? null), 'is_show_in_print' => $showInPrint],
            'primary_quantity' => ['value' => round($item->primary_quantity ?? 0, 2).' '.($primaryUnitName), 'is_show_in_print' => $showInPrint],
            'secondary_quantity' => ['value' => round($item->secondary_quantity ?? 0, 2).' '.($secondaryUnitName), 'is_show_in_print' => $showInPrint],
            'mrp' => ['value' => $this->pdfSymbol.getCurrencyFormatFor3digit($item->mrp) ?? null, 'is_show_in_print' => $showInPrint],
            'rpu_with_gst' => ['value' => $this->pdfSymbol.getCurrencyFormatFor3digit($item->rpu_with_gst) ?? null, 'is_show_in_print' => $showInPrint],
            'rpu_without_gst' => ['value' => $this->pdfSymbol.getCurrencyFormatFor3digit($item->rpu_without_gst) ?? null, 'is_show_in_print' => $showInPrint],
            'discount_value' => ['value' => $item->discount_type == \App\Models\SaleTransaction::DISCOUNT_TYPE_AMOUNT ? ($this->pdfSymbol.getCurrencyFormatFor3digit($item->discount_value ?? '0.00')) : (($item->discount_value ?? 0).' %'), 'is_show_in_print' => $showInPrint],
            'discount_value_2' => ['value' => $item->discount_type_2 == \App\Models\SaleTransaction::DISCOUNT_TYPE_AMOUNT ? ($this->pdfSymbol.getCurrencyFormatFor3digit($item->discount_value_2 ?? '0.00')) : (($item->discount_value_2 ?? 0).' %'), 'is_show_in_print' => $showInPrint],
            'total_discount_amount' => ['value' => $this->pdfSymbol.getCurrencyFormatFor3digit($item->total_discount_amount) ?? null, 'is_show_in_print' => $showInPrint],
            'gst_tax_percentage' => ['value' => $item->gst_tax_percentage ?? null, 'is_show_in_print' => $showInPrint],
            'gst_amount' => ['value' => $this->pdfSymbol.getCurrencyFormatFor3digit($gstAmount) ?? null, 'is_show_in_print' => $showInPrint],
            'total' => ['value' => $this->pdfSymbol.getCurrencyFormatFor3digit($total) ?? null, 'is_show_in_print' => $showInPrint],
            'total_amount' => ['value' => $this->pdfSymbol.getCurrencyFormatFor3digit(($totalAmount)) ?? null, 'is_show_in_print' => $showInPrint],
            default => $this->customFieldValues($item->customItemsValues ?? $item->customLedgerValues ?? [], $key, $showInPrint),
        };
    }

    /**
     * Build footer values.
     *
     * @param  array  $validKeys Valid keys and their print settings
     * @param  array  $data Input data
     * @return array Footer values
     */
    private function buildFooter(array $validKeys, array $data): array
    {
        $footer = [];
        $sums = [
            'primary_quantity' => $this->items->sum('primary_quantity') ?? 0,
            'secondary_quantity' => $this->items->sum('secondary_quantity') ?? 0,
            'total_discount_amount' => $this->items->sum('total_discount_amount') ?? 0,
            'gst_amount' => $this->items->sum(function ($item) {
                return round($item->classification_igst_tax + $item->classification_cgst_tax + $item->classification_sgst_tax, 2);
            }) ?? 0,
        ];

        $total = $data['transaction']->gross_value ?? 0;
        $totalAmount = $total + $sums['gst_amount'];

        foreach ($validKeys as $key => $showInPrint) {
            $footer[$key] = match ($key) {
                'sn' => ['value' => null, 'is_show_in_print' => $showInPrint],
                'item_name' => ['value' => 'Total', 'is_show_in_print' => $showInPrint],
                'hsn_code' => ['value' => null, 'is_show_in_print' => $showInPrint],
                'primary_quantity' => ['value' => round($sums['primary_quantity'] ?? 0, 2), 'is_show_in_print' => $showInPrint],
                'secondary_quantity' => ['value' => round($sums['secondary_quantity'] ?? 0, 2), 'is_show_in_print' => $showInPrint],
                'mrp' => ['value' => null, 'is_show_in_print' => $showInPrint],
                'rpu_with_gst' => ['value' => null, 'is_show_in_print' => $showInPrint],
                'rpu_without_gst' => ['value' => null, 'is_show_in_print' => $showInPrint],
                'discount_value' => ['value' => null, 'is_show_in_print' => $showInPrint],
                'discount_value_2' => ['value' => null, 'is_show_in_print' => $showInPrint],
                'total_discount_amount' => ['value' => $this->pdfSymbol.getCurrencyFormatFor3digit($sums['total_discount_amount'] ?? null), 'is_show_in_print' => $showInPrint],
                'gst_tax_percentage' => ['value' => null, 'is_show_in_print' => $showInPrint],
                'gst_amount' => ['value' => $this->pdfSymbol.getCurrencyFormatFor3digit($sums['gst_amount'] ?? null), 'is_show_in_print' => $showInPrint],
                'total' => ['value' => $this->pdfSymbol.getCurrencyFormatFor3digit($total ?? null), 'is_show_in_print' => $showInPrint],
                'total_amount' => ['value' => $this->pdfSymbol.getCurrencyFormatFor3digit($totalAmount ?? null), 'is_show_in_print' => $showInPrint],
                default => $this->customFieldValues($this->items->toArray(), $key, $showInPrint, true),
            };
        }

        return $footer;
    }

    /**
     * Get custom field value for a key.
     *
     * @param  array  $items Custom field items
     * @param  string  $key Field key
     * @param  bool  $showInPrint Whether to show in print
     * @param  bool  $isFooter Whether it's a footer value
     * @return array Value and print setting
     */
    public function customFieldValues(array $items, string $key, bool $showInPrint, bool $isFooter = false): array
    {
        if ($isFooter) {
            $totals = [];

            foreach ($items as &$item) {
                if (! isset($item['customItemsValues'])) {
                    continue;
                }
                foreach ($item['customItemsValues'] as $customField) {
                    $lableName = $customField['label_name'];
                    if ($customField['show_total'] && $customField['custom_field_type'] == ItemCustomField::CF_TYPE_NUMBER) {
                        $value = (float) $customField['value'] ?? 0;
                        if (! isset($totals[$lableName])) {
                            $totals[$lableName] = 0;
                        }
                        $totals[$lableName] += $value;
                    } else {
                        $totals[$lableName] = null;
                    }
                }
            }

            return ['value' => $totals[$key] ?? null, 'is_show_in_print' => $showInPrint];
        }

        foreach ($items as $item) {
            if ($item['label_name'] === $key) {
                return ['value' => $item['value'], 'is_show_in_print' => $showInPrint];
            }
        }

        return ['value' => null, 'is_show_in_print' => $showInPrint];
    }

    public function getFullItemDetails($item, $showInPrint)
    {
        return [
            'value' => $this->isItemInvoice ? ($item->items->item_name ?? null) : ($item->ledgers->name ?? null),
            'is_show_in_print' => $showInPrint,
            'show_sku' => (isset($item->items) && ($item->items->sku != null) && ($this->invoiceSetting['show_estimate_item_sku'] ?? true)) ? true : false,
            'sku' => isset($item->items) ? $item->items->sku : null,
            'show_consolidating_items' => (! empty($item->consolidating_items_to_invoice)) ? true : false,
            'consolidating_items' => ('('.consolidatingItemsToInvoice($item->consolidating_items_to_invoice).')') ?? null,
            'show_additional_description' => (! empty($item->additional_description)) ? true : false,
            'additional_description' => nl2br('('.$item->additional_description.')'),
            'show_item_image' => (! empty($item->items->item_image) && ($this->invoiceSetting['show_estimate_item_image'] ?? true)) ? true : false,
            'item_image' => isset($item->items) ? $item->items->item_image : null,
            'customItemsInventoryValues' => $item->customItemsInventoryValues ?? [],
        ];
    }
}
