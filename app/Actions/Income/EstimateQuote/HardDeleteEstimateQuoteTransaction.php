<?php

namespace App\Actions\Income\EstimateQuote;

use App\Actions\CustomFieldsItemMaster\Inventory\CalculateAvailableQTY;
use App\Models\IncomeEstimateQuoteAccountingInvoice;
use App\Models\IncomeEstimateQuoteItemInvoice;
use App\Models\IncomeEstimateQuoteTransaction;
use App\Models\ItemCustomFieldCombinationInventory;
use App\Models\ItemCustomFieldValue;
use Lorisleiva\Actions\Concerns\AsAction;

class HardDeleteEstimateQuoteTransaction
{
    use AsAction;

    public function handle($id, $allTransaction = false, $isDirectHardDelete = false)
    {
        $query = $isDirectHardDelete ? IncomeEstimateQuoteTransaction::withTrashed() : IncomeEstimateQuoteTransaction::onlyTrashed();
        if ($allTransaction) {
            $estimateQuoteTransactions = $query->get();
        } else {
            $estimateQuoteTransactions = $query->whereId($id)->get();
        }

        foreach ($estimateQuoteTransactions as $estimateQuote) {

            $itemsIds = $estimateQuote->transactionItems->pluck('id')->toArray();
            $ledgersIds = $estimateQuote->transactionLedgers->pluck('id')->toArray();
            $allCFIds = array_merge($itemsIds, $ledgersIds);
            ItemCustomFieldValue::whereIn('model_id', $allCFIds)->whereIn('model_type', [IncomeEstimateQuoteItemInvoice::class, IncomeEstimateQuoteAccountingInvoice::class])?->delete();

            // Delete Inventory
            $inventoryItems = ItemCustomFieldCombinationInventory::whereIn('model_id', $allCFIds)->whereIn('model_type', [IncomeEstimateQuoteItemInvoice::class, IncomeEstimateQuoteAccountingInvoice::class])->get();
            foreach ($inventoryItems as $inventoryItem) {
                $combination = $inventoryItem->itemCustomFieldCombination;
                $inventoryItem->delete();
                CalculateAvailableQTY::run($combination);
            }

            $estimateQuote->customFieldValues()->delete();
            $estimateQuote->forceDelete();
        }

        return true;
    }
}
