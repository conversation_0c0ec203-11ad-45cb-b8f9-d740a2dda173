<?php

namespace App\Actions\Income\EstimateQuote;

use App\Actions\CommonAction\PreparePDFItemDataAction;
use App\Actions\CustomFields\GetTransactionCustomFieldsAction;
use App\Actions\CustomFieldsItemMaster\Inventory\PrepareCustomFieldInventoryDataForPdfPreview;
use App\Actions\CustomFieldsItemMaster\Transaction\PrepareCustomFieldDataForPdfPreview;
use App\Actions\v1\PdfConfiguration\GetPdfAdjustmentsAction;
use App\Models\Address;
use App\Models\Company;
use App\Models\CompanySetting;
use App\Models\Configuration\IncomeEstimateQuoteConfiguration;
use App\Models\IncomeEstimateQuoteItemInvoice;
use App\Models\IncomeEstimateQuoteTransaction;
use App\Models\InvoicesLabel;
use App\Models\ItemCustomField;
use App\Models\Master\Bank;
use App\Models\Master\IncomeEstimateQuoteTransactionMaster;
use App\Models\Master\IncomeTransactionMaster;
use App\Models\PrintSetting;
use App\Models\TransactionCustomField;
use Carbon\Carbon;
use Lorisleiva\Actions\Concerns\AsAction;

class GetInvoicePDFDataForIncomeEstimateQuote
{
    use AsAction;

    public function handle($estimateQuoteId)
    {
        $data = [];

        // Get Transaction
        $data['transaction'] = IncomeEstimateQuoteTransaction::with([
            'party.model', 'transactionItems.items.model', 'transactionItems.unit', 'transactionLedgers.ledgers', 'transportDetails', 'brokerDetails',
            'billingAddress', 'shippingAddress', 'transactionTitle', 'addLess.ledger',
            'additionalCharges.ledger', 'dispatchAddress', 'bankLedgerDetails.model',
        ])->whereId($estimateQuoteId)->first();

        $data['customFieldValues'] = GetTransactionCustomFieldsAction::run(TransactionCustomField::INCOME_ESTIMATE_QUOTE, $estimateQuoteId, IncomeEstimateQuoteTransaction::class);

        // Get Company
        $data['currentCompany'] = Company::with('addresses', 'companyTax', 'user')->whereId($data['transaction']->company_id)->first();
        session(['current_company' => $data['currentCompany']]);

        // customer details
        $data['customerDetail'] = $data['transaction']->party;
        $data['panNumber'] = $data['customerDetail']->model->pan_card_number ?? null;

        // Load Address Data
        $data['companyBillingAddress'] = $data['currentCompany']->addresses->where('address_type', Company::BILLING_ADDRESS)->first();
        $shippingAddress = $data['transaction']->addresses->where('address_type', Company::DISPATCH_ADDRESS)->first();
        $data['billingAddress'] = $data['transaction']->addresses->where('address_type', IncomeEstimateQuoteTransaction::BILLING_ADDRESS)->first();

        if ($data['transaction']->same_as_billing) {
            $data['shippingAddress'] = $data['billingAddress'];
            $data['transaction']->shipping_gstin = $data['transaction']->gstin;
        } else {
            $data['shippingAddress'] = Address::whereId($data['transaction']->shipping_address_id)->first() ?? $data['transaction']->addresses->where('address_type', IncomeEstimateQuoteTransaction::SHIPPING_ADDRESS)->first();
        }

        $data['dispatchAddress'] = $data['transaction']->dispatchAddress;
        $data['ledgerShippingAddress'] = $data['customerDetail']->model->shippingAddress;

        // Process Add/Less and Additional Charges
        $data['addLess'] = prepareAddLess($data['transaction']->addLess);
        $data['additionalCharges'] = prepareAdditionalCharges($data['transaction']->additionalCharges);

        $data['fromNameExists'] = CompanySetting::where('key', 'from_name')->exists();
        $data['replyToEmailExists'] = CompanySetting::where('key', 'replay_to_email')->exists();
        $data['incomeEstimateQuoteTransactionMaster'] = IncomeEstimateQuoteTransactionMaster::with('ledgerDetails.model')
            ->whereCompanyId($data['currentCompany']->id)->first();
        $data['configuration'] = IncomeEstimateQuoteConfiguration::whereCompanyId($data['currentCompany']->id)->first();
        $data['companyBillingAddress'] = $data['currentCompany']->billingAddress;
        $data['companyShippingAddress'] = $data['currentCompany']->dispatchAddress;
        $data['bankDetail'] = null;
        $data['accountNumber'] = null;
        $data['accountType'] = null;
        $data['branchName'] = null;
        if ($data['transaction']->bankLedgerDetails) {
            /** @var Bank $bankDetail */
            $bankDetail = $data['transaction']->bankLedgerDetails?->model ?? null;
            $data['bankDetail'] = $bankDetail;
            $data['accountNumber'] = $data['bankDetail'] ? $data['bankDetail']->account_number : null;
            $data['accountType'] = $data['bankDetail'] ? $data['bankDetail']->account_type : null;
            $data['branchName'] = $data['bankDetail'] ? $data['bankDetail']->branch_name : null;
        }
        $data['invoiceSetting'] = CompanySetting::pluck('value', 'key')->toArray();
        $data['transactionItems'] = PreparePDFItemDataAction::run($data['transaction']->transactionItems);
        if (! empty($data['transactionItems'])) {
            foreach ($data['transactionItems'] as $key => $item) {
                $data['transactionItems'][$key]['customItemsValues'] = PrepareCustomFieldDataForPdfPreview::run(ItemCustomField::INCOME_ESTIMATE_QUOTE, $item->id, IncomeEstimateQuoteItemInvoice::class);
                if (count($item->customFieldTransactionItemsInventoryValues) > 0) {
                    $data['transactionItems'][$key]['customItemsInventoryValues'] = PrepareCustomFieldInventoryDataForPdfPreview::run($item->id, IncomeEstimateQuoteItemInvoice::class, $item->item_id);
                }
            }
        }

        $data['transactionLedgers'] = $data['transaction']->transactionLedgers;

        $data['showPanNumber'] = isset($data['invoiceSetting']['estimate_pan_no']) ? $data['invoiceSetting']['estimate_pan_no'] : true;
        // $data['billingAddress'] = $data['transaction']->billingAddress;
        // $data['shippingAddress'] = $data['transaction']->shippingAddress;

        $data['itemType'] = $data['transaction']->invoice_type;
        $data['invoiceName'] = 'Estimate Quote Invoice';

        // Credit Period
        $data = array_merge($data, $this->calculateCreditPeriod($data['transaction']));

        $data['isCompanyGstApplicable'] = $data['currentCompany']->is_gst_applicable ? true : false;

        $data['showGst'] = false;

        if (! $data['isCompanyGstApplicable']) {
            if (! empty($data['customerDetail']->model->gstin)) {
                $data['showGst'] = true;
            }
        } elseif (! empty($data['transaction']->gstin)) {
            $data['showGst'] = true;
        }
        $incomeTransactionMaster = IncomeTransactionMaster::with('ledgerDetails.model')->whereCompanyId($data['currentCompany']->id)->first();
        $data['taxInvoice'] = $incomeTransactionMaster?->title_of_print;
        $data['validFor'] = ! empty($data['transaction']->valid_for) && ! empty($data['transaction']->valid_for_type) ?
        $data['transaction']->valid_for.' '.IncomeEstimateQuoteTransaction::CREDIT_PERIOD_TYPE[$data['transaction']->valid_for_type] : null;
        $data['changeLabel'] = InvoicesLabel::whereTransactionType(InvoicesLabel::INCOME_ESTIMATE)->pluck('label_value', 'label_name')->toArray();
        $data['showPrintSettings'] = PrintSetting::pluck('status', 'name')->toArray();
        $isA5Pdf = isset($data['invoiceSetting']['estimate_pdf_format']) ? ($data['invoiceSetting']['estimate_pdf_format'] == CompanySetting::A5 || $data['invoiceSetting']['estimate_pdf_format'] == CompanySetting::LANDSCAPE_A5) : false;

        // Custom Font Size
        $data['customFontSize'] = GetPdfAdjustmentsAction::run($isA5Pdf);
        $data['customProp'] = InvoicesLabel::where('is_custom_label', InvoicesLabel::PROP_NAME)->select('label_value', 'label_name')->first();

        $data = EstimateInvoiceFormatterAction::run($data);

        return $data;
    }

    private function calculateCreditPeriod($transaction)
    {

        $creditPeriod = $transaction->credit_period ?? $transaction->party->model->credit_limit_period;
        $creditType = $transaction->credit_period_type ?? $transaction->party->model->credit_period_type;

        if (! $creditPeriod) {
            return ['creditPeriod' => null, 'dueDate' => null];
        }

        $dueDate = Carbon::parse($transaction->date);
        if ($creditType === IncomeEstimateQuoteTransaction::CREDIT_PERIOD_TYPE_MONTH) {
            $dueDate->addMonths($creditPeriod);
        } elseif ($creditType === IncomeEstimateQuoteTransaction::CREDIT_PERIOD_TYPE_DAY) {
            $dueDate->addDays($creditPeriod);
        }

        return [
            'creditPeriod' => getCreditPeriod($creditPeriod, $creditType),
            'dueDate' => $dueDate->format('d-m-Y'),
        ];
    }
}
