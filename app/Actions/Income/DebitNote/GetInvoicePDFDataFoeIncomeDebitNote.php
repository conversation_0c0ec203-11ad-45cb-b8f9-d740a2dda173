<?php

namespace App\Actions\Income\DebitNote;

use App\Actions\CommonAction\PreparePDFItemDataAction;
use App\Actions\CustomFields\GetTransactionCustomFieldsAction;
use App\Actions\CustomFieldsItemMaster\Inventory\PrepareCustomFieldInventoryDataForPdfPreview;
use App\Actions\CustomFieldsItemMaster\Transaction\PrepareCustomFieldDataForPdfPreview;
use App\Actions\Income\Sale\SaleInvoiceFormatterAction;
use App\Actions\v1\PdfConfiguration\GetPdfAdjustmentsAction;
use App\Models\Address;
use App\Models\Company;
use App\Models\CompanySetting;
use App\Models\Configuration\IncomeDebitNote;
use App\Models\EwayBill;
use App\Models\IncomeDebitNoteItemTransaction;
use App\Models\IncomeDebitNoteTransaction;
use App\Models\InvoicesLabel;
use App\Models\ItemCustomField;
use App\Models\Master\Bank;
use App\Models\Master\IncomeDebitNoteTransactionMaster;
use App\Models\PrintSetting;
use App\Models\RearrangeItem;
use App\Models\SaleTransaction;
use App\Models\TransactionCustomField;
use Carbon\Carbon;
use Lorisleiva\Actions\Concerns\AsAction;

class GetInvoicePDFDataFoeIncomeDebitNote
{
    use AsAction;

    public function handle(int $debitNoteId)
    {
        $data = [];
        // load Income Debit Note Transaction
        $transaction = IncomeDebitNoteTransaction::with(
            'addresses',
            'customer.model',
            'transport',
            'incomeDebitNoteItems.items',
            'incomeDebitNoteLedgers.ledgers',
            'addLess.ledger',
            'additionalCharges.ledger',
            'dispatchAddress',
            'bankLedgerDetails.model',
        )->whereId($debitNoteId)->firstOrFail();

        $data['transaction'] = $transaction;

        // Load Company Data
        $data['currentCompany'] = Company::with('addresses', 'companyTax', 'user', 'mailConfiguration')->findOrFail($data['transaction']->company_id);
        session(['current_company' => $data['currentCompany']]);
        setCompanyInSession($data['currentCompany']);

        // Load E Way Bill Data
        $data['eWayBill'] = EwayBill::whereTransactionType(IncomeDebitNoteTransaction::class)->whereTransactionId($debitNoteId)->whereNot('is_canceled', 1)->first();

        // Process Add/Less and Additional Charges
        $data['addLess'] = prepareAddLess($transaction->addLess);
        $data['additionalCharges'] = prepareAdditionalCharges($transaction->additionalCharges);

        $saleTransaction = SaleTransaction::whereId($transaction->original_inv_no)->first();
        $data['originalInvoiceNumber'] = $saleTransaction->full_invoice_number ?? null;
        $data['originalInvoiceDate'] = $saleTransaction->date ?? null;

        // Custom fields
        $data['customFieldValues'] = GetTransactionCustomFieldsAction::run(TransactionCustomField::INCOME_DEBIT_NOTE, $debitNoteId, IncomeDebitNoteTransaction::class);

        // Load Master Data
        $incomeDebitNoteTransactionMaster = IncomeDebitNoteTransactionMaster::with('ledgerDetails.model')->first();
        $data['taxInvoice'] = $incomeDebitNoteTransactionMaster?->title_of_print;
        $data['invoiceName'] = 'Debit Note Invoice';
        $data['configuration'] = IncomeDebitNote::first();
        // Load Bank Details
        $data['bankDetail'] = null;
        $data['accountNumber'] = null;
        $data['accountType'] = null;
        $data['branchName'] = null;
        if ($transaction->bankLedgerDetails) {
            /** @var Bank $bankDetail */
            $bankDetail = $transaction->bankLedgerDetails?->model ?? null;
            $data['bankDetail'] = $bankDetail;
            $data['accountNumber'] = $data['bankDetail'] ? $data['bankDetail']->account_number : null;
            $data['accountType'] = $data['bankDetail'] ? $data['bankDetail']->account_type : null;
            $data['branchName'] = $data['bankDetail'] ? $data['bankDetail']->branch_name : null;
        }

        // Customer Details
        $data['customerDetail'] = $data['transaction']->customer->load('model');
        $data['panNumber'] = $data['customerDetail']->model->pan_card_number ?? null;
        $data['showPanNumber'] = isset($data['invoiceSetting']) && isset($data['invoiceSetting']['pan_no']) ? $data['invoiceSetting']['pan_no'] : true;

        // Load Address Data
        $data['companyBillingAddress'] = $data['currentCompany']->addresses->where('address_type', Company::BILLING_ADDRESS)->first();
        $shippingAddress = $data['transaction']->addresses->where('address_type', Company::DISPATCH_ADDRESS)->first();
        $data['billingAddress'] = $data['transaction']->addresses->where('address_type', IncomeDebitNoteTransaction::BILLING_ADDRESS)->first();

        if ($data['transaction']->same_as_billing) {
            $data['shippingAddress'] = $data['billingAddress'];
            $data['transaction']->shipping_gstin = $data['transaction']->gstin;
        } else {
            $data['shippingAddress'] = Address::whereId($data['transaction']->shipping_address_id)->first() ?? $data['transaction']->addresses->where('address_type', IncomeDebitNoteTransaction::SHIPPING_ADDRESS)->first();
        }

        $data['dispatchAddress'] = $data['transaction']->dispatchAddress;

        $data['ledgerShippingAddress'] = $data['customerDetail']->model->shippingAddress;
        $data['invoiceName'] = 'Debit Note Invoice';
        $data['invoiceSetting'] = CompanySetting::pluck('value', 'key')->toArray();

        // Load Invoice Item
        $data['transactionItems'] = PreparePDFItemDataAction::run($data['transaction']->incomeDebitNoteItems);
        if (! empty($data['transactionItems'])) {
            foreach ($data['transactionItems'] as $key => $item) {
                $data['transactionItems'][$key]['customItemsValues'] = PrepareCustomFieldDataForPdfPreview::run(ItemCustomField::INCOME_DEBIT_NOTE, $item->id, IncomeDebitNoteItemTransaction::class);
                if (count($item->customFieldTransactionItemsInventoryValues) > 0) {
                    $data['transactionItems'][$key]['customItemsInventoryValues'] = PrepareCustomFieldInventoryDataForPdfPreview::run($item->id, IncomeDebitNoteItemTransaction::class, $item->item_id);
                }
            }
        }

        $data['transactionLedgers'] = $data['transaction']->incomeDebitNoteLedgers;

        $data['invoiceDate'] = Carbon::parse($data['transaction']->date)->format('d-m-Y');
        // Credit Period
        $data = array_merge($data, $this->calculateCreditPeriod($data['transaction']));

        $data['isCompanyGstApplicable'] = $data['currentCompany']->is_gst_applicable ? true : false;

        $data['showGst'] = false;

        if (! $data['isCompanyGstApplicable']) {
            if (! empty($data['customerDetail']->model->gstin)) {
                $data['showGst'] = true;
            }
        } elseif (! empty($transaction->gstin)) {
            $data['showGst'] = true;
        }
        $data['changeLabel'] = InvoicesLabel::whereTransactionType(InvoicesLabel::SALE_TRANSACTION)->pluck('label_value', 'label_name')->toArray();
        $data['invoiceDetailLabel'] = 'Debit Note Details';
        $data['invoiceNumberLabel'] = 'Debit Note Number';
        $data['invoiceDateLabel'] = 'Debit Note Date';
        $data['itemType'] = $data['transaction']->dn_item_type;

        // Show Print Columns and change Label
        $data['showPrintSettings'] = PrintSetting::pluck('status', 'name')->toArray();
        $data['changeLabel'] = InvoicesLabel::whereTransactionType(InvoicesLabel::SALE_TRANSACTION)->pluck('label_value', 'label_name')->toArray();
        $isA5Pdf = isset($data['invoiceSetting']['pdf_format']) ? ($data['invoiceSetting']['pdf_format'] == CompanySetting::A5 || $data['invoiceSetting']['pdf_format'] == CompanySetting::LANDSCAPE_A5) : false;
        // Custom Font Size
        $data['customFontSize'] = GetPdfAdjustmentsAction::run($isA5Pdf);
        $data['customProp'] = InvoicesLabel::where('is_custom_label', InvoicesLabel::PROP_NAME)->select('label_value', 'label_name')->first();

        $data = SaleInvoiceFormatterAction::run($data, RearrangeItem::INCOME_DEBIT_NOTE);

        return $data;
    }

    private function calculateCreditPeriod($transaction)
    {
        $creditPeriod = $transaction->credit_period ?? $transaction->customer->model->credit_limit_period;
        $creditType = $transaction->credit_period_type ?? $transaction->customer->model->credit_period_type;

        if (! $creditPeriod) {
            return ['creditPeriod' => null, 'dueDate' => null];
        }

        $dueDate = Carbon::parse($transaction->date);
        if ($creditType === IncomeDebitNoteTransaction::CREDIT_PERIOD_TYPE_MONTH) {
            $dueDate->addMonths($creditPeriod);
        } elseif ($creditType === IncomeDebitNoteTransaction::CREDIT_PERIOD_TYPE_DAY) {
            $dueDate->addDays($creditPeriod);
        }

        return [
            'creditPeriod' => getCreditPeriod($creditPeriod, $creditType),
            'dueDate' => $dueDate->format('d-m-Y'),
        ];
    }
}
