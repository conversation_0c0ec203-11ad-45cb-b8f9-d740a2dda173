<?php

namespace App\Actions\Income\DebitNote;

use App\Actions\CustomFieldsItemMaster\Inventory\CalculateAvailableQTY;
use App\Models\IncomeDebitNoteItemTransaction;
use App\Models\IncomeDebitNoteLedgerTransaction;
use App\Models\IncomeDebitNoteTransaction;
use App\Models\ItemCustomFieldCombinationInventory;
use App\Models\ItemCustomFieldValue;
use App\Models\ReceiptTransaction;
use Lorisleiva\Actions\Concerns\AsAction;

class HardDeleteDebitNoteTransaction
{
    use AsAction;

    public function handle($id, $allTransaction = false, $isDirectHardDelete = false)
    {
        $query = $isDirectHardDelete ? IncomeDebitNoteTransaction::withTrashed() : IncomeDebitNoteTransaction::onlyTrashed();
        if ($allTransaction) {
            $incomeDebitNoteTransactions = $query->get();
        } else {
            $incomeDebitNoteTransactions = $query->whereId($id)->get();
        }

        foreach ($incomeDebitNoteTransactions as $incomeDebitNote) {
            ReceiptTransaction::withTrashed()
                ->where('company_id', $incomeDebitNote->company_id)
                ->whereReceiptNumber('income-debit-note/'.$incomeDebitNote->full_invoice_number)->financialYearDate()?->forceDelete();

            $itemsIds = $incomeDebitNote->incomeDebitNoteItems->pluck('id')->toArray();
            $ledgersIds = $incomeDebitNote->incomeDebitNoteLedgers->pluck('id')->toArray();
            $allCFIds = array_merge($itemsIds, $ledgersIds);
            ItemCustomFieldValue::whereIn('model_id', $allCFIds)->whereIn('model_type', [IncomeDebitNoteItemTransaction::class, IncomeDebitNoteLedgerTransaction::class])?->delete();

            // Delete Inventory
            $inventoryItems = ItemCustomFieldCombinationInventory::whereIn('model_id', $allCFIds)->whereIn('model_type', [IncomeDebitNoteItemTransaction::class, IncomeDebitNoteLedgerTransaction::class])->get();
            foreach ($inventoryItems as $inventoryItem) {
                $combination = $inventoryItem->itemCustomFieldCombination;
                $inventoryItem->delete();
                CalculateAvailableQTY::run($combination);
            }

            $incomeDebitNote->customFieldValues()->delete();
            $incomeDebitNote->addresses()->delete();
            $incomeDebitNote->forceDelete();
        }

        return true;
    }
}
