<?php

namespace App\Actions\Income\SaleReturn;

use App\Actions\CommonAction\PreparePDFItemDataAction;
use App\Actions\CustomFields\GetTransactionCustomFieldsAction;
use App\Actions\CustomFieldsItemMaster\Inventory\PrepareCustomFieldInventoryDataForPdfPreview;
use App\Actions\CustomFieldsItemMaster\Transaction\PrepareCustomFieldDataForPdfPreview;
use App\Actions\Income\Sale\SaleInvoiceFormatterAction;
use App\Actions\v1\PdfConfiguration\GetPdfAdjustmentsAction;
use App\Models\Address;
use App\Models\Company;
use App\Models\CompanySetting;
use App\Models\Configuration\IncomeSalesReturn;
use App\Models\EwayBill;
use App\Models\InvoicesLabel;
use App\Models\ItemCustomField;
use App\Models\Master\Bank;
use App\Models\Master\IncomeReturnTransactionMaster;
use App\Models\PrintSetting;
use App\Models\RearrangeItem;
use App\Models\SaleReturnItemTransaction;
use App\Models\SaleReturnTransaction;
use App\Models\SaleTransaction;
use App\Models\TransactionCustomField;
use Carbon\Carbon;
use Lorisleiva\Actions\Concerns\AsAction;

class GetInvoicePDFDataForSaleReturn
{
    use AsAction;

    public function handle(int $saleReturnId)
    {
        $data = [];
        // Load Sale Return Transaction
        $transaction = SaleReturnTransaction::with(
            'addresses',
            'customer.model',
            'transport',
            'saleReturnItems.items',
            'saleReturnLedgers.ledgers',
            'addLess.ledger',
            'additionalCharges.ledger',
            'dispatchAddress',
            'bankLedgerDetails.model',
        )->whereId($saleReturnId)->firstOrFail();

        $data['transaction'] = $transaction;

        // Store Company Data in session
        $data['currentCompany'] = Company::with('addresses', 'companyTax', 'user', 'mailConfiguration')->findOrFail($transaction->company_id);
        session(['current_company' => $data['currentCompany']]);
        setCompanyInSession($data['currentCompany']);

        // Process Add/Less and Additional Charges
        $data['addLess'] = prepareAddLess($transaction->addLess);
        $data['additionalCharges'] = prepareAdditionalCharges($transaction->additionalCharges);

        $incomeReturnTransactionMaster = IncomeReturnTransactionMaster::whereCompanyId($data['currentCompany']->id)->first();
        $data['eWayBill'] = EwayBill::whereTransactionType(SaleReturnTransaction::class)->whereTransactionId($saleReturnId)->whereNot('is_canceled', 1)->first();
        $data['invoiceName'] = 'Sale Return Invoice';

        $saleTransaction = SaleTransaction::whereId($transaction->original_inv_no)->first();
        $data['originalInvoiceNumber'] = $saleTransaction->full_invoice_number ?? null;
        $data['originalInvoiceDate'] = $saleTransaction->date ?? null;

        // Custom fields
        $data['customFieldValues'] = GetTransactionCustomFieldsAction::run(TransactionCustomField::SALE_RETURN, $saleReturnId, SaleReturnTransaction::class);

        // Customer Details
        $data['customerDetail'] = $data['transaction']->customer->load('model');

        // Addresses
        $data['companyBillingAddress'] = $data['currentCompany']->addresses->where('address_type', Company::BILLING_ADDRESS)->first();
        $shippingAddress = $data['transaction']->addresses->where('address_type', Company::DISPATCH_ADDRESS)->first();
        $data['billingAddress'] = $data['transaction']->addresses->where('address_type', SaleReturnTransaction::BILLING_ADDRESS)->first();
        if ($transaction->same_as_billing) {
            $data['shippingAddress'] = $data['billingAddress'];
            $data['transaction']->shipping_gstin = $transaction->gstin;
        } else {
            $data['shippingAddress'] = Address::whereId($data['transaction']->shipping_address_id)->first() ?? $transaction->addresses->where('address_type', SaleReturnTransaction::SHIPPING_ADDRESS)->first();
        }
        $data['dispatchAddress'] = $data['transaction']->dispatchAddress;
        $data['ledgerShippingAddress'] = $data['customerDetail']->model->shippingAddress ?? null;

        // Bank Details
        $data['bankDetail'] = null;
        $data['accountNumber'] = null;
        $data['accountType'] = null;
        $data['branchName'] = null;

        if ($transaction->bankLedgerDetails) {
            /** @var Bank $bankDetail */
            $bankDetail = $transaction->bankLedgerDetails?->model ?? null;
            $data['bankDetail'] = $bankDetail;
            $data['accountNumber'] = $data['bankDetail'] ? $data['bankDetail']->account_number : null;
            $data['accountType'] = $data['bankDetail'] ? $data['bankDetail']->account_type : null;
            $data['branchName'] = $data['bankDetail'] ? $data['bankDetail']->branch_name : null;
        }

        // Invoice Setting
        $data['invoiceSetting'] = CompanySetting::pluck('value', 'key')->toArray();

        // Invoice Items
        $data['transactionItems'] = PreparePDFItemDataAction::run($data['transaction']->saleReturnItems);
        if (! empty($data['transactionItems'])) {
            foreach ($data['transactionItems'] as $key => $item) {
                $data['transactionItems'][$key]['customItemsValues'] = PrepareCustomFieldDataForPdfPreview::run(ItemCustomField::SALE_RETURN, $item->id, SaleReturnItemTransaction::class);
                if (count($item->customFieldTransactionItemsInventoryValues) > 0) {
                    $data['transactionItems'][$key]['customItemsInventoryValues'] = PrepareCustomFieldInventoryDataForPdfPreview::run($item->id, SaleReturnItemTransaction::class, $item->item_id);
                }
            }
        }

        $data['transactionLedgers'] = $data['transaction']->saleReturnLedgers;

        // Invoice and Credit Period
        $data['invoiceDate'] = Carbon::parse($data['transaction']->date)->format('d-m-Y');
        $data['itemType'] = $data['transaction']->sale_return_item_type;
        $data = array_merge($data, $this->calculateCreditPeriod($data['transaction']));

        // Customer Details
        $data['customerDetail'] = $data['transaction']->customer;
        $data['panNumber'] = $data['customerDetail']->model->pan_card_number ?? null;

        $data['isCompanyGstApplicable'] = $data['currentCompany']->is_gst_applicable ? true : false;

        $data['showGst'] = false;

        if (! $data['isCompanyGstApplicable']) {
            if (! empty($data['customerDetail']->model->gstin)) {
                $data['showGst'] = true;
            }
        } elseif (! empty($transaction->gstin)) {
            $data['showGst'] = true;
        }

        // Change Label
        $data['changeLabel'] = InvoicesLabel::whereTransactionType(InvoicesLabel::SALE_TRANSACTION)->pluck('label_value', 'label_name')->toArray();

        $data['showCreditPeriod'] = true;

        // Invoice Details Label and Header
        $data['invoiceDetailLabel'] = 'Credit Note Details';
        $data['invoiceNumberLabel'] = 'Credit Note Number';
        $data['invoiceDateLabel'] = 'Credit Note Date';
        $data['invoiceName'] = 'Sale Return Invoice';
        $data['taxInvoice'] = $incomeReturnTransactionMaster?->title_of_print;

        // Show and Hide Column
        $data['showPrintSettings'] = PrintSetting::pluck('status', 'name')->toArray();
        $data['showPanNumber'] = isset($data['invoiceSetting']['pan_no']) ? $data['invoiceSetting']['pan_no'] : true;
        $isA5Pdf = isset($data['invoiceSetting']['pdf_format']) ? ($data['invoiceSetting']['pdf_format'] == CompanySetting::A5 || $data['invoiceSetting']['pdf_format'] == CompanySetting::LANDSCAPE_A5) : false;

        // Custom Font Size
        $data['customFontSize'] = GetPdfAdjustmentsAction::run($isA5Pdf);
        $data['customProp'] = InvoicesLabel::where('is_custom_label', InvoicesLabel::PROP_NAME)->select('label_value', 'label_name')->first();
        $data['configuration'] = IncomeSalesReturn::whereCompanyId($data['currentCompany']->id)->first();
        $data = SaleInvoiceFormatterAction::run($data, RearrangeItem::SALE_RETURN);

        return $data;
    }

    private function calculateCreditPeriod($transaction)
    {
        $creditPeriod = $transaction->credit_period ?? $transaction->customer->model->credit_limit_period;
        $creditType = $transaction->credit_period_type ?? $transaction->customer->model->credit_period_type;

        if (! $creditPeriod) {
            return ['creditPeriod' => null, 'dueDate' => null];
        }

        $dueDate = Carbon::parse($transaction->date);
        if ($creditType === SaleTransaction::CREDIT_PERIOD_TYPE_MONTH) {
            $dueDate->addMonths($creditPeriod);
        } elseif ($creditType === SaleTransaction::CREDIT_PERIOD_TYPE_DAY) {
            $dueDate->addDays($creditPeriod);
        }

        return [
            'creditPeriod' => getCreditPeriod($creditPeriod, $creditType),
            'dueDate' => $dueDate->format('d-m-Y'),
        ];
    }
}
