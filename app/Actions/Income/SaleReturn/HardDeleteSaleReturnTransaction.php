<?php

namespace App\Actions\Income\SaleReturn;

use App\Actions\CustomFieldsItemMaster\Inventory\CalculateAvailableQTY;
use App\Models\ItemCustomFieldCombination;
use App\Models\ItemCustomFieldCombinationInventory;
use App\Models\ItemCustomFieldValue;
use App\Models\PaymentTransaction;
use App\Models\SaleReturnItemTransaction;
use App\Models\SaleReturnLedgerTransaction;
use App\Models\SaleReturnTransaction;
use Lorisleiva\Actions\Concerns\AsAction;

class HardDeleteSaleReturnTransaction
{
    use AsAction;

    public function handle($id, $allTransaction = false, $isDirectHardDelete = false)
    {
        $query = $isDirectHardDelete ? SaleReturnTransaction::withTrashed() : SaleReturnTransaction::onlyTrashed();
        if ($allTransaction) {
            $saleReturnTransactions = $query->get();
        } else {
            $saleReturnTransactions = $query->whereId($id)->get();
        }

        foreach ($saleReturnTransactions as $saleReturn) {
            PaymentTransaction::withTrashed()
                ->where('company_id', $saleReturn->company_id)
                ->wherePaymentVoucherNumber('sale-return/'.$saleReturn->full_invoice_number)->financialYearDate()?->forceDelete();

            $itemsIds = $saleReturn->saleReturnItems->pluck('id')->toArray();
            $ledgersIds = $saleReturn->saleReturnLedgers->pluck('id')->toArray();
            $allCFIds = array_merge($itemsIds, $ledgersIds);
            ItemCustomFieldValue::whereIn('model_id', $allCFIds)->whereIn('model_type', [SaleReturnItemTransaction::class, SaleReturnLedgerTransaction::class])?->delete();

            // Start Delete Inventory
            $removeCombinationIds = [];
            $inventoryItems = ItemCustomFieldCombinationInventory::whereIn('model_id', $allCFIds)->whereIn('model_type', [SaleReturnItemTransaction::class, SaleReturnLedgerTransaction::class])->get();
            foreach ($inventoryItems as $inventoryItem) {
                $combination = $inventoryItem->itemCustomFieldCombination;
                $removeCombinationIds[] = $combination->id;
                $inventoryItem->delete();
                CalculateAvailableQTY::run($combination);
            }
            // Manage if combination has no any inventory then delete
            if (count($removeCombinationIds) > 0) {
                foreach ($removeCombinationIds as $combinationId) {
                    $check = ItemCustomFieldCombinationInventory::where('item_custom_field_combination_id', $combinationId)->count();
                    if ($check == 0) {
                        ItemCustomFieldCombination::whereId($combinationId)->where('available_quantity', 0)->delete();
                    }
                }
            }
            // End Delete Inventory

            $saleReturn->customFieldValues()->delete();
            $saleReturn->addresses()->delete();
            $saleReturn->forceDelete();
        }

        return true;
    }
}
