<?php

namespace App\Actions\Income\Sale;

use App\Actions\CustomFieldsItemMaster\Inventory\CalculateAvailableQTY;
use App\Models\IncomeCreditNoteTransaction;
use App\Models\IncomeDebitNoteTransaction;
use App\Models\ItemCustomFieldCombinationInventory;
use App\Models\ItemCustomFieldValue;
use App\Models\PaymentTransaction;
use App\Models\ReceiptTransaction;
use App\Models\SaleReturnTransaction;
use App\Models\SaleTransaction;
use App\Models\SaleTransactionItem;
use App\Models\SaleTransactionLedger;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;

class HardDeleteSaleTransaction
{
    use AsAction;

    public function handle($id, $allTransaction = false, $isDirectHardDelete = false)
    {
        try {
            DB::beginTransaction();
            disableRecurringScope();

            $query = $isDirectHardDelete ? SaleTransaction::withTrashed() : SaleTransaction::onlyTrashed();

            if ($allTransaction) {
                $sales = $query->get();
            } else {
                $sales = $query->whereId($id)->get();
            }
            enableRecurringScope();

            foreach ($sales as $sale) {
                ReceiptTransaction::withTrashed()
                    ->where('company_id', $sale->company_id)
                    ->whereReceiptNumber('sale/'.$sale->full_invoice_number)
                    ->financialYearDate()?->forceDelete();

                $saleReturn = SaleReturnTransaction::withTrashed()->whereOriginalInvNo($sale->id)->first();
                if ($saleReturn) {
                    PaymentTransaction::withTrashed()
                        ->where('company_id', $saleReturn->company_id)
                        ->wherePaymentVoucherNumber('sale-return/'.$saleReturn->full_invoice_number)
                        ->financialYearDate()?->forceDelete();
                    $saleReturn->addresses()->delete();
                    $saleReturn->forceDelete();
                }
                $incomeDebitNote = IncomeDebitNoteTransaction::withTrashed()->whereOriginalInvNo($sale->id)->first();
                if ($incomeDebitNote) {
                    ReceiptTransaction::withTrashed()
                        ->where('company_id', $incomeDebitNote->company_id)
                        ->whereReceiptNumber('income-debit-note/'.$incomeDebitNote->full_invoice_number)->financialYearDate()?->forceDelete();
                    $incomeDebitNote->addresses()->delete();
                    $incomeDebitNote->forceDelete();
                }

                $incomeCreditNote = IncomeCreditNoteTransaction::withTrashed()->whereOriginalInvNo($sale->id)->first();
                if ($incomeCreditNote) {
                    PaymentTransaction::withTrashed()
                        ->where('company_id', $incomeCreditNote->company_id)
                        ->wherePaymentVoucherNumber('income-credit-note/'.$incomeCreditNote->full_invoice_number)->financialYearDate()?->forceDelete();
                    $incomeCreditNote->addresses()->delete();
                    $incomeCreditNote->forceDelete();
                }

                $itemsIds = $sale->saleItems->pluck('id')->toArray();
                $ledgersIds = $sale->saleLedgers->pluck('id')->toArray();
                $allCFIds = array_merge($itemsIds, $ledgersIds);
                ItemCustomFieldValue::whereIn('model_id', $allCFIds)->whereIn('model_type', [SaleTransactionItem::class, SaleTransactionLedger::class])?->delete();

                // Delete Inventory
                $inventoryItems = ItemCustomFieldCombinationInventory::whereIn('model_id', $allCFIds)->whereIn('model_type', [SaleTransactionItem::class, SaleTransactionLedger::class])->get();
                foreach ($inventoryItems as $inventoryItem) {
                    $combination = $inventoryItem->itemCustomFieldCombination;
                    $inventoryItem->delete();
                    CalculateAvailableQTY::run($combination);
                }

                $sale->customFieldValues()->delete();
                $sale->addresses()->delete();
                $sale->forceDelete();
            }

            DB::commit();

            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
}
