<?php

namespace App\Actions\Income\Sale;

use App\Actions\CommonAction\PreparePDFItemDataAction;
use App\Actions\CustomFields\GetTransactionCustomFieldsAction;
use App\Actions\CustomFieldsItemMaster\Inventory\PrepareCustomFieldInventoryDataForPdfPreview;
use App\Actions\CustomFieldsItemMaster\Transaction\PrepareCustomFieldDataForPdfPreview;
use App\Actions\Ledger\GetLedgerClosingBalanceAndType;
use App\Actions\v1\PdfConfiguration\GetPdfAdjustmentsAction;
use App\Models\Address;
use App\Models\Company;
use App\Models\CompanySetting;
use App\Models\Configuration\SaleConfiguration;
use App\Models\DeliveryChallanTransaction;
use App\Models\EwayBill;
use App\Models\InvoicesLabel;
use App\Models\ItemCustomField;
use App\Models\Ledger;
use App\Models\Master\Bank;
use App\Models\Master\IncomeTransactionMaster;
use App\Models\PrintSetting;
use App\Models\RearrangeItem;
use App\Models\SaleTransaction;
use App\Models\SaleTransactionItem;
use App\Models\SettleAdvancePayment;
use App\Models\TransactionCustomField;
use Carbon\Carbon;
use Lorisleiva\Actions\Concerns\AsAction;

class GetInvoicePDFDataForSale
{
    use AsAction;

    public function handle(int $saleId)
    {
        $data = [];

        disableRecurringScope();
        // Load Sale Transaction with related data
        $transaction = SaleTransaction::with(
            'addresses',
            'customer.model',
            'transport',
            'saleItems.items.model',
            'saleLedgers.ledgers',
            'receiptTransactionItem',
            'brokerDetails',
            'addLess.ledger',
            'additionalCharges.ledger',
            'dispatchAddress',
            'bankLedgerDetails.model',
        )->whereId($saleId)->firstOrFail();

        $data['transaction'] = $transaction;
        enableRecurringScope();
        // Process Add/Less and Additional Charges
        $data['addLess'] = prepareAddLess($transaction->addLess);
        $data['additionalCharges'] = prepareAdditionalCharges($transaction->additionalCharges);

        // Load Company Data
        $currentCompany = Company::with([
            'billingAddress',
            'dispatchAddress',
            'companyTax',
            'user',
            'mailConfiguration',
        ])->findOrFail($transaction->company_id);

        session(['current_company' => $currentCompany]);
        setCompanyInSession($currentCompany);

        // custom fields
        $data['customFieldValues'] = GetTransactionCustomFieldsAction::run(TransactionCustomField::SALE, $saleId, SaleTransaction::class);

        // Customer Details
        $data['customerDetail'] = $transaction->customer;
        $data['currentCompany'] = $currentCompany;
        $data['companyBillingAddress'] = $currentCompany->billingAddress;
        $data['isCompanyGstApplicable'] = $data['currentCompany']->is_gst_applicable ? true : false;

        $data['showGst'] = false;

        if (! $data['isCompanyGstApplicable']) {
            if (! empty($data['customerDetail']->model->gstin)) {
                $data['showGst'] = true;
            }
        } elseif (! empty($transaction->gstin)) {
            $data['showGst'] = true;
        }

        // E-Way Bill
        $data['eWayBill'] = EwayBill::whereTransactionId($saleId)->whereNot('is_canceled', 1)->first();
        $data['fromNameExists'] = CompanySetting::whereCompanyId($data['currentCompany']->id)->where('key', 'from_name')->exists();
        $data['replyToEmailExists'] = CompanySetting::whereCompanyId($data['currentCompany']->id)->where('key', 'replay_to_email')->exists();
        $incomeTransactionMaster = IncomeTransactionMaster::with('ledgerDetails.model')->whereCompanyId($data['currentCompany']->id)->first();
        $data['taxInvoice'] = $incomeTransactionMaster?->title_of_print;
        $data['invoiceName'] = 'Sale Invoice';

        $data['configuration'] = SaleConfiguration::whereCompanyId($currentCompany->id)->first();

        $data['panNumber'] = $transaction->customer->model->pan_card_number ?? null;
        $data['deliveryChallan'] = $data['transaction']->delivery_challan_no;
        $data = array_merge($data, $this->getDeliveryChallanData($transaction));

        // Bank Details
        $data['bankDetail'] = null;
        $data['accountNumber'] = null;
        $data['accountType'] = null;
        $data['branchName'] = null;
        $companySettings = CompanySetting::whereCompanyId($data['currentCompany']->id)->pluck('value', 'key')->toArray();

        if ($transaction->bankLedgerDetails) {
            /** @var Bank $bankDetail */
            $bankDetail = $transaction->bankLedgerDetails?->model ?? null;
            $data['bankDetail'] = $bankDetail;
            $data['accountNumber'] = $data['bankDetail'] ? $data['bankDetail']->account_number : null;
            $data['accountType'] = $data['bankDetail'] ? $data['bankDetail']->account_type : null;
            $data['branchName'] = $data['bankDetail'] ? $data['bankDetail']->branch_name : null;
        }

        $data['invoiceSetting'] = $companySettings;
        $data['transactionItems'] = PreparePDFItemDataAction::run($data['transaction']->saleItems);
        if (! empty($data['transactionItems'])) {
            foreach ($data['transactionItems'] as $key => $item) {
                $data['transactionItems'][$key]['customItemsValues'] = PrepareCustomFieldDataForPdfPreview::run(ItemCustomField::SALE, $item->id, SaleTransactionItem::class);
                if (count($item->customFieldTransactionItemsInventoryValues) > 0) {
                    $data['transactionItems'][$key]['customItemsInventoryValues'] = PrepareCustomFieldInventoryDataForPdfPreview::run($item->id, SaleTransactionItem::class, $item->item_id);
                }
            }
        }

        $data['transactionLedgers'] = $data['transaction']->saleLedgers;

        $data['showPanNumber'] = isset($data['invoiceSetting']['pan_no']) ? $data['invoiceSetting']['pan_no'] : true;
        $data['ledgerShippingAddress'] = $data['customerDetail']->model->shippingAddress ?? null;

        // Invoice Addresses
        $data['billingAddress'] = $transaction->addresses->where('address_type', SaleTransaction::BILLING_ADDRESS)->first();
        if ($transaction->same_as_billing) {
            $data['shippingAddress'] = $data['billingAddress'];
            $data['transaction']->shipping_gstin = $transaction->gstin;
        } else {
            $data['shippingAddress'] = Address::whereId($data['transaction']->shipping_address_id)->first() ?? $transaction->addresses->where('address_type', SaleTransaction::SHIPPING_ADDRESS)->first();
        }
        $data['dispatchAddress'] = $transaction->dispatchAddress;

        // Invoice and Credit Period
        $data['invoiceDate'] = Carbon::parse($transaction->date)->format('d-m-Y');
        $data['itemType'] = $transaction->sales_item_type;
        $data = array_merge($data, $this->calculateCreditPeriod($transaction));

        $totalDueAmount = GetLedgerClosingBalanceAndType::run($transaction->customer_ledger_id, Carbon::parse($transaction->date)->format('Y-m-d'));

        $totalDueAmount = $totalDueAmount['closing_bal_type'] === Ledger::CR
            ? -$totalDueAmount['closing_bal']
            : $totalDueAmount['closing_bal'];

        $settleAdvancePayments = SettleAdvancePayment::where('model_type', SaleTransaction::class)->where('model_id', $saleId)->get()->sum('total_amount');

        $data['totalDueAmount'] = $totalDueAmount - ($transaction->due_amount + $settleAdvancePayments);
        $data['currentBalance'] = $totalDueAmount - $settleAdvancePayments;

        $data['invoiceSetting'] = $companySettings;
        $data['currentOutstanding'] = $data['invoiceSetting']['current_outstanding'] ?? 0;
        $data['balance'] = $transaction->due_amount;

        // Invoice Labels and Print Settings
        $data['changeLabel'] = InvoicesLabel::whereTransactionType(InvoicesLabel::SALE_TRANSACTION)
            ->pluck('label_value', 'label_name')
            ->toArray();

        $data['invoiceDetailLabel'] = empty($data['changeLabel']['invoice_details']) ? 'Invoice Details' : $data['changeLabel']['invoice_details'];
        $data['invoiceNumberLabel'] = empty($data['changeLabel']['invoice_number']) ? 'Invoice No.' : $data['changeLabel']['invoice_number'];
        $data['invoiceDateLabel'] = empty($data['changeLabel']['invoice_date']) ? 'Invoice Date' : $data['changeLabel']['invoice_date'];
        $data['invoiceName'] = 'Sale Invoice';
        $data['showPrintSettings'] = PrintSetting::whereCompanyId($data['currentCompany']->id)->pluck('status', 'name')->toArray();
        $isA5Pdf = isset($data['invoiceSetting']['pdf_format']) ? ($data['invoiceSetting']['pdf_format'] == CompanySetting::A5 || $data['invoiceSetting']['pdf_format'] == CompanySetting::LANDSCAPE_A5) : false;

        $data['customFontSize'] = GetPdfAdjustmentsAction::run($isA5Pdf);
        $data['customProp'] = InvoicesLabel::where('is_custom_label', InvoicesLabel::PROP_NAME)->select('label_value', 'label_name')->first();

        $data = SaleInvoiceFormatterAction::run($data, RearrangeItem::SALE);

        return $data;
    }

    private function calculateCreditPeriod($transaction)
    {
        $creditPeriod = $transaction->credit_period ?? $transaction->customer->model->credit_limit_period;
        $creditType = $transaction->credit_period_type ?? $transaction->customer->model->credit_period_type;

        if (! $creditPeriod) {
            return ['creditPeriod' => null, 'dueDate' => null];
        }

        $dueDate = Carbon::parse($transaction->date);
        if ($creditType === SaleTransaction::CREDIT_PERIOD_TYPE_MONTH) {
            $dueDate->addMonths($creditPeriod);
        } elseif ($creditType === SaleTransaction::CREDIT_PERIOD_TYPE_DAY) {
            $dueDate->addDays($creditPeriod);
        }

        return [
            'creditPeriod' => getCreditPeriod($creditPeriod, $creditType),
            'dueDate' => $dueDate->format('d-m-Y'),
        ];
    }

    private function getDeliveryChallanData($transaction)
    {
        $deliveryChallan = $transaction->delivery_challan_no;
        if (empty($deliveryChallan)) {
            return ['deliveryChallanInvoiceNumber' => null, 'deliveryChallanInvoiceDate' => null];
        }

        $ids = explode(',', $deliveryChallan);
        $challans = DeliveryChallanTransaction::whereIn('id', $ids)->get();

        return [
            'deliveryChallanInvoiceNumber' => $challans->pluck('challan_number')->implode(','),
            'deliveryChallanInvoiceDate' => Carbon::parse($challans->first()->challan_date)->format('d-m-Y') ?? null,
        ];
    }
}
