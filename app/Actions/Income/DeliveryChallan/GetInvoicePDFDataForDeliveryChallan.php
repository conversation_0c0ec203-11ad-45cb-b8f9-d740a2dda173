<?php

namespace App\Actions\Income\DeliveryChallan;

use App\Actions\CommonAction\PreparePDFItemDataAction;
use App\Actions\CustomFields\GetTransactionCustomFieldsAction;
use App\Actions\CustomFieldsItemMaster\Inventory\PrepareCustomFieldInventoryDataForPdfPreview;
use App\Actions\CustomFieldsItemMaster\Transaction\PrepareCustomFieldDataForPdfPreview;
use App\Actions\v1\PdfConfiguration\GetPdfAdjustmentsAction;
use App\Models\Address;
use App\Models\Company;
use App\Models\CompanySetting;
use App\Models\Configuration\DeliveryChallanConfiguration;
use App\Models\DeliveryChallanTransaction;
use App\Models\DeliveryChallanTransactionItem;
use App\Models\DeliveryChallanTransactionMaster;
use App\Models\InvoicesLabel;
use App\Models\ItemCustomField;
use App\Models\Master\IncomeTransactionMaster;
use App\Models\PrintSetting;
use App\Models\TransactionCustomField;
use Lorisleiva\Actions\Concerns\AsAction;

class GetInvoicePDFDataForDeliveryChallan
{
    use AsAction;

    public function handle($deliveryChallanId)
    {
        $data = [];

        $data['transaction'] = DeliveryChallanTransaction::with([
            'party.model', 'transactionItems.items.model', 'transactionItems.unit', 'transportDetails', 'brokerDetails',
            'billingAddress', 'shippingAddress', 'addLess.ledger', 'additionalCharges.ledger',
        ])->whereId($deliveryChallanId)->first();

        $data['currentCompany'] = Company::with('billingAddress', 'dispatchAddress', 'companyTax', 'user')->whereId($data['transaction']->company_id)->first();

        session(['current_company' => $data['currentCompany']]);

        // Process Add/Less and Additional Charges
        $data['addLess'] = prepareAddLess($data['transaction']->addLess);
        $data['additionalCharges'] = prepareAdditionalCharges($data['transaction']->additionalCharges);

        // custom fields
        $data['customFieldValues'] = GetTransactionCustomFieldsAction::run(TransactionCustomField::DELIVERY_CHALLAN, $deliveryChallanId, DeliveryChallanTransaction::class);

        $data['fromNameExists'] = CompanySetting::where('key', 'from_name')->exists();
        $data['replyToEmailExists'] = CompanySetting::where('key', 'replay_to_email')->exists();
        $data['deliveryChallanTransactionMaster'] = DeliveryChallanTransactionMaster::whereCompanyId($data['currentCompany']->id)->first();
        $data['configuration'] = DeliveryChallanConfiguration::whereCompanyId($data['currentCompany']->id)->first();

        $data['companyBillingAddress'] = $data['currentCompany']->billingAddress;
        $data['companyShippingAddress'] = $data['currentCompany']->shippingAddress;

        $data['bankDetail'] = null;
        $data['accountNumber'] = null;
        $data['accountType'] = null;
        $data['branchName'] = null;
        $data['transactionItems'] = PreparePDFItemDataAction::run($data['transaction']->transactionItems);
        if (! empty($data['transactionItems'])) {
            foreach ($data['transactionItems'] as $key => $item) {
                if ($data['transaction']['invoice_type'] == DeliveryChallanTransaction::WITH_AMOUNT) {
                    $data['transactionItems'][$key]['customItemsValues'] = PrepareCustomFieldDataForPdfPreview::run(ItemCustomField::DELIVERY_CHALLAN, $item->id, DeliveryChallanTransactionItem::class);
                } else {
                    $data['transactionItems'][$key]['customItemsValues'] = PrepareCustomFieldDataForPdfPreview::run(ItemCustomField::DELIVERY_CHALLAN, $item->id, DeliveryChallanTransactionItem::class);
                }
                if (count($item->customFieldTransactionItemsInventoryValues) > 0) {
                    $data['transactionItems'][$key]['customItemsInventoryValues'] = PrepareCustomFieldInventoryDataForPdfPreview::run($item->id, DeliveryChallanTransactionItem::class, $item->item_id);
                }
            }
        }
        $data['dispatchAddress'] = $data['transaction']->dispatchAddress;
        $data['customerDetail'] = $data['transaction']->party;
        $data['ledgerShippingAddress'] = $data['customerDetail']->shippingAddress ?? null;
        $data['billingAddress'] = $data['transaction']->billingAddress;
        if ($data['transaction']->same_as_billing) {
            $data['shippingAddress'] = $data['billingAddress'];
            $data['transaction']->shipping_gstin = $data['transaction']->gstin;
        } else {
            $data['shippingAddress'] = Address::whereId($data['transaction']->shipping_address_id)->first() ?? $data['transaction']->shippingAddress;
        }

        $data['itemType'] = $data['transaction']->invoice_type;
        $data['dueDate'] = null;
        $data['creditPeriod'] = null;
        $data['isCompanyGstApplicable'] = $data['currentCompany']->is_gst_applicable ? true : false;

        $data['showGst'] = false;

        if (! $data['isCompanyGstApplicable']) {
            if (! empty($data['customerDetail']->model->gstin)) {
                $data['showGst'] = true;
            }
        } elseif (! empty($data['transaction']->gstin)) {
            $data['showGst'] = true;
        }
        $data['invoiceSetting'] = CompanySetting::pluck('value', 'key')->toArray();
        $incomeTransactionMaster = IncomeTransactionMaster::with('ledgerDetails.model')->whereCompanyId($data['currentCompany']->id)->first();
        $data['taxInvoice'] = $incomeTransactionMaster?->title_of_print;
        $data['panNumber'] = $data['customerDetail']->model->pan_card_number ?? null;
        $data['showPanNumber'] = isset($data['invoiceSetting']['delivery_challan_pan_no']) ? $data['invoiceSetting']['delivery_challan_pan_no'] : true;
        $data['changeLabel'] = InvoicesLabel::whereTransactionType(InvoicesLabel::DELIVERY_CHALLAN)->pluck('label_value', 'label_name')->toArray();
        $data['showPrintSettings'] = PrintSetting::pluck('status', 'name')->toArray();
        $isA5Pdf = isset($data['invoiceSetting']['delivery_challan_pdf_format']) ? ($data['invoiceSetting']['delivery_challan_pdf_format'] == CompanySetting::A5 || $data['invoiceSetting']['delivery_challan_pdf_format'] == CompanySetting::LANDSCAPE_A5) : false;

        $data['customFontSize'] = GetPdfAdjustmentsAction::run($isA5Pdf);
        $data['customProp'] = InvoicesLabel::where('is_custom_label', InvoicesLabel::PROP_NAME)->select('label_value', 'label_name')->first();

        return $data;
    }
}
