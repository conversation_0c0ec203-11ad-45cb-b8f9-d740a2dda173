<?php

namespace App\Actions\Income\DeliveryChallan;

use App\Actions\CustomFieldsItemMaster\Inventory\CalculateAvailableQTY;
use App\Models\DeliveryChallanTransaction;
use App\Models\DeliveryChallanTransactionItem;
use App\Models\ItemCustomFieldCombinationInventory;
use App\Models\ItemCustomFieldValue;
use Lorisleiva\Actions\Concerns\AsAction;

class HardDeleteDeliveryChallanTransaction
{
    use AsAction;

    public function handle($id, $allTransaction = false, $isDirectHardDelete = false)
    {
        $query = $isDirectHardDelete ? DeliveryChallanTransaction::withTrashed() : DeliveryChallanTransaction::onlyTrashed();
        if ($allTransaction) {
            $deliveryChallanTransactions = $query->get();
        } else {
            $deliveryChallanTransactions = $query->whereId($id)->get();
        }

        foreach ($deliveryChallanTransactions as $deliveryChallan) {

            $itemsIds = $deliveryChallan->transactionItems->pluck('id')->toArray();
            ItemCustomFieldValue::whereIn('model_id', $itemsIds)->where('model_type', DeliveryChallanTransactionItem::class)?->delete();

            // Delete Inventory
            $inventoryItems = ItemCustomFieldCombinationInventory::whereIn('model_id', $itemsIds)->where('model_type', DeliveryChallanTransactionItem::class)->get();
            foreach ($inventoryItems as $inventoryItem) {
                $combination = $inventoryItem->itemCustomFieldCombination;
                $inventoryItem->delete();
                CalculateAvailableQTY::run($combination);
            }

            $deliveryChallan->customFieldValues()->delete();
            $deliveryChallan->forceDelete();
        }

        return true;
    }
}
