<?php

namespace App\Actions\CustomFieldsItemMaster\Inventory;

use App\Models\ItemCustomField;
use App\Models\ItemCustomFieldCombination;
use App\Models\ItemCustomFieldCombinationInventory;
use App\Models\PurchaseItemTransaction;
use App\Models\SaleTransactionItem;
use Lorisleiva\Actions\Concerns\AsAction;

class GetItemCFInventoryListForSale
{
    use AsAction;

    public function handle($itemId, $type)
    {
        if (in_array($type, [
            ItemCustomField::SALE,
            // ItemCustomField::INCOME_DEBIT_NOTE,
            // ItemCustomField::PURCHASE_RETURN,
            // ItemCustomField::EXPENSE_DEBIT_NOTE,
            ItemCustomField::INCOME_ESTIMATE_QUOTE,
            ItemCustomField::DELIVERY_CHALLAN,
        ])) {
            $inventoryData = ItemCustomFieldCombination::where('item_id', $itemId)->where('available_quantity', '>', 0)->get();

            $response = [];

            foreach ($inventoryData as $inventory) {
                $fields = [];

                for ($i = 1; $i <= 5; $i++) {
                    $cfIdKey = 'cf_id_'.$i;
                    $cfValueKey = 'cf_value_'.$i;

                    if (! empty($inventory[$cfIdKey]) && ! empty($inventory[$cfValueKey])) {
                        $cf = $inventory->{'customField'.$i};

                        if ($cf) {
                            $fields[] = [
                                'custom_field_id' => $cf->id,
                                'label_name' => $cf->label_name,
                                'custom_field_type' => $cf->custom_field_type,
                                'purchase_rate' => $inventory['purchase_rate'],
                                'purchase_date' => $inventory['purchase_date'],
                                'value' => $inventory[$cfValueKey],
                                'ordering' => $cf->ordering ?? $i,
                            ];
                        }
                    }
                }

                $response[] = [
                    'combination_id' => $inventory['id'],
                    'available_quantity' => $inventory['available_quantity'],
                    'purchase_rate' => $inventory['purchase_rate'],
                    'purchase_date' => $inventory['purchase_date'],
                    'is_selected' => false,
                    'fields' => collect($fields)->sortBy('ordering')->values()->toArray(),
                ];
            }

            return $response;
        }

        if (in_array($type, [
            ItemCustomField::SALE_RETURN,
            ItemCustomField::INCOME_DEBIT_NOTE,
            ItemCustomField::INCOME_CREDIT_NOTE,
        ])) {
            $combinationsInventoryData = ItemCustomFieldCombinationInventory::whereHas('itemCustomFieldCombination', function ($query) use ($itemId) {
                $query->where('item_id', $itemId);
            })
                ->where('model_type', SaleTransactionItem::class)
                ->pluck('item_custom_field_combination_id')
                ->unique()
                ->toArray();

            $response = [];

            $inventoryData = ItemCustomFieldCombination::whereIn('id', $combinationsInventoryData)->get();

            foreach ($inventoryData as $inventory) {
                $fields = [];

                for ($i = 1; $i <= 5; $i++) {
                    $cfIdKey = 'cf_id_'.$i;
                    $cfValueKey = 'cf_value_'.$i;

                    if (! empty($inventory[$cfIdKey]) && ! empty($inventory[$cfValueKey])) {
                        $cf = $inventory->{'customField'.$i};

                        if ($cf) {
                            $fields[] = [
                                'custom_field_id' => $cf->id,
                                'label_name' => $cf->label_name,
                                'custom_field_type' => $cf->custom_field_type,
                                'purchase_rate' => $inventory['purchase_rate'],
                                'purchase_date' => $inventory['purchase_date'],
                                'value' => $inventory[$cfValueKey],
                                'ordering' => $cf->ordering ?? $i,
                            ];
                        }
                    }
                }

                $response[] = [
                    'combination_id' => $inventory['id'],
                    'available_quantity' => $inventory['available_quantity'],
                    'total_quantity' => ItemCustomFieldCombinationInventory::where('item_custom_field_combination_id', $inventory['id'])->where('model_type', SaleTransactionItem::class)->sum('quantity'),
                    'purchase_rate' => $inventory['purchase_rate'],
                    'purchase_date' => $inventory['purchase_date'],
                    'is_selected' => false,
                    'fields' => collect($fields)->sortBy('ordering')->values()->toArray(),
                ];
            }

            return $response;
        }

        if (in_array($type, [
            ItemCustomField::PURCHASE_RETURN,
            ItemCustomField::EXPENSE_DEBIT_NOTE,
            ItemCustomField::EXPENSE_CREDIT_NOTE,
        ])) {
            $combinationsInventoryData = ItemCustomFieldCombinationInventory::whereHas('itemCustomFieldCombination', function ($query) use ($itemId) {
                $query->where('item_id', $itemId);
            })
                ->where('model_type', PurchaseItemTransaction::class)
                ->pluck('item_custom_field_combination_id')
                ->unique()
                ->toArray();

            $response = [];

            $inventoryData = ItemCustomFieldCombination::whereIn('id', $combinationsInventoryData)->get();

            foreach ($inventoryData as $inventory) {
                $fields = [];

                for ($i = 1; $i <= 5; $i++) {
                    $cfIdKey = 'cf_id_'.$i;
                    $cfValueKey = 'cf_value_'.$i;

                    if (! empty($inventory[$cfIdKey]) && ! empty($inventory[$cfValueKey])) {
                        $cf = $inventory->{'customField'.$i};

                        if ($cf) {
                            $fields[] = [
                                'custom_field_id' => $cf->id,
                                'label_name' => $cf->label_name,
                                'custom_field_type' => $cf->custom_field_type,
                                'purchase_rate' => $inventory['purchase_rate'],
                                'purchase_date' => $inventory['purchase_date'],
                                'value' => $inventory[$cfValueKey],
                                'ordering' => $cf->ordering ?? $i,
                            ];
                        }
                    }
                }

                $response[] = [
                    'combination_id' => $inventory['id'],
                    'available_quantity' => $inventory['available_quantity'],
                    'total_quantity' => ItemCustomFieldCombinationInventory::where('item_custom_field_combination_id', $inventory['id'])->where('model_type', PurchaseItemTransaction::class)->sum('quantity'),
                    'purchase_rate' => $inventory['purchase_rate'],
                    'purchase_date' => $inventory['purchase_date'],
                    'is_selected' => false,
                    'fields' => collect($fields)->sortBy('ordering')->values()->toArray(),
                ];
            }

            return $response;
        }
    }
}
