<?php

namespace App\Actions\CustomFieldsItemMaster\Inventory;

use App\Models\DeliveryChallanTransactionItem;
use App\Models\ExpenseCreditNoteItemTransaction;
use App\Models\ExpenseDebitNoteItemTransaction;
use App\Models\IncomeCreditNoteItemTransaction;
use App\Models\IncomeDebitNoteItemTransaction;
use App\Models\IncomeEstimateQuoteItemInvoice;
use App\Models\ItemCustomFieldCombination;
use App\Models\ItemCustomFieldCombinationInventory;
use App\Models\Master\ItemMaster;
use App\Models\PurchaseItemTransaction;
use App\Models\PurchaseOrderItemInvoice;
use App\Models\PurchaseReturnItemTransaction;
use App\Models\SaleReturnItemTransaction;
use App\Models\SaleTransactionItem;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Lorisleiva\Actions\Concerns\AsAction;

class UpdateItemCFInventoryAction
{
    use AsAction;

    protected array $inwardModeTypes = [
        PurchaseItemTransaction::class,
        ExpenseCreditNoteItemTransaction::class,
        SaleReturnItemTransaction::class,
        IncomeCreditNoteItemTransaction::class,
        ItemMaster::class,
        PurchaseOrderItemInvoice::class,
    ];

    protected array $outwardModeTypes = [
        SaleTransactionItem::class,
        IncomeDebitNoteItemTransaction::class,
        PurchaseReturnItemTransaction::class,
        ExpenseDebitNoteItemTransaction::class,
        IncomeEstimateQuoteItemInvoice::class,
        DeliveryChallanTransactionItem::class,
    ];

    public function handle($inventoryData, $itemId, $modelId, $modelType)
    {
        try {
            DB::beginTransaction();

            $isInwardMode = in_array($modelType, $this->inwardModeTypes);
            $isOutwardMode = in_array($modelType, $this->outwardModeTypes);

            if ($isInwardMode || $isOutwardMode) {

                $removeCombinationIds = [];

                // Delete existing inventories
                $oldInventories = ItemCustomFieldCombinationInventory::with('itemCustomFieldCombination')
                    ->where('model_id', $modelId)
                    ->where('model_type', $modelType)
                    ->get();

                foreach ($oldInventories as $entry) {
                    $combination = $entry->itemCustomFieldCombination;
                    $removeCombinationIds[] = $combination->id;
                    $entry->delete();
                    CalculateAvailableQTY::run($combination);
                }

                // Process new data
                foreach ($inventoryData as $row) {
                    $this->processInventoryRow($row, $itemId, $modelId, $modelType);
                }

                // Manage if combination has no any inventory then delete
                if (count($removeCombinationIds) > 0) {
                    foreach ($removeCombinationIds as $combinationId) {
                        $check = ItemCustomFieldCombinationInventory::where('item_custom_field_combination_id', $combinationId)->count();
                        if ($check == 0) {
                            ItemCustomFieldCombination::whereId($combinationId)->where('available_quantity', 0)->delete();
                        }
                    }
                }
            }

            DB::commit();

            return true;
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Line No:-'.$e->getLine().' => '.$e->getMessage());
            throw $e;
        }
    }

    private function processInventoryRow($row, $itemId, $modelId, $modelType)
    {
        $purchaseRate = $row[0]['purchase_rate'] ?? null;
        $purchaseDate = ! empty($row[0]['purchase_date']) ? Carbon::parse($row[0]['purchase_date'])->format('Y-m-d') : null;
        $qty = $row[0]['quantity'] ?? 0;

        $customFields = collect($row)->take(5)->mapWithKeys(function ($field, $i) {
            return [
                'cf_id_'.($i + 1) => $field['custom_field_id'],
                'cf_value_'.($i + 1) => $field['value'],
            ];
        })->all();

        $expectedKeys = array_keys($customFields);

        // Find or create combination
        $combinationQuery = ItemCustomFieldCombination::where('item_id', $itemId)
            ->when($purchaseRate, fn ($q) => $q->where('purchase_rate', $purchaseRate))
            ->when($purchaseDate, fn ($q) => $q->where('purchase_date', $purchaseDate))
            ->where(function ($query) use ($customFields, $expectedKeys) {
                foreach ($customFields as $key => $value) {
                    $query->where($key, $value);
                }

                // Ensure the record doesn't have *extra* cf_id/value fields
                for ($i = 1; $i <= 5; $i++) {
                    if (! in_array("cf_id_$i", $expectedKeys)) {
                        $query->whereNull("cf_id_$i")->whereNull("cf_value_$i");
                    }
                }
            });

        $combination = $combinationQuery->first();

        if (! $combination) {
            $combination = ItemCustomFieldCombination::create(array_merge([
                'item_id' => $itemId,
                'available_quantity' => $qty,
                'purchase_rate' => $purchaseRate,
                'purchase_date' => $purchaseDate,
            ], $customFields));
        }

        // Create inventory record
        ItemCustomFieldCombinationInventory::create([
            'item_custom_field_combination_id' => $combination->id,
            'model_id' => $modelId,
            'model_type' => $modelType,
            'quantity' => $qty,
        ]);

        $combination = CalculateAvailableQTY::run($combination);
    }
}
