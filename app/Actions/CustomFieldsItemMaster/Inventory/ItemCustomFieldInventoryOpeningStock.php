<?php

namespace App\Actions\CustomFieldsItemMaster\Inventory;

use App\Models\ItemCustomFieldSetting;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Lorisleiva\Actions\Concerns\AsAction;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

class ItemCustomFieldInventoryOpeningStock
{
    use AsAction;

    public function handle($inventoryData, $itemId, $modelType, $method = 'store')
    {
        try {
            DB::beginTransaction();

            if (empty($inventoryData)) {
                return;
            }

            $incomingIds = collect($inventoryData)->flatten(1)->pluck('custom_field_id')->filter()->unique()->toArray();

            $existingIds = ItemCustomFieldSetting::where('item_id', $itemId)->whereIn('custom_field_id', $incomingIds)->exists();

            if (! $existingIds) {
                throw new UnprocessableEntityHttpException('Please enable custom field first for this item inventory.');
            }

            if ($method == 'store') {
                StoreItemCFInventoryAction::run($inventoryData, $itemId, $itemId, $modelType);
            }

            if ($method == 'update') {
                UpdateItemCFInventoryAction::run($inventoryData, $itemId, $itemId, $modelType);
            }

            DB::commit();

            return true;
        } catch (Exception $th) {
            DB::rollBack();
            Log::error('Line No:-'.$th->getLine().' => '.$th->getMessage());
            throw $th;
        }
    }
}
