<?php

namespace App\Actions\CustomFieldsItemMaster\Inventory;

use App\Models\DeliveryChallanTransactionItem;
use App\Models\ExpenseCreditNoteItemTransaction;
use App\Models\ExpenseDebitNoteItemTransaction;
use App\Models\IncomeCreditNoteItemTransaction;
use App\Models\IncomeDebitNoteItemTransaction;
use App\Models\IncomeEstimateQuoteItemInvoice;
use App\Models\ItemCustomField;
use App\Models\ItemCustomFieldCombination;
use App\Models\ItemCustomFieldCombinationInventory;
use App\Models\ItemCustomFieldTransactionSetting;
use App\Models\Master\ItemMaster;
use App\Models\PurchaseItemTransaction;
use App\Models\PurchaseOrderItemInvoice;
use App\Models\PurchaseReturnItemTransaction;
use App\Models\SaleReturnItemTransaction;
use App\Models\SaleTransactionItem;
use Lorisle<PERSON>\Actions\Concerns\AsAction;

class GetItemCFInventoryAction
{
    use AsAction;

    public function handle($modelId, $modelType, $itemId, $transactionId = null)
    {
        $transactionType = null;
        if ($modelType != ItemMaster::class) {
            $transactionType = $this->handleTransactionType($modelType);
        }

        if (in_array($modelType, [
            PurchaseItemTransaction::class,
            ExpenseCreditNoteItemTransaction::class,
            SaleReturnItemTransaction::class,
            IncomeCreditNoteItemTransaction::class,
            ItemMaster::class,
            PurchaseOrderItemInvoice::class,
        ])) {
            $inventoryData = ItemCustomFieldCombinationInventory::with('itemCustomFieldCombination')->where('model_id', $modelId)
                ->where('model_type', $modelType)
                ->get();

            $response = [];

            foreach ($inventoryData as $entry) {
                $combination = $entry->itemCustomFieldCombination;

                $fields = [];

                for ($i = 1; $i <= 5; $i++) {
                    $cfId = $combination->{'cf_id_'.$i};
                    $cfValue = $combination->{'cf_value_'.$i};

                    if ($cfId && $cfValue) {
                        $customField = ItemCustomField::find($cfId);
                        $isForPrint = ItemCustomFieldTransactionSetting::where('custom_field_id', $cfId)->where('transaction_type', $transactionType)->first();

                        $fields[] = [
                            'id' => $entry->id,
                            'custom_field_id' => $cfId,
                            'label_name' => $customField->label_name ?? null,
                            'custom_field_type' => $customField->custom_field_type ?? null,
                            'purchase_rate' => $combination->purchase_rate,
                            'purchase_date' => $combination->purchase_date,
                            'value' => $cfValue,
                            'is_show_in_print' => $isForPrint ? $isForPrint->is_show_in_print : false,
                            'ordering' => $customField->ordering ?? $i,
                        ];
                    }
                }

                $response[] = [
                    'combination_id' => $entry->itemCustomFieldCombination->id,
                    'quantity' => $entry->quantity,
                    'available_quantity' => $combination->available_quantity,
                    'purchase_rate' => $combination->purchase_rate,
                    'purchase_date' => $combination->purchase_date,
                    'fields' => collect($fields)->sortBy('ordering')->values()->toArray(),
                ];
            }

            return $response;
        }

        if (in_array($modelType, [
            SaleTransactionItem::class,
            IncomeDebitNoteItemTransaction::class,
            PurchaseReturnItemTransaction::class,
            ExpenseDebitNoteItemTransaction::class,
            IncomeEstimateQuoteItemInvoice::class,
            DeliveryChallanTransactionItem::class,
        ])) {
            $parentModelKey = $this->parentModelkey($modelType);

            $selectedInventory = ItemCustomFieldCombinationInventory::with(['itemCustomFieldCombination', 'model'])->where('model_id', $modelId)
                ->where('model_type', $modelType)
                ->get()
                ->keyBy('item_custom_field_combination_id');

            $existingInventory = ItemCustomFieldCombination::where('item_id', $itemId)
                ->whereHas('combinationInventory', function ($query) use ($modelId, $modelType, $parentModelKey, $transactionId) {
                    $query->where(function ($query) use ($modelId, $modelType) {
                        $query->where('model_id', $modelId)->where('model_type', $modelType);
                    })->orWhereHasMorph('model', [$modelType], function ($query) use ($parentModelKey, $transactionId) {
                        $query->where($parentModelKey, $transactionId);
                    });
                })->get();

            $inventoryData = ItemCustomFieldCombination::where('item_id', $itemId)
                ->where('available_quantity', '>', 0)
                ->get();

            if ($existingInventory->count() > 0) {
                $inventoryData = $inventoryData->merge($existingInventory);
            }

            $response = [];

            foreach ($inventoryData as $inventory) {
                $fields = [];

                for ($i = 1; $i <= 5; $i++) {
                    $cfIdKey = 'cf_id_'.$i;
                    $cfValueKey = 'cf_value_'.$i;

                    if (! empty($inventory[$cfIdKey]) && ! empty($inventory[$cfValueKey])) {
                        $cf = $inventory->{'customField'.$i};
                        $isForPrint = ItemCustomFieldTransactionSetting::where('custom_field_id', $cf->id)->where('transaction_type', $transactionType)->first();

                        if ($cf) {
                            $fields[] = [
                                'custom_field_id' => $cf->id,
                                'label_name' => $cf->label_name,
                                'custom_field_type' => $cf->custom_field_type,
                                'purchase_rate' => $inventory['purchase_rate'],
                                'purchase_date' => $inventory['purchase_date'],
                                'value' => $inventory[$cfValueKey],
                                'is_show_in_print' => $isForPrint ? $isForPrint->is_show_in_print : false,
                                'ordering' => $cf->ordering ?? $i,
                            ];
                        }
                    }
                }

                $itemRowQty = 0;
                if (isset($selectedInventory[$inventory['id']]) && ! (in_array($modelType, [DeliveryChallanTransactionItem::class, IncomeEstimateQuoteItemInvoice::class]))) {
                    $itemRowQty = $selectedInventory[$inventory['id']]->quantity;
                }

                $availableQuantity = $inventory['available_quantity'] + $itemRowQty;

                $response[] = [
                    'combination_id' => $inventory['id'],
                    'sale_quantity' => isset($selectedInventory[$inventory['id']]) ? $selectedInventory[$inventory['id']]->quantity : 0,
                    'available_quantity' => $availableQuantity,
                    'purchase_rate' => $inventory['purchase_rate'],
                    'purchase_date' => $inventory['purchase_date'],
                    // 'is_selected' => isset($selectedInventory[$inventory['id']]) && $availableQuantity == ($selectedInventory[$inventory['id']]->quantity ?? 0) ? true : false,
                    'is_selected' => isset($selectedInventory[$inventory['id']]),
                    'fields' => collect($fields)->sortBy('ordering')->values()->toArray(),
                ];
            }

            // $response = collect($response)->filter(function ($item) {
            //     return ! ($item['sale_quantity'] == 0 && $item['available_quantity'] == 0);
            // })->values()->all();

            return $response;
        }
    }

    public function handleTransactionType($modelType)
    {
        $modelTypeToKeyMap = [
            SaleTransactionItem::class => ItemCustomField::SALE,
            SaleReturnItemTransaction::class => ItemCustomField::SALE_RETURN,
            IncomeDebitNoteItemTransaction::class => ItemCustomField::INCOME_DEBIT_NOTE,
            IncomeCreditNoteItemTransaction::class => ItemCustomField::INCOME_CREDIT_NOTE,
            IncomeEstimateQuoteItemInvoice::class => ItemCustomField::INCOME_ESTIMATE_QUOTE,
            DeliveryChallanTransactionItem::class => ItemCustomField::DELIVERY_CHALLAN,
            PurchaseOrderItemInvoice::class => ItemCustomField::PURCHASE_ORDER,
            PurchaseItemTransaction::class => ItemCustomField::PURCHASE,
            PurchaseReturnItemTransaction::class => ItemCustomField::PURCHASE_RETURN,
            ExpenseDebitNoteItemTransaction::class => ItemCustomField::EXPENSE_DEBIT_NOTE,
            ExpenseCreditNoteItemTransaction::class => ItemCustomField::EXPENSE_CREDIT_NOTE,
        ];

        return $modelTypeToKeyMap[$modelType];
    }

    public function parentModelkey($modelType)
    {
        $modelTypeToKeyMap = [
            SaleTransactionItem::class => 'sale_transactions_id',
            SaleReturnItemTransaction::class => 'srt_id',
            IncomeDebitNoteItemTransaction::class => 'income_dn_id',
            IncomeCreditNoteItemTransaction::class => 'income_cn_id',
            IncomeEstimateQuoteItemInvoice::class => 'transactions_id',
            DeliveryChallanTransactionItem::class => 'transaction_id',
            PurchaseOrderItemInvoice::class => 'transactions_id',
            PurchaseItemTransaction::class => 'purchase_transaction_id',
            PurchaseReturnItemTransaction::class => 'purchase_return_id',
            ExpenseDebitNoteItemTransaction::class => 'debit_note_id',
            ExpenseCreditNoteItemTransaction::class => 'expense_cn_id',
        ];

        return $modelTypeToKeyMap[$modelType];
    }
}
