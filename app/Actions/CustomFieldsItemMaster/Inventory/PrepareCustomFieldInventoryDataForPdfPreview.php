<?php

namespace App\Actions\CustomFieldsItemMaster\Inventory;

use App\Models\DeliveryChallanTransactionItem;
use App\Models\ExpenseCreditNoteItemTransaction;
use App\Models\ExpenseDebitNoteItemTransaction;
use App\Models\IncomeCreditNoteItemTransaction;
use App\Models\IncomeDebitNoteItemTransaction;
use App\Models\IncomeEstimateQuoteItemInvoice;
use App\Models\PurchaseItemTransaction;
use App\Models\PurchaseOrderItemInvoice;
use App\Models\PurchaseReturnItemTransaction;
use App\Models\SaleReturnItemTransaction;
use App\Models\SaleTransactionItem;
use Lorisleiva\Actions\Concerns\AsAction;

class PrepareCustomFieldInventoryDataForPdfPreview
{
    use AsAction;

    public function handle($modelId, $modeltype, $itemId)
    {
        $data = GetItemCFInventoryAction::run($modelId, $modeltype, $itemId);

        $pdfPreviewResponse = [];

        $quantityKey = $this->handleQuantityKey($modeltype);

        foreach ($data as $value) {
            $qty = $value[$quantityKey];
            if ($qty > 0) {
                $displayFields = collect($value['fields'])->filter(fn ($field) => $field['is_show_in_print']);

                if ($displayFields->isEmpty()) {
                    continue;
                }

                $fieldString = $displayFields->pluck('value')->implode(', ');
                $pdfPreviewResponse[] = $qty == 1 ? $fieldString : $fieldString.' * '.$qty;
            }
        }

        return $pdfPreviewResponse;
    }

    public function handleQuantityKey($modelType)
    {
        $modelTypeToQuantityKeyMap = [
            PurchaseItemTransaction::class => 'quantity',
            ExpenseCreditNoteItemTransaction::class => 'quantity',
            SaleReturnItemTransaction::class => 'quantity',
            IncomeCreditNoteItemTransaction::class => 'quantity',
            PurchaseOrderItemInvoice::class => 'quantity',
            SaleTransactionItem::class => 'sale_quantity',
            IncomeDebitNoteItemTransaction::class => 'sale_quantity',
            PurchaseReturnItemTransaction::class => 'sale_quantity',
            ExpenseDebitNoteItemTransaction::class => 'sale_quantity',
            IncomeEstimateQuoteItemInvoice::class => 'sale_quantity',
            DeliveryChallanTransactionItem::class => 'sale_quantity',
        ];

        return $modelTypeToQuantityKeyMap[$modelType] ?? 'quantity';
    }
}
