<?php

namespace App\Actions\CustomFieldsItemMaster;

use App\Models\ItemCustomField;
use App\Models\ItemCustomFieldDefaultValue;
use App\Models\ItemCustomFieldFormula;
use App\Models\ItemCustomFieldTransactionSetting;
use App\Models\ItemCustomFieldValue;
use Lorisleiva\Actions\Concerns\AsAction;

class EditCustomFieldItemMaster
{
    use AsAction;

    public function handle(int $id)
    {
        $customFieldItemMaster = ItemCustomField::findOrFail($id);

        if (count($customFieldItemMaster->options) > 0) {
            $usedOptionIds = ItemCustomFieldValue::whereHas('customField', function ($query) {
                $query->where('custom_field_type', ItemCustomField::CF_TYPE_DROPDOWN);
            })
                ->whereIn('value_select_option_id', $customFieldItemMaster->options->pluck('id'))
                ->pluck('value_select_option_id')
                ->toArray();

            $customFieldItemMaster->options->transform(function ($option) use ($usedOptionIds) {
                $option->can_delete = ! in_array($option->id, $usedOptionIds);

                return $option;
            });
        }

        $checkUsed = ItemCustomFieldValue::where('custom_field_id', $id)->first() ? true : false;
        $defaultValue = ItemCustomFieldDefaultValue::where('custom_field_id', $id)->whereNull('item_id')->first();
        $value = $defaultValue ? GetInputTypeWiseValue::run($defaultValue) : null;

        $defaultFormula = ItemCustomFieldFormula::where('custom_field_id', $id)->whereNull('item_id')->first();

        $types = ItemCustomFieldTransactionSetting::where('custom_field_id', $id)->select('id', 'transaction_type', 'is_show_in_print')->get();

        $customFieldItemMaster['is_field_used'] = $customFieldItemMaster->open_in_popup ? true : $checkUsed; // if CF type is popup is true then always return true otherwise return checkUsed
        $customFieldItemMaster['default_value'] = $value;
        $customFieldItemMaster['default_formula'] = $defaultFormula ? $defaultFormula : null;
        $customFieldItemMaster['types'] = $types;
        $customFieldItemMaster['input_type'] = ItemCustomField::CUSTOM_FIELD_ARRAY_WITH_INPUT_TYPE[$customFieldItemMaster->custom_field_type]['input_type'] ?? null;
        $customFieldItemMaster['field_type'] = $defaultValue ? $defaultValue['field_type'] : null;

        return $customFieldItemMaster;
    }
}
