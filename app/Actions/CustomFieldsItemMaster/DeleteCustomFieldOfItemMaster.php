<?php

namespace App\Actions\CustomFieldsItemMaster;

use App\Models\ItemCustomField;
use App\Models\ItemCustomFieldCombination;
use App\Models\ItemCustomFieldDefaultValue;
use App\Models\ItemCustomFieldFormula;
use App\Models\ItemCustomFieldValue;
use Lorisleiva\Actions\Concerns\AsAction;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

class DeleteCustomFieldOfItemMaster
{
    use AsAction;

    public function handle($id)
    {
        $customFieldItemMaster = ItemCustomField::findOrFail($id);

        $customFieldUsedInFormula = ItemCustomFieldFormula::with('customField')->whereRaw("JSON_CONTAINS(used_cf_ids_for_formula, '[{$id}]')")->first();
        if ($customFieldUsedInFormula) {
            if ($customFieldUsedInFormula->custom_field_id != null && $customFieldUsedInFormula->is_system_field == false) {
                throw new UnprocessableEntityHttpException('This custom field cannot be deleted because it is used in the "' . $customFieldUsedInFormula->customField->label_name . '" custom field formula.');
            } else {
                throw new UnprocessableEntityHttpException('This custom field cannot be deleted because it is used in the Quantity formula.');
            }
        }

        $customFieldsTransactionValue = ItemCustomFieldValue::where('custom_field_id', $id)->exists();
        $customFieldUsedInInventory = ItemCustomFieldCombination::where(function ($query) use ($id) {
            $query->where('cf_id_1', $id)
                ->orWhere('cf_id_2', $id)
                ->orWhere('cf_id_3', $id)
                ->orWhere('cf_id_4', $id)
                ->orWhere('cf_id_5', $id);
        })->exists();

        if ($customFieldsTransactionValue || $customFieldUsedInInventory) {
            throw new UnprocessableEntityHttpException('Custom field cannot be deleted since it is used in transactions.');
        }

        $customFieldItemMaster->delete();
    }
}
