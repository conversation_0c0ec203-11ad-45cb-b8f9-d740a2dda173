<?php

namespace App\Actions\CommonAction;

use App\Actions\CustomFieldsItemMaster\GetCustomFieldItemsValue;
use App\Actions\CustomFieldsItemMaster\Inventory\GetItemCFInventoryListForSale;
use App\Actions\ItemMaster\GetItemClosingStock;
use App\Models\ItemCustomField;
use App\Models\ItemCustomFieldSetting;
use App\Models\Master\ItemMaster;
use App\Models\UnitOfMeasurement;
use Lorisleiva\Actions\Concerns\AsAction;

class GetItemDetailsAction
{
    use AsAction;

    public function handle($itemId, $transactionType = null)
    {
        $company = getCurrentCompany();
        $data = [];

        // TO_COMMENT =>
        $data['itemMaster'] = $itemId;
        $data['item_master'] = $itemId;
        if (! $itemId instanceof ItemMaster) {
            $item = ItemMaster::with(['model.gstTax', 'model.gstGstCessRate', 'model.unitOfMeasurement'])
                ->whereCompanyId($company->id)
                ->whereId($itemId)
                ->firstOrFail();
            // TO_COMMENT =>
            $data['itemMaster'] = $item;
            $data['item_master'] = $item;
        }

        // TO_COMMENT =>
        $data['unitOfArray'] = UnitOfMeasurement::whereIn('id', [$data['itemMaster']->model->unit_of_measurement, $data['itemMaster']->model->secondary_unit_of_measurement])->pluck('name', 'id')->toArray();
        $data['unit_of_array'] = UnitOfMeasurement::whereIn('id', [$data['item_master']->model->unit_of_measurement, $data['item_master']->model->secondary_unit_of_measurement])->get()->toArray();

        // TO_COMMENT =>
        $data['step'] = GetStepForDecimalPlaceAction::run($data['itemMaster']->model->decimal_places);
        $data['step'] = GetStepForDecimalPlaceAction::run($data['item_master']->model->decimal_places);

        // TO_COMMENT =>
        $itemData = GetItemClosingStock::run($data['itemMaster']);
        $itemData = GetItemClosingStock::run($data['item_master']);

        // TO_COMMENT =>
        $data['currentStock'] = $itemData['closing_balance_qty'].' '.$data['itemMaster']->model->unitOfMeasurement->code;
        $data['current_stock'] = $itemData['closing_balance_qty'].' '.$data['item_master']->model->unitOfMeasurement->code;
        $data['is_current_stock_nagitive'] = $itemData['closing_balance_qty'] < 0; // For Warn On low Stock

        if ($transactionType) {
            $data['custom_fields_item_values'] = GetCustomFieldItemsValue::run($data['itemMaster']->id, $transactionType);
        }

        $data['model_inventory_custom_fields'] = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($data) {
            $query->where('item_id', $data['itemMaster']->id);
        })->where('open_in_popup', true)->orderBy('ordering', 'ASC')->get();

        if ($transactionType) {
            $data['model_select_inventory_custom_fields'] = GetItemCFInventoryListForSale::run($data['itemMaster']->id, $transactionType);
        }

        return $data;
    }
}
