<?php

namespace App\Actions\v1\Transactions\SaleReturn;

use App\Actions\CommonAction\GetTcsTdsDetailsAction;
use App\Actions\CustomFields\GetTransactionCustomFieldsAction;
use App\Actions\CustomFieldsItemMaster\Transaction\GetAllCustomFieldsItemTransactionWise;
use App\Actions\CustomFieldsItemMaster\Inventory\GetItemCFInventoryAction;
use App\Actions\CustomFieldsItemMaster\Transaction\GetCFTransactionWiseAction;
use App\Models\AdditionalChargesForSalesReturnTransaction;
use App\Models\AddLessForSalesReturnTransaction;
use App\Models\ItemCustomField;
use App\Models\PaymentDetailsForSalesReturnTransaction;
use App\Models\SaleReturnItemTransaction;
use App\Models\SaleReturnTransaction;
use App\Models\TransactionCustomField;
use App\Models\UnitOfMeasurement;
use Lorisleiva\Actions\Concerns\AsAction;

class GetSaleReturnTransactionAction
{
    use AsAction;

    public function handle($id)
    {
        $saleReturnTransaction = SaleReturnTransaction::with([
            'customer',
            'dispatchAddress',
            'billingAddress',
            'shippingAddress',
            'saleReturnItems',
            'saleReturnLedgers',
            'additionalCharges.ledger',
            'addLess.ledger',
            'paymentDetails.ledger',
        ])->financialYearDate()->findOrFail($id);

        $saleReturnTransaction->customer_ledger_name = $saleReturnTransaction->customer->name ?? null;
        $saleReturnTransaction->media = $saleReturnTransaction->getSaleReturnFileAttribute();

        if ($saleReturnTransaction->tcs_tax_id) {
            $saleReturnTransaction->tcs_calculated = GetTcsTdsDetailsAction::run($saleReturnTransaction->tcs_tax_id, $saleReturnTransaction->customer_ledger_id);
        }
        if ($saleReturnTransaction->tds_tax_id) {
            $saleReturnTransaction->tds_calculated = GetTcsTdsDetailsAction::run($saleReturnTransaction->tds_tax_id, $saleReturnTransaction->customer_ledger_id);
            $saleReturnTransaction->tds_rounding_method = $saleReturnTransaction->tds_calculated['rounding_method'] ?? null;
        }

        if (count($saleReturnTransaction->customFieldValues) > 0) {
            $saleReturnTransaction->custom_values = GetTransactionCustomFieldsAction::run(TransactionCustomField::SALE_RETURN, $saleReturnTransaction->id, SaleReturnTransaction::class);
        }

        foreach ($saleReturnTransaction->saleReturnItems as $item) {

            /** @var SaleReturnItemTransaction $item */
            $item->item_name = $item->items->item_name ?? null;
            $item->ledger_name = $item->ledger->name ?? null;

            /* This is for custom fields if transaction item has custom fields values */
            if (count($item->customFieldTransactionItemsValues) > 0) {
                $item->custom_items_values = GetCFTransactionWiseAction::run(ItemCustomField::SALE_RETURN, $item->id, SaleReturnItemTransaction::class, $item->item_id);
            }

            /* This is for all custom fields */
            $item->custom_fields = GetAllCustomFieldsItemTransactionWise::run(ItemCustomField::SALE_RETURN, $item->id, SaleReturnItemTransaction::class, $item->item_id);

            if (count($item->customFieldTransactionItemsInventoryValues) > 0) {
                $item->custom_items_inventory_values = GetItemCFInventoryAction::run($item->id, SaleReturnItemTransaction::class, $item->item_id);
            }

            $item->model_inventory_custom_fields = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
                $query->where('item_id', $item->item_id);
            })->where('open_in_popup', true)->orderBy('ordering', 'ASC')->get();

            if (is_null($item->items) || is_null($item->items->model)) {
                $item->unitOfArray = [];

                continue;
            }

            $unitIds = array_filter([
                $item->unit_id,
                $item->items->model->unit_of_measurement,
                $item->items->model->secondary_unit_of_measurement ?? null,
            ]);

            // TO_COMMENT =>
            $item->unitOfArray = UnitOfMeasurement::whereIn('id', $unitIds)->pluck('name', 'id')->toArray();
            $item->units_of_array = UnitOfMeasurement::whereIn('id', $unitIds)->get()->toArray();

            $item->decimal_places = ! empty($item->decimal_places_for_quantity) ? $item->decimal_places_for_quantity : ($item->items->model->decimal_places ?? 2);
            $item->item_decimal_places = $item->items->model->decimal_places ?? 2;
            $item->decimal_places_for_rate = ! empty($item->decimal_places_for_rate) ? $item->decimal_places_for_rate : ($item->items->model->decimal_places_for_rate ?? 2);
            $item->item_decimal_places_for_rate = $item->items->model->decimal_places_for_rate ?? 2;
            $item->secondary_unit_of_measurement = $item->items->model->secondary_unit_of_measurement ?? null;
            $item->conversion_rate = $item->items->model->conversion_rate ?? null;
            $item->is_re_order = $item->items->model->is_re_order ?? false;
            unset($item->items);
            unset($item->ledger);
        }

        foreach ($saleReturnTransaction->saleReturnLedgers as $ledger) {
            $ledger->ledger_name = $ledger->ledgers->name ?? null;
            unset($ledger->ledgers);
        }

        foreach ($saleReturnTransaction->additionalCharges as $charge) {
            /** @var AdditionalChargesForSalesReturnTransaction $charge */
            $charge->ledger_name = $charge->ledger->name ?? null;
            unset($charge->ledger);
        }

        foreach ($saleReturnTransaction->addLess as $less) {
            /** @var AddLessForSalesReturnTransaction $less */
            $less->ledger_name = $less->ledger->name ?? null;
            unset($less->ledger);
        }

        foreach ($saleReturnTransaction->paymentDetails as $payment) {
            /** @var PaymentDetailsForSalesReturnTransaction $payment */
            $payment->ledger_name = $payment->ledger->name ?? null;
            unset($payment->ledger);
        }

        return $saleReturnTransaction;
    }
}
