<?php

namespace App\Actions\v1\Transactions\Purchase;

use App\Actions\CommonAction\GetTcsTdsDetailsAction;
use App\Actions\CustomFields\GetTransactionCustomFieldsAction;
use App\Actions\CustomFieldsItemMaster\Transaction\GetAllCustomFieldsItemTransactionWise;
use App\Actions\CustomFieldsItemMaster\Inventory\GetItemCFInventoryAction;
use App\Actions\CustomFieldsItemMaster\Transaction\GetCFTransactionWiseAction;
use App\Models\AdditionalChargesForPurchaseTransaction;
use App\Models\AddLessForPurchaseTransaction;
use App\Models\TransactionCustomField;
use App\Models\ItemCustomField;
use App\Models\PaymentDetailsForPurchaseTransaction;
use App\Models\PurchaseItemTransaction;
use App\Models\PurchaseLedgerTransaction;
use App\Models\PurchaseTransaction;
use App\Models\UnitOfMeasurement;
use Lorisleiva\Actions\Concerns\AsAction;

class GetPurchaseTransactionAction
{
    use AsAction;

    public function handle($id, $isEdit = true)
    {
        $query = PurchaseTransaction::with([
            'supplier',
            'billingAddress',
            'dispatchAddress',
            'purchaseTransactionItems',
            'purchaseTransactionLedger',
            'additionalCharges.ledger',
            'addLess.ledger',
            'paymentDetails.ledger',
        ]);

        $purchaseTransaction = $isEdit ? $query->financialYearDate()->findOrFail($id) : $query->findOrFail($id);
        $purchaseTransaction->supplier_ledger_name = $purchaseTransaction->supplier->name ?? null;
        $purchaseTransaction->media = $purchaseTransaction->getPurchaseFileAttribute();

        if ($purchaseTransaction->tcs_tax_id) {
            $purchaseTransaction->tcs_calculated = GetTcsTdsDetailsAction::run($purchaseTransaction->tcs_tax_id, $purchaseTransaction->supplier_ledger_id);
        }
        if ($purchaseTransaction->ledger_of_tds) {
            $purchaseTransaction->tds_calculated = GetTcsTdsDetailsAction::run($purchaseTransaction->ledger_of_tds, $purchaseTransaction->supplier_ledger_id);
        }

        if (count($purchaseTransaction->customFieldValues) > 0) {
            $purchaseTransaction->custom_values = GetTransactionCustomFieldsAction::run(TransactionCustomField::PURCHASE, $purchaseTransaction->id, PurchaseTransaction::class);
        }

        $selectedPurchaseOrderIds = explode(',', $purchaseTransaction->purchase_order_no) ?? [];
        $purchaseTransaction->purchase_order_list = PurchaseOrderNumberListAction::run($purchaseTransaction->supplier_ledger_id, $selectedPurchaseOrderIds);

        foreach ($purchaseTransaction->purchaseTransactionItems as $item) {

            /** @var PurchaseItemTransaction $item */
            $item->item_name = $item->items->item_name ?? null;
            $item->ledger_name = $item->ledger->name ?? null;

            /* This is for custom fields if transaction item has custom fields values */
            if (count($item->customFieldTransactionItemsValues) > 0) {
                $item->custom_items_values = GetCFTransactionWiseAction::run(ItemCustomField::PURCHASE, $item->id, PurchaseItemTransaction::class, $item->item_id);
            }

            /* This is for all custom fields */
            $item->custom_fields = GetAllCustomFieldsItemTransactionWise::run(ItemCustomField::PURCHASE, $item->id, PurchaseItemTransaction::class, $item->item_id);

            if (count($item->customFieldTransactionItemsInventoryValues) > 0) {
                $item->custom_items_inventory_values = GetItemCFInventoryAction::run($item->id, PurchaseItemTransaction::class, $item->item_id);
            }

            $item->model_inventory_custom_fields = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
                $query->where('item_id', $item->item_id);
            })->where('open_in_popup', true)->orderBy('ordering', 'ASC')->get();

            if (is_null($item->items) || is_null($item->items->model)) {
                $item->unitOfArray = [];

                continue;
            }

            $unitIds = array_filter([
                $item->unit_id,
                $item->items->model->unit_of_measurement,
                $item->items->model->secondary_unit_of_measurement ?? null,
            ]);

            // TO_COMMENT =>
            $item->unitOfArray = UnitOfMeasurement::whereIn('id', $unitIds)->pluck('name', 'id')->toArray();
            $item->units_of_array = UnitOfMeasurement::whereIn('id', $unitIds)->get()->toArray();

            $item->decimal_places = ! empty($item->decimal_places_for_quantity) ? $item->decimal_places_for_quantity : ($item->items->model->decimal_places ?? 2);
            $item->item_decimal_places = $item->items->model->decimal_places ?? 2;
            $item->decimal_places_for_rate = ! empty($item->decimal_places_for_rate) ? $item->decimal_places_for_rate : ($item->items->model->decimal_places_for_rate ?? 2);
            $item->item_decimal_places_for_rate = $item->items->model->decimal_places_for_rate ?? 2;
            $item->secondary_unit_of_measurement = $item->items->model->secondary_unit_of_measurement ?? null;
            $item->conversion_rate = $item->items->model->conversion_rate ?? null;
            $item->is_re_order = $item->items->model->is_re_order ?? false;
            unset($item->items);
            unset($item->ledger);
        }

        foreach ($purchaseTransaction->purchaseTransactionLedger as $ledger) {
            /** @var PurchaseLedgerTransaction $ledger */
            $ledger->ledger_name = $ledger->ledger->name ?? null;
            unset($ledger->ledger);
        }

        foreach ($purchaseTransaction->additionalCharges as $charge) {
            /** @var AdditionalChargesForPurchaseTransaction $charge */
            $charge->ledger_name = $charge->ledger->name ?? null;
            unset($charge->ledger);
        }

        foreach ($purchaseTransaction->addLess as $less) {
            /** @var AddLessForPurchaseTransaction $less */
            $less->ledger_name = $less->ledger->name ?? null;
            unset($less->ledger);
        }

        foreach ($purchaseTransaction->paymentDetails as $payment) {
            /** @var PaymentDetailsForPurchaseTransaction $payment */
            $payment->ledger_name = $payment->ledger->name ?? null;
            unset($payment->ledger);
        }

        return $purchaseTransaction;
    }
}
