<?php

namespace App\Actions\v1\Transactions\DeliveryChallan;

use App\Actions\CustomFields\GetTransactionCustomFieldsAction;
use App\Actions\CustomFieldsItemMaster\Inventory\GetItemCFInventoryAction;
use App\Actions\CustomFieldsItemMaster\Transaction\GetAllCustomFieldsItemTransactionWise;
use App\Actions\CustomFieldsItemMaster\Transaction\GetCFTransactionWiseAction;
use App\Models\TransactionCustomField;
use App\Models\ItemCustomField;
use App\Models\DeliveryChallanTransaction;
use App\Models\DeliveryChallanTransactionItem;
use App\Models\UnitOfMeasurement;
use Lorisleiva\Actions\Concerns\AsAction;

class GetDeliveryChallanTransactionAction
{
    use AsAction;

    public function handle($id, $isEdit = true)
    {
        $query = DeliveryChallanTransaction::with([
            'transactionItems.items.model.gstTax',
            'party',
            'dispatchAddress',
            'billingAddress',
            'shippingAddress',
            'additionalCharges.ledger',
            'addLess.ledger',
            'brokerDetails',
            'transportDetails',
        ]);

        $deliveryChallanTransaction = $isEdit ? $query->financialYearDate()->findOrFail($id) : $query->findOrFail($id);
        $deliveryChallanTransaction->customer_ledger_name = $deliveryChallanTransaction->party->name ?? null;
        $deliveryChallanTransaction->media = $deliveryChallanTransaction->getDeliveryChallanFileAttribute();

        if (count($deliveryChallanTransaction->customFieldValues) > 0) {
            $deliveryChallanTransaction->custom_values = GetTransactionCustomFieldsAction::run(TransactionCustomField::DELIVERY_CHALLAN, $deliveryChallanTransaction->id, DeliveryChallanTransaction::class);
        }

        foreach ($deliveryChallanTransaction->transactionItems as $item) {
            /** @var DeliveryChallanTransactionItem $item */
            $item->item_name = $item->items->item_name ?? null;

            /* This is for custom fields if transaction item has custom fields values */
            if (count($item->customFieldTransactionItemsValues) > 0) {
                $item->custom_items_values = GetCFTransactionWiseAction::run(ItemCustomField::DELIVERY_CHALLAN, $item->id, DeliveryChallanTransactionItem::class, $item->item_id);
            }

            /* This is for all custom fields */
            $item->custom_fields = GetAllCustomFieldsItemTransactionWise::run(ItemCustomField::DELIVERY_CHALLAN, $item->id, DeliveryChallanTransactionItem::class, $item->item_id);

            if (count($item->customFieldTransactionItemsInventoryValues) > 0) {
                $item->custom_items_inventory_values = GetItemCFInventoryAction::run($item->id, DeliveryChallanTransactionItem::class, $item->item_id, $id);
            }

            $item->model_inventory_custom_fields = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
                $query->where('item_id', $item->item_id);
            })->where('open_in_popup', true)->orderBy('ordering', 'ASC')->get();

            if (is_null($item->items) || is_null($item->items->model)) {
                $item->unitOfArray = [];

                continue;
            }

            $unitIds = array_filter([
                $item->unit_id,
                $item->items->model->unit_of_measurement,
                $item->items->model->secondary_unit_of_measurement ?? null,
            ]);

            // TO_COMMENT =>
            $item->unitOfArray = UnitOfMeasurement::whereIn('id', $unitIds)->pluck('name', 'id')->toArray();
            $item->units_of_array = UnitOfMeasurement::whereIn('id', $unitIds)->get()->toArray();

            $item->decimal_places = ! empty($item->decimal_places_for_quantity) ? $item->decimal_places_for_quantity : ($item->items->model->decimal_places ?? 2);
            $item->item_decimal_places = $item->items->model->decimal_places ?? 2;
            $item->decimal_places_for_rate = ! empty($item->decimal_places_for_rate) ? $item->decimal_places_for_rate : ($item->items->model->decimal_places_for_rate ?? 2);
            $item->item_decimal_places_for_rate = $item->items->model->decimal_places_for_rate ?? 2;
            $item->secondary_unit_of_measurement = $item->items->model->secondary_unit_of_measurement ?? null;
            $item->conversion_rate = $item->items->model->conversion_rate ?? null;
            $item->is_re_order = $item->items->model->is_re_order ?? false;
            // unset($item->items);
        }

        return $deliveryChallanTransaction;
    }
}
