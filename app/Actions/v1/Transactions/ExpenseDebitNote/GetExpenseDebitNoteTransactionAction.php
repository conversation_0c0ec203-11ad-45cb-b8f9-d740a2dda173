<?php

namespace App\Actions\v1\Transactions\ExpenseDebitNote;

use App\Actions\CommonAction\GetTcsTdsDetailsAction;
use App\Actions\CustomFields\GetTransactionCustomFieldsAction;
use App\Actions\CustomFieldsItemMaster\Transaction\GetAllCustomFieldsItemTransactionWise;
use App\Actions\CustomFieldsItemMaster\Inventory\GetItemCFInventoryAction;
use App\Actions\CustomFieldsItemMaster\Transaction\GetCFTransactionWiseAction;
use App\Models\AdditionalChargesForExpenseDebitNoteTransaction;
use App\Models\AddLessForExpenseDebitNoteTransaction;
use App\Models\TransactionCustomField;
use App\Models\ItemCustomField;
use App\Models\ExpenseDebitNoteItemTransaction;
use App\Models\ExpenseDebitNoteTransaction;
use App\Models\PaymentDetailsForExpenseDebitNoteTransaction;
use App\Models\UnitOfMeasurement;
use Lorisleiva\Actions\Concerns\AsAction;

class GetExpenseDebitNoteTransactionAction
{
    use AsAction;

    public function handle($id)
    {
        $expenseDebitNoteTransaction = ExpenseDebitNoteTransaction::with([
            'supplier',
            'dispatchAddress',
            'billingAddress',
            'debitNoteLedgers',
            'debitNoteItems',
            'additionalCharges.ledger',
            'addLess.ledger',
            'paymentDetails.ledger',
        ])->financialYearDate()->findOrFail($id);

        $expenseDebitNoteTransaction->supplier_ledger_name = $expenseDebitNoteTransaction->supplier->name ?? null;
        $expenseDebitNoteTransaction->media = $expenseDebitNoteTransaction->getExpenseDebitNoteFileAttribute();

        if ($expenseDebitNoteTransaction->tcs_tax_id) {
            $expenseDebitNoteTransaction->tcs_calculated = GetTcsTdsDetailsAction::run($expenseDebitNoteTransaction->tcs_tax_id, $expenseDebitNoteTransaction->supplier_id);
        }
        if ($expenseDebitNoteTransaction->ledger_of_tds) {
            $expenseDebitNoteTransaction->tds_calculated = GetTcsTdsDetailsAction::run($expenseDebitNoteTransaction->ledger_of_tds, $expenseDebitNoteTransaction->supplier_id);
        }

        if (count($expenseDebitNoteTransaction->customFieldValues) > 0) {
            $expenseDebitNoteTransaction->custom_values = GetTransactionCustomFieldsAction::run(TransactionCustomField::EXPENSE_DEBIT_NOTE, $expenseDebitNoteTransaction->id, ExpenseDebitNoteTransaction::class);
        }

        foreach ($expenseDebitNoteTransaction->debitNoteItems as $item) {

            /** @var ExpenseDebitNoteItemTransaction $item */
            $item->item_name = $item->items->item_name ?? null;
            $item->ledger_name = $item->ledger->name ?? null;

            /* This is for custom fields if transaction item has custom fields values */
            if (count($item->customFieldTransactionItemsValues) > 0) {
                $item->custom_items_values = GetCFTransactionWiseAction::run(ItemCustomField::EXPENSE_DEBIT_NOTE, $item->id, ExpenseDebitNoteItemTransaction::class, $item->item_id);
            }

            /* This is for all custom fields */
            $item->custom_fields = GetAllCustomFieldsItemTransactionWise::run(ItemCustomField::EXPENSE_DEBIT_NOTE, $item->id, ExpenseDebitNoteItemTransaction::class, $item->item_id);

            if (count($item->customFieldTransactionItemsInventoryValues) > 0) {
                $item->custom_items_inventory_values = GetItemCFInventoryAction::run($item->id, ExpenseDebitNoteItemTransaction::class, $item->item_id, $id);
            }

            $item->model_inventory_custom_fields = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
                $query->where('item_id', $item->item_id);
            })->where('open_in_popup', true)->orderBy('ordering', 'ASC')->get();

            if (is_null($item->items) || is_null($item->items->model)) {
                $item->unitOfArray = [];

                continue;
            }

            $unitIds = array_filter([
                $item->unit_id,
                $item->items->model->unit_of_measurement,
                $item->items->model->secondary_unit_of_measurement ?? null,
            ]);

            // TO_COMMENT => $item->unitOfArray = UnitOfMeasurement::whereIn('id', $unitIds)->pluck('name', 'id')->toArray();
            $item->units_of_array = UnitOfMeasurement::whereIn('id', $unitIds)->get()->toArray();

            $item->decimal_places = ! empty($item->decimal_places_for_quantity) ? $item->decimal_places_for_quantity : ($item->items->model->decimal_places ?? 2);
            $item->item_decimal_places = $item->items->model->decimal_places ?? 2;
            $item->decimal_places_for_rate = ! empty($item->decimal_places_for_rate) ? $item->decimal_places_for_rate : ($item->items->model->decimal_places_for_rate ?? 2);
            $item->item_decimal_places_for_rate = $item->items->model->decimal_places_for_rate ?? 2;
            $item->secondary_unit_of_measurement = $item->items->model->secondary_unit_of_measurement ?? null;
            $item->conversion_rate = $item->items->model->conversion_rate ?? null;
            $item->is_re_order = $item->items->model->is_re_order ?? false;
            unset($item->items);
            unset($item->ledger);
        }

        foreach ($expenseDebitNoteTransaction->debitNoteLedgers as $ledger) {
            $ledger->ledger_name = $ledger->ledger->name ?? null;
            unset($ledger->ledger);
        }

        foreach ($expenseDebitNoteTransaction->additionalCharges as $charge) {
            /** @var AdditionalChargesForExpenseDebitNoteTransaction $charge */
            $charge->ledger_name = $charge->ledger->name ?? null;
            unset($charge->ledger);
        }

        foreach ($expenseDebitNoteTransaction->addLess as $less) {
            /** @var AddLessForExpenseDebitNoteTransaction $less */
            $less->ledger_name = $less->ledger->name ?? null;
            unset($less->ledger);
        }

        foreach ($expenseDebitNoteTransaction->paymentDetails as $payment) {
            /** @var PaymentDetailsForExpenseDebitNoteTransaction $payment */
            $payment->ledger_name = $payment->ledger->name ?? null;
            unset($payment->ledger);
        }

        return $expenseDebitNoteTransaction;
    }
}
