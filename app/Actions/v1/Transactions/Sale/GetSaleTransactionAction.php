<?php

namespace App\Actions\v1\Transactions\Sale;

use App\Actions\CommonAction\GetTcsTdsDetailsAction;
use App\Actions\CustomFields\GetTransactionCustomFieldsAction;
use App\Actions\CustomFieldsItemMaster\Transaction\GetAllCustomFieldsItemTransactionWise;
use App\Actions\CustomFieldsItemMaster\Inventory\GetItemCFInventoryAction;
use App\Actions\CustomFieldsItemMaster\Transaction\GetCFTransactionWiseAction;
use App\Actions\SaleTransaction\GetDeliveryChallanNumberListAction;
use App\Actions\SaleTransaction\GetEstimateQuotesNumberListAction;
use App\Models\AdditionalChargesForSalesTransaction;
use App\Models\AddLessForSalesTransaction;
use App\Models\TransactionCustomField;
use App\Models\CustomFieldItemSetting;
use App\Models\ItemCustomField;
use App\Models\PaymentDetailsForSalesTransaction;
use App\Models\SaleTransaction;
use App\Models\SaleTransactionItem;
use App\Models\UnitOfMeasurement;
use Lorisleiva\Actions\Concerns\AsAction;

class GetSaleTransactionAction
{
    use AsAction;

    public function handle($id, $isEdit = true)
    {

        $query = SaleTransaction::with([
            'customer',
            'dispatchAddress',
            'billingAddress',
            'shippingAddress',
            'saleItems',
            'saleLedgers',
            'additionalCharges.ledger',
            'addLess.ledger',
            'paymentDetails.ledger',
        ]);

        $saleTransaction = $isEdit ? $query->financialYearDate()->findOrFail($id) : $query->findOrFail($id);
        $saleTransaction->customer_ledger_name = $saleTransaction->customer->name ?? null;
        $saleTransaction->media = $saleTransaction->getSaleFileAttribute();

        if ($saleTransaction->tcs_tax_id) {
            $saleTransaction->tcs_calculated = GetTcsTdsDetailsAction::run($saleTransaction->tcs_tax_id, $saleTransaction->customer_ledger_id);
        }
        if ($saleTransaction->tds_tax_id) {
            $saleTransaction->tds_calculated = GetTcsTdsDetailsAction::run($saleTransaction->tds_tax_id, $saleTransaction->customer_ledger_id);
            $saleTransaction->tds_rounding_method = $saleTransaction->tds_calculated['rounding_method'] ?? null;
        }

        if (count($saleTransaction->customFieldValues) > 0) {
            $saleTransaction->custom_values = GetTransactionCustomFieldsAction::run(TransactionCustomField::SALE, $saleTransaction->id, SaleTransaction::class);
        }

        $selectedChallanIds = explode(',', $saleTransaction->delivery_challan_no) ?? [];
        $saleTransaction->delivery_challan_list = GetDeliveryChallanNumberListAction::run($saleTransaction->customer_ledger_id, $selectedChallanIds);
        $selectedEstimateIds = explode(',', $saleTransaction->estimate_quote_no) ?? [];
        $saleTransaction->estimate_number_list = GetEstimateQuotesNumberListAction::run($saleTransaction->customer_ledger_id, $selectedEstimateIds);

        foreach ($saleTransaction->saleItems as $item) {

            /** @var SaleTransactionItem $item */
            $item->item_name = $item->items->item_name ?? null;
            $item->ledger_name = $item->ledger->name ?? null;

            /* This is for custom fields if transaction item has custom fields values */
            if (count($item->customFieldTransactionItemsValues) > 0) {
                $item->custom_items_values = GetCFTransactionWiseAction::run(ItemCustomField::SALE, $item->id, SaleTransactionItem::class, $item->item_id);
            }

            /* This is for all custom fields */
            $item->custom_fields = GetAllCustomFieldsItemTransactionWise::run(ItemCustomField::SALE, $item->id, SaleTransactionItem::class, $item->item_id);

            if (count($item->customFieldTransactionItemsInventoryValues) > 0) {
                $item->custom_items_inventory_values = GetItemCFInventoryAction::run($item->id, SaleTransactionItem::class, $item->item_id, $id);
            }

            $item->model_inventory_custom_fields = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
                $query->where('item_id', $item->item_id);
            })->where('open_in_popup', true)->orderBy('ordering', 'ASC')->get();

            if (is_null($item->items) || is_null($item->items->model)) {
                $item->unitOfArray = [];

                continue;
            }

            $unitIds = array_filter([
                $item->unit_id,
                $item->items->model->unit_of_measurement,
                $item->items->model->secondary_unit_of_measurement ?? null,
            ]);

            // TO_COMMENT =>
            $item->unitOfArray = UnitOfMeasurement::whereIn('id', $unitIds)->pluck('name', 'id')->toArray();
            $item->units_of_array = UnitOfMeasurement::whereIn('id', $unitIds)->get()->toArray();

            $item->decimal_places = ! empty($item->decimal_places_for_quantity) ? $item->decimal_places_for_quantity : ($item->items->model->decimal_places ?? 2);
            $item->item_decimal_places = $item->items->model->decimal_places ?? 2;
            $item->decimal_places_for_rate = ! empty($item->decimal_places_for_rate) ? $item->decimal_places_for_rate : ($item->items->model->decimal_places_for_rate ?? 2);
            $item->item_decimal_places_for_rate = $item->items->model->decimal_places_for_rate ?? 2;
            $item->secondary_unit_of_measurement = $item->items->model->secondary_unit_of_measurement ?? null;
            $item->conversion_rate = $item->items->model->conversion_rate ?? null;
            $item->is_re_order = $item->items->model->is_re_order ?? false;
            unset($item->items);
            unset($item->ledger);
            unset($item->customFieldTransactionItemsValues);
        }

        foreach ($saleTransaction->saleLedgers as $ledger) {
            $ledger->ledger_name = $ledger->ledgers->name ?? null;
            unset($ledger->ledgers);
        }

        foreach ($saleTransaction->additionalCharges as $charge) {
            /** @var AdditionalChargesForSalesTransaction $charge */
            $charge->ledger_name = $charge->ledger->name ?? null;
            unset($charge->ledger);
        }

        foreach ($saleTransaction->addLess as $less) {
            /** @var AddLessForSalesTransaction $less */
            $less->ledger_name = $less->ledger->name ?? null;
            unset($less->ledger);
        }

        foreach ($saleTransaction->paymentDetails as $payment) {
            /** @var PaymentDetailsForSalesTransaction $payment */
            $payment->ledger_name = $payment->ledger->name ?? null;
            unset($payment->ledger);
        }

        return $saleTransaction;
    }
}
