<?php

namespace App\Actions\v1\Transactions\PurchaseReturn;

use App\Actions\CommonAction\GetTcsTdsDetailsAction;
use App\Actions\CustomFields\GetTransactionCustomFieldsAction;
use App\Actions\CustomFieldsItemMaster\Transaction\GetAllCustomFieldsItemTransactionWise;
use App\Actions\CustomFieldsItemMaster\Inventory\GetItemCFInventoryAction;
use App\Actions\CustomFieldsItemMaster\Transaction\GetCFTransactionWiseAction;
use App\Models\AdditionalChargesForPurchaseReturnTransaction;
use App\Models\AddLessForPurchaseReturnTransaction;
use App\Models\TransactionCustomField;
use App\Models\ItemCustomField;
use App\Models\PaymentDetailsForPurchaseReturnTransaction;
use App\Models\PurchaseReturnItemTransaction;
use App\Models\PurchaseReturnTransaction;
use App\Models\UnitOfMeasurement;
use Lorisleiva\Actions\Concerns\AsAction;

class GetPurchaseReturnTransactionAction
{
    use AsAction;

    public function handle($id)
    {
        $purchaseReturnTransaction = PurchaseReturnTransaction::with([
            'supplier',
            'billingAddress',
            'shippingAddress',
            'dispatchAddress',
            'purchaseReturnItems',
            'purchaseReturnLedgers',
            'additionalCharges.ledger',
            'addLess.ledger',
            'paymentDetails.ledger',
        ])->financialYearDate()->findOrFail($id);

        $purchaseReturnTransaction->supplier_ledger_name = $purchaseReturnTransaction->supplier->name ?? null;
        $purchaseReturnTransaction->media = $purchaseReturnTransaction->getPurchaseReturnFileAttribute();

        if ($purchaseReturnTransaction->tcs_tax_id) {
            $purchaseReturnTransaction->tcs_calculated = GetTcsTdsDetailsAction::run($purchaseReturnTransaction->tcs_tax_id, $purchaseReturnTransaction->supplier_id);
        }
        if ($purchaseReturnTransaction->ledger_of_tds) {
            $purchaseReturnTransaction->tds_calculated = GetTcsTdsDetailsAction::run($purchaseReturnTransaction->ledger_of_tds, $purchaseReturnTransaction->supplier_id);
        }

        if (count($purchaseReturnTransaction->customFieldValues) > 0) {
            $purchaseReturnTransaction->custom_values = GetTransactionCustomFieldsAction::run(TransactionCustomField::PURCHASE_RETURN, $purchaseReturnTransaction->id, PurchaseReturnTransaction::class);
        }

        foreach ($purchaseReturnTransaction->purchaseReturnItems as $item) {

            /** @var PurchaseReturnItemTransaction $item */
            $item->item_name = $item->items->item_name ?? null;
            $item->ledger_name = $item->ledger->name ?? null;

            /* This is for custom fields if transaction item has custom fields values */
            if (count($item->customFieldTransactionItemsValues) > 0) {
                $item->custom_items_values = GetCFTransactionWiseAction::run(ItemCustomField::PURCHASE_RETURN, $item->id, PurchaseReturnItemTransaction::class, $item->item_id);
            }

            /* This is for all custom fields */
            $item->custom_fields = GetAllCustomFieldsItemTransactionWise::run(ItemCustomField::PURCHASE_RETURN, $item->id, PurchaseReturnItemTransaction::class, $item->item_id);

            if (count($item->customFieldTransactionItemsInventoryValues) > 0) {
                $item->custom_items_inventory_values = GetItemCFInventoryAction::run($item->id, PurchaseReturnItemTransaction::class, $item->item_id, $id);
            }

            $item->model_inventory_custom_fields = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
                $query->where('item_id', $item->item_id);
            })->where('open_in_popup', true)->orderBy('ordering', 'ASC')->get();

            if (is_null($item->items) || is_null($item->items->model)) {
                $item->unitOfArray = [];

                continue;
            }

            $unitIds = array_filter([
                $item->unit_id,
                $item->items->model->unit_of_measurement,
                $item->items->model->secondary_unit_of_measurement ?? null,
            ]);

            // TO_COMMENT =>
            $item->unitOfArray = UnitOfMeasurement::whereIn('id', $unitIds)->pluck('name', 'id')->toArray();
            $item->units_of_array = UnitOfMeasurement::whereIn('id', $unitIds)->get()->toArray();

            $item->decimal_places = ! empty($item->decimal_places_for_quantity) ? $item->decimal_places_for_quantity : ($item->items->model->decimal_places ?? 2);
            $item->item_decimal_places = $item->items->model->decimal_places ?? 2;
            $item->decimal_places_for_rate = ! empty($item->decimal_places_for_rate) ? $item->decimal_places_for_rate : ($item->items->model->decimal_places_for_rate ?? 2);
            $item->item_decimal_places_for_rate = $item->items->model->decimal_places_for_rate ?? 2;
            $item->secondary_unit_of_measurement = $item->items->model->secondary_unit_of_measurement ?? null;
            $item->conversion_rate = $item->items->model->conversion_rate ?? null;
            $item->is_re_order = $item->items->model->is_re_order ?? false;
            unset($item->items);
            unset($item->ledger);
        }

        foreach ($purchaseReturnTransaction->purchaseReturnLedgers as $ledger) {
            $ledger->ledger_name = $ledger->ledger->name ?? null;
            unset($ledger->ledger);
        }

        foreach ($purchaseReturnTransaction->additionalCharges as $charge) {
            /** @var AdditionalChargesForPurchaseReturnTransaction $charge */
            $charge->ledger_name = $charge->ledger->name ?? null;
            unset($charge->ledger);
        }

        foreach ($purchaseReturnTransaction->addLess as $less) {
            /** @var AddLessForPurchaseReturnTransaction $less */
            $less->ledger_name = $less->ledger->name ?? null;
            unset($less->ledger);
        }

        foreach ($purchaseReturnTransaction->paymentDetails as $payment) {
            /** @var PaymentDetailsForPurchaseReturnTransaction $payment */
            $payment->ledger_name = $payment->ledger->name ?? null;
            unset($payment->ledger);
        }

        return $purchaseReturnTransaction;
    }
}
