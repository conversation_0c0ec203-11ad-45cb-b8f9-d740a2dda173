<?php

namespace App\Actions\v1\Transactions\PurchaseOrder;

use App\Actions\CustomFields\GetTransactionCustomFieldsAction;
use App\Actions\CustomFieldsItemMaster\Transaction\GetAllCustomFieldsItemTransactionWise;
use App\Actions\CustomFieldsItemMaster\Inventory\GetItemCFInventoryAction;
use App\Actions\CustomFieldsItemMaster\Transaction\GetCFTransactionWiseAction;
use App\Models\AdditionalChargesForPurchaseOrderTransaction;
use App\Models\AddLessForPurchaseOrderTransaction;
use App\Models\TransactionCustomField;
use App\Models\ItemCustomField;
use App\Models\PurchaseItemTransaction;
use App\Models\PurchaseOrderAccountingInvoice;
use App\Models\PurchaseOrderItemInvoice;
use App\Models\PurchaseOrderTransaction;
use App\Models\PurchaseTransaction;
use App\Models\TaxClassificationDetails;
use App\Models\UnitOfMeasurement;
use Illuminate\Support\Arr;
use Lorisleiva\Actions\Concerns\AsAction;

class GetMultiPurchaseOrderTransactionAction
{
    use AsAction;

    public $purchaseOrderTransactions = null;

    public $data = [];

    public function handle($input)
    {
        $purchaseOrderIds = $input['ids'] ?? [];
        $invoiceType = $input['invoiceType'] ?? null;
        $invoiceNumber = $input['invoiceNumber'] ?? null;
        $transactionId = $input['transactionId'] ?? null;
        $withTax = ! isset($input['withTax']) ?? true;

        $this->purchaseOrderTransactions = PurchaseOrderTransaction::with([
            'party',
            'transactionItems.items',
            'transactionLedgers',
            'billingAddress',
            'shippingAddress',
            'transactionItems.classificationNatureType',
            'transactionLedgers.classificationNatureType',
            'additionalCharges',
            'addLess',
        ])
            ->when($invoiceType != null, function ($query) use ($invoiceType) {
                return $query->where('order_type', $invoiceType);
            })
            ->whereIn('id', $purchaseOrderIds)
            ->get();

        if (! empty($invoiceNumber)) {
            $purchaseOrderTransaction = $this->purchaseOrderTransactions->where('id', $invoiceNumber)->first();
        } else {
            $purchaseOrderTransaction = $this->purchaseOrderTransactions->where('id', $this->purchaseOrderTransactions->first()->id)->first();
        }

        if (count($purchaseOrderTransaction->customFieldValues) > 0) {
            $purchaseOrderTransaction->custom_values = GetTransactionCustomFieldsAction::run(TransactionCustomField::PURCHASE_ORDER, $purchaseOrderTransaction->id, PurchaseOrderTransaction::class);
        }

        $this->data['isShowInvoiceTypeModal'] = false;
        $this->data['isShowInvoiceNumberModal'] = false;
        $this->data['isShowWithAndWithoutTaxModal'] = false;
        $this->data['invoiceTypeModalList'] = [];
        $this->data['invoiceNumberModalList'] = [];
        $this->data['invoiceWithAndWithoutTaxModalList'] = [];
        $this->data['purchaseOrderIds'] = $this->purchaseOrderTransactions->pluck('id'); // Ids for Invoice Type Modal
        $this->data['invoiceType'] = $invoiceType;
        $this->data['matchedInputName'] = '';

        if (count($purchaseOrderIds) > 1 && $this->data['isShowWithAndWithoutTaxModal'] != true && $withTax && ($invoiceNumber != null || $invoiceType != null)) {
            foreach ($this->purchaseOrderTransactions as $transaction) {
                $this->data['invoiceWithAndWithoutTaxModalList'][$transaction->full_order_number] = $transaction->transactionItems->first()->with_tax ?? $transaction->transactionLedgers->first()->with_tax;
            }
            $hasTrue = in_array(true, $this->data['invoiceWithAndWithoutTaxModalList'], true);
            $hasFalse = in_array(false, $this->data['invoiceWithAndWithoutTaxModalList'], true);
            $isShowWithAndWithoutTaxModal = $hasTrue && $hasFalse;
            if ($isShowWithAndWithoutTaxModal) {
                $this->data['isShowWithAndWithoutTaxModal'] = true;
            } else {
                $this->data['isShowWithAndWithoutTaxModal'] = false;
                $this->data['invoiceWithAndWithoutTaxModalList'] = [];
            }
        }

        if (count($purchaseOrderIds) > 1) {
            $data = [];
            $data['firstInvoiceType'] = $this->purchaseOrderTransactions->first()->order_type;

            if ($invoiceType == null && $invoiceNumber == null) {
                foreach ($this->purchaseOrderTransactions as $transaction) {
                    $this->data['invoiceTypeModalList'][$transaction->full_order_number] = PurchaseOrderTransaction::INVOICE_TYPE[$transaction->order_type];
                    if ($data['firstInvoiceType'] != $transaction->order_type) {
                        $this->data['isShowInvoiceTypeModal'] = true;
                    }
                }
            }
        }

        if (count($purchaseOrderIds) > 1 && $this->data['isShowInvoiceTypeModal'] != true && $invoiceNumber == null) {

            $keysToCompare = [
                'billing_address',
                'shipping_address',
                'broker_id',
                'brokerage',
                'brokerage_on_value_type',
                'credit_period',
                'credit_period_type',
                'transport_id',
                'transporter_document_number',
                'transporter_document_date',
                'po_no',
                'po_date',
                'transaction_items',
                'transaction_ledgers',
            ];

            $purchaseOrders = $this->purchaseOrderTransactions->toArray();

            unset($purchaseOrders[0]['transaction_items']);
            unset($purchaseOrders[0]['transaction_ledgers']);

            $count = count($purchaseOrders);

            for ($i = 0; $i < $count; $i++) {
                $this->data['invoiceNumberModalList'][$purchaseOrders[$i]['id']] = $purchaseOrders[$i]['full_order_number']; // Show Invoices in Modal
                if ($count > 1) {
                    for ($j = $i + 1; $j < $count; $j++) {
                        $isSimilar = true;
                        $firstTransaction = Arr::only($purchaseOrders[$i], $keysToCompare);
                        $restPOTransaction = Arr::only($purchaseOrders[$j], $keysToCompare);
                        foreach ($firstTransaction as $key => $value) {
                            if (empty($firstTransaction[$key]) || empty($restPOTransaction[$key])) {
                                $isSimilar = false;
                                $this->data['matchedInputName'] = $this->formatFieldString($key);
                                break;
                            }
                            if ($firstTransaction[$key] != $restPOTransaction[$key] && ! in_array($key, ['billing_address', 'shipping_address', 'transaction_items', 'transaction_ledgers', 'tcs_rate'])) {
                                $isSimilar = false;
                                $this->data['matchedInputName'] = $this->formatFieldString($key);
                                break;
                            }

                            // Compare Billing Address
                            foreach ($firstTransaction['billing_address'] as $field => $value) {
                                if ($firstTransaction['billing_address'][$field] != $restPOTransaction['billing_address'][$field] && ! in_array($field, ['id', 'model_id', 'model_type', 'address_type', 'created_at', 'updated_at'])) {
                                    $isSimilar = false;
                                    $this->data['matchedInputName'] = $this->formatFieldString($field);
                                    break;
                                }
                            }

                            // Compare Shipping Address
                            if (! empty($firstTransaction['shipping_address']) && ! empty($restPOTransaction['shipping_address'])) {
                                foreach ($firstTransaction['shipping_address'] as $field => $value) {
                                    if ($firstTransaction['shipping_address'][$field] != $restPOTransaction['shipping_address'][$field] && ! in_array($field, ['id', 'model_id', 'model_type', 'address_type', 'created_at', 'updated_at'])) {
                                        $isSimilar = false;
                                        $this->data['matchedInputName'] = $this->formatFieldString($field);
                                        break;
                                    }
                                }
                            }
                        }
                        $key = 1; // old code it's not used currently used app/Actions/v1/Transactions/PurchaseOrder/GetMultiPurchaseOrderTransactionAction.php
                        if (isset($firstTransaction['transaction_items']) && count($restPOTransaction['transaction_items']) > 0) {  // Transaction Items Classification compare
                            if (empty($firstTransaction['transaction_items'][0]['classification_nature_type']) || empty($restPOTransaction['transaction_items'][0]['classification_nature_type'])) {
                                $isSimilar = false;
                                $this->data['matchedInputName'] = $this->formatFieldString($key);
                            } elseif ($firstTransaction['transaction_items'][0]['classification_nature_type']['name'] != $restPOTransaction['transaction_items'][0]['classification_nature_type']['name']) {
                                $isSimilar = false;
                                $this->data['matchedInputName'] = $this->formatFieldString($key);
                            }
                        }

                        if (isset($firstTransaction['transaction_ledgers']) && count($restPOTransaction['transaction_ledgers']) > 0) {  // Transaction Ledgers Classification compare
                            if (! empty($firstTransaction['transaction_ledgers'][0]['classification_nature_type']) && ! empty($restPOTransaction['transaction_ledgers'][0]['classification_nature_type'])) {
                                if ($firstTransaction['transaction_ledgers'][0]['classification_nature_type']['name'] != $restPOTransaction['transaction_ledgers'][0]['classification_nature_type']['name']) {
                                    $isSimilar = false;
                                    $this->data['matchedInputName'] = $this->formatFieldString($key);
                                }
                            }
                        }

                        if (! $isSimilar) {
                            $this->data['isShowInvoiceNumberModal'] = true;
                        }
                    }
                }
            }
        }

        $purchaseOrderTransaction['billingAddress'] = $purchaseOrderTransaction->billingAddress;
        $purchaseOrderTransaction['shippingAddress'] = $purchaseOrderTransaction->shippingAddress;
        $purchaseOrderTransaction['classificationNatureType'] = null;
        $purchaseOrderTransaction['isRcmApplicable'] = 0;
        if ($purchaseOrderTransaction->order_type == PurchaseOrderTransaction::ITEM_INVOICE) {
            $purchaseOrderTransaction->transactionItems->load('classificationNatureType');
            $purchaseOrderTransaction['classificationNatureType'] = $purchaseOrderTransaction->transactionItems[0]?->classificationNatureType?->name;
            $purchaseOrderTransaction['isRcmApplicable'] = $purchaseOrderTransaction->transactionItems[0]?->classification_is_rcm_applicable;
        }
        if ($purchaseOrderTransaction->order_type == PurchaseOrderTransaction::ACCOUNTING_INVOICE) {
            $purchaseOrderTransaction->transactionLedgers->load('classificationNatureType');
            $purchaseOrderTransaction['classificationNatureType'] = $purchaseOrderTransaction->transactionLedgers[0]?->classificationNatureType?->name;
            $purchaseOrderTransaction['isRcmApplicable'] = $purchaseOrderTransaction->transactionLedgers[0]?->classification_is_rcm_applicable;
        }

        $purchaseOrderTransaction['supplier_ledger_name'] = $purchaseOrderTransaction->party->name ?? null;
        $this->data['purchaseOrderTransaction'] = $purchaseOrderTransaction;
        $this->data['totalCessRate'] = 0;
        $firstTransaction = $this->purchaseOrderTransactions->first();
        $transactionItems = [];

        foreach ($this->purchaseOrderTransactions as $key => $transaction) {
            $this->data['totalCessRate'] += $transaction['cess'];
            if ($firstTransaction->order_type == PurchaseOrderTransaction::ITEM_INVOICE) {
                foreach ($transaction->transactionItems as $item) {
                    /** @var PurchaseOrderItemInvoice $item */
                    $item->item_name = $item->items->item_name ?? null;
                    $item->ledger_name = $item->ledgers->name ?? null;

                    /* This is for custom fields if transaction item has custom fields values */
                    if (count($item->customFieldTransactionItemsValues) > 0) {
                        $item->custom_items_values = GetCFTransactionWiseAction::run(ItemCustomField::PURCHASE_ORDER, $item->id, PurchaseOrderItemInvoice::class, $item->item_id);
                    }

                    /* This is for all custom fields */
                    $item->custom_fields = GetAllCustomFieldsItemTransactionWise::run(ItemCustomField::PURCHASE_ORDER, $item->id, PurchaseOrderItemInvoice::class, $item->item_id);

                    if (count($item->customFieldTransactionItemsInventoryValues) > 0) {
                        $item->custom_items_inventory_values = GetItemCFInventoryAction::run($item->id, PurchaseOrderItemInvoice::class, $item->item_id);
                    }

                    $item->model_inventory_custom_fields = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
                        $query->where('item_id', $item->item_id);
                    })->where('open_in_popup', true)->orderBy('ordering', 'ASC')->get();

                    if (is_null($item->items) || is_null($item->items->model)) {
                        $item->unitOfArray = [];

                        continue;
                    }

                    $unitIds = array_filter([
                        $item->items->model->unit_of_measurement,
                        $item->items->model->secondary_unit_of_measurement ?? null,
                    ]);

                    $item->unitOfArray = UnitOfMeasurement::whereIn('id', $unitIds)->pluck('name', 'id')->toArray();

                    $item->decimal_places = ! empty($item->decimal_places_for_quantity) ? $item->decimal_places_for_quantity : ($item->items->model->decimal_places ?? 2);
                    $item->item_decimal_places = $item->items->model->decimal_places ?? 2;
                    $item->decimal_places_for_rate = ! empty($item->decimal_places_for_rate) ? $item->decimal_places_for_rate : ($item->items->model->decimal_places_for_rate ?? 2);
                    $item->item_decimal_places_for_rate = $item->items->model->decimal_places_for_rate ?? 2;
                    $item->secondary_unit_of_measurement = $item->items->model->secondary_unit_of_measurement ?? null;
                    $item->conversion_rate = $item->items->model->conversion_rate ?? null;
                    $item->is_re_order = $item->items->model->is_re_order ?? false;

                    unset($item->ledgers);
                    $transactionItems[] = $item;
                }
            } else {
                foreach ($transaction->transactionLedgers as $ledger) {
                    /** @var PurchaseOrderAccountingInvoice $ledger */
                    $ledger->ledger_name = $ledger->ledgers->name ?? null;
                    $transactionItems[] = $ledger;
                }
            }

            foreach ($transaction->additionalCharges as $charge) {
                /** @var AdditionalChargesForPurchaseOrderTransaction $charge */
                $charge->ledger_name = $charge->ledger->name ?? null;
                unset($charge->ledger);
            }

            foreach ($transaction->addLess as $less) {
                /** @var AddLessForPurchaseOrderTransaction $less */
                $less->ledger_name = $less->ledger->name ?? null;
                unset($less->ledger);
            }
        }
        $itemCollectData = [];
        if ($firstTransaction->order_type == PurchaseOrderTransaction::ITEM_INVOICE) {
            // Remaining Items qty calculate
            $transactionItems = collect($transactionItems)->filter(function ($item) use ($transactionId) {
                $purchaseOrderId = $item['transactions_id'];

                $purchaseItemQty = PurchaseItemTransaction::whereHas('purchaseTransaction', function ($query) use ($purchaseOrderId) {
                    $query->whereRaw('FIND_IN_SET(?, purchase_order_no)', [$purchaseOrderId])
                        ->whereNull('deleted_at');
                })
                    ->when(! empty($transactionId), function ($query) use ($transactionId) {
                        $query->where('purchase_transaction_id', '!=', $transactionId);
                    })
                    ->where('item_id', $item['item_id'])
                    ->sum('quantity');

                if (! empty($purchaseItemQty)) {
                    $qty = max(0, $item['quantity'] - $purchaseItemQty);
                    $total = $qty * $item['rpu_without_gst'];

                    if ($item['discount_value'] == PurchaseTransaction::DISCOUNT_TYPE_AMOUNT) {
                        $total -= $item['discount_value'];
                    } else {
                        $total -= ($total * $item['discount_value'] / 100);
                    }

                    $item['quantity'] = round($qty, 2);
                    $item['total'] = round($total, 2);
                }

                return $item['quantity'] > 0;
            })->values();

            $itemCollect = collect($transactionItems)->groupBy('item_id');

            foreach ($itemCollect as $key => $itemGroup) {
                if (count($itemGroup) > 1) {
                    $combinedItems = $itemGroup->groupBy(function ($item) {
                        return $item['ledger_id'].'-'.$item['unit_id'].'-'.$item['mrp'].'-'.$item['rpu_with_gst'].'-'.$item['rpu_without_gst'].'-'.$item['discount_type'].'-'.$item['discount_value'].'-'.$item['discount_type_2'].'-'.$item['discount_value_2'].'-'.($item['gst_tax_percentage'] ?? 0.0).'-'.$item['classification_is_rcm_applicable'].'-'.$item['classification_nature_type'].'-'.$item['additional_description'];
                    })->map(function ($group) {
                        $collectionItem = $group->first();
                        $collectionItem->quantity = $group->sum('quantity');
                        $this->calculateTransactions($collectionItem);

                        return $collectionItem;
                    });
                    foreach ($combinedItems as $combinedItem) {
                        $itemCollectData[] = $combinedItem;
                    }
                } else {
                    $itemCollectData[] = $itemGroup->first();
                }
            }
        } else {
            $ledgerCollect = collect($transactionItems)->groupBy('ledger_id');

            foreach ($ledgerCollect as $key => $ledgerGroup) {
                if (count($ledgerGroup) > 1) {
                    $combinedLedgers = $ledgerGroup->groupBy(function ($ledger) {
                        return $ledger['ledger_id'].'-'.$ledger['discount_type'].'-'.$ledger['discount_value'].'-'.$ledger['discount_type_2'].'-'.$ledger['discount_value_2'].'-'.($ledger['gst_tax_percentage'] ?? 0.0).'-'.$ledger['classification_is_rcm_applicable'].'-'.$ledger['classification_nature_type'].'-'.$ledger['additional_description'];
                    })->map(function ($group) {
                        $collectionItem = $this->calculateLedgerTransactions($group);

                        return $collectionItem;
                    });
                    foreach ($combinedLedgers as $combinedLedger) {
                        $itemCollectData[] = $combinedLedger;
                    }
                } else {
                    $itemCollectData[] = $ledgerGroup->first();
                }
            }
        }

        $this->data['tcs_amount'] = $this->tcsCalculate($purchaseOrderTransaction, $itemCollectData);
        $this->data['allTransactionItems'] = $itemCollectData;

        return $this->data;
    }

    public function formatFieldString($string)
    {
        $string = str_replace('_', ' ', $string);
        $string = ucwords($string);

        return $string;
    }

    public function calculateTransactions($transactionItem)
    {
        $taxableValue = $transactionItem->rpu_without_gst * $transactionItem->quantity;
        $fixDigit = getCompanyFixedDigitNumber();
        if ($transactionItem->discount_type == PurchaseOrderTransaction::DISCOUNT_TYPE_PERCENTAGE) {
            $discount_1 = round($taxableValue * $transactionItem->discount_value / 100, $fixDigit) ?? 0;
            $discount_2 = round($taxableValue * $transactionItem->discount_value_2 / 100, $fixDigit) ?? 0;
            $transactionItem->total_discount_amount = $discount_1 + $discount_2;
            $transactionItem->total = round($taxableValue - $transactionItem->total_discount_amount, $fixDigit);

        } elseif ($transactionItem->discount_type == PurchaseOrderTransaction::DISCOUNT_TYPE_AMOUNT) {
            $discount_1 = round(($transactionItem->discount_value * $transactionItem->quantity), $fixDigit) ?? 0;
            $discount_2 = round(($transactionItem->discount_value_2 * $transactionItem->quantity), $fixDigit) ?? 0;
            $transactionItem->total_discount_amount = $discount_1 + $discount_2;
            $discountWithAmount = round(($transactionItem->rpu_without_gst - $transactionItem->discount_value - ($transactionItem->discount_value_2 ?? 0)), $fixDigit);
            $transactionItem->total = round($discountWithAmount * $transactionItem->quantity, $fixDigit);

        } else {
            $transactionItem->total = round($taxableValue, $fixDigit);
        }
        $transactionCollect = collect($this->purchaseOrderTransactions)->groupBy('id');
        $transaction = $transactionCollect[$transactionItem->transactions_id]->first();
        $isRcmApplicable = strtolower($transaction->rcm_applicable) == 'yes';

        if (! $transaction['is_gst_na']) {
            $taxClassificationDetails = TaxClassificationDetails::whereId($transactionItem->classification_nature_type)->first();

            $gstData = $this->getGSTCalculationDetails($taxClassificationDetails->name, $isRcmApplicable);
        }

        if (! $transaction['is_gst_na']) {
            if ($gstData['isGSTCalculated'] && ! $transaction['is_gst_na']) {
                $transactionItem->rpu_without_gst = $transactionItem->rpu_without_gst;

                if ($gstData['gstType'] == 'CGST') {
                    $transactionItem->classification_igst_tax = 0;
                    $transactionItem->classification_cgst_tax = round(($transactionItem->total * $transactionItem->gst_tax_percentage) / 200, $fixDigit);
                    $transactionItem->classification_sgst_tax = round(($transactionItem->total * $transactionItem->gst_tax_percentage) / 200, $fixDigit);
                } else {
                    $transactionItem->classification_igst_tax = round(($transactionItem->total * $transactionItem->gst_tax_percentage) / 100, $fixDigit);
                    $transactionItem->classification_cgst_tax = 0;
                    $transactionItem->classification_sgst_tax = 0;
                }
            } else {
                $transactionItem->rpu_without_gst = round($transactionItem->rpu_without_gst, $fixDigit);
                $transactionItem->classification_igst_tax = 0;
                $transactionItem->classification_cgst_tax = 0;
                $transactionItem->classification_sgst_tax = 0;
            }
        }

        return $transactionItem;
    }

    public function calculateLedgerTransactions($transactionLedger)
    {
        $ledger = $transactionLedger->first();
        $fixDigit = getCompanyFixedDigitNumber();
        $ledger->rpu_without_gst = $transactionLedger->sum('rpu_without_gst');
        $transactionCollect = collect($this->purchaseOrderTransactions)->groupBy('id');
        $transaction = $transactionCollect[$ledger->transactions_id]->first();
        $isRcmApplicable = strtolower($ledger->rcm_applicable) == 'yes';

        if (! $transaction['is_gst_na']) {
            $ledger->rpu_with_gst = $transactionLedger->sum('rpu_with_gst');
            $taxClassificationDetails = TaxClassificationDetails::whereId($ledger->classification_nature_type)->first();
            $gstData = $this->getGSTCalculationDetails($taxClassificationDetails->name, $isRcmApplicable);
        }

        if ($ledger->discount_type == PurchaseOrderTransaction::DISCOUNT_TYPE_PERCENTAGE) {
            $ledger->total_discount_amount = round($ledger->rpu_without_gst * $ledger->discount_value / 100, $fixDigit);
            $ledger->total = round($ledger->rpu_without_gst - $ledger->total_discount_amount, $fixDigit);
        } elseif ($ledger->discount_type == PurchaseOrderTransaction::DISCOUNT_TYPE_AMOUNT) {
            $ledger->total_discount_amount = round(($ledger->total_discount_amount), $fixDigit);
            $discountWithAmount = round(($ledger->rpu_without_gst - $ledger->total_discount_amount), $fixDigit);
            $ledger->total = round($discountWithAmount, $fixDigit);
        } else {
            $ledger->total = round($ledger->rpu_without_gst, $fixDigit);
        }

        if (! $transaction['is_gst_na']) {
            if ($gstData['isGSTCalculated'] && ! $transaction['is_gst_na']) {
                if ($gstData['gstType'] == 'CGST') {
                    $ledger->classification_igst_tax = 0;
                    $ledger->classification_cgst_tax = round(($ledger->total * $ledger->gst_tax_percentage) / 200, $fixDigit);
                    $ledger->classification_sgst_tax = round(($ledger->total * $ledger->gst_tax_percentage) / 200, $fixDigit);
                } else {
                    $ledger->classification_igst_tax = round(($ledger->total * $ledger->gst_tax_percentage) / 100, $fixDigit);
                    $ledger->classification_cgst_tax = 0;
                    $ledger->classification_sgst_tax = 0;
                }
            } else {
                $ledger->rpu_without_gst = round($ledger->rpu_without_gst, $fixDigit);
                $ledger->classification_igst_tax = 0;
                $ledger->classification_cgst_tax = 0;
                $ledger->classification_sgst_tax = 0;
            }
        }

        return $ledger;
    }

    public function getGSTCalculationDetails($classificationNatureType, $isRcmApplicable): array
    {
        $isGSTCalculated = false;
        $gstType = false;
        if (\in_array($classificationNatureType, intraStateArray())) {
            if ($isRcmApplicable) {
                $isGSTCalculated = false;
            } else {
                $isGSTCalculated = true;
                $gstType = 'CGST';
            }
        } elseif (\in_array($classificationNatureType, interStateArray())) {
            if ($isRcmApplicable) {
                $isGSTCalculated = false;
            } else {
                $isGSTCalculated = true;
                $gstType = 'IGST';
            }
        } elseif (\in_array($classificationNatureType, exportArray()) || \in_array($classificationNatureType, sezArray())) {
            $isGSTCalculated = true;
            $gstType = 'IGST';
        } elseif ($classificationNatureType == 'Deemed Export - Intrastate') {
            $isGSTCalculated = true;
            $gstType = 'CGST';
        } elseif ($classificationNatureType == 'Deemed Export - Interstate') {
            $isGSTCalculated = true;
            $gstType = 'IGST';
        }

        return [
            'isGSTCalculated' => $isGSTCalculated,
            'gstType' => $gstType,
        ];
    }

    public function tcsCalculate($transaction, $itemCollectData)
    {
        $taxableValues = array_column($itemCollectData, 'total');
        $totalSubTotal = array_sum($taxableValues);

        $fixDigit = getCompanyFixedDigitNumber();

        // additional charges
        $totalSubTotal += $transaction->additionalCharges->sum('total_without_tax') ?? 0;

        $totalSubTotal += $transaction->cess;
        $totalSubTotal += $transaction->cgst;
        $totalSubTotal += $transaction->sgst;
        $totalSubTotal += $transaction->igst;

        $tcsAmount = round(($totalSubTotal * $transaction->tcs_rate) / 100, $fixDigit);

        return $tcsAmount;
    }
}
