<?php

namespace App\Actions\v1\Transactions\PurchaseOrder;

use App\Actions\CustomFields\GetTransactionCustomFieldsAction;
use App\Actions\CustomFieldsItemMaster\Transaction\GetAllCustomFieldsItemTransactionWise;
use App\Actions\CustomFieldsItemMaster\Inventory\GetItemCFInventoryAction;
use App\Actions\CustomFieldsItemMaster\Transaction\GetCFTransactionWiseAction;
use App\Models\AdditionalChargesForPurchaseOrderTransaction;
use App\Models\AddLessForPurchaseOrderTransaction;
use App\Models\TransactionCustomField;
use App\Models\ItemCustomField;
use App\Models\PurchaseOrderAccountingInvoice;
use App\Models\PurchaseOrderItemInvoice;
use App\Models\PurchaseOrderTransaction;
use App\Models\UnitOfMeasurement;
use Lorisleiva\Actions\Concerns\AsAction;

class GetPurchaseOrderTransactionAction
{
    use AsAction;

    public function handle($id, $isEdit = true)
    {
        $query = PurchaseOrderTransaction::with([
            'billingAddress',
            'shippingAddress',
            'transactionItems',
            'transactionLedgers',
            'party',
            'additionalCharges.ledger',
            'addLess.ledger',
        ]);

        $purchaseOrderTransaction = $isEdit ? $query->financialYearDate()->findOrFail($id) : $query->findOrFail($id);
        $purchaseOrderTransaction->supplier_ledger_name = $purchaseOrderTransaction->party->name ?? null;
        $purchaseOrderTransaction->media = $purchaseOrderTransaction->getPurchaseOrderFileAttribute();

        if (count($purchaseOrderTransaction->customFieldValues) > 0) {
            $purchaseOrderTransaction->custom_values = GetTransactionCustomFieldsAction::run(TransactionCustomField::PURCHASE_ORDER, $purchaseOrderTransaction->id, PurchaseOrderTransaction::class);
        }

        foreach ($purchaseOrderTransaction->transactionItems as $item) {

            /** @var PurchaseOrderItemInvoice $item */
            $item->item_name = $item->items->item_name ?? null;
            $item->ledger_name = $item->ledgers->name ?? null;

            /* This is for custom fields if transaction item has custom fields values */
            if (count($item->customFieldTransactionItemsValues) > 0) {
                $item->custom_items_values = GetCFTransactionWiseAction::run(ItemCustomField::PURCHASE_ORDER, $item->id, PurchaseOrderItemInvoice::class, $item->item_id);
            }

            /* This is for all custom fields */
            $item->custom_fields = GetAllCustomFieldsItemTransactionWise::run(ItemCustomField::PURCHASE_ORDER, $item->id, PurchaseOrderItemInvoice::class, $item->item_id);

            if (count($item->customFieldTransactionItemsInventoryValues) > 0) {
                $item->custom_items_inventory_values = GetItemCFInventoryAction::run($item->id, PurchaseOrderItemInvoice::class, $item->item_id);
            }

            $item->model_inventory_custom_fields = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
                $query->where('item_id', $item->item_id);
            })->where('open_in_popup', true)->orderBy('ordering', 'ASC')->get();

            if (is_null($item->items) || is_null($item->items->model)) {
                $item->unitOfArray = [];

                continue;
            }

            $unitIds = array_filter([
                $item->unit_id,
                $item->items->model->unit_of_measurement,
                $item->items->model->secondary_unit_of_measurement ?? null,
            ]);

            // TO_COMMENT =>
            $item->unitOfArray = UnitOfMeasurement::whereIn('id', $unitIds)->pluck('name', 'id')->toArray();
            $item->units_of_array = UnitOfMeasurement::whereIn('id', $unitIds)->get()->toArray();

            $item->decimal_places = ! empty($item->decimal_places_for_quantity) ? $item->decimal_places_for_quantity : ($item->items->model->decimal_places ?? 2);
            $item->item_decimal_places = $item->items->model->decimal_places ?? 2;
            $item->decimal_places_for_rate = ! empty($item->decimal_places_for_rate) ? $item->decimal_places_for_rate : ($item->items->model->decimal_places_for_rate ?? 2);
            $item->item_decimal_places_for_rate = $item->items->model->decimal_places_for_rate ?? 2;
            $item->secondary_unit_of_measurement = $item->items->model->secondary_unit_of_measurement ?? null;
            $item->conversion_rate = $item->items->model->conversion_rate ?? null;
            $item->is_re_order = $item->items->model->is_re_order ?? false;
            unset($item->items);
            unset($item->ledgers);
        }

        foreach ($purchaseOrderTransaction->transactionLedgers as $ledger) {
            /** @var PurchaseOrderAccountingInvoice $ledger */
            $ledger->ledger_name = $ledger->ledgers->name ?? null;
            unset($ledger->ledgers);
        }

        foreach ($purchaseOrderTransaction->additionalCharges as $charge) {
            /** @var AdditionalChargesForPurchaseOrderTransaction $charge */
            $charge->ledger_name = $charge->ledger->name ?? null;
            unset($charge->ledger);
        }

        foreach ($purchaseOrderTransaction->addLess as $less) {
            /** @var AddLessForPurchaseOrderTransaction $less */
            $less->ledger_name = $less->ledger->name ?? null;
            unset($less->ledger);
        }

        return $purchaseOrderTransaction;
    }
}
