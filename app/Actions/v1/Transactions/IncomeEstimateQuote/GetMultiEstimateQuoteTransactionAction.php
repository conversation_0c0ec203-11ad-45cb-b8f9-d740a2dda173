<?php

namespace App\Actions\v1\Transactions\IncomeEstimateQuote;

use App\Actions\CommonAction\GetTcsTdsDetailsAction;
use App\Actions\CustomFields\GetTransactionCustomFieldsAction;
use App\Actions\CustomFieldsItemMaster\Transaction\GetAllCustomFieldsItemTransactionWise;
use App\Actions\CustomFieldsItemMaster\Inventory\GetItemCFInventoryAction;
use App\Actions\CustomFieldsItemMaster\Transaction\GetCFTransactionWiseAction;
use App\Models\AdditionalChargesForIncomeEstimateQuoteTransaction;
use App\Models\AddLessForIncomeEstimateQuoteTransaction;
use App\Models\TransactionCustomField;
use App\Models\ItemCustomField;
use App\Models\IncomeEstimateQuoteAccountingInvoice;
use App\Models\IncomeEstimateQuoteItemInvoice;
use App\Models\IncomeEstimateQuoteTransaction;
use App\Models\SaleTransaction;
use App\Models\SaleTransactionItem;
use App\Models\TaxClassificationDetails;
use App\Models\UnitOfMeasurement;
use Illuminate\Support\Arr;
use Lorisleiva\Actions\Concerns\AsAction;

class GetMultiEstimateQuoteTransactionAction
{
    use AsAction;

    public $estimateQuoteTransactions = null;

    public $data = [];

    public function handle($input)
    {
        $estimateIds = $input['ids'] ?? [];
        $invoiceType = $input['invoiceType'] ?? null;
        $invoiceNumber = $input['invoiceNumber'] ?? null;
        $transactionId = $input['transactionId'] ?? null;
        $withTax = ! isset($input['withTax']) ?? true;

        $this->estimateQuoteTransactions = IncomeEstimateQuoteTransaction::with([
            'party',
            'transactionItems',
            'transactionLedgers',
            'billingAddress',
            'shippingAddress',
            'transactionItems.classificationNatureType',
            'transactionLedgers.classificationNatureType',
            'additionalCharges',
            'addLess',
        ])
            ->when($invoiceType != null, function ($query) use ($invoiceType) {
                return $query->where('invoice_type', $invoiceType);
            })
            ->whereIn('id', $estimateIds)
            ->get();

        if (! empty($invoiceNumber)) {
            $estimateQuoteTransaction = $this->estimateQuoteTransactions->where('id', $invoiceNumber)->first();
        } else {
            $estimateQuoteTransaction = $this->estimateQuoteTransactions->first() ?? [];
        }

        if (isset($estimateQuoteTransaction->tcs_tax_id) && $estimateQuoteTransaction->tcs_tax_id) {
            $estimateQuoteTransaction->tcs_calculated = GetTcsTdsDetailsAction::run($estimateQuoteTransaction->tcs_tax_id, $estimateQuoteTransaction->party_ledger_id);
        }
        if (isset($estimateQuoteTransaction->tds_tax_id) && $estimateQuoteTransaction->tds_tax_id) {
            $estimateQuoteTransaction->tds_calculated = GetTcsTdsDetailsAction::run($estimateQuoteTransaction->tds_tax_id, $estimateQuoteTransaction->party_ledger_id);
            $estimateQuoteTransaction->tds_rounding_method = $estimateQuoteTransaction->tds_calculated['rounding_method'] ?? null;
        }

        if (count($estimateQuoteTransaction->customFieldValues) > 0) {
            $estimateQuoteTransaction->custom_values = GetTransactionCustomFieldsAction::run(TransactionCustomField::INCOME_ESTIMATE_QUOTE, $estimateQuoteTransaction->id, IncomeEstimateQuoteTransaction::class);
        }

        $this->data['isShowInvoiceTypeModal'] = false;
        $this->data['isShowInvoiceNumberModal'] = false;
        $this->data['isShowWithAndWithoutTaxModal'] = false;
        $this->data['invoiceTypeModalList'] = [];
        $this->data['invoiceNumberModalList'] = [];
        $this->data['invoiceWithAndWithoutTaxModalList'] = [];
        $this->data['estimateIds'] = $this->estimateQuoteTransactions->pluck('id'); // Ids for Invoice Type Modal
        $this->data['invoiceType'] = $invoiceType;
        $this->data['matchedInputName'] = '';

        if (count($estimateIds) > 1 && $this->data['isShowWithAndWithoutTaxModal'] != true && $withTax && ($invoiceNumber != null || $invoiceType != null)) {
            foreach ($this->estimateQuoteTransactions as $transaction) {
                $this->data['invoiceWithAndWithoutTaxModalList'][$transaction->document_number] = $transaction->transactionItems->first()->with_tax ?? $transaction->transactionLedgers->first()->with_tax;
            }
            $hasTrue = in_array(true, $this->data['invoiceWithAndWithoutTaxModalList'], true);
            $hasFalse = in_array(false, $this->data['invoiceWithAndWithoutTaxModalList'], true);
            $isShowWithAndWithoutTaxModal = $hasTrue && $hasFalse;
            if ($isShowWithAndWithoutTaxModal) {
                $this->data['isShowWithAndWithoutTaxModal'] = true;
            } else {
                $this->data['isShowWithAndWithoutTaxModal'] = false;
                $this->data['invoiceWithAndWithoutTaxModalList'] = [];
            }
        }

        if (count($estimateIds) > 1) {
            $data = [];
            $data['firstInvoiceType'] = $this->estimateQuoteTransactions->first()->invoice_type;
            if ($invoiceType == null && $invoiceNumber == null) {
                foreach ($this->estimateQuoteTransactions as $transaction) {
                    $this->data['invoiceTypeModalList'][$transaction->document_number] = IncomeEstimateQuoteTransaction::INVOICE_TYPE[$transaction->invoice_type];
                    if ($data['firstInvoiceType'] != $transaction->invoice_type) {
                        $this->data['isShowInvoiceTypeModal'] = true;
                    }
                }
            }
        }

        if (count($estimateIds) > 1 && $this->data['isShowInvoiceTypeModal'] != true && $invoiceNumber == null) {

            $keysToCompare = [
                'billing_address',
                'shipping_address',
                'broker_id',
                'brokerage',
                'brokerage_on_value_type',
                'credit_period',
                'credit_period_type',
                'transport_id',
                'transporter_document_number',
                'transporter_document_date',
                'po_no',
                'po_date',
                'transaction_items',
                'transaction_ledgers',
            ];

            $estimateQuotes = $this->estimateQuoteTransactions->toArray();

            unset($estimateQuotes[0]['sale_transactions']);
            unset($estimateQuotes[0]['transaction_items']);
            unset($estimateQuotes[0]['transaction_ledgers']);

            $count = count($estimateQuotes);

            for ($i = 0; $i < $count; $i++) {
                $this->data['invoiceNumberModalList'][$estimateQuotes[$i]['id']] = $estimateQuotes[$i]['document_number']; // Show Invoices in Modal
                if ($count > 1) {
                    for ($j = $i + 1; $j < $count; $j++) {
                        $isSimilar = true;
                        $firstTransaction = Arr::only($estimateQuotes[$i], $keysToCompare);
                        $restEQTransaction = Arr::only($estimateQuotes[$j], $keysToCompare);
                        foreach ($firstTransaction as $key => $value) {
                            if (empty($firstTransaction[$key]) || empty($restEQTransaction[$key])) {
                                $isSimilar = false;
                                $this->data['matchedInputName'] = $this->formatFieldString($key);
                                break;
                            }
                            if ($firstTransaction[$key] != $restEQTransaction[$key] && ! in_array($key, ['billing_address', 'shipping_address', 'transaction_items', 'transaction_ledgers', 'tcs_rate'])) {
                                $isSimilar = false;
                                $this->data['matchedInputName'] = $this->formatFieldString($key);
                                break;
                            }

                            // Compare Billing Address
                            foreach ($firstTransaction['billing_address'] as $field => $value) {
                                if ($firstTransaction['billing_address'][$field] != $restEQTransaction['billing_address'][$field] && ! in_array($field, ['id', 'model_id', 'model_type', 'address_type', 'created_at', 'updated_at'])) {
                                    $isSimilar = false;
                                    $this->data['matchedInputName'] = $this->formatFieldString($field);
                                    break;
                                }
                            }

                            // Compare Shipping Address
                            if (! empty($firstTransaction['shipping_address']) && ! empty($restEQTransaction['shipping_address'])) {
                                foreach ($firstTransaction['shipping_address'] as $field => $value) {
                                    if ($firstTransaction['shipping_address'][$field] != $restEQTransaction['shipping_address'][$field] && ! in_array($field, ['id', 'model_id', 'model_type', 'address_type', 'created_at', 'updated_at'])) {
                                        $isSimilar = false;
                                        $this->data['matchedInputName'] = $this->formatFieldString($field);
                                        break;
                                    }
                                }
                            }
                        }
                        $key = 1; // old code it's not used currently used app/Actions/v1/Transactions/IncomeEstimateQuote/GetMultiEstimateQuoteTransactionAction.php
                        if (isset($firstTransaction['transaction_items']) && count($restEQTransaction['transaction_items']) > 0) {  // Transaction Items Classification compare
                            if (empty($firstTransaction['transaction_items'][0]['classification_nature_type']) || empty($restEQTransaction['transaction_items'][0]['classification_nature_type'])) {
                                $isSimilar = false;
                                $this->data['matchedInputName'] = $this->formatFieldString($key);
                            } elseif ($firstTransaction['transaction_items'][0]['classification_nature_type']['name'] != $restEQTransaction['transaction_items'][0]['classification_nature_type']['name']) {
                                $isSimilar = false;
                                $this->data['matchedInputName'] = $this->formatFieldString($key);
                            }
                        }

                        if (isset($firstTransaction['transaction_ledgers']) && count($restEQTransaction['transaction_ledgers']) > 0) {  // Transaction Ledgers Classification compare
                            if (! empty($firstTransaction['transaction_ledgers'][0]['classification_nature_type']) && ! empty($restEQTransaction['transaction_ledgers'][0]['classification_nature_type'])) {
                                if ($firstTransaction['transaction_ledgers'][0]['classification_nature_type']['name'] != $restEQTransaction['transaction_ledgers'][0]['classification_nature_type']['name']) {
                                    $isSimilar = false;
                                    $this->data['matchedInputName'] = $this->formatFieldString($key);
                                }
                            }
                        }

                        if (! $isSimilar) {
                            $this->data['isShowInvoiceNumberModal'] = true;
                        }
                    }
                }
            }
        }

        if (! empty($estimateQuoteTransaction)) {
            $estimateQuoteTransaction['billingAddress'] = $estimateQuoteTransaction->billingAddress;
            $estimateQuoteTransaction['shippingAddress'] = $estimateQuoteTransaction->shippingAddress;
            $estimateQuoteTransaction['classificationNatureType'] = null;
            $estimateQuoteTransaction['isRcmApplicable'] = 0;
            if ($estimateQuoteTransaction->invoice_type == IncomeEstimateQuoteTransaction::ITEM_INVOICE) {
                $estimateQuoteTransaction->transactionItems->load('classificationNatureType');
                $estimateQuoteTransaction['classificationNatureType'] = $estimateQuoteTransaction->transactionItems[0]?->classificationNatureType?->name;
                $estimateQuoteTransaction['isRcmApplicable'] = $estimateQuoteTransaction->transactionItems[0]?->classification_is_rcm_applicable;
            }
            if ($estimateQuoteTransaction->invoice_type == IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE) {
                $estimateQuoteTransaction->transactionLedgers->load('classificationNatureType');
                $estimateQuoteTransaction['classificationNatureType'] = $estimateQuoteTransaction->transactionLedgers[0]?->classificationNatureType?->name;
                $estimateQuoteTransaction['isRcmApplicable'] = $estimateQuoteTransaction->transactionLedgers[0]?->classification_is_rcm_applicable;
            }
        }
        $estimateQuoteTransaction['customer_ledger_name'] = $estimateQuoteTransaction->party->name ?? null;
        $this->data['estimateQuoteTransaction'] = $estimateQuoteTransaction;
        $this->data['totalCessRate'] = 0;
        $firstTransaction = $this->estimateQuoteTransactions->first();
        $transactionItems = [];

        foreach ($this->estimateQuoteTransactions as $key => $transaction) {
            $this->data['totalCessRate'] += $transaction['cess'];
            if ($firstTransaction->invoice_type == SaleTransaction::ITEM_INVOICE) {
                foreach ($transaction->transactionItems as $item) {
                    /** @var IncomeEstimateQuoteItemInvoice $item */
                    $item->item_name = $item->items->item_name ?? null;
                    $item->ledger_name = $item->ledgers->name ?? null;

                    /* This is for custom fields if transaction item has custom fields values */
                    if (count($item->customFieldTransactionItemsValues) > 0) {
                        $item->custom_items_values = GetCFTransactionWiseAction::run(ItemCustomField::INCOME_ESTIMATE_QUOTE, $item->id, IncomeEstimateQuoteItemInvoice::class, $item->item_id);
                    }

                    /* This is for all custom fields */
                    $item->custom_fields = GetAllCustomFieldsItemTransactionWise::run(ItemCustomField::INCOME_ESTIMATE_QUOTE, $item->id, IncomeEstimateQuoteItemInvoice::class, $item->item_id);

                    if (count($item->customFieldTransactionItemsInventoryValues) > 0) {
                        $item->custom_items_inventory_values = GetItemCFInventoryAction::run($item->id, IncomeEstimateQuoteItemInvoice::class, $item->item_id, $transaction->id);
                    }

                    $item->model_inventory_custom_fields = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
                        $query->where('item_id', $item->item_id);
                    })->where('open_in_popup', true)->orderBy('ordering', 'ASC')->get();

                    if (is_null($item->items) || is_null($item->items->model)) {
                        $item->unitOfArray = [];

                        continue;
                    }
                    $unitIds = array_filter([
                        $item->items->model->unit_of_measurement,
                        $item->items->model->secondary_unit_of_measurement ?? null,
                    ]);

                    $item->unitOfArray = UnitOfMeasurement::whereIn('id', $unitIds)->pluck('name', 'id')->toArray();

                    $item->decimal_places = ! empty($item->decimal_places_for_quantity) ? $item->decimal_places_for_quantity : ($item->items->model->decimal_places ?? 2);
                    $item->item_decimal_places = $item->items->model->decimal_places ?? 2;
                    $item->decimal_places_for_rate = ! empty($item->decimal_places_for_rate) ? $item->decimal_places_for_rate : ($item->items->model->decimal_places_for_rate ?? 2);
                    $item->item_decimal_places_for_rate = $item->items->model->decimal_places_for_rate ?? 2;
                    $item->secondary_unit_of_measurement = $item->items->model->secondary_unit_of_measurement ?? null;
                    $item->conversion_rate = $item->items->model->conversion_rate ?? null;
                    $item->is_re_order = $item->items->model->is_re_order ?? false;

                    unset($item->ledgers);
                    $transactionItems[] = $item;
                }
            } else {
                foreach ($transaction->transactionLedgers as $ledger) {
                    /** @var IncomeEstimateQuoteAccountingInvoice $ledger */
                    $ledger->ledger_name = $ledger->ledgers->name ?? null;
                    unset($ledger->ledgers);
                    $transactionItems[] = $ledger;
                }
            }

            foreach ($transaction->additionalCharges as $charge) {
                /** @var AdditionalChargesForIncomeEstimateQuoteTransaction $charge */
                $charge->ledger_name = $charge->ledger->name ?? null;
                unset($charge->ledger);
            }

            foreach ($transaction->addLess as $less) {
                /** @var AddLessForIncomeEstimateQuoteTransaction $less */
                $less->ledger_name = $less->ledger->name ?? null;
                unset($less->ledger);
            }
        }

        $itemCollectData = [];
        if ($firstTransaction->invoice_type == SaleTransaction::ITEM_INVOICE) {
            // Remaining Items qty calculate
            $transactionItems = collect($transactionItems)->filter(function ($item) use ($transactionId) {
                $estimateQuoteId = $item->transactions_id;
                $saleItemQty = SaleTransactionItem::whereHas('saleTransaction', function ($query) use ($estimateQuoteId) {
                    $query->whereNull('deleted_at')->whereRaw('FIND_IN_SET(?, estimate_quote_no)', [$estimateQuoteId]);
                })
                    ->when(! empty($transactionId), function ($query) use ($transactionId) {
                        $query->where('sale_transactions_id', '!=', $transactionId);
                    })
                    ->where('item_id', $item->item_id)
                    ->sum('quantity');

                if (! empty($saleItemQty)) {
                    $qty = max(0, $item->quantity - $saleItemQty);
                    $total = $qty * $item->rpu_without_gst;

                    if ($item->discount_value == SaleTransaction::DISCOUNT_TYPE_AMOUNT) {
                        $total = $total - $item->discount_value;
                    } else {
                        $total = $total - ($total * $item->discount_value / 100);
                    }

                    $item->quantity = round($qty, 2);
                    $item->total = round($total, 2);
                }

                return $item->quantity > 0;
            })->values();

            $itemCollect = collect($transactionItems)->groupBy('item_id');

            foreach ($itemCollect as $key => $itemGroup) {
                if (count($itemGroup) > 1) {
                    $combinedItems = $itemGroup->groupBy(function ($item) {
                        return $item['ledger_id'].'-'.$item['unit_id'].'-'.$item['mrp'].'-'.$item['rpu_with_gst'].'-'.$item['rpu_without_gst'].'-'.$item['discount_type'].'-'.$item['discount_value'].'-'.$item['discount_type_2'].'-'.$item['discount_value_2'].'-'.($item['gst_tax_percentage'] ?? 0.0).'-'.$item['classification_is_rcm_applicable'].'-'.$item['classification_nature_type'].'-'.$item['additional_description'];
                    })->map(function ($group) {
                        $collectionItem = $group->first();
                        $collectionItem->quantity = $group->sum('quantity');
                        $this->calculateTransactions($collectionItem);

                        return $collectionItem;
                    });

                    foreach ($combinedItems as $combinedItem) {
                        $itemCollectData[] = $combinedItem;
                    }
                } else {
                    $itemCollectData[] = $itemGroup->first();
                }
            }
        } else {
            $ledgerCollect = collect($transactionItems)->groupBy('ledger_id');

            foreach ($ledgerCollect as $key => $ledgerGroup) {
                if (count($ledgerGroup) > 1) {
                    $combinedLedgers = $ledgerGroup->groupBy(function ($ledger) {
                        return $ledger['ledger_id'].'-'.$ledger['discount_type'].'-'.$ledger['discount_value'].'-'.$ledger['discount_type_2'].'-'.$ledger['discount_value_2'].'-'.($ledger['gst_tax_percentage'] ?? 0.0).'-'.$ledger['classification_is_rcm_applicable'].'-'.$ledger['classification_nature_type'].'-'.$ledger['additional_description'];
                    })->map(function ($group) {
                        $collectionItem = $this->calculateLedgerTransactions($group);

                        return $collectionItem;
                    });

                    foreach ($combinedLedgers as $combinedLedger) {
                        $itemCollectData[] = $combinedLedger;
                    }
                } else {
                    $itemCollectData[] = $ledgerGroup->first();
                }
            }
        }

        $this->data['tcs_amount'] = $this->tcsCalculate($estimateQuoteTransaction, $itemCollectData);
        $this->data['allTransactionItems'] = $itemCollectData;

        return $this->data;
    }

    public function formatFieldString($string)
    {
        $string = str_replace('_', ' ', $string);
        $string = ucwords($string);

        return $string;
    }

    public function calculateTransactions($transactionItem)
    {
        $taxableValue = $transactionItem->rpu_without_gst * $transactionItem->quantity;
        $fixDigit = getCompanyFixedDigitNumber();
        if ($transactionItem->discount_type == SaleTransaction::DISCOUNT_TYPE_PERCENTAGE) {
            $discount_1 = round($taxableValue * $transactionItem->discount_value / 100, $fixDigit) ?? 0;
            $discount_2 = round($taxableValue * $transactionItem->discount_value_2 / 100, $fixDigit) ?? 0;
            $transactionItem->total_discount_amount = $discount_1 + $discount_2;
            $transactionItem->total = round($taxableValue - $transactionItem->total_discount_amount, $fixDigit);

        } elseif ($transactionItem->discount_type == SaleTransaction::DISCOUNT_TYPE_AMOUNT) {
            $discount_1 = round(($transactionItem->discount_value * $transactionItem->quantity), $fixDigit) ?? 0;
            $discount_2 = round(($transactionItem->discount_value_2 * $transactionItem->quantity), $fixDigit) ?? 0;
            $transactionItem->total_discount_amount = $discount_1 + $discount_2;
            $discountWithAmount = round(($transactionItem->rpu_without_gst - $transactionItem->discount_value - ($transactionItem->discount_value_2 ?? 0)), $fixDigit);
            $transactionItem->total = round($discountWithAmount * $transactionItem->quantity, $fixDigit);
        } else {

            $transactionItem->total = round($taxableValue, $fixDigit);
        }
        $transactionCollect = collect($this->estimateQuoteTransactions)->groupBy('id');
        $transaction = $transactionCollect[$transactionItem->transactions_id]->first();
        $isRcmApplicable = strtolower($transaction->rcm_applicable) == 'yes';

        if (! $transaction['is_gst_na']) {
            $taxClassificationDetails = TaxClassificationDetails::whereId($transactionItem->classification_nature_type)->first();

            $gstData = $this->getGSTCalculationDetails($taxClassificationDetails->name, $isRcmApplicable);
        }

        if (! $transaction['is_gst_na']) {
            if ($gstData['isGSTCalculated'] && ! $transaction['is_gst_na']) {
                $transactionItem->rpu_without_gst = $transactionItem->rpu_without_gst;

                if ($gstData['gstType'] == 'CGST') {
                    $transactionItem->classification_igst_tax = 0;
                    $transactionItem->classification_cgst_tax = round(($transactionItem->total * $transactionItem->gst_tax_percentage) / 200, $fixDigit);
                    $transactionItem->classification_sgst_tax = round(($transactionItem->total * $transactionItem->gst_tax_percentage) / 200, $fixDigit);
                } else {
                    $transactionItem->classification_igst_tax = round(($transactionItem->total * $transactionItem->gst_tax_percentage) / 100, $fixDigit);
                    $transactionItem->classification_cgst_tax = 0;
                    $transactionItem->classification_sgst_tax = 0;
                }
            } else {
                $transactionItem->rpu_without_gst = round($transactionItem->rpu_without_gst, $fixDigit);
                $transactionItem->classification_igst_tax = 0;
                $transactionItem->classification_cgst_tax = 0;
                $transactionItem->classification_sgst_tax = 0;
            }
        }

        return $transactionItem;
    }

    public function calculateLedgerTransactions($transactionLedger)
    {
        $ledger = $transactionLedger->first();
        $fixDigit = getCompanyFixedDigitNumber();
        $ledger->rpu_without_gst = $transactionLedger->sum('rpu_without_gst');
        $transactionCollect = collect($this->estimateQuoteTransactions)->groupBy('id');
        $transaction = $transactionCollect[$ledger->transactions_id]->first();
        $isRcmApplicable = strtolower($ledger->rcm_applicable) == 'yes';

        if (! $transaction['is_gst_na']) {
            $ledger->rpu_with_gst = $transactionLedger->sum('rpu_with_gst');
            $taxClassificationDetails = TaxClassificationDetails::whereId($ledger->classification_nature_type)->first();
            $gstData = $this->getGSTCalculationDetails($taxClassificationDetails->name, $isRcmApplicable);
        }

        if ($ledger->discount_type == SaleTransaction::DISCOUNT_TYPE_PERCENTAGE) {
            $ledger->total_discount_amount = round($ledger->rpu_without_gst * $ledger->discount_value / 100, $fixDigit);
            $ledger->total = round($ledger->rpu_without_gst - $ledger->total_discount_amount, $fixDigit);
        } elseif ($ledger->discount_type == SaleTransaction::DISCOUNT_TYPE_AMOUNT) {
            $ledger->total_discount_amount = round(($ledger->total_discount_amount), $fixDigit);
            $discountWithAmount = round(($ledger->rpu_without_gst - $ledger->total_discount_amount), $fixDigit);
            $ledger->total = round($discountWithAmount, $fixDigit);
        } else {
            $ledger->total = round($ledger->rpu_without_gst, $fixDigit);
        }

        if (! $transaction['is_gst_na']) {
            if ($gstData['isGSTCalculated'] && ! $transaction['is_gst_na']) {
                if ($gstData['gstType'] == 'CGST') {
                    $ledger->classification_igst_tax = 0;
                    $ledger->classification_cgst_tax = round(($ledger->total * $ledger->gst_tax_percentage) / 200, $fixDigit);
                    $ledger->classification_sgst_tax = round(($ledger->total * $ledger->gst_tax_percentage) / 200, $fixDigit);
                } else {
                    $ledger->classification_igst_tax = round(($ledger->total * $ledger->gst_tax_percentage) / 100, $fixDigit);
                    $ledger->classification_cgst_tax = 0;
                    $ledger->classification_sgst_tax = 0;
                }
            } else {
                $ledger->rpu_without_gst = round($ledger->rpu_without_gst, $fixDigit);
                $ledger->classification_igst_tax = 0;
                $ledger->classification_cgst_tax = 0;
                $ledger->classification_sgst_tax = 0;
            }
        }

        return $ledger;
    }

    public function getGSTCalculationDetails($classificationNatureType, $isRcmApplicable): array
    {
        $isGSTCalculated = false;
        $gstType = false;
        if (\in_array($classificationNatureType, intraStateArray())) {
            if ($isRcmApplicable) {
                $isGSTCalculated = false;
            } else {
                $isGSTCalculated = true;
                $gstType = 'CGST';
            }
        } elseif (\in_array($classificationNatureType, interStateArray())) {
            if ($isRcmApplicable) {
                $isGSTCalculated = false;
            } else {
                $isGSTCalculated = true;
                $gstType = 'IGST';
            }
        } elseif (\in_array($classificationNatureType, exportArray()) || \in_array($classificationNatureType, sezArray())) {
            $isGSTCalculated = true;
            $gstType = 'IGST';
        } elseif ($classificationNatureType == 'Deemed Export - Intrastate') {
            $isGSTCalculated = true;
            $gstType = 'CGST';
        } elseif ($classificationNatureType == 'Deemed Export - Interstate') {
            $isGSTCalculated = true;
            $gstType = 'IGST';
        }

        return [
            'isGSTCalculated' => $isGSTCalculated,
            'gstType' => $gstType,
        ];
    }

    public function tcsCalculate($transaction, $itemCollectData)
    {
        $taxableValues = array_column($itemCollectData, 'total');
        $totalSubTotal = array_sum($taxableValues);

        $fixDigit = getCompanyFixedDigitNumber();

        // additional charges
        $totalSubTotal += $transaction->additionalCharges->sum('total_without_tax') ?? 0;

        $totalSubTotal += $transaction->cess;
        $totalSubTotal += $transaction->cgst;
        $totalSubTotal += $transaction->sgst;
        $totalSubTotal += $transaction->igst;

        $tcsAmount = round(($totalSubTotal * $transaction->tcs_rate) / 100, $fixDigit);

        return $tcsAmount;
    }
}
