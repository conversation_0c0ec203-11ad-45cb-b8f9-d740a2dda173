<?php

namespace App\Repositories;

use App\Actions\v1\PdfConfiguration\GetPdfAdjustmentsAction;
use App\Http\Controllers\CompanyController;
use App\Models\DeliveryChallanTransaction;
use App\Models\EwayBill;
use App\Models\EwayBillAPICredentials;
use App\Models\Ledger;
use App\Models\LockTransaction;
use App\Models\PurchaseReturnTransaction;
use App\Models\SaleReturnTransaction;
use App\Models\SaleTransaction;
use App\Services\GSTService;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Exception;
use Illuminate\Container\Container as Application;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;
use Picqer\Barcode\BarcodeGeneratorPNG;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

/**
 * Class EWayBillRepository
 */
class EWayBillRepository extends BaseRepository
{
    /** @var GSTService */
    public mixed $GSTService;

    public function __construct(Application $app)
    {
        parent::__construct($app);

        $this->GSTService = app(GSTService::class);
    }

    public array $fieldSearchable = [];

    /**
     * Return searchable fields
     */
    public function getFieldsSearchable(): array
    {
        return $this->fieldSearchable;
    }

    /**
     * Configure the Model
     **/
    public function model(): string
    {
        return Ledger::class;
    }

    /**
     * @throws Exception
     */
    public function generateEWayBill(Request $request, $transactionId)
    {
        $company = getCurrentCompany();

        if ($company === null) {
            throw new UnprocessableEntityHttpException('You have not logged in with company.');
        }

        $fromGstIn = $company->companyTax->gstin ?? '';
        $input = $request->all();

        if (empty($fromGstIn) || $fromGstIn == '') {
            throw new UnprocessableEntityHttpException('From GSTIN is compulsory for generate E-Way Bill.');
        }

        /** @var EwayBillAPICredentials $ewayBillCred */
        $ewayBillCred = EwayBillAPICredentials::first();
        if (empty($ewayBillCred) || $ewayBillCred->ewbuser == '' || $ewayBillCred->ewbpwd == '') {
            throw new UnprocessableEntityHttpException('Company have not fill up e-way bill ID and password.');
        }

        if ($input['eway_bill_transaction_type'] == EwayBill::PURCHASE_RETURN) {
            $transaction = PurchaseReturnTransaction::whereId($transactionId)->firstOrFail();
            $customerSupplier = $transaction->supplier;
            $docDate = $transaction->voucher_date;
        } elseif ($input['eway_bill_transaction_type'] == EwayBill::DELIVERY_CHALLAN) {
            $transaction = DeliveryChallanTransaction::whereId($transactionId)->firstOrFail();
            $customerSupplier = $transaction->party;
            $docDate = $transaction->challan_date;
        } else {
            if ($input['eway_bill_transaction_type'] == EwayBill::SALE_RETURN) {
                $transaction = SaleReturnTransaction::whereId($transactionId)->firstOrFail();
            } else {
                $transaction = SaleTransaction::whereId($transactionId)->firstOrFail();
            }
            $customerSupplier = $transaction->customer;
            $docDate = $transaction->date;
        }

        $saleItemData = [];
        foreach ($input['product_name'] as $key => $item) {
            $cgstSgstRate = explode('+', $input['cgst_sgst'][$key])[0] ?? 0.00;
            $saleItemData[] = [
                'productName' => $input['product_name'][$key] ?? '',
                // "type": "string","maxLength": 100, "description": "Product / Item Name"
                'productDesc' => $input['product_description'][$key] ?? '',
                // "type": "string", "maxLength": 100, "description": "Product / Item description"
                'hsnCode' => (int) $input['product_hsn'][$key] ?? '',
                // "type": "number", "description": "HSN Code"
                'quantity' => (float) ($input['quantity'][$key] ?? 0.00), // "type": "number", "description":"Quantity"
                'qtyUnit' => isset($input['quantity_unit'][$key]) ? getForGSTItemUnit($input['quantity_unit'][$key]) : '',
                // "type": "string", "maxLength": 3, "minLength": 3, "description": "Unit"
                'taxableAmount' => (float) ($input['taxable_value'][$key] ?? 0.00),
                // "type": "number", "multipleOf": 0.01, "description":"Taxable Amount"
                'cgstRate' => (float) $cgstSgstRate,
                // "type": "number", "multipleOf": 0.001, "description":"CGST Rate of Tax"
                'sgstRate' => (float) $cgstSgstRate,
                // "type": "number", "multipleOf": 0.001, "description":"SGST Rate of Tax"
                'igstRate' => (float) ($input['igst'][$key] ?? 0.00),
                // "type": "number", "multipleOf": 0.001, "description":"IGST Rate of Tax"
                'cessRate' => (float) ($input['cess_rate'][$key] ?? 0.00),
                // "type": "number", "multipleOf": 0.001, "description":"CESS Rate of Tax"
                'cessNonadvol' => (float) ($input['cess_non_advol_rate'][$key] ?? 0),
                // "type": "number", "description":"Cess Non-Advolerum"
            ];
        }

        $isPartB = false;
        if (isset($input['eway_bill_part_b']) && $input['eway_bill_part_b'] == 1) {
            if ($input['transporter_mode'] == '1') { // Road
                $tranDocNo = $input['transporter_no'];
                $transDocDate = Carbon::parse($input['transporter_date'])->format('d/m/Y');
            } elseif ($input['transporter_mode'] == '2') { // Rail
                $tranDocNo = $input['rr_no'];
                $transDocDate = Carbon::parse($input['rr_date'])->format('d/m/Y');
            } elseif ($input['transporter_mode'] == '3') { // Air
                $tranDocNo = $input['air_no'];
                $transDocDate = Carbon::parse($input['air_date'])->format('d/m/Y');
            } elseif ($input['transporter_mode'] == '4') { // Ship or Ship Cum Road/Rail
                $tranDocNo = $input['bill_landing_no'];
                $transDocDate = Carbon::parse($input['bill_landing_date'])->format('d/m/Y');
            } else {
                $tranDocNo = '';
                $transDocDate = Carbon::parse($transaction->transporter_document_date)->format('d/m/Y') ?? '';
            }
            $isPartB = true;
        }

        /** @var CompanyController $companyController */
        $companyController = App::make(CompanyController::class);
        $gstResponse = $companyController->getGstInformation($transaction->gstin);
        $gstInformation = $gstResponse->getData();

        if ($gstInformation->success) {
            $decodedData = json_decode($gstInformation->data);
            $result = $decodedData->result ?? null;
        }

        if (isset($result->dty) && $result->dty === 'SEZ Unit') {
            $toStateCode = 96;
        } else {
            $toStateCode = getStateCode(/* $input['to_state'] ?? */ $transaction->billingAddress->state_id ?? $customerSupplier->model->billingAddress->state_id ?? null);
        }

        $ewayBillData = [
            /** @Details Basic Details */
            'supplyType' => $input['supply_type'] ?? '',
            // "type": "string", "maxLength": 1, "minLength": 1, "enum": [ "O","I" ], "description": "Supply Type", Only alpha Character
            'subSupplyType' => (int) $input['sub_type'] ?? '',
            // "type": "string", "description": "Sub Supply Type"
            'subSupplyDesc' => $input['sub_type_other_description'] ?? '',
            // "type": "string", "maxLength": 20, "description": "Other Sub Supply Description"
            'docType' => ! empty($input['document_type']) ? $input['document_type'] : 'INV',
            // "type": "string", "enum": [ "INV", "CHL", "BIL","BOE","CNT","OTH" ], "description": "Document Type"
            'docNo' => $input['document_no'],
            // "type": "string", "maxLength": 16, "description": "Document Number (Alphanumeric with / and - are allowed)"
            'docDate' => Carbon::parse($docDate)->format('d/m/Y') ?? '',
            // "type": "string", "pattern": "[0-3][0-9]/[0-1][0-9]/[2][0][1-2][0-9]", "description": "Document Date"
            'transactionType' => $input['transaction_type'] ?? '', // To be discuss with GST API provider

            /** @Details From Details */
            'fromTrdName' => $input['bill_from_name'] ?? $company->trade_name,
            // "type": "string", "maxLength": 100, "description": "From Trade Name (Consignor Trade name)"
            'fromGstin' => $input['bill_from_gstin'] ?? $company->companyTax->gstin,
            // "type": "string", "maxLength": 15, "minLength": 15, "pattern": "[0-9]{2}[0-9|A-Z]{13}", "description": "From GSTIN (Supplier or Consignor)"
            // 'fromStateCode' => (int) getStateCode(/* $input['from_state'] ?? */ $company->billingAddress->state_id ?? null), // Need to change after dispatch address implementation.
            'fromStateCode' => (int) getStateCode($transaction->dispatchAddress->state_id ?? $company->billingAddress->state_id ?? null),
            // "type": "integer", "maximum": 99,  "description": "From State Code"
            'actFromStateCode' => (int) getStateCode($input['from_state'] ?? $transaction->dispatchAddress->state_id ?? $company->billingAddress->state_id ?? null),
            // "type": "integer", "maximum": 99, "description": "Actual From State Code"
            'fromAddr1' => $transaction->dispatchAddress->address_1 ?? $company->billingAddress->address_1 ?? '',
            // "type": "string", "maxLength": 120, "description": "From Address Line 1 (Valid Special Chars #,-,/)"
            'fromAddr2' => $transaction->dispatchAddress->address_2 ?? $company->billingAddress->address_2 ?? '',
            // "type": "string", "maxLength": 120, "description": "From Address Line 1 (Valid Special Chars #,-,/)"
            'fromPlace' => getCityName($input['from_place'] ?? $transaction->dispatchAddress->city_id ?? $company->billingAddress->city_id ?? ''),
            // "type": "string", "maxLength": 50, "description": "From Place"
            'fromPincode' => (int) $input['from_pincode'] ?? $transaction->dispatchAddress->pin_code ?? $company->billingAddress->pin_code ?? '',
            // "type": "integer", "maximum": 999999, "minimum": 100000, "description": "From Pincode"
            'dispatchFromGSTIN' => '',
            'dispatchFromTradeName' => '',

            /** @Details To Details */
            'toTrdName' => $customerSupplier->name,
            // "type": "string","maxLength": 100, "description": "To Trade Name (Consignee Trade name or Recipient Trade name)"
            'toGstin' => $input['bill_to_gstin'] ?? $transaction->gstin ?? $customerSupplier->model->gstin ?? 'URP',
            // "type": "string","maxLength": 15, "minLength":15, "pattern": "[0-9]{2}[0-9|A-Z]{13}", "description": "To GSTIN (Consignee or Recipient)"
            'toStateCode' => (int) $toStateCode,
            // "type": "integer", "maximum": 99,  "description": "To State Code"
            'actToStateCode' => (int) getStateCode($input['to_state'] ?? $transaction->billingAddress->state_id ?? $customerSupplier->model->billingAddress->state_id ?? null),
            // "type": "integer", "maximum": 99, "description": "Actual To State Code"
            'toAddr1' => $input['to_address_1'] ?? $transaction->billingAddress->address_1 ?? $customerSupplier->model->billingAddress->address_1 ?? '',
            // "type": "string","maxLength": 120, "description": "To Address Line 1 (Valid Special Chars #,-,/)"
            'toAddr2' => $input['to_address_2'] ?? $transaction->billingAddress->address_2 ?? $customerSupplier->model->billingAddress->address_2 ?? '',
            // "type": "string","maxLength": 120, "description": "To Address Line 1 (Valid Special Chars #,-,/)"
            'toPlace' => getCityName($input['to_city'] ?? $transaction->billingAddress->city_id ?? $customerSupplier->model->billingAddress->city_id ?? null),
            // "type": "string", "maxLength": 50, "description": "To Place"
            'toPincode' => (int) $input['to_pincode'] ?? $transaction->billingAddress->pin_code ?? $customerSupplier->model->billingAddress->pin_code ?? '',
            // "type": "integer", "maximum": 999999, "minimum": 100000, "description": "To Pincode"
            'shipToTradeName' => '',
            'shipToGSTIN' => '',

            /** @Details Invoice Details */
            'cgstValue' => (float) $transaction->cgst ?? 0.00,
            // "type": "number", "multipleOf": 0.01, "description": "CGST value
            'sgstValue' => (float) $transaction->sgst ?? 0.00,
            // "type": "number", "multipleOf": 0.01, "description": "SGST value
            'igstValue' => (float) $transaction->igst ?? 0.00,
            // "type": "number", "multipleOf": 0.01, "description": "IGST value
            'cessValue' => (float) $transaction->cess ?? 0.00,
            // "type": "number", "multipleOf": 0.01, "description": "CESS value
            'cessNonAdvolValue' => (float) $input['total_cess_non_advol'] ?? 0.00,
            // "type": "number", "multipleOf": 0.01, "description": "CESS Advol value
            'otherValue' => (float) $input['total_other_amount'] ?? 0.00,
            // "type": "number", "multipleOf": 0.01, "description": "OTHE amount value
            // Comment due to add additional charges & add-less charges in other amount field. 'totalValue' => (float) $transaction->taxable_value ?? 0.00,
            'totalValue' => (float) $transaction->gross_value ?? 0.00,
            // "type": "number", "multipleOf": 0.01, "description": " Taxable value "
            'totInvValue' => (float) $input['total_invoice_amount'] ?? 0.00,
            // "type": "number","multipleOf": 0.01, "description": "Total Invoice Value (Including taxable value, tax value,and other charges if any)"

            /** @Details Transport Details Part A */

            // 'transporterId' => $isPartB ? '' : ($input['transporter_id'] ?? ''),
            /* Change condition after discuss with @gopal bhai on 21-05-2024 for client requirement. */
            'transporterId' => $input['transporter_id'] ?? '',
            // "type": "string", "pattern": "[0-9]{2}[0-9|A-Z]{13}", "description": "15 Digit Transporter GSTIN/TRANSIN"

            // 'transporterName' => $input['transporter_name'] ?? '',
            /* Change condition after discuss with @gopal bhai on 21-05-2024 for client requirement. */
            'transporterName' => $input['transporter_name'] ?? '',
            // "type": "string", "maxLength": 100,"description": "Name of the transporter"
            'transDistance' => ((float) $input['distance'] ?? ''),
            // "type": "string",  "description": "Distance (<4000 km) "

            /** @Details  Transport Details Part B */
            'transMode' => $isPartB ? ($input['transporter_mode'] ?? '') : '',
            // "type": "string","enum": ["1","2","3","4"], "description": "Mode of transport (Road-1, Rail-2, Air-3, Ship-4) "
            'transDocNo' => $isPartB ? ($tranDocNo ?? '') : '',
            // "type": "string", "maxLength": 15, "description": "Transport Document Number (Alphanumeric with / and – are allowed)"
            'transDocDate' => $isPartB ? ($transDocDate ?? '') : '',
            // "type": "string", "description": "Transport Document Date"
            'vehicleNo' => $isPartB ? ($input['vehicle_no'] ?? '') : '',
            // "type": "string", "minLength": 7, "maxLength": 15, "description": "Vehicle Number"
            'vehicleType' => $isPartB ? ($input['vehicle_type'] ?? '') : '',
            // To be discuss with GST API Provider and change it accordingly to discussion
            // "type": "string", "description": "Vehicle Type"

            /** @Details  Items Details */
            'itemList' => $saleItemData,
            // Item List
        ];

        $headers = [
            'ewbuser' => $ewayBillCred->ewbuser,
            'ewbpwd' => $ewayBillCred->ewbpwd,
            'gstin' => $company->companyTax->gstin,
        ];

        $response = $this->GSTService->generateEWayBill($ewayBillData, $headers);
        Log::info("Company Name: {$company->trade_name} {$company->id} ".json_encode($response));

        try {
            if (! isset($response['success'])) {
                return [
                    'success' => false,
                    'message' => $response['message'],
                ];
            }
            // If any errors are occurred then throw the error message
            if (! $response['success']) {
                return $response;
            }

            $meta = [
                'data' => $ewayBillData,
                'response' => $response,
            ];

            $result = (array) $response['result'];
            $ewayBillDate = explode(' ', $result['ewayBillDate'])[0] ?? $transaction->date;
            $ewayBillDate = Carbon::createFromFormat('d/m/Y', $ewayBillDate)->format('Y-m-d');

            $validUpto = explode(' ', $result['validUpto'])[0] ?? '';
            $validUpto = $validUpto != '' ? Carbon::createFromFormat('d/m/Y', $validUpto)->format('Y-m-d') : '';
            $eWayBill = EwayBill::create([
                'company_id' => $company->id,
                'eway_bill_no' => $result['ewayBillNo'],
                'eway_bill_date' => $ewayBillDate,
                'valid_upto' => $validUpto,
                'supply_type' => $input['supply_type'],
                'sub_type' => $input['sub_type'],
                'bill_type' => $input['document_type'],
                'transaction_id' => $transactionId,
                'transaction_type' => get_class($transaction),
                'meta' => json_encode($meta),
            ]);

            $response['eway_bill_id'] = $eWayBill->id;

            return $response;
        } catch (\Exception $e) {
            Log::info("Company Name: {$company->trade_name} {$company->id} ".$e->getMessage());
            Log::info($e);
        }
    }

    /**
     * @return mixed
     */
    public function cancelEwayBill(Request $request, $id)
    {
        /** @var EwayBill $ewayBill */
        $ewayBill = EwayBill::findOrFail($id);
        $input = $request->all();

        $company = getCurrentCompany();
        /** @var EwayBillAPICredentials $ewayBillCred */
        $ewayBillCred = EwayBillAPICredentials::first();

        $headers = [
            'ewbuser' => $ewayBillCred->ewbuser,
            'ewbpwd' => $ewayBillCred->ewbpwd,
            'gstin' => $company->companyTax->gstin,
        ];

        $cancelEwayBillData = [
            'ewbNo' => $ewayBill->eway_bill_no,
            'cancelRsnCode' => $input['reason'] ?? 2,
            'cancelRmrk' => $input['cancel_remark'],
        ];

        $response = $this->GSTService->cancelEWayBill($cancelEwayBillData, $headers);

        if (! isset($response['success'])) {
            return [
                'success' => false,
                'message' => 'EwayBill not cancelled due to some error. Please try again after some time.',
            ];
        }

        // If any errors are occurred then throw the error message
        if (! $response['success']) {
            return $response;
        }

        $ewayBill->update([
            'is_canceled' => true,
            'meta' => json_encode(array_merge((array) json_decode($ewayBill->meta),
                ['cancelResponse' => $response])),
        ]);

        return $response;
    }

    public function prepareDataForTable($rows): array
    {
        $data = [];
        $incomeTransactionLockDate = getTransactionsLockDate()[LockTransaction::INCOME] ?? null;
        $expenseTransactionLockDate = getTransactionsLockDate()[LockTransaction::EXPENSE] ?? null;
        /** @var EwayBill $row */
        foreach ($rows as $row) {
            $meta = json_decode($row->meta);
            if ($row->transaction_type == SaleTransaction::class) {
                $invoiceNumber = $row->saleTransaction->full_invoice_number ?? '';
                $invoiceDate = $row->saleTransaction->date ?? '';
                $invoiceType = 'Sale';
                $invoiceUrl = route('company.sales.edit', $row->transaction_id) ?? '';
                $partyName = $row->saleTransaction->customer->name ?? '';
                $invoiceValue = $row->saleTransaction->grand_total ?? 0;
                $isLocked = ! empty($row->saleTransaction->date) ? ! empty($incomeTransactionLockDate) && Carbon::parse($incomeTransactionLockDate)->greaterThanOrEqualTo($row->saleTransaction->date) : false;
                $lockDate = $incomeTransactionLockDate;
            } elseif ($row->transaction_type == DeliveryChallanTransaction::class) {
                $invoiceNumber = $row->deliveryChallanTransaction->challan_number ?? '';
                $invoiceDate = $row->deliveryChallanTransaction->challan_date ?? '';
                $invoiceType = 'Delivery Challan';
                $invoiceUrl = route('company.deilvery-challan.edit', ['delivery_challan' => $row->transaction_id]);
                $partyName = $row->deliveryChallanTransaction->party->name ?? '';
                $invoiceValue = $row->deliveryChallanTransaction->grand_total ?? 0;
                $isLocked = ! empty($row->deliveryChallanTransaction->challan_date) ? ! empty($incomeTransactionLockDate) && Carbon::parse($incomeTransactionLockDate)->greaterThanOrEqualTo($row->deliveryChallanTransaction->challan_date) : false;
                $lockDate = $incomeTransactionLockDate;
            } elseif ($row->transaction_type == PurchaseReturnTransaction::class) {
                $invoiceNumber = $row->purchaseReturnTransaction->voucher_number ?? '';
                $invoiceDate = $row->purchaseReturnTransaction->voucher_date ?? '';
                $invoiceType = 'Purchase Return';
                $invoiceUrl = route('company.purchase-returns.edit', $row->transaction_id);
                $partyName = $row->purchaseReturnTransaction->supplier->name ?? '';
                $invoiceValue = $row->purchaseReturnTransaction->grand_total ?? 0;
                $isLocked = ! empty($row->purchaseReturnTransaction->voucher_date) ? ! empty($expenseTransactionLockDate) && Carbon::parse($expenseTransactionLockDate)->greaterThanOrEqualTo($row->purchaseReturnTransaction->voucher_date) : false;
                $lockDate = $expenseTransactionLockDate;
            } elseif ($row->transaction_type == SaleReturnTransaction::class) {
                $invoiceNumber = $row->saleReturnTransaction->full_invoice_number ?? '';
                $invoiceDate = $row->saleReturnTransaction->date ?? '';
                $invoiceType = 'Sale Return';
                $invoiceUrl = route('company.sale-returns.edit', $row->transaction_id);
                $partyName = $row->saleReturnTransaction->customer->name ?? '';
                $invoiceValue = $row->saleReturnTransaction->grand_total ?? 0;
                $isLocked = ! empty($row->saleReturnTransaction->date) ? ! empty($incomeTransactionLockDate) && Carbon::parse($incomeTransactionLockDate)->greaterThanOrEqualTo($row->saleReturnTransaction->date) : false;
                $lockDate = $incomeTransactionLockDate;
            }

            $data[] = [
                'id' => $row->id,
                'transaction_type' => $invoiceType,
                'invoice_number' => $invoiceNumber,
                'invoice_date' => Carbon::parse($invoiceDate)->format('d-m-Y'),
                'party_name' => $row->response->result->toTrdName ?? $partyName,
                'invoice_value' => ! empty($invoiceValue) ? $invoiceValue : 0,
                'supply_type' => $row->supply_type == 'O' ? 'Outward' : 'Inward',
                'eway_bill_number' => $row->eway_bill_no,
                'eway_bill_date' => $meta->response->result->ewayBillDate ?? $row->eway_bill_date ?? '',
                'valid_upto_date' => $meta->response->result->validUpto ?? $row->valid_upto ?? '',
                'transaction_id' => $row->transaction_id,
                'cancel' => $row->is_canceled,
                'invoice_url' => $invoiceUrl,
                'is_locked' => $isLocked,
                'lock_date' => $lockDate ?? null,
            ];
        }

        return $data;
    }

    public function getDistanceFromPincode($request)
    {
        $input = $request->all();
        $company = getCurrentCompany();

        $headers = [
            'CLIENTGSTIN' => $company->companyTax->gstin,
            'P1' => $input['from_pincode'],
            'P2' => $input['to_pincode'],
        ];

        return $this->GSTService->getDistanceFromPincode($headers);
    }

    public function getEwaybillPdf($ewayBillId)
    {
        /** @var EwayBillAPICredentials $ewayBillCred */
        $ewayBillCred = EwayBillAPICredentials::first();
        $company = getCurrentCompany();

        $ewayBill = EwayBill::findOrFail($ewayBillId);

        $headers = [
            'gstin' => $company->companyTax->gstin,
            'ewbuser' => $ewayBillCred->ewbuser,
            'ewbpwd' => $ewayBillCred->ewbpwd,
            'data' => $ewayBill->eway_bill_no,
            'invoice_number' => json_decode($ewayBill['meta'])->data->docNo,
        ];

        return $this->GSTService->getEwaybillPdf($headers);
    }

    public function getEwaybillDeatilsPdf($ewayBillId)
    {
        $company = getCurrentCompany();

        $ewayBill = EwayBill::findOrFail($ewayBillId);
        $data['ewayBill'] = $this->prepareEwayPdfData($ewayBill);
        $data['currentCompany'] = $company;
        $data['customFontSize'] = GetPdfAdjustmentsAction::run(false);

        $customPaperSize = [0, 0, 700, 900];
        $fileName = $ewayBill->eway_bill_no.'_'.json_decode($ewayBill['meta'])->data->docNo;
        $fileName = replaceSpecialCharacters($fileName);
        $data['fileName'] = $fileName;

        $pdf = Pdf::loadView('company.pdf.e-way-bill.e-way-bill', $data)->setPaper($customPaperSize);

        return $pdf->download($fileName.'.pdf');
    }

    public function prepareEwayPdfData($ewayBill)
    {
        $ewayBillDetails = json_decode($ewayBill->meta);

        $items = $ewayBillDetails->data->itemList;
        $ewayBillItems = [];
        if (count($items) > 0) {
            foreach ($items as $item) {
                $ewayBillItems[] = [
                    'hsn_code' => $item->hsnCode ?? '',
                    'product_name' => $item->productName ?? '',
                    'product_description' => $item->productDesc ?? '',
                    'quantity' => $item->quantity ?? 0,
                    'unit' => $item->qtyUnit ?? '',
                    'taxable_amount' => $item->taxableAmount ?? 0,
                    'cgst' => $item->cgstRate ?? 0,
                    'sgst' => $item->sgstRate ?? 0,
                    'igst' => $item->igstRate ?? 0,
                    'cess' => $item->cessRate ?? 0,
                    'cess_nonadvol' => $item->cessNonadvol ?? 0,
                ];
            }
        }

        $barcode = '';
        if (! empty($ewayBill->eway_bill_no)) {
            $generator = new BarcodeGeneratorPNG;
            $barcodeData = $generator->getBarcode($ewayBill->eway_bill_no, $generator::TYPE_CODE_128);
            $barcode = base64_encode($barcodeData);
        }

        $stateCodeArray = SaleTransaction::STATE_CODE;
        $fromState = isset($ewayBillDetails->data->actFromStateCode) ? getStateName(array_search($ewayBillDetails->data->actFromStateCode, $stateCodeArray)) : null;
        $toState = isset($ewayBillDetails->data->actToStateCode) ? getStateName(array_search($ewayBillDetails->data->actToStateCode, $stateCodeArray)) : null;

        $transDistance = $ewayBillDetails->data->transDistance ?? 0;
        if ($transDistance == 0) {
            $headers = [
                'CLIENTGSTIN' => $ewayBillDetails->data->fromGstin,
                'P1' => $ewayBillDetails->data->fromPincode,
                'P2' => $ewayBillDetails->data->toPincode,
            ];

            $distance = $this->GSTService->getDistanceFromPincode($headers);
            $transDistance = isset($distance['result']) && isset($distance['result']->accurateDistance) ? $distance['result']->accurateDistance : 0;
        }

        if ($ewayBill->transaction_type == SaleTransaction::class) {
            $transaction = SaleTransaction::find($ewayBill->transaction_id);
        } elseif ($ewayBill->transaction_type == DeliveryChallanTransaction::class) {
            $transaction = DeliveryChallanTransaction::find($ewayBill->transaction_id);
        } else {
            $transaction = PurchaseReturnTransaction::find($ewayBill->transaction_id);
        }

        return [
            'eway_bill_no' => $ewayBill->eway_bill_no,
            'bill_type' => $ewayBill->bill_type,
            'supply_type' => $ewayBill->supply_type ? ($ewayBill->supply_type == 'O' ? 'Outward' : 'Inward') : '',
            'sub_type' => $ewayBill->sub_type,
            'is_canceled' => $ewayBill->is_canceled,
            'eway_bill_date' => $ewayBillDetails->response->result->ewayBillDate ? Carbon::createFromFormat('d/m/Y h:i:s A', $ewayBillDetails->response->result->ewayBillDate)->format('d/m/Y h:i A') : '',
            'valid_upto' => $ewayBillDetails->response->result->validUpto ? Carbon::createFromFormat('d/m/Y h:i:s A', $ewayBillDetails->response->result->validUpto)->format('d/m/Y') : '',
            'trans_mode' => isset($ewayBillDetails->data->transMode) && isset(SaleTransaction::TRANSPORTER_MODE[$ewayBillDetails->data->transMode]) ? SaleTransaction::TRANSPORTER_MODE[$ewayBillDetails->data->transMode] : '',
            'trans_distance' => $transDistance,
            'doc_no' => $ewayBillDetails->data->docNo ?? '',
            'doc_date' => ! empty($ewayBillDetails->data->docDate) ? (
                Carbon::hasFormat($ewayBillDetails->data->docDate, 'd/m/Y h:i:s A')
                    ? Carbon::createFromFormat('d/m/Y h:i:s A', $ewayBillDetails->data->docDate)->format('d/m/Y')
                    : Carbon::createFromFormat('d/m/Y', $ewayBillDetails->data->docDate)->format('d/m/Y')
            ) : '',
            'doc_type' => isset($ewayBillDetails->data->docType) && isset(SaleTransaction::DOCUMENT_TYPE[$ewayBillDetails->data->docType])
                ? SaleTransaction::DOCUMENT_TYPE[$ewayBillDetails->data->docType] : '',
            'transaction_type' => isset($ewayBillDetails->data->transactionType) && isset(SaleTransaction::E_BILL_TRANSACTION_TYPE_ARR[$ewayBillDetails->data->transactionType])
                ? SaleTransaction::E_BILL_TRANSACTION_TYPE_ARR[$ewayBillDetails->data->transactionType] : '',
            'from_gstin' => $ewayBillDetails->data->fromGstin ?? '',
            'from_trd_name' => $ewayBillDetails->data->fromTrdName ?? '',
            'from_address' => (isset($ewayBillDetails->data->fromAddr1) ? $ewayBillDetails->data->fromAddr1 : '').
                (isset($ewayBillDetails->data->fromAddr2) ? ',<br>'.$ewayBillDetails->data->fromAddr2 : '').
                (isset($ewayBillDetails->data->fromPlace) ? ',<br>'.$ewayBillDetails->data->fromPlace : '').
                (isset($ewayBillDetails->data->actFromStateCode) ? ', '.$fromState : '').
                (isset($ewayBillDetails->data->fromPincode) ? ' - '.$ewayBillDetails->data->fromPincode : ''),
            'to_gstin' => $ewayBillDetails->data->toGstin ?? '',
            'to_trd_name' => $ewayBillDetails->data->toTrdName ?? '',
            'to_address' => (isset($ewayBillDetails->data->toAddr1) ? $ewayBillDetails->data->toAddr1 : '').
                (isset($ewayBillDetails->data->toAddr2) ? ',<br>'.$ewayBillDetails->data->toAddr2 : '').
                (isset($ewayBillDetails->data->toPlace) ? ',<br>'.$ewayBillDetails->data->toPlace : '').
                (isset($ewayBillDetails->data->actToStateCode) ? ', '.$toState : '').
                (isset($ewayBillDetails->data->toPincode) ? ' - '.$ewayBillDetails->data->toPincode : ''),
            'from_state' => isset($ewayBillDetails->data->actFromStateCode) ? $fromState : '',
            'to_state' => isset($ewayBillDetails->data->actToStateCode) ? $toState : '',
            'items' => $ewayBillItems,
            'total_invoice_value' => $ewayBillDetails->data->totInvValue ?? 0,
            'other_value' => $ewayBillDetails->data->otherValue ?? 0,
            'transporter_id' => $ewayBillDetails->data->transporterId ?? '',
            'transporter_name' => $ewayBillDetails->data->transporterName ?? '',
            'transporter_doc_no' => empty($ewayBillDetails->data->transDocNo) ? (isset($transaction['transporter_document_number']) && ! empty($transaction['transporter_document_number']) ? $transaction['transporter_document_number'] : '') : $ewayBillDetails->data->transDocNo,
            'transporter_doc_date' => empty($ewayBillDetails->data->transDocDate) ? $this->parseDate($transaction['transporter_document_date'] ?? '') : $this->parseDate($ewayBillDetails->data->transDocDate),
            'vehicle_no' => $ewayBillDetails->data->vehicleNo ?? '',
            'from_place' => $ewayBillDetails->data->fromPlace ?? '',
            'barcode' => $barcode,
            'cewb_no' => null,
            'multi_veh_info' => null,
        ];
    }

    public function parseDate($date)
    {
        try {
            return Carbon::parse($date)->format('d/m/Y');
        } catch (\Exception $e) {
            try {
                return Carbon::createFromFormat('Y-m-d', $date)->format('d/m/Y');
            } catch (\Exception $e) {
                try {
                    return Carbon::createFromFormat('d-m-Y', $date)->format('d/m/Y');
                } catch (\Exception $e) {
                    return '';
                }
            }
        }
    }
}
