<?php

namespace App\Repositories\v1;

use App\Actions\CommonAction\CheckPartyLedgerMobileNumberAction;
use App\Actions\CommonAction\DirectCustomerSupplierCreate;
use App\Actions\CustomFields\StoreTransactionCustomFieldsAction;
use App\Actions\CustomFields\UpdateTransactionCustomFieldsAction;
use App\Actions\CustomFieldsItemMaster\Inventory\CalculateAvailableQTY;
use App\Actions\CustomFieldsItemMaster\Inventory\StoreItemCFInventoryAction;
use App\Actions\CustomFieldsItemMaster\Inventory\UpdateItemCFInventoryAction;
use App\Actions\CustomFieldsItemMaster\Transaction\StoreCFTransactionWiseAction;
use App\Actions\CustomFieldsItemMaster\Transaction\UpdateCFTransactionWiseAction;
use App\Models\AdditionalChargesForIncomeEstimateQuoteTransaction;
use App\Models\AddLessForIncomeEstimateQuoteTransaction;
use App\Models\GstTax;
use App\Models\IncomeEstimateQuoteAccountingInvoice;
use App\Models\IncomeEstimateQuoteItemInvoice;
use App\Models\IncomeEstimateQuoteTransaction;
use App\Models\ItemCustomField;
use App\Models\ItemCustomFieldCombinationInventory;
use App\Models\ItemCustomFieldSetting;
use App\Models\Ledger;
use App\Models\Master\ItemMaster;
use App\Models\TaxClassificationDetails;
use App\Models\TransactionCustomField;
use App\Repositories\API\v1\BaseAPIRepository;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

/**
 * Interface IncomeEstimateQuoteTransactionRepository.
 */
class IncomeEstimateQuoteTransactionRepository extends BaseAPIRepository
{
    public $classificationNatureType = null;

    public $isRCMApplicable = false;

    public $isGSTEnabled = null;

    public function model()
    {
        return IncomeEstimateQuoteTransaction::class;
    }

    public function store($input)
    {
        try {
            DB::beginTransaction();
            unset($input['_token']);

            $this->isGSTEnabled = $input['is_gst_enabled'];

            /* If is_create_party is true then create party */
            if (isset($input['is_create_party']) && $input['is_create_party']) {
                if (! getLoginUser()->can('mobile_company_add_new_account_masters')) {
                    throw new UnprocessableEntityHttpException('You do not have permission to create new party.');
                }
                $partyId = DirectCustomerSupplierCreate::run($input['party_ledger_id'], Ledger::CUSTOMER);
                $input['party_ledger_id'] = $partyId;
            }

            /* Ledger mobile number */
            if (isset($input['party_phone_number'])) {
                $ledgerData = [
                    'ledger_id' => $input['party_ledger_id'],
                    'phone_1' => $input['party_phone_number'],
                    'region_iso_1' => $input['region_iso'],
                    'region_code_1' => $input['region_code'],
                ];
                CheckPartyLedgerMobileNumberAction::run($ledgerData);
            }

            /* Prepare Estimate Quote transaction */
            $estimateTransactionData = $this->prepareEstimateQuoteData($input);

            /* Create Estimate Quote */
            $estimateQuoteTransaction = IncomeEstimateQuoteTransaction::create($estimateTransactionData);

            /* Store Estimate Quote Document */
            if (isset($input['estimate_quote_document']) && ! empty($input['estimate_quote_document'])) {
                foreach ($input['estimate_quote_document'] as $image) {
                    $estimateQuoteTransaction->addMedia($image)->toMediaCollection(IncomeEstimateQuoteTransaction::INCOME_ESTIMATE_QUOTE_INVOICE, config('app.media_disc'));
                }
            }

            if ($this->isGSTEnabled) {
                /* Store Estimate Quote items or ledgers based on invoice type */
                $this->classificationNatureType = TaxClassificationDetails::whereName($input['main_classification_nature_type'] ?? null)->first()->id ?? null;
                $this->isRCMApplicable = $input['is_rcm_applicable'] ?? false;
            }

            if ($input['invoice_type'] == IncomeEstimateQuoteTransaction::ITEM_INVOICE) {
                $this->storeItems($input['items'], $estimateQuoteTransaction);
            }
            if ($input['invoice_type'] == IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE) {
                $this->storeLedgers($input['ledgers'], $estimateQuoteTransaction);
            }

            /* Store Addresses */
            $this->storeAddresses($estimateQuoteTransaction, $input);

            /* This is update shipping address id bcz when excel import & frontside local shipping address */
            if (empty($estimateQuoteTransaction->shipping_address_id) && ! empty($estimateQuoteTransaction->shippingAddress)) {
                $estimateQuoteTransaction->update([
                    'shipping_address_id' => $estimateQuoteTransaction->shippingAddress->id,
                ]);
            }

            /* Store Additional Charges */
            if (isset($input['additional_charges']) && count($input['additional_charges']) != 0) {
                $this->storeAdditionalCharges($input['additional_charges'], $estimateQuoteTransaction->id);
            }

            /* Store Add Less */
            if (isset($input['add_less']) && count($input['add_less']) != 0) {
                $this->storeAddLess($input['add_less'], $estimateQuoteTransaction->id);
            }

            /* store Custom Fields */
            if (isset($input['custom_fields']) && count($input['custom_fields']) != 0) {
                StoreTransactionCustomFieldsAction::run(
                    $input['custom_fields'],
                    TransactionCustomField::INCOME_ESTIMATE_QUOTE,
                    $estimateQuoteTransaction->id,
                    IncomeEstimateQuoteTransaction::class
                );
            }

            /* Update Estimate Quote Transaction Status Column Value */
            updateEstimateQuoteStatus($estimateQuoteTransaction->id);

            DB::commit();

            return $estimateQuoteTransaction;
        } catch (Exception $e) {
            DB::rollBack();
            Log::error($e);
            throw $e;
        }
    }

    public function update($input, $estimateTransaction)
    {
        try {
            DB::beginTransaction();

            $this->isGSTEnabled = $input['is_gst_enabled'];

            /* Old Estimate Quote transaction */
            $oldEstimateTransaction = clone $estimateTransaction;

            if (isset($input['is_create_party']) && $input['is_create_party']) {
                if (! getLoginUser()->can('mobile_company_add_new_account_masters')) {
                    throw new UnprocessableEntityHttpException('You do not have permission to create new party.');
                }
                $partyId = DirectCustomerSupplierCreate::run($input['party_ledger_id'], Ledger::CUSTOMER);
                $input['party_ledger_id'] = $partyId;
            }

            /* Ledger mobile number */
            if (isset($input['party_phone_number'])) {
                $ledgerData = [
                    'ledger_id' => $input['party_ledger_id'],
                    'phone_1' => $input['party_phone_number'],
                    'region_iso_1' => $input['region_iso'],
                    'region_code_1' => $input['region_code'],
                ];
                CheckPartyLedgerMobileNumberAction::run($ledgerData);
            }

            /* Prepare Estimate Quote transaction */
            $input['created_at'] = $estimateTransaction->created_at;
            $estimateTransactionData = $this->prepareEstimateQuoteData($input);
            unset($estimateTransactionData['number_for_document']);

            /* Update Estimate Quote transaction */
            /** @var IncomeEstimateQuoteTransaction $estimateTransaction */
            $estimateTransaction->update($estimateTransactionData);

            /* Store Estimate Quote Document */
            if (isset($input['estimate_quote_document']) && ! empty($input['estimate_quote_document'])) {
                foreach ($input['estimate_quote_document'] as $image) {
                    $estimateTransaction->addMedia($image)->toMediaCollection(IncomeEstimateQuoteTransaction::INCOME_ESTIMATE_QUOTE_INVOICE, config('app.media_disc'));
                }
            }

            if ($this->isGSTEnabled) {
                /* Update Estimate Quote items or ledgers based on invoice type */
                $this->classificationNatureType = TaxClassificationDetails::whereName($input['main_classification_nature_type'] ?? null)->first()->id ?? null;
                $this->isRCMApplicable = $input['is_rcm_applicable'] ?? false;
            }

            if ($input['invoice_type'] == IncomeEstimateQuoteTransaction::ITEM_INVOICE && $oldEstimateTransaction->invoice_type == $input['invoice_type']) {
                $this->updateItems($input['items'], $estimateTransaction);
            } elseif ($input['invoice_type'] == IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE && $oldEstimateTransaction->invoice_type == $input['invoice_type']) {
                $this->updateLedgers($input['ledgers'], $estimateTransaction);
            } elseif ($input['invoice_type'] == IncomeEstimateQuoteTransaction::ITEM_INVOICE && $oldEstimateTransaction->invoice_type != $input['invoice_type']) {
                $estimateTransaction->transactionLedgers()->delete();
                $this->storeItems($input['items'], $estimateTransaction);
            } elseif ($input['invoice_type'] == IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE && $oldEstimateTransaction->invoice_type != $input['invoice_type']) {
                $estimateTransaction->transactionItems()->delete();
                $this->storeLedgers($input['ledgers'], $estimateTransaction);
            }

            /* Update Addresses */
            $this->updateAddresses($estimateTransaction, $input);

            /* This is update shipping address id bcz when excel import & frontside local shipping address */
            if (empty($estimateTransaction->shipping_address_id) && ! empty($estimateTransaction->shippingAddress)) {
                $estimateTransaction->update([
                    'shipping_address_id' => $estimateTransaction->shippingAddress->id,
                ]);
            }

            /* Update or Create Additional Charges */
            if (isset($input['additional_charges']) && count($input['additional_charges']) != 0) {
                $this->updateAdditionalCharges($input['additional_charges'], $estimateTransaction->id);
            } else {
                $estimateTransaction->additionalCharges()->delete();
            }

            /* Update or Create Add Less */
            if (isset($input['add_less']) && count($input['add_less']) != 0) {
                $this->updateAddLess($input['add_less'], $estimateTransaction->id);
            } else {
                $estimateTransaction->addLess()->delete();
            }

            /* Update Custom Fields */
            if (isset($input['custom_fields']) && count($input['custom_fields']) != 0) {
                UpdateTransactionCustomFieldsAction::run(
                    $input['custom_fields'],
                    TransactionCustomField::INCOME_ESTIMATE_QUOTE,
                    $estimateTransaction->id,
                    IncomeEstimateQuoteTransaction::class
                );
            } else {
                $estimateTransaction->customFieldValues()->delete();
            }

            /* Update Estimate Quote Transaction Status Column Value */
            updateEstimateQuoteStatus($estimateTransaction->id);

            DB::commit();

            return $estimateTransaction;
        } catch (Exception $e) {
            DB::rollBack();
            Log::error($e);
            throw $e;
        }
    }

    private function prepareEstimateQuoteData($input): array
    {
        /* Destructure input arrays for clarity */
        $otherDetails = $input['other_details'] ?? [];
        $brokerDetails = $input['broker_details'] ?? [];
        $transportDetails = $input['transport_details'] ?? [];
        $ewayBillDetails = $input['eway_bill_details'] ?? [];
        $tcsDetails = $input['tcs_details'] ?? [];
        $tdsDetails = $input['tds_details'] ?? [];

        /* Calculate Valid Till Date */
        $validTillDate = null;
        if (! empty($input['valid_for'])) {
            if ($input['valid_for_type'] == IncomeEstimateQuoteTransaction::CREDIT_PERIOD_TYPE_DAY) {
                $validTillDate = Carbon::parse($input['date'])->addDays($input['valid_for']);
            } else {
                $validTillDate = Carbon::parse($input['date'])->addMonths($input['valid_for']);
            }
        }

        /* calculate credit period date */
        $creditPeriod = $otherDetails['credit_period'] ?? null;
        $creditPeriodType = $otherDetails['credit_period_type'] ?? null;
        $invoiceDate = Carbon::parse($input['date']);
        $creditPeriodDueDate = $creditPeriod != null && $creditPeriodType != null ? calculateCreditPeriodDate($creditPeriod, $creditPeriodType, $invoiceDate) : null;

        $shippingName = $input['shipping_address']['shipping_name'] ?? $input['shipping_name'] ?? null;
        $shippingGSTIN = $input['shipping_address']['shipping_gstin'] ?? $input['shipping_gstin'] ?? null;
        $addressName = $input['shipping_address']['address_name'] ?? $input['address_name'] ?? null;
        $partyNameSameAsAddressName = $input['shipping_address']['party_name_same_as_address_name'] ?? $input['party_name_same_as_address_name'] ?? false;
        if ($partyNameSameAsAddressName) {
            $addressName = $shippingName;
        }

        return [
            'company_id' => $input['company']->id,
            'title' => $input['title'] ?? null,
            'party_ledger_id' => $input['party_ledger_id'],
            'document_number' => $input['full_invoice_number'],
            'number_for_document' => $input['document_number'],
            'document_date' => Carbon::parse($input['date']),
            'party_phone_number' => $input['party_phone_number'] ?? null,
            'region_iso' => $input['region_iso'] ?? null,
            'region_code' => $input['region_code'] ?? null,
            'valid_for' => $input['valid_for'] ?? null,
            'valid_for_type' => $input['valid_for_type'] ?? null,
            'valid_till_date' => $validTillDate,
            'gstin' => $this->isGSTEnabled && isset($input['gstin']) ? $input['gstin'] : null,
            'broker_id' => $brokerDetails['broker_id'] ?? null,
            'brokerage' => $brokerDetails['brokerage_for_sale'] ?? null,
            'brokerage_on_value_type' => $brokerDetails['brokerage_on_value_type'] ?? null,
            'credit_period' => $creditPeriod,
            'credit_period_type' => $creditPeriodType,
            'credit_period_due_date' => $creditPeriodDueDate,
            'transport_id' => $transportDetails['transport_id'] ?? null,
            'transporter_document_number' => $transportDetails['transporter_document_number'] ?? null,
            'transporter_document_date' => isset($transportDetails['transporter_document_date']) ? Carbon::parse($transportDetails['transporter_document_date']) : null,
            'transporter_vehicle_number' => $transportDetails['transporter_vehicle_number'] ?? null,
            'shipping_name' => $shippingName,
            'shipping_gstin' => $shippingGSTIN,
            'address_name' => $addressName,
            'party_name_same_as_address_name' => $partyNameSameAsAddressName,
            'po_no' => $otherDetails['po_no'] ?? null,
            'po_date' => isset($otherDetails['po_date']) ? Carbon::parse($otherDetails['po_date']) : null,
            'invoice_type' => $input['invoice_type'],
            'tcs_tax_id' => $tcsDetails['tcs_tax_id'] ?? null,
            'tcs_rate' => $tcsDetails['tcs_rate'] ?? null,
            'tcs_amount' => $tcsDetails['tcs_amount'] ?? null,
            'tds_tax_id' => $tdsDetails['tds_tax_id'] ?? null,
            'tds_rate' => $tdsDetails['tds_rate'] ?? null,
            'tds_amount' => $tdsDetails['tds_amount'] ?? null,
            'cgst' => $this->isGSTEnabled ? $input['cgst'] : 0,
            'sgst' => $this->isGSTEnabled ? $input['sgst'] : 0,
            'igst' => $this->isGSTEnabled ? $input['igst'] : 0,
            'round_off_amount' => $input['rounding_amount'] ?? null,
            'total' => $input['grand_total'],
            'grand_total' => $input['grand_total'],
            'taxable_value' => $input['taxable_value'],
            'gross_value' => $input['gross_value'],
            'narration' => $input['narration'] ?? null,
            'term_and_condition' => $input['term_and_condition'] ?? null,
            'dispatch_address_id' => $input['dispatch_address_id'] ?? null,
            'shipping_address_id' => $input['shipping_address_id'] ?? null,
            'same_as_billing' => $input['same_as_billing'] ?? false,
            'is_gst_enabled' => $this->isGSTEnabled,
            'cess' => $this->isGSTEnabled ? $input['cess'] : 0,
            'is_cgst_sgst_igst_calculated' => $input['is_cgst_sgst_igst_calculated'],
            'is_gst_na' => $input['is_gst_na'],
            'created_by' => getLoginUser()->id,
            'via_api' => $input['via_api'] ?? false,
            'is_import' => $input['is_import'] ?? false,
            'is_round_off_not_changed' => $input['is_round_off_not_changed'] ?? true,
            'created_at' => $input['created_at'] ?? Carbon::now(), // this is for import excel
            'updated_at' => $input['updated_at'] ?? Carbon::now(), // this is for import excel
            'round_off_method' => $input['round_off_method'],
            'bank_id' => $input['bank_id'] ?? null,
        ];
    }

    private function storeItems($items, $estimateTransaction)
    {
        try {
            foreach ($items as $key => $item) {
                /* First check if item inventory has custom fields and user not selected any inventory for this item then throw error */
                $iteminventoryCustomFields = ItemCustomFieldSetting::whereHas('customField', function ($query) {
                    $query->where('open_in_popup', true);
                })->where('item_id', $item['item_id'])->count();

                if ($iteminventoryCustomFields > 0) {
                    if (! (isset($item['custom_field_inventory']) && count($item['custom_field_inventory']) != 0)) {
                        response()->json(['message' => 'Please select inventory for this item or otherwise inward inventory.'], 422)->send();
                        exit();
                    }
                }

                /* Prepare item data */
                $estimateTransactionItemData = $this->prepareItemData($item, $estimateTransaction);

                /* Store item */
                $incomeTransactionItem = IncomeEstimateQuoteItemInvoice::create($estimateTransactionItemData);

                /* Store Custom Fields for Items */
                if (isset($item['custom_fields']) && count($item['custom_fields']) != 0) {
                    StoreCFTransactionWiseAction::run(
                        $item['custom_fields'],
                        ItemCustomField::INCOME_ESTIMATE_QUOTE,
                        $incomeTransactionItem->id,
                        IncomeEstimateQuoteItemInvoice::class
                    );
                }

                /* Store Custom Fields for Item Inventory */
                if (isset($item['custom_field_inventory']) && count($item['custom_field_inventory']) != 0) {
                    $sortedInventory = collect($item['custom_field_inventory'])->map(function ($group) {
                        return collect($group)->sortBy('custom_field_id')->values();
                    })->toArray();

                    StoreItemCFInventoryAction::run(
                        $sortedInventory,
                        $item['item_id'],
                        $incomeTransactionItem->id,
                        IncomeEstimateQuoteItemInvoice::class
                    );
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updateItems($items, $estimateTransaction)
    {
        try {
            /* Get item ids to be removed */
            $itemIds = IncomeEstimateQuoteItemInvoice::where('transactions_id', $estimateTransaction->id)->pluck('id')->toArray();
            $editItemIds = Arr::pluck($items, 'id') ?? [];
            $removeItemIds = array_diff($itemIds, $editItemIds);

            /* Delete removed items */
            if (count($removeItemIds) > 0) {
                IncomeEstimateQuoteItemInvoice::whereIn('id', array_values($removeItemIds))?->delete();

                // if remove item from transaction then update remaining quantity in combination
                $inventoryItems = ItemCustomFieldCombinationInventory::whereIn('model_id', array_values($removeItemIds))
                    ->where('model_type', IncomeEstimateQuoteItemInvoice::class)
                    ->get();

                foreach ($inventoryItems as $inventoryItem) {
                    $combination = $inventoryItem->itemCustomFieldCombination;
                    $inventoryItem->delete();
                    CalculateAvailableQTY::run($combination);
                }
            }

            foreach ($items as $key => $item) {
                /* First check if item inventory has custom fields and user not selected any inventory for this item then throw error */
                $iteminventoryCustomFields = ItemCustomFieldSetting::whereHas('customField', function ($query) {
                    $query->where('open_in_popup', true);
                })->where('item_id', $item['item_id'])->count();

                if ($iteminventoryCustomFields > 0) {
                    if (! (isset($item['custom_field_inventory']) && count($item['custom_field_inventory']) != 0)) {
                        response()->json(['message' => 'Please select inventory for this item or otherwise inward inventory.'], 422)->send();
                        exit();
                    }
                }

                /* Prepare item data */
                $estimateTransactionItemData = $this->prepareItemData($item, $estimateTransaction);

                /* Update or create item */
                if (! isset($item['id']) || $item['id'] == null) {
                    $estimateTransactionItem = IncomeEstimateQuoteItemInvoice::create($estimateTransactionItemData);
                } else {
                    $estimateTransactionItem = IncomeEstimateQuoteItemInvoice::whereId($item['id'])->first();
                    if (! empty($estimateTransactionItem)) {
                        $estimateTransactionItem->update($estimateTransactionItemData);
                    }
                }

                /* Update Custom Fields for Items */
                if (isset($item['custom_fields']) && count($item['custom_fields']) != 0) {
                    UpdateCFTransactionWiseAction::run(
                        $item['custom_fields'],
                        ItemCustomField::INCOME_ESTIMATE_QUOTE,
                        $estimateTransactionItem->id,
                        IncomeEstimateQuoteItemInvoice::class
                    );
                } else {
                    $estimateTransactionItem->customFieldTransactionItemsValues()->delete();
                }

                /* Update Custom Fields for Item Inventory */
                if (isset($item['custom_field_inventory']) && count($item['custom_field_inventory']) != 0) {
                    $sortedInventory = collect($item['custom_field_inventory'])->map(function ($group) {
                        return collect($group)->sortBy('custom_field_id')->values();
                    })->toArray();

                    UpdateItemCFInventoryAction::run(
                        $sortedInventory,
                        $item['item_id'],
                        $estimateTransactionItem->id,
                        IncomeEstimateQuoteItemInvoice::class
                    );
                } else {
                    // if custom field inventory is removed then update remaining quantity in combination row batch as well
                    $inventoryItems = $estimateTransactionItem->customFieldTransactionItemsInventoryValues;

                    foreach ($inventoryItems as $inventoryItem) {
                        $combination = $inventoryItem->itemCustomFieldCombination;
                        $inventoryItem->delete();
                        CalculateAvailableQTY::run($combination);
                    }
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareItemData($item, $estimateTransaction)
    {
        $cessRate = 0;
        $cessAmount = 0;
        if ($this->isGSTEnabled) {
            $itemMaster = ItemMaster::with(['model.gstGstCessRate'])->whereId($item['item_id'])->first();
            $cessRate = $itemMaster->model->gstGstCessRate->rate ?? 0;
            $taxableValue = ($item['quantity'] * $item['rpu_without_gst']) - $item['total_discount_amount'];
            $cessAmount = ($taxableValue * $cessRate) / 100;
        }

        return [
            'transactions_id' => $estimateTransaction->id,
            'item_id' => $item['item_id'],
            'additional_description' => $item['additional_description'] ?? null,
            'ledger_id' => $item['ledger_id'],
            'unit_id' => $item['unit_id'],
            'hsn_code' => $item['hsn_code'] ?? null,
            'quantity' => $item['quantity'],
            'mrp' => $item['mrp'] ?? null,
            'with_tax' => $item['with_tax'] ?? false,
            'rpu_with_gst' => $item['rpu_with_gst'],
            'rpu_without_gst' => $item['rpu_without_gst'],
            'discount_type' => $item['discount_type'] ?? null,
            'discount_value' => $item['discount_value'] ?? null,
            'discount_type_2' => $item['discount_type_2'] ?? null, // will be added
            'discount_value_2' => $item['discount_value_2'] ?? null, // will be added
            'total_discount_amount' => $item['total_discount_amount'] ?? 0,
            'gst_id' => $item['gst_tax'],
            'gst_tax_percentage' => $item['gst_tax_percentage'],
            'total' => $item['total'],
            'classification_nature_type' => $this->classificationNatureType,
            'classification_is_rcm_applicable' => $this->isRCMApplicable,
            'classification_igst_tax' => $this->isGSTEnabled ? $item['classification_igst_tax'] : null,
            'classification_cgst_tax' => $this->isGSTEnabled ? $item['classification_cgst_tax'] : null,
            'classification_sgst_tax' => $this->isGSTEnabled ? $item['classification_sgst_tax'] : null,
            'classification_cess_tax' => $this->isGSTEnabled && isset($item['cess']) ? $item['cess'] : null,
            'consolidating_items_to_invoice' => $item['consolidating_items_to_invoice'] ?? false,
            'cess_rate' => $cessRate,
            'cess_amount' => $cessAmount,
            'decimal_places_for_quantity' => $item['decimal_places'] ?? 2,
            'decimal_places_for_rate' => $item['decimal_places_for_rate'] ?? 2,
        ];
    }

    private function storeLedgers($ledgers, $estimateTransaction)
    {
        try {
            foreach ($ledgers as $key => $ledger) {
                /* Prepare ledger data */
                $estimateTransactionLedgerData = $this->prepareLedgerData($ledger, $estimateTransaction);

                /* Store ledger */
                $incomeTransactionLedger = IncomeEstimateQuoteAccountingInvoice::create($estimateTransactionLedgerData);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updateLedgers($ledgers, $estimateTransaction)
    {
        try {
            $ledgerIds = IncomeEstimateQuoteAccountingInvoice::where('transactions_id', $estimateTransaction->id)->pluck('id')->toArray();
            $editLedgerIds = Arr::pluck($ledgers, 'id') ?? [];
            $removeLedgerIds = array_diff($ledgerIds, $editLedgerIds);

            /* Delete removed ledgers */
            IncomeEstimateQuoteAccountingInvoice::whereIn('id', array_values($removeLedgerIds))?->delete();

            foreach ($ledgers as $key => $ledger) {
                /* Prepare ledger data */
                $estimateTransactionLedgerData = $this->prepareLedgerData($ledger, $estimateTransaction);

                /* Update or create ledger */
                if (! isset($ledger['id']) || $ledger['id'] == null) {
                    $estimateTransactionLedger = IncomeEstimateQuoteAccountingInvoice::create($estimateTransactionLedgerData);
                } else {
                    $estimateTransactionLedger = IncomeEstimateQuoteAccountingInvoice::whereId($ledger['id'])->first();
                    if (! empty($estimateTransactionLedger)) {
                        $estimateTransactionLedger->update($estimateTransactionLedgerData);
                    }
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareLedgerData($ledger, $estimateTransaction)
    {
        return [
            'transactions_id' => $estimateTransaction->id,
            'ledger_id' => $ledger['ledger_id'],
            'additional_description' => $ledger['additional_description'] ?? null,
            'with_tax' => $ledger['with_tax'] ?? false,
            'rpu_with_gst' => $ledger['rpu_with_gst'],
            'rpu_without_gst' => $ledger['rpu_without_gst'],
            'discount_type' => $ledger['discount_type'] ?? null,
            'discount_value' => $ledger['discount_value'] ?? null,
            'discount_type_2' => $ledger['discount_type_2'] ?? null, // will be added
            'discount_value_2' => $ledger['discount_value_2'] ?? null, // will be added
            'total_discount_amount' => $ledger['total_discount_amount'] ?? 0,
            'gst_id' => $ledger['gst_tax'],
            'gst_tax_percentage' => $ledger['gst_tax_percentage'],
            'total' => $ledger['total'],
            'classification_igst_tax' => $this->isGSTEnabled ? $ledger['classification_igst_tax'] : null,
            'classification_cgst_tax' => $this->isGSTEnabled ? $ledger['classification_cgst_tax'] : null,
            'classification_sgst_tax' => $this->isGSTEnabled ? $ledger['classification_sgst_tax'] : null,
            // 'classification_cess_tax' => $this->isGSTEnabled ? $ledger['cess'] : null,
            'classification_is_rcm_applicable' => $this->isRCMApplicable,
            'classification_nature_type' => $this->classificationNatureType,
        ];
    }

    private function storeAddresses($estimateTransaction, $input)
    {
        try {
            $dispatchAddress = $input['dispatch_address'] ?? [];
            $billingAddress = $input['billing_address'] ?? [];
            $shippingAddress = $input['shipping_address'] ?? [];

            $addresses = [];

            if ($dispatchAddress) {
                $addresses[] = $this->prepareAddressData($estimateTransaction, $dispatchAddress, IncomeEstimateQuoteTransaction::DISPATCH_ADDRESS);
            }

            if ($billingAddress) {
                $addresses[] = $this->prepareAddressData($estimateTransaction, $billingAddress, IncomeEstimateQuoteTransaction::BILLING_ADDRESS);
            }

            if (! (isset($input['shipping_address_id']) && $input['shipping_address_id'] != null)) { // This is for if shipping address is not selected or address from local storage
                if ($shippingAddress) {
                    $addresses[] = $this->prepareAddressData($estimateTransaction, $shippingAddress, IncomeEstimateQuoteTransaction::SHIPPING_ADDRESS);
                }
            }

            if (! empty($addresses)) {
                $estimateTransaction->addresses()->createMany($addresses);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updateAddresses($estimateTransaction, $input)
    {
        try {
            $dispatchAddress = $input['dispatch_address'] ?? [];
            $billingAddress = $input['billing_address'] ?? [];
            $shippingAddress = $input['shipping_address'] ?? [];

            if ($dispatchAddress) {
                $estimateTransaction->addresses()->updateOrCreate(
                    ['address_type' => IncomeEstimateQuoteTransaction::DISPATCH_ADDRESS, 'model_id' => $estimateTransaction->id],
                    $this->prepareAddressData($estimateTransaction, $dispatchAddress, IncomeEstimateQuoteTransaction::DISPATCH_ADDRESS)
                );
            }

            if ($billingAddress) {
                $estimateTransaction->addresses()->updateOrCreate(
                    ['address_type' => IncomeEstimateQuoteTransaction::BILLING_ADDRESS, 'model_id' => $estimateTransaction->id],
                    $this->prepareAddressData($estimateTransaction, $billingAddress, IncomeEstimateQuoteTransaction::BILLING_ADDRESS)
                );
            }

            if (! (isset($input['shipping_address_id']) && $input['shipping_address_id'] != null)) { // This is for if shipping address is not selected or address from local storage
                if ($shippingAddress) {
                    $estimateTransaction->addresses()->updateOrCreate(
                        ['address_type' => IncomeEstimateQuoteTransaction::SHIPPING_ADDRESS, 'model_id' => $estimateTransaction->id],
                        $this->prepareAddressData($estimateTransaction, $shippingAddress, IncomeEstimateQuoteTransaction::SHIPPING_ADDRESS)
                    );
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareAddressData($estimateTransaction, $address, $addressType)
    {
        return [
            'address_1' => $address['address_1'] ?? null,
            'address_2' => $address['address_2'] ?? null,
            'country_id' => $address['country_id'] ?? null,
            'state_id' => $address['state_id'] ?? null,
            'city_id' => $address['city_id'] ?? null,
            'pin_code' => $address['pin_code'] ?? null,
            'model_id' => $estimateTransaction->id,
            'model_type' => IncomeEstimateQuoteTransaction::class,
            'address_type' => $addressType,
        ];
    }

    private function storeAdditionalCharges($additionChargesData, $estimateTransactionId)
    {
        try {
            foreach ($additionChargesData as $additionCharge) {
                /* Prepare additional charge data */
                $additionChargeData = $this->prepareAdditionalChargesData($additionCharge, $estimateTransactionId);

                /* Store additional charge */
                AdditionalChargesForIncomeEstimateQuoteTransaction::create($additionChargeData);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updateAdditionalCharges($additionChargesData, $estimateTransactionId)
    {
        try {
            $additionalChargesIds = AdditionalChargesForIncomeEstimateQuoteTransaction::where('estimate_quote_id', $estimateTransactionId)->pluck('id')->toArray();
            $editedAdditionalChargesIds = Arr::pluck($additionChargesData, 'id');
            $removeAdditionalChargesIds = array_diff($additionalChargesIds, $editedAdditionalChargesIds);

            /* Delete additional charges */
            AdditionalChargesForIncomeEstimateQuoteTransaction::whereIn('id', array_values($removeAdditionalChargesIds))?->delete();

            foreach ($additionChargesData as $additionCharge) {
                /* Prepare additional charge data */
                $additionChargeData = $this->prepareAdditionalChargesData($additionCharge, $estimateTransactionId);

                /* Update additional charge */
                if (! isset($additionCharge['id']) || $additionCharge['id'] == null) {
                    AdditionalChargesForIncomeEstimateQuoteTransaction::create($additionChargeData);
                } else {
                    $additionalChargeRecord = AdditionalChargesForIncomeEstimateQuoteTransaction::where('id', $additionCharge['id'])->first();
                    if (! empty($additionalChargeRecord)) {
                        $additionalChargeRecord->update($additionChargeData);
                    }
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareAdditionalChargesData($additionCharge, $estimateTransactionId)
    {
        if ($this->isGSTEnabled) {
            $gst = null;
            if (isset($additionCharge['ac_gst_rate_id'])) {
                $gst = GstTax::whereId($additionCharge['ac_gst_rate_id'])->first();
            }
            $gstTaxPercentage = ! empty($gst) ? $gst->tax_rate : null;
        }

        return [
            'estimate_quote_id' => $estimateTransactionId,
            'ledger_id' => $additionCharge['ac_ledger_id'],
            'charge_type' => $additionCharge['ac_type'],
            'value' => $additionCharge['ac_value'],
            'gst_rate_id' => $this->isGSTEnabled && ! empty($gst) ? $gst->id : null,
            'gst_percentage' => $this->isGSTEnabled ? $gstTaxPercentage : null,
            'total_without_tax' => $additionCharge['ac_total_without_tax'],
            'total' => $additionCharge['ac_total'],
        ];
    }

    private function storeAddLess($addLessData, $estimateTransactionId)
    {
        try {

            foreach ($addLessData as $addLessCharge) {
                /* Prepare add less data */
                $addLessChargeData = $this->prepareAddLessData($addLessCharge, $estimateTransactionId);

                /* Store add less */
                AddLessForIncomeEstimateQuoteTransaction::create($addLessChargeData);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updateAddLess($addLessData, $estimateTransactionId)
    {
        try {
            $addLessIds = AddLessForIncomeEstimateQuoteTransaction::where('estimate_quote_id', $estimateTransactionId)->pluck('id')->toArray();
            $editedAddLessIds = Arr::pluck($addLessData, 'id');
            $removeAddLessIds = array_diff($addLessIds, $editedAddLessIds);

            /* Delete add less */
            AddLessForIncomeEstimateQuoteTransaction::whereIn('id', array_values($removeAddLessIds))?->delete();

            foreach ($addLessData as $addLessCharge) {
                /* Prepare add less data */
                $addLessChargeData = $this->prepareAddLessData($addLessCharge, $estimateTransactionId);

                /* Update add less */
                if (! isset($addLessCharge['id']) || $addLessCharge['id'] == null) {
                    AddLessForIncomeEstimateQuoteTransaction::create($addLessChargeData);
                } else {
                    $addLessRecord = AddLessForIncomeEstimateQuoteTransaction::where('id', $addLessCharge['id'])->first();
                    if (! empty($addLessRecord)) {
                        $addLessRecord->update($addLessChargeData);
                    }
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareAddLessData($addLessCharge, $estimateTransactionId)
    {
        return [
            'estimate_quote_id' => $estimateTransactionId,
            'ledger_id' => $addLessCharge['al_ledger_id'],
            'is_show_in_print' => $addLessCharge['al_is_show_in_print'] ?? false,
            'type' => $addLessCharge['al_type'],
            'value' => $addLessCharge['al_value'],
            'total' => $addLessCharge['al_total'],
        ];
    }
}
