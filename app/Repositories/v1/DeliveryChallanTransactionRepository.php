<?php

namespace App\Repositories\v1;

use App\Actions\CommonAction\CheckPartyLedgerMobileNumberAction;
use App\Actions\CommonAction\DirectCustomerSupplierCreate;
use App\Actions\CustomFields\StoreTransactionCustomFieldsAction;
use App\Actions\CustomFields\UpdateTransactionCustomFieldsAction;
use App\Actions\CustomFieldsItemMaster\Inventory\CalculateAvailableQTY;
use App\Actions\CustomFieldsItemMaster\Inventory\StoreItemCFInventoryAction;
use App\Actions\CustomFieldsItemMaster\Inventory\UpdateItemCFInventoryAction;
use App\Actions\CustomFieldsItemMaster\Transaction\StoreCFTransactionWiseAction;
use App\Actions\CustomFieldsItemMaster\Transaction\UpdateCFTransactionWiseAction;
use App\Models\AdditionalChargesForDeliveryChallanTransaction;
use App\Models\AddLessForDeliveryChallanTransaction;
use App\Models\DeliveryChallanTransaction;
use App\Models\DeliveryChallanTransactionItem;
use App\Models\GstTax;
use App\Models\ItemCustomField;
use App\Models\ItemCustomFieldCombinationInventory;
use App\Models\ItemCustomFieldSetting;
use App\Models\Ledger;
use App\Models\SaleTransaction;
use App\Models\TaxClassificationDetails;
use App\Models\TransactionCustomField;
use App\Repositories\API\v1\BaseAPIRepository;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

/**
 * Interface DeliveryChallanTransactionRepository.
 */
class DeliveryChallanTransactionRepository extends BaseAPIRepository
{
    public $classificationNatureType = null;

    public $isRCMApplicable = false;

    public $isGSTEnabled = null;

    public $invoiceTypeWithAmount = false;

    public function model()
    {
        return DeliveryChallanTransaction::class;
    }

    public function store($input)
    {
        try {
            DB::beginTransaction();
            unset($input['_token']);
            $this->invoiceTypeWithAmount = isset($input['invoice_type']) && $input['invoice_type'] == DeliveryChallanTransaction::WITH_AMOUNT ? true : false;
            $this->isGSTEnabled = $this->invoiceTypeWithAmount ? $input['is_gst_enabled'] : false;

            /* If is_create_party is true then create party */
            if (isset($input['is_create_party']) && $input['is_create_party']) {
                if (! getLoginUser()->can('mobile_company_add_new_account_masters')) {
                    throw new UnprocessableEntityHttpException('You do not have permission to create new party.');
                }
                $partyId = DirectCustomerSupplierCreate::run($input['party_ledger_id'], Ledger::CUSTOMER);
                $input['party_ledger_id'] = $partyId;
            }

            /* Ledger mobile number */
            if (isset($input['party_phone_number'])) {
                $ledgerData = [
                    'ledger_id' => $input['party_ledger_id'],
                    'phone_1' => $input['party_phone_number'],
                    'region_iso_1' => $input['region_iso'],
                    'region_code_1' => $input['region_code'],
                ];
                CheckPartyLedgerMobileNumberAction::run($ledgerData);
            }

            /* Prepare Delivery Challan transaction */
            $deliveryChallanData = $this->prepareDeliveryChallanData($input);

            /* Create Delivery Challan */
            $deliveryChallanTransaction = DeliveryChallanTransaction::create($deliveryChallanData);

            /* Store Delivery Challan Document */
            if (isset($input['delivery_challan_document']) && ! empty($input['delivery_challan_document'])) {
                foreach ($input['delivery_challan_document'] as $image) {
                    $deliveryChallanTransaction->addMedia($image)->toMediaCollection(DeliveryChallanTransaction::DELIVERY_CHALLAN_DOCUMENT, config('app.media_disc'));
                }
            }

            if ($this->isGSTEnabled) {
                /* Store Delivery Challan items based on invoice */
                $this->classificationNatureType = TaxClassificationDetails::whereName($input['main_classification_nature_type'] ?? null)->first()->id ?? null;
                $this->isRCMApplicable = $input['is_rcm_applicable'] ?? false;
            }

            // Store Items
            $this->storeItems($input['items'], $deliveryChallanTransaction);

            /* Store Addresses */
            $this->storeAddresses($deliveryChallanTransaction, $input);

            /* This is update shipping address id bcz when excel import & frontside local shipping address */
            if (empty($deliveryChallanTransaction->shipping_address_id) && ! empty($deliveryChallanTransaction->shippingAddress)) {
                $deliveryChallanTransaction->update([
                    'shipping_address_id' => $deliveryChallanTransaction->shippingAddress->id,
                ]);
            }

            if ($this->invoiceTypeWithAmount) {
                /* Store Additional Charges */
                if (isset($input['additional_charges']) && count($input['additional_charges']) != 0) {
                    $this->storeAdditionalCharges($input['additional_charges'], $deliveryChallanTransaction->id);
                }

                /* Store Add Less */
                if (isset($input['add_less']) && count($input['add_less']) != 0) {
                    $this->storeAddLess($input['add_less'], $deliveryChallanTransaction->id);
                }
            }

            $saleId = $input['invoice_number'] ?? null;
            if ($saleId) {
                $saleTransaction = SaleTransaction::find($saleId);
                if (! empty($saleTransaction)) {
                    $saleTransaction->update(['delivery_challan_no' => $deliveryChallanTransaction->id]);
                } else {
                    Log::error('Sale Transaction not found for invoice number: '.$saleId);
                }
            }

            /* Store Custom Fields */
            if (isset($input['custom_fields']) && count($input['custom_fields']) != 0) {
                StoreTransactionCustomFieldsAction::run(
                    $input['custom_fields'],
                    TransactionCustomField::DELIVERY_CHALLAN,
                    $deliveryChallanTransaction->id,
                    DeliveryChallanTransaction::class
                );
            }

            /* Update delivery challan Transaction Status Column Value */
            updateDeliveryChallanStatus($deliveryChallanTransaction->id);

            DB::commit();

            return $deliveryChallanTransaction;
        } catch (Exception $e) {
            DB::rollBack();
            Log::error($e);
            throw $e;
        }
    }

    public function update($input, $deliveryChallanTransaction)
    {
        try {
            DB::beginTransaction();

            $this->invoiceTypeWithAmount = isset($input['invoice_type']) && $input['invoice_type'] == DeliveryChallanTransaction::WITH_AMOUNT ? true : false;
            $this->isGSTEnabled = $this->invoiceTypeWithAmount ? $input['is_gst_enabled'] : false;

            /* If is_create_party is true then create party */
            if (isset($input['is_create_party']) && $input['is_create_party']) {
                if (! getLoginUser()->can('mobile_company_add_new_account_masters')) {
                    throw new UnprocessableEntityHttpException('You do not have permission to create new party.');
                }
                $partyId = DirectCustomerSupplierCreate::run($input['party_ledger_id'], Ledger::CUSTOMER);
                $input['party_ledger_id'] = $partyId;
            }

            /* Ledger mobile number */
            if (isset($input['party_phone_number'])) {
                $ledgerData = [
                    'ledger_id' => $input['party_ledger_id'],
                    'phone_1' => $input['party_phone_number'],
                    'region_iso_1' => $input['region_iso'],
                    'region_code_1' => $input['region_code'],
                ];
                CheckPartyLedgerMobileNumberAction::run($ledgerData);
            }

            /* Prepare delivery challan transaction */
            $input['created_at'] = $deliveryChallanTransaction->created_at;
            $deliveryChallanTransactionData = $this->prepareDeliveryChallanData($input);
            /** @var DeliveryChallanTransaction $deliveryChallanTransaction */
            unset($deliveryChallanTransactionData['number_for_challan']);

            /* Update delivery challan transaction */
            $deliveryChallanTransaction->update($deliveryChallanTransactionData);

            /* Update delivery challan Document */
            if (isset($input['delivery_challan_document']) && ! empty($input['delivery_challan_document'])) {
                foreach ($input['delivery_challan_document'] as $image) {
                    $deliveryChallanTransaction->addMedia($image)->toMediaCollection(DeliveryChallanTransaction::DELIVERY_CHALLAN_DOCUMENT, config('app.media_disc'));
                }
            }

            if ($this->isGSTEnabled) {
                /* Store delivery challan items based on invoice */
                $this->classificationNatureType = TaxClassificationDetails::whereName($input['main_classification_nature_type'] ?? null)->first()->id ?? null;
                $this->isRCMApplicable = $input['is_rcm_applicable'] ?? false;
            }

            /* Update delivery challan items */
            $this->updateItems($input['items'], $deliveryChallanTransaction);

            /* Update Addresses */
            $this->updateAddresses($deliveryChallanTransaction, $input);

            /* This is update shipping address id bcz when excel import & frontside local shipping address */
            if (empty($deliveryChallanTransaction->shipping_address_id) && ! empty($deliveryChallanTransaction->shippingAddress)) {
                $deliveryChallanTransaction->update([
                    'shipping_address_id' => $deliveryChallanTransaction->shippingAddress->id,
                ]);
            }

            /* Update Custom Fields */
            if (isset($input['custom_fields']) && count($input['custom_fields']) != 0) {
                UpdateTransactionCustomFieldsAction::run(
                    $input['custom_fields'],
                    TransactionCustomField::DELIVERY_CHALLAN,
                    $deliveryChallanTransaction->id,
                    DeliveryChallanTransaction::class
                );
            } else {
                $deliveryChallanTransaction->customFieldValues()->delete();
            }

            if ($this->invoiceTypeWithAmount) {
                /* Update or Create Additional Charges */
                if (isset($input['additional_charges']) && count($input['additional_charges']) != 0) {
                    $this->updateAdditionalCharges($input['additional_charges'], $deliveryChallanTransaction->id);
                } else {
                    $deliveryChallanTransaction->additionalCharges()->delete();
                }

                /* Update or Create Add Less */
                if (isset($input['add_less']) && count($input['add_less']) != 0) {
                    $this->updateAddLess($input['add_less'], $deliveryChallanTransaction->id);
                } else {
                    $deliveryChallanTransaction->addLess()->delete();
                }
            }

            /* Update delivery challan Transaction Status Column Value */
            updateDeliveryChallanStatus($deliveryChallanTransaction->id);

            DB::commit();

            return $deliveryChallanTransaction;
        } catch (Exception $e) {
            DB::rollBack();
            Log::error($e);
            throw $e;
        }
    }

    private function prepareDeliveryChallanData($input): array
    {
        $brokerDetails = $input['broker_details'] ?? [];
        $transportDetails = $input['transport_details'] ?? [];
        $otherDetails = $input['other_details'] ?? [];
        $tcsDetails = $input['tcs_details'] ?? [];
        $shippingName = $input['shipping_address']['shipping_name'] ?? $input['shipping_name'] ?? null;
        $shippingGSTIN = $input['shipping_address']['shipping_gstin'] ?? $input['shipping_gstin'] ?? null;
        $addressName = $input['shipping_address']['address_name'] ?? $input['address_name'] ?? null;
        $partyNameSameAsAddressName = $input['shipping_address']['party_name_same_as_address_name'] ?? $input['party_name_same_as_address_name'] ?? false;
        if ($partyNameSameAsAddressName) {
            $addressName = $shippingName;
        }
        $tcsDetails = $input['tcs_details'] ?? [];
        $tdsDetails = $input['tds_details'] ?? [];

        $data = [
            'company_id' => $input['company']->id,
            'created_by' => $input['company']->user->id ?? null,
            'challan_number' => $input['challan_number'] ?? null,
            'number_for_challan' => $input['number_for_challan'],
            'challan_date' => Carbon::parse($input['challan_date']),
            'party_ledger_id' => $input['party_ledger_id'],
            'party_phone_number' => $input['party_phone_number'] ?? null,
            'region_iso' => $input['region_iso'] ?? null,
            'region_code' => $input['region_code'] ?? null,
            'gstin' => $input['gstin'] ?? null,
            'invoice_number' => $input['invoice_number'] ?? null,
            'invoice_date' => isset($input['invoice_date']) ? Carbon::parse($input['invoice_date']) : null,
            'broker_id' => $brokerDetails['broker_id'] ?? null,
            'brokerage' => $brokerDetails['brokerage_for_sale'] ?? null,
            'brokerage_on_value_type' => $brokerDetails['brokerage_on_value_type'] ?? null,
            'shipping_name' => $shippingName,
            'shipping_gstin' => $shippingGSTIN,
            'address_name' => $addressName,
            'party_name_same_as_address_name' => $partyNameSameAsAddressName,
            'transport_id' => $transportDetails['transport_id'] ?? null,
            'transporter_document_number' => $transportDetails['transporter_document_number'] ?? null,
            'transporter_document_date' => isset($transportDetails['transporter_document_date']) ? Carbon::parse($transportDetails['transporter_document_date']) : null,
            'transporter_vehicle_number' => $transportDetails['transporter_vehicle_number'] ?? null,
            'po_no' => $otherDetails['po_no'] ?? null,
            'po_date' => isset($otherDetails['po_date']) ? Carbon::parse($otherDetails['po_date']) : null,
            'narration' => $input['narration'] ?? null,
            'term_and_condition' => $input['term_and_condition'] ?? null,
            'dispatch_address_id' => $input['dispatch_address_id'] ?? null,
            'shipping_address_id' => $input['shipping_address_id'] ?? null,
            'same_as_billing' => $input['same_as_billing'] ?? false,
            'via_api' => $input['via_api'] ?? false,
            'is_import' => $input['is_import'] ?? false,
            'created_at' => $input['created_at'] ?? Carbon::now(), // this is for import excel
            'updated_at' => $input['updated_at'] ?? Carbon::now(), // this is for import excel
            'invoice_type' => $input['invoice_type'] ?? DeliveryChallanTransaction::WITHOUT_AMOUNT,
        ];

        // Add fields conditionally if invoiceTypeWithAmount is true
        if ($this->invoiceTypeWithAmount) {
            $data = array_merge($data, [
                'gross_value' => $input['gross_value'] ?? false,
                'cgst' => $input['cgst'] ?? false,
                'sgst' => $input['sgst'] ?? false,
                'igst' => $input['igst'] ?? false,
                'taxable_value' => $input['taxable_value'] ?? false,
                'rounding_amount' => $input['rounding_amount'] ?? false,
                'total' => $input['grand_total'] ?? false,
                'grand_total' => $input['grand_total'] ?? false,
                'tcs_tax_id' => $tcsDetails['tcs_tax_id'] ?? null,
                'tcs_rate' => $tcsDetails['tcs_rate'] ?? null,
                'tcs_amount' => $tcsDetails['tcs_amount'] ?? null,
                'is_gst_enabled' => $this->isGSTEnabled,
                'cess' => $this->isGSTEnabled ? $input['cess'] : 0,
                'is_cgst_sgst_igst_calculated' => $input['is_cgst_sgst_igst_calculated'],
                'is_gst_na' => $input['is_gst_na'],
                'is_round_off_not_changed' => $input['is_round_off_not_changed'] ?? true,
                'round_off_method' => $input['round_off_method'],
            ]);
        }

        return $data;
    }

    private function storeItems($items, $deliveryChallanTransaction)
    {
        try {
            foreach ($items as $key => $value) {
                /* First check if item inventory has custom fields and user not selected any inventory for this item then throw error */
                $iteminventoryCustomFields = ItemCustomFieldSetting::whereHas('customField', function ($query) {
                    $query->where('open_in_popup', true);
                })->where('item_id', $value['item_id'])->count();

                if ($iteminventoryCustomFields > 0) {
                    if (! (isset($value['custom_field_inventory']) && count($value['custom_field_inventory']) != 0)) {
                        response()->json(['message' => 'Please select inventory for this item or otherwise inward inventory.'], 422)->send();
                        exit();
                    }
                }

                /* Prepare item data */
                $deliveryChallanTransactionItemData = $this->prepareItemData($value, $deliveryChallanTransaction);

                /* Store item */
                $deliveryChallanTransactionItem = DeliveryChallanTransactionItem::create($deliveryChallanTransactionItemData);

                /* Store Custom Fields for Items */
                if (isset($value['custom_fields']) && count($value['custom_fields']) != 0) {
                    StoreCFTransactionWiseAction::run(
                        $value['custom_fields'],
                        ItemCustomField::DELIVERY_CHALLAN,
                        $deliveryChallanTransactionItem->id,
                        DeliveryChallanTransactionItem::class
                    );
                }

                /* Store Custom Fields for Item Inventory */
                if (isset($value['custom_field_inventory']) && count($value['custom_field_inventory']) != 0) {
                    $sortedInventory = collect($value['custom_field_inventory'])->map(function ($group) {
                        return collect($group)->sortBy('custom_field_id')->values();
                    })->toArray();

                    StoreItemCFInventoryAction::run(
                        $sortedInventory,
                        $value['item_id'],
                        $deliveryChallanTransactionItem->id,
                        DeliveryChallanTransactionItem::class
                    );
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updateItems($items, $deliveryChallanTransaction)
    {
        try {

            /* Get item ids to be removed */
            $oldItemIds = DeliveryChallanTransactionItem::where('transaction_id', $deliveryChallanTransaction->id)->pluck('id')->toArray();
            $editItemIds = Arr::pluck($items, 'id') ?? [];
            $removeItemIds = array_diff($oldItemIds, $editItemIds);

            if (count($removeItemIds) > 0) {
                DeliveryChallanTransactionItem::whereIn('id', array_values($removeItemIds))?->delete();

                // if remove item from transaction then update remaining quantity in combination
                $inventoryItems = ItemCustomFieldCombinationInventory::whereIn('model_id', array_values($removeItemIds))
                    ->where('model_type', DeliveryChallanTransactionItem::class)
                    ->get();

                foreach ($inventoryItems as $inventoryItem) {
                    $combination = $inventoryItem->itemCustomFieldCombination;
                    $inventoryItem->delete();
                    CalculateAvailableQTY::run($combination);
                }
            }

            foreach ($items as $key => $item) {
                /* First check if item inventory has custom fields and user not selected any inventory for this item then throw error */
                $iteminventoryCustomFields = ItemCustomFieldSetting::whereHas('customField', function ($query) {
                    $query->where('open_in_popup', true);
                })->where('item_id', $item['item_id'])->count();

                if ($iteminventoryCustomFields > 0) {
                    if (! (isset($item['custom_field_inventory']) && count($item['custom_field_inventory']) != 0)) {
                        response()->json(['message' => 'Please select inventory for this item or otherwise inward inventory.'], 422)->send();
                        exit();
                    }
                }

                /* Prepare item data */
                $deliveryChallanTransactionItemItemData = $this->prepareItemData($item, $deliveryChallanTransaction);

                /* Update or create item */
                if (! isset($item['id']) || $item['id'] == null) {
                    $deliveryChallanTransactionItem = DeliveryChallanTransactionItem::create($deliveryChallanTransactionItemItemData);
                } else {
                    $deliveryChallanTransactionItem = DeliveryChallanTransactionItem::whereId($item['id'])->first();
                    if (! empty($deliveryChallanTransactionItem)) {
                        $deliveryChallanTransactionItem->update($deliveryChallanTransactionItemItemData);
                    }
                }

                /* Update Custom Fields for Items */
                if (isset($item['custom_fields']) && count($item['custom_fields']) != 0) {
                    UpdateCFTransactionWiseAction::run(
                        $item['custom_fields'],
                        ItemCustomField::DELIVERY_CHALLAN,
                        $deliveryChallanTransactionItem->id,
                        DeliveryChallanTransactionItem::class
                    );
                } else {
                    $deliveryChallanTransactionItem->customFieldTransactionItemsValues()->delete();
                }

                /* Update Custom Fields for Item Inventory */
                if (isset($item['custom_field_inventory']) && count($item['custom_field_inventory']) != 0) {
                    $sortedInventory = collect($item['custom_field_inventory'])->map(function ($group) {
                        return collect($group)->sortBy('custom_field_id')->values();
                    })->toArray();

                    UpdateItemCFInventoryAction::run(
                        $sortedInventory,
                        $item['item_id'],
                        $deliveryChallanTransactionItem->id,
                        DeliveryChallanTransactionItem::class
                    );
                } else {
                    // if custom field inventory is removed then update remaining quantity in combination row batch as well
                    $inventoryItems = $deliveryChallanTransactionItem->customFieldTransactionItemsInventoryValues;

                    foreach ($inventoryItems as $inventoryItem) {
                        $combination = $inventoryItem->itemCustomFieldCombination;
                        $inventoryItem->delete();
                        CalculateAvailableQTY::run($combination);
                    }
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareItemData($value, $deliveryChallanTransaction)
    {
        $data = [
            'transaction_id' => $deliveryChallanTransaction->id,
            'item_id' => $value['item_id'],
            'additional_description' => $value['additional_description'] ?? null,
            'unit_id' => $value['unit_id'] ?? null,
            'quantity' => $value['quantity'] ?? null,
            'consolidating_items_to_invoice' => $value['consolidating_items_to_invoice'] ?? null,
        ];

        // Conditionally add fields if invoiceTypeWithAmount is true
        if ($this->invoiceTypeWithAmount) {
            $data = array_merge($data, [
                'ledger_id' => $value['ledger_id'] ?? null,
                'hsn_code' => $value['hsn_code'] ?? null,
                'mrp' => $value['mrp'] ?? null,
                'with_tax' => $value['with_tax'] ?? false,
                'rpu_with_gst' => $value['rpu_with_gst'],
                'rpu_without_gst' => $value['rpu_without_gst'],
                'discount_type' => $value['discount_type'] ?? null,
                'discount_value' => $value['discount_value'] ?? null,
                'discount_type_2' => $value['discount_type_2'] ?? null,
                'discount_value_2' => $value['discount_value_2'] ?? null,
                'total_discount_amount' => $value['total_discount_amount'] ?? 0,
                'gst_id' => $value['gst_tax'],
                'gst_tax_percentage' => $value['gst_tax_percentage'],
                'total' => $value['total'],
                'classification_nature_type' => $this->classificationNatureType,
                'classification_is_rcm_applicable' => $this->isRCMApplicable,
                'classification_igst_tax' => $this->isGSTEnabled ? $value['classification_igst_tax'] : null,
                'classification_cgst_tax' => $this->isGSTEnabled ? $value['classification_cgst_tax'] : null,
                'classification_sgst_tax' => $this->isGSTEnabled ? $value['classification_sgst_tax'] : null,
                'classification_cess_tax' => $this->isGSTEnabled && isset($value['cess']) ? $value['cess'] : null,
                'decimal_places_for_quantity' => $value['decimal_places'] ?? 2,
                'decimal_places_for_rate' => $value['decimal_places_for_rate'] ?? 2,
            ]);
        }

        return $data;
    }

    private function storeAddresses($deliveryChallanTransaction, $input)
    {
        try {
            $dispatchAddress = $input['dispatch_address'] ?? [];
            $billingAddress = $input['billing_address'] ?? [];
            $shippingAddress = $input['shipping_address'] ?? [];

            $addresses = [];

            if ($dispatchAddress) {
                $addresses[] = $this->prepareAddressData($deliveryChallanTransaction, $dispatchAddress, DeliveryChallanTransaction::DISPATCH_ADDRESS);
            }

            if ($billingAddress) {
                $addresses[] = $this->prepareAddressData($deliveryChallanTransaction, $billingAddress, DeliveryChallanTransaction::BILLING_ADDRESS);
            }

            if (! (isset($input['shipping_address_id']) && $input['shipping_address_id'] != null)) { // This is for if shipping address is not selected or address from local storage
                if ($shippingAddress) {
                    $addresses[] = $this->prepareAddressData($deliveryChallanTransaction, $shippingAddress, DeliveryChallanTransaction::SHIPPING_ADDRESS);
                }
            }

            if (! empty($addresses)) {
                $deliveryChallanTransaction->addresses()->createMany($addresses);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updateAddresses($deliveryChallanTransaction, $input)
    {
        try {
            $dispatchAddress = $input['dispatch_address'] ?? [];
            $billingAddress = $input['billing_address'] ?? [];
            $shippingAddress = $input['shipping_address'] ?? [];

            if ($dispatchAddress) {
                $deliveryChallanTransaction->addresses()->updateOrCreate(
                    ['address_type' => DeliveryChallanTransaction::DISPATCH_ADDRESS, 'model_id' => $deliveryChallanTransaction->id],
                    $this->prepareAddressData($deliveryChallanTransaction, $dispatchAddress, DeliveryChallanTransaction::DISPATCH_ADDRESS)
                );
            }

            if ($billingAddress) {
                $deliveryChallanTransaction->addresses()->updateOrCreate(
                    ['address_type' => DeliveryChallanTransaction::BILLING_ADDRESS, 'model_id' => $deliveryChallanTransaction->id],
                    $this->prepareAddressData($deliveryChallanTransaction, $billingAddress, DeliveryChallanTransaction::BILLING_ADDRESS)
                );
            }

            if (! (isset($input['shipping_address_id']) && $input['shipping_address_id'] != null)) { // This is for if shipping address is not selected or address from local storage
                if ($shippingAddress) {
                    $deliveryChallanTransaction->addresses()->updateOrCreate(
                        ['address_type' => DeliveryChallanTransaction::SHIPPING_ADDRESS, 'model_id' => $deliveryChallanTransaction->id],
                        $this->prepareAddressData($deliveryChallanTransaction, $shippingAddress, DeliveryChallanTransaction::SHIPPING_ADDRESS)
                    );
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareAddressData($deliveryChallanTransaction, $address, $addressType)
    {
        return [
            'address_1' => $address['address_1'] ?? null,
            'address_2' => $address['address_2'] ?? null,
            'country_id' => $address['country_id'] ?? null,
            'state_id' => $address['state_id'] ?? null,
            'city_id' => $address['city_id'] ?? null,
            'pin_code' => $address['pin_code'] ?? null,
            'model_id' => $deliveryChallanTransaction->id,
            'model_type' => DeliveryChallanTransaction::class,
            'address_type' => $addressType,
        ];
    }

    private function storeAdditionalCharges($additionChargesData, $deliveryChallanTransactionId)
    {
        try {
            foreach ($additionChargesData as $additionCharge) {
                /* Prepare additional charge data */
                $additionChargeData = $this->prepareAdditionalChargesData($additionCharge, $deliveryChallanTransactionId);

                /* Store additional charge */
                AdditionalChargesForDeliveryChallanTransaction::create($additionChargeData);
            }

            return true;
        } catch (\Exception $e) {

            throw new \Exception($e->getMessage());
        }
    }

    private function updateAdditionalCharges($additionChargesData, $deliveryChallanTransactionId)
    {
        try {
            $additionalChargesIds = AdditionalChargesForDeliveryChallanTransaction::where('delivery_challan_id', $deliveryChallanTransactionId)->pluck('id')->toArray();
            $editedAdditionalChargesIds = Arr::pluck($additionChargesData, 'id');
            $removeAdditionalChargesIds = array_diff($additionalChargesIds, $editedAdditionalChargesIds);

            /* Delete additional charges */
            AdditionalChargesForDeliveryChallanTransaction::whereIn('id', array_values($removeAdditionalChargesIds))?->delete();

            foreach ($additionChargesData as $additionCharge) {
                /* Prepare additional charge data */
                $additionChargeData = $this->prepareAdditionalChargesData($additionCharge, $deliveryChallanTransactionId);

                /* Update additional charge */
                if (! isset($additionCharge['id']) || $additionCharge['id'] == null) {
                    AdditionalChargesForDeliveryChallanTransaction::create($additionChargeData);
                } else {
                    $additionalChargeRecord = AdditionalChargesForDeliveryChallanTransaction::where('id', $additionCharge['id'])->first();
                    if (! empty($additionalChargeRecord)) {
                        $additionalChargeRecord->update($additionChargeData);
                    }
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareAdditionalChargesData($additionCharge, $deliveryChallanTransactionId)
    {
        if ($this->isGSTEnabled) {
            $gst = null;
            if (isset($additionCharge['ac_gst_rate_id'])) {
                $gst = GstTax::whereId($additionCharge['ac_gst_rate_id'])->first();
            }
            $gstTaxPercentage = ! empty($gst) ? $gst->tax_rate : null;
        }

        return [
            'delivery_challan_id' => $deliveryChallanTransactionId,
            'ledger_id' => $additionCharge['ac_ledger_id'],
            'charge_type' => $additionCharge['ac_type'],
            'value' => $additionCharge['ac_value'],
            'gst_rate_id' => $this->isGSTEnabled && ! empty($gst) ? $gst->id : null,
            'gst_percentage' => $this->isGSTEnabled ? $gstTaxPercentage : null,
            'total_without_tax' => $additionCharge['ac_total_without_tax'],
            'total' => $additionCharge['ac_total'],
        ];
    }

    private function storeAddLess($addLessData, $deliveryChallanTransactionId)
    {
        try {

            foreach ($addLessData as $addLessCharge) {
                /* Prepare add less data */
                $addLessChargeData = $this->prepareAddLessData($addLessCharge, $deliveryChallanTransactionId);

                /* Store add less */
                AddLessForDeliveryChallanTransaction::create($addLessChargeData);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updateAddLess($addLessData, $deliveryChallanTransactionId)
    {
        try {
            $addLessIds = AddLessForDeliveryChallanTransaction::where('delivery_challan_id', $deliveryChallanTransactionId)->pluck('id')->toArray();
            $editedAddLessIds = Arr::pluck($addLessData, 'id');
            $removeAddLessIds = array_diff($addLessIds, $editedAddLessIds);

            /* Delete add less */
            AddLessForDeliveryChallanTransaction::whereIn('id', array_values($removeAddLessIds))?->delete();

            foreach ($addLessData as $addLessCharge) {
                /* Prepare add less data */
                $addLessChargeData = $this->prepareAddLessData($addLessCharge, $deliveryChallanTransactionId);

                /* Update add less */
                if (! isset($addLessCharge['id']) || $addLessCharge['id'] == null) {
                    AddLessForDeliveryChallanTransaction::create($addLessChargeData);
                } else {
                    $addLessRecord = AddLessForDeliveryChallanTransaction::where('id', $addLessCharge['id'])->first();
                    if (! empty($addLessRecord)) {
                        $addLessRecord->update($addLessChargeData);
                    }
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareAddLessData($addLessCharge, $deliveryChallanTransactionId)
    {
        return [
            'delivery_challan_id' => $deliveryChallanTransactionId,
            'ledger_id' => $addLessCharge['al_ledger_id'],
            'is_show_in_print' => $addLessCharge['al_is_show_in_print'] ?? false,
            'type' => $addLessCharge['al_type'],
            'value' => $addLessCharge['al_value'],
            'total' => $addLessCharge['al_total'],
        ];
    }
}
