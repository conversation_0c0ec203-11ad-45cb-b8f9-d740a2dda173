<?php

namespace App\Repositories;

use App\Actions\CustomFields\GetInputTypeWiseValue;
use App\Jobs\CreateReceiptPaymentTransactionJob;
use App\Models\Address;
use App\Models\Configuration\IncomeSalesReturn;
use App\Models\EInvoice;
use App\Models\EwayBill;
use App\Models\LockTransaction;
use App\Models\PaymentTransaction;
use App\Models\SaleReturnItemTransaction;
use App\Models\SaleReturnLedgerTransaction;
use App\Models\SaleReturnTransaction;
use App\Models\TaxClassificationDetails;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;

/**
 * Class SaleReturnTransactionRepository
 */
class SaleReturnTransactionRepository extends BaseRepository
{
    /**
     * @var array
     */
    public $fieldSearchable = [

    ];

    /**
     * Return searchable fields
     */
    public function getFieldsSearchable(): array
    {
        return $this->fieldSearchable;
    }

    /**
     * Configure the Model
     **/
    public function model()
    {
        return SaleReturnTransaction::class;
    }

    public function store($input)
    {
        try {
            DB::beginTransaction();
            unset($input['_token']);
            $currentCompanyId = getCurrentCompany()->id;
            $paymentTypeLedgerId = $input['payment_type_ledger_id'] ?? null;
            if ($input['payment_mode'] == SaleReturnTransaction::CREDIT_MODE) {
                $paymentTypeLedgerId = null;
            }
            $creditPeriod = $input['credit_period'] ?? null;
            $creditPeriodType = $input['credit_period_type'] ?? null;
            $invoiceDate = Carbon::parse($input['date']);
            $creditPeriodDueDate = calculateCreditPeriodDate($creditPeriod, $creditPeriodType, $invoiceDate);

            $saleReturnTransactionData = [
                'company_id' => $currentCompanyId,
                'payment_mode' => $input['payment_mode'] ?? null,
                'payment_type_ledger_id' => $paymentTypeLedgerId,
                'credit_note_number' => $input['credit_note_number'] ?? null,
                'full_invoice_number' => $input['full_invoice_number'],
                'original_inv_no' => $input['original_inv_no'] ?? null,
                'original_inv_date' => $input['original_inv_date'] ?? null,
                'date' => Carbon::parse($input['date']),
                'customer_ledger_id' => $input['customer_ledger_id'],
                'gstin' => $input['gstin'] ?? null,
                'broker_id' => $input['broker_id'] ?? null,
                'brokerage_for_sale' => $input['brokerage_for_sale'] ?? null,
                'brokerage_on_value_type' => $input['brokerage_on_value_type'] ?? null,
                'credit_period' => $creditPeriod,
                'credit_period_type' => $creditPeriodType,
                'credit_period_due_date' => $creditPeriodDueDate,
                'transport_id' => $input['transport_id'] ?? null,
                'shipping_name' => $input['shipping_name'] ?? null,
                'shipping_gstin' => $input['shipping_gstin'] ?? null,
                'transporter_document_number' => $input['transporter_document_number'] ?? null,
                'transporter_document_date' => $input['transporter_document_date'] ?? null,
                'transporter_vehicle_number' => $input['transporter_vehicle_number'] ?? null,
                'po_no' => $input['po_no'] ?? null,
                'po_date' => isset($input['po_date']) ? Carbon::parse($input['po_date']) : null,
                'sale_return_item_type' => $input['sale_return_item_type'],
                'shipping_freight' => $input['shipping_freight'] ?? 0,
                'packing_charge' => $input['packing_charge'] ?? 0,
                'shipping_freight_with_gst' => $input['shipping_freight_with_gst'] ?? 0,
                'packing_charge_with_gst' => $input['packing_charge_with_gst'] ?? 0,
                'shipping_freight_sgst_amount' => $input['shipping_tax_cgst'] ?? 0,
                'shipping_freight_cgst_amount' => $input['shipping_tax_sgst'] ?? 0,
                'shipping_freight_igst_amount' => $input['shipping_tax_igst'] ?? 0,
                'packing_charge_sgst_amount' => $input['packing_tax_cgst'] ?? 0,
                'packing_charge_cgst_amount' => $input['packing_tax_sgst'] ?? 0,
                'packing_charge_igst_amount' => $input['packing_tax_igst'] ?? 0,
                'tcs_tax_id' => $input['tcs_tax_id'] ?? null,
                'tcs_rate' => $input['tcs_rate'] ?? 0,
                'tcs_amount' => $input['tcs_amount'] ?? 0,
                'cgst' => $input['cgst'] ?? 0,
                'sgst' => $input['sgst'] ?? 0,
                'igst' => $input['igst'] ?? 0,
                'rounding_amount' => $input['rounding_amount'] ?? 0,
                'total' => $input['total'],
                'grand_total' => $input['grand_total'],
                'narration' => $input['narration'] ?? null,
                'term_and_condition' => $input['term_and_condition'] ?? null,
                'is_gst_enabled' => getCurrentCompany()->is_gst_applicable,
                'cess' => $input['cess'] ?? 0,
                'billing_state_id' => $input['billing_state_id'],
                'is_cgst_sgst_igst_calculated' => $input['is_cgst_sgst_igst_calculated'] ?? false,
                'is_gst_na' => $input['is_gst_na'] ?? false,
                'created_by' => getLoginUser()->id,
                'via_api' => $input['via_api'] ?? false,
                'is_import' => false,
            ];

            $saleReturnTransaction = SaleReturnTransaction::create($saleReturnTransactionData);

            if (isset($input['sale_return_document']) && ! empty($input['sale_return_document'])) {
                $saleReturnTransaction->addMedia($input['sale_return_document'])->toMediaCollection(SaleReturnTransaction::SALE_RETURN_DOCUMENT, config('app.media_disc'));
            }

            if ($input['payment_mode'] == SaleReturnTransaction::CASH_MODE) {
                CreateReceiptPaymentTransactionJob::dispatch($saleReturnTransaction, SaleReturnTransaction::class,
                    PaymentTransaction::PAYMENT_TRANSACTION);
            }

            $shippingAddress = [
                'address_1' => $input['shipping_address_1'] ?? null,
                'address_2' => $input['shipping_address_2'] ?? null,
                'country_id' => $input['shipping_country_id'] ?? null,
                'state_id' => $input['shipping_state_id'] ?? null,
                'city_id' => $input['shipping_city_id'] ?? null,
                'pin_code' => $input['shipping_pin_code'] ?? null,
                'model_id' => $saleReturnTransaction->id,
                'model_type' => SaleReturnTransaction::class,
                'address_type' => SaleReturnTransaction::SHIPPING_ADDRESS,
            ];
            Address::create($shippingAddress);
            $billingAddress = [
                'address_1' => $input['billing_address_1'] ?? null,
                'address_2' => $input['billing_address_2'] ?? null,
                'country_id' => $input['billing_country_id'] ?? null,
                'state_id' => $input['billing_state_id'] ?? null,
                'city_id' => $input['billing_city_id'] ?? null,
                'pin_code' => $input['billing_pin_code'] ?? null,
                'model_id' => $saleReturnTransaction->id,
                'model_type' => SaleReturnTransaction::class,
                'address_type' => SaleReturnTransaction::BILLING_ADDRESS,
            ];
            Address::create($billingAddress);
            $classificationNatureType = TaxClassificationDetails::whereName($input['main_classification_nature_type'] ?? null)->first()->id ?? null;
            if ($input['sale_return_item_type'] == SaleReturnTransaction::ACCOUNTING_INVOICE) {
                $this->storeSaleReturnLedgers($input, $saleReturnTransaction, $classificationNatureType);
            }
            if ($input['sale_return_item_type'] == SaleReturnTransaction::ITEM_INVOICE) {
                $this->storeSaleReturnItems($input, $saleReturnTransaction, $classificationNatureType);
            }

            /* Update Taxable Value, Payment Status And Due Amount Column Value */
            updateFieldsValue($saleReturnTransaction);

            DB::commit();

            return true;
        } catch (Exception $exception) {
            DB::rollBack();

            throw $exception;
        }
    }

    /**
     * @param  null  $classificationNatureType
     */
    public function storeSaleReturnLedgers($input, $saleReturnTransaction, $classificationNatureType = null)
    {
        foreach ($input['ledger_id'] as $key => $value) {
            $saleLedgerData = [
                'srt_id' => $saleReturnTransaction->id,
                'ledger_id' => $input['ledger_id'][$key] ?? null,
                'additional_description' => $input['additional_description'][$key] ?? null,
                'rpu_with_gst' => $input['rpu_with_gst'][$key] ?? 0,
                'rpu_without_gst' => $input['rpu_without_gst'][$key] ?? 0,
                'discount_type' => $input['discount_type'][$key],
                'discount_value' => $input['discount_value'][$key] ?? 0,
                'total_discount_amount' => $input['total_discount_amount'][$key] ?? 0,
                'gst_id' => $input['gst_id'][$key] ?? null,
                'gst_tax_percentage' => $input['gst_tax_percentage'][$key] ?? 0,
                'total' => $input['sub_total'][$key],
                'classification_igst_tax' => $saleReturnTransaction->is_gst_na ? 0.0 : ($input['classification_igst_tax'][$key] ?? 0),
                'classification_cgst_tax' => $saleReturnTransaction->is_gst_na ? 0.0 : ($input['classification_cgst_tax'][$key] ?? 0),
                'classification_sgst_tax' => $saleReturnTransaction->is_gst_na ? 0.0 : ($input['classification_sgst_tax'][$key] ?? 0),
                'classification_cess_tax' => $saleReturnTransaction->is_gst_na ? 0.0 : ($input['classification_cess_tax'][$key] ?? 0),
                'classification_is_rcm_applicable' => $input['main_is_rcm_applicable'] ?? 0,
                'classification_nature_type' => $classificationNatureType,
            ];
            SaleReturnLedgerTransaction::create($saleLedgerData);
        }
    }

    public function storeSaleReturnItems($input, $saleReturnTransaction, $classificationNatureType = null)
    {
        $saleReturnConfiguration = IncomeSalesReturn::toBase()->first();
        $saleReturnConfigurationConsolidatingItemsToInvoice = $saleReturnConfiguration->consolidating_items_to_invoice ?? false;

        foreach ($input['item_id'] as $key => $value) {
            $saleReturnTransactionItemData = [
                'srt_id' => $saleReturnTransaction->id,
                'item_id' => $input['item_id'][$key],
                'additional_description' => (($input['via_api'] ?? false) || $input['is_additional_item_description']) ? ($input['additional_description'][$key] ?? null) : null,
                'ledger_id' => $input['ledger_id'][$key] ?? null,
                'unit_id' => $input['unit_id'][$key] ?? null,
                'mrp' => $input['mrp'][$key] ?? null,
                'quantity' => $input['quantity'][$key] ?? 0,
                'rpu_with_gst' => $input['rpu_with_gst'][$key] ?? 0,
                'rpu_without_gst' => $input['rpu_without_gst'][$key] ?? 0,
                'discount_type' => $input['discount_type'][$key] ?? null,
                'discount_value' => $input['discount_value'][$key] ?? 0,
                'total_discount_amount' => $input['total_discount_amount'][$key] ?? 0,
                'gst_id' => $input['gst_id'][$key] ?? null,
                'gst_tax_percentage' => $input['gst_tax_percentage'][$key] ?? 0,
                'total' => $input['sub_total'][$key] ?? 0,
                'classification_is_rcm_applicable' => $input['main_is_rcm_applicable'] ?? 0,
                'classification_nature_type' => $classificationNatureType,
                'classification_igst_tax' => $saleReturnTransaction->is_gst_na ? 0.0 : ($input['classification_igst_tax'][$key] ?? null),
                'classification_cgst_tax' => $saleReturnTransaction->is_gst_na ? 0.0 : ($input['classification_cgst_tax'][$key] ?? null),
                'classification_sgst_tax' => $saleReturnTransaction->is_gst_na ? 0.0 : ($input['classification_sgst_tax'][$key] ?? null),
                'classification_cess_tax' => $saleReturnTransaction->is_gst_na ? 0.0 : ($input['classification_cess_tax'][$key] ?? null),
                'consolidating_items_to_invoice' => $saleReturnConfigurationConsolidatingItemsToInvoice ? $input['consolidating_items_to_invoice'][$key] ?? null : null,
                'cess_rate' => $input['cess_rate'][$key] ?? null,
                'cess_amount' => $input['cess_amount'][$key] ?? null,
            ];

            SaleReturnItemTransaction::create($saleReturnTransactionItemData);
        }

        return true;
    }

    public function updateSaleReturn($input, $saleReturn)
    {

        try {
            DB::beginTransaction();
            $paymentTypeLedgerId = $input['payment_type_ledger_id'] ?? null;
            if ($input['payment_mode'] == SaleReturnTransaction::CREDIT_MODE) {
                $paymentTypeLedgerId = null;
            }
            $oldSaleReturn = SaleReturnTransaction::whereId($saleReturn->id)->first();
            PaymentTransaction::whereCompanyId(getCurrentCompany()->id)->wherePaymentVoucherNumber('sale-return/'.$oldSaleReturn->full_invoice_number)
                ->update(['payment_voucher_number' => 'sale-return/'.$input['full_invoice_number']]);

            $creditPeriod = $input['credit_period'] ?? null;
            $creditPeriodType = $input['credit_period_type'] ?? null;
            $invoiceDate = Carbon::parse($input['date']);
            $creditPeriodDueDate = calculateCreditPeriodDate($creditPeriod, $creditPeriodType, $invoiceDate);

            $saleReturnTransactionData = [
                'payment_mode' => $input['payment_mode'] ?? null,
                'payment_type_ledger_id' => $paymentTypeLedgerId,
                // 'credit_note_number'           => $input['credit_note_number'] ?? null,
                'full_invoice_number' => $input['full_invoice_number'],
                'original_inv_no' => $input['original_inv_no'] ?? null,
                'original_inv_date' => $input['original_inv_date'] ?? null,
                'date' => Carbon::parse($input['date']),
                'customer_ledger_id' => $input['customer_ledger_id'],
                'gstin' => $input['gstin'] ?? null,
                'broker_id' => $input['broker_id'] ?? null,
                'brokerage_for_sale' => $input['brokerage_for_sale'] ?? null,
                'brokerage_on_value_type' => $input['brokerage_on_value_type'] ?? null,
                'credit_period' => $creditPeriod,
                'credit_period_type' => $creditPeriodType,
                'credit_period_due_date' => $creditPeriodDueDate,
                'transport_id' => $input['transport_id'] ?? null,
                'shipping_name' => $input['shipping_name'] ?? null,
                'shipping_gstin' => $input['shipping_gstin'] ?? null,
                'transporter_document_number' => $input['transporter_document_number'] ?? null,
                'transporter_document_date' => $input['transporter_document_date'] ?? null,
                'transporter_vehicle_number' => $input['transporter_vehicle_number'] ?? null,
                'po_no' => $input['po_no'] ?? null,
                'po_date' => Carbon::parse($input['po_date']),
                'sale_return_item_type' => $input['sale_return_item_type'],
                'shipping_freight' => $input['shipping_freight'] ?? 0,
                'packing_charge' => $input['packing_charge'] ?? 0,
                'shipping_freight_with_gst' => $input['shipping_freight_with_gst'] ?? 0,
                'packing_charge_with_gst' => $input['packing_charge_with_gst'] ?? 0,
                'shipping_freight_sgst_amount' => $input['shipping_tax_cgst'] ?? 0,
                'shipping_freight_cgst_amount' => $input['shipping_tax_sgst'] ?? 0,
                'shipping_freight_igst_amount' => $input['shipping_tax_igst'] ?? 0,
                'packing_charge_sgst_amount' => $input['packing_tax_cgst'] ?? 0,
                'packing_charge_cgst_amount' => $input['packing_tax_sgst'] ?? 0,
                'packing_charge_igst_amount' => $input['packing_tax_igst'] ?? 0,
                'tcs_tax_id' => $input['tcs_tax_id'] ?? null,
                'tcs_rate' => $input['tcs_rate'] ?? 0,
                'tcs_amount' => $input['tcs_amount'] ?? 0,
                'cgst' => $input['cgst'] ?? 0,
                'sgst' => $input['sgst'] ?? 0,
                'igst' => $input['igst'] ?? 0,
                'rounding_amount' => $input['rounding_amount'] ?? 0,
                'total' => $input['total'],
                'grand_total' => $input['grand_total'],
                'narration' => $input['narration'] ?? null,
                'term_and_condition' => $input['term_and_condition'] ?? null,
                'cess' => $input['cess'] ?? 0,
                'billing_state_id' => $input['billing_state_id'],
                'is_cgst_sgst_igst_calculated' => $input['is_cgst_sgst_igst_calculated'] ?? false,
                'is_gst_na' => $input['is_gst_na'] ?? false,
                'via_api' => $input['via_api'] ?? false,
            ];

            $saleReturn->update($saleReturnTransactionData);

            if (isset($input['sale_return_document']) && ! empty($input['sale_return_document'])) {
                $saleReturn->clearMediaCollection(SaleReturnTransaction::SALE_RETURN_DOCUMENT);
                $saleReturn->addMedia($input['sale_return_document'])->toMediaCollection(SaleReturnTransaction::SALE_RETURN_DOCUMENT, config('app.media_disc'));
            }

            if ($input['payment_mode'] == SaleReturnTransaction::CASH_MODE) {
                CreateReceiptPaymentTransactionJob::dispatch($saleReturn, SaleReturnTransaction::class,
                    PaymentTransaction::PAYMENT_TRANSACTION);
            } else {
                PaymentTransaction::wherePaymentVoucherNumber('sale-return/'.$saleReturn->full_invoice_number)
                    ->whereCompanyId($saleReturn->company_id)
                    ->financialYearDate()?->forceDelete();
            }

            $this->updateAddresses($saleReturn, $input);
            $classificationNatureType = TaxClassificationDetails::whereName($input['main_classification_nature_type'] ?? null)->first()->id ?? null;
            if ($input['sale_return_item_type'] == SaleReturnTransaction::ITEM_INVOICE
                && $oldSaleReturn?->sale_return_item_type == $input['sale_return_item_type']) {

                $this->updateSaleReturnItems($saleReturn, $input, $classificationNatureType);

            } elseif ($input['sale_return_item_type'] == SaleReturnTransaction::ACCOUNTING_INVOICE
                && $oldSaleReturn?->sale_return_item_type == $input['sale_return_item_type']) {

                $this->updateSaleReturnLedger($saleReturn, $input, $classificationNatureType);

            } elseif ($input['sale_return_item_type'] == SaleReturnTransaction::ITEM_INVOICE
                && $oldSaleReturn?->sale_return_item_type != $input['sale_return_item_type']) {
                $saleReturn->saleReturnLedgers()->delete();
                $this->storeSaleReturnItems($input, $saleReturn, $classificationNatureType);

            } elseif ($input['sale_return_item_type'] == SaleReturnTransaction::ACCOUNTING_INVOICE
                && $oldSaleReturn?->sale_return_item_type != $input['sale_return_item_type']) {
                $saleReturn->saleReturnItems()->delete();
                $this->storeSaleReturnLedgers($input, $saleReturn, $classificationNatureType);

            }

            /* Update Taxable Value, Payment Status And Due Amount Column Value */
            updateFieldsValue($saleReturn);

            DB::commit();

            return true;
        } catch (Exception $exception) {
            DB::rollBack();

            throw $exception;
        }
    }

    public function updateSaleReturnItems($saleReturn, $input, $classificationNatureType = null)
    {

        $itemIds = SaleReturnItemTransaction::whereSrtId($saleReturn->id)->pluck('id')->toArray();
        $editSaleReturnLedgerIds = $input['sale_return_item_id'] ?? [];
        $removeItemIds = array_diff($itemIds, $editSaleReturnLedgerIds);
        SaleReturnItemTransaction::whereIn('id', array_values($removeItemIds))?->delete();
        $saleReturnConfiguration = IncomeSalesReturn::toBase()->first();
        $saleReturnConfigurationConsolidatingItemsToInvoice = $saleReturnConfiguration->consolidating_items_to_invoice ?? false;

        foreach ($input['item_id'] as $key => $value) {
            $saleReturnTransactionItemData = [
                'srt_id' => $saleReturn->id,
                'item_id' => $input['item_id'][$key],
                'ledger_id' => $input['ledger_id'][$key] ?? null,
                'additional_description' => (($input['via_api'] ?? false) || $input['is_additional_item_description']) ? ($input['additional_description'][$key] ?? null) : null,
                'unit_id' => $input['unit_id'][$key] ?? null,
                'quantity' => $input['quantity'][$key] ?? 0,
                'mrp' => $input['mrp'][$key] ?? null,
                'rpu_with_gst' => $input['rpu_with_gst'][$key] ?? 0,
                'rpu_without_gst' => $input['rpu_without_gst'][$key] ?? 0,
                'discount_type' => $input['discount_type'][$key] ?? null,
                'discount_value' => $input['discount_value'][$key] ?? 0,
                'total_discount_amount' => $input['total_discount_amount'][$key] ?? 0,
                'gst_id' => $input['gst_id'][$key] ?? null,
                'gst_tax_percentage' => $input['gst_tax_percentage'][$key] ?? 0,
                'total' => $input['sub_total'][$key] ?? 0,
                'classification_is_rcm_applicable' => $input['main_is_rcm_applicable'] ?? 0,
                'classification_nature_type' => $classificationNatureType,
                'classification_igst_tax' => $saleReturn->is_gst_na ? 0.0 : ($input['classification_igst_tax'][$key] ?? 0),
                'classification_cgst_tax' => $saleReturn->is_gst_na ? 0.0 : ($input['classification_cgst_tax'][$key] ?? 0),
                'classification_sgst_tax' => $saleReturn->is_gst_na ? 0.0 : ($input['classification_sgst_tax'][$key] ?? 0),
                'classification_cess_tax' => $saleReturn->is_gst_na ? 0.0 : ($input['classification_cess_tax'][$key] ?? 0),
                'consolidating_items_to_invoice' => $saleReturnConfigurationConsolidatingItemsToInvoice ? $input['consolidating_items_to_invoice'][$key] ?? null : null,
                'cess_rate' => $input['cess_rate'][$key] ?? null,
                'cess_amount' => $input['cess_amount'][$key] ?? null,
            ];
            if (isset($input['sale_return_item_id'][$key])) {
                $oldSaleReturnItem = SaleReturnItemTransaction::whereId($input['sale_return_item_id'][$key])->first();
                $oldSaleReturnItem?->update($saleReturnTransactionItemData);
            } else {
                SaleReturnItemTransaction::create($saleReturnTransactionItemData);
            }
        }

        return true;
    }

    public function updateSaleReturnLedger($saleReturn, $input, $classificationNatureType = null)
    {
        $itemIds = SaleReturnLedgerTransaction::whereSrtId($saleReturn->id)->pluck('id')->toArray();
        $editSaleReturnLedgerIds = $input['sale_return_ledger_id'] ?? [];
        $removeLedgerIds = array_diff($itemIds, $editSaleReturnLedgerIds);
        SaleReturnLedgerTransaction::whereIn('id', array_values($removeLedgerIds))?->delete();

        foreach ($input['ledger_id'] as $key => $value) {
            $saleReturnLedgerData = [
                'srt_id' => $saleReturn->id,
                'ledger_id' => $input['ledger_id'][$key] ?? null,
                'additional_description' => $input['additional_description'][$key] ?? null,
                'rpu_with_gst' => $input['rpu_with_gst'][$key] ?? 0,
                'rpu_without_gst' => $input['rpu_without_gst'][$key] ?? 0,
                'discount_type' => $input['discount_type'][$key],
                'discount_value' => $input['discount_value'][$key] ?? 0,
                'total_discount_amount' => $input['total_discount_amount'][$key] ?? 0,
                'gst_id' => $input['gst_id'][$key] ?? null,
                'gst_tax_percentage' => $input['gst_tax_percentage'][$key] ?? 0,
                'total' => $input['sub_total'][$key] ?? 0,
                'classification_igst_tax' => $saleReturn->is_gst_na ? 0.0 : ($input['classification_igst_tax'][$key] ?? 0),
                'classification_cgst_tax' => $saleReturn->is_gst_na ? 0.0 : ($input['classification_cgst_tax'][$key] ?? 0),
                'classification_sgst_tax' => $saleReturn->is_gst_na ? 0.0 : ($input['classification_sgst_tax'][$key] ?? 0),
                'classification_cess_tax' => $saleReturn->is_gst_na ? 0.0 : ($input['classification_cess_tax'][$key] ?? 0),
                'classification_is_rcm_applicable' => $input['main_is_rcm_applicable'] ?? 0,
                'classification_nature_type' => $classificationNatureType,
            ];
            if (isset($input['sale_return_ledger_id'][$key])) {
                $saleReturnExistLedgerTransactions = SaleReturnLedgerTransaction::whereId($input['sale_return_ledger_id'][$key])->first();
                $saleReturnExistLedgerTransactions?->update($saleReturnLedgerData);
            } else {
                SaleReturnLedgerTransaction::create($saleReturnLedgerData);
            }
        }

        return true;
    }

    public function updateAddresses($saleReturn, $input)
    {

        $saleReturn->load('addresses');
        $shippingAddress = $saleReturn->addresses->firstWhere('address_type',
            SaleReturnTransaction::SHIPPING_ADDRESS);
        $shippingAddressData = [
            'address_1' => $input['shipping_address_1'] ?? null,
            'address_2' => $input['shipping_address_2'] ?? null,
            'country_id' => $input['shipping_country_id'] ?? null,
            'state_id' => $input['shipping_state_id'] ?? null,
            'city_id' => $input['shipping_city_id'] ?? null,
            'pin_code' => $input['shipping_pin_code'] ?? null,
        ];
        $shippingAddress?->update($shippingAddressData);
        $billingAddress = $saleReturn->addresses->firstWhere('address_type',
            SaleReturnTransaction::BILLING_ADDRESS);
        $billingAddressData = [
            'address_1' => $input['billing_address_1'] ?? null,
            'address_2' => $input['billing_address_2'] ?? null,
            'country_id' => $input['billing_country_id'] ?? null,
            'state_id' => $input['billing_state_id'] ?? null,
            'city_id' => $input['billing_city_id'] ?? null,
            'pin_code' => $input['billing_pin_code'] ?? null,
        ];
        $billingAddress?->update($billingAddressData);

        return true;
    }

    public function prepareDataForTable($rows): array
    {
        $data = [];
        $settingDecimal = getCompanyFixedDigitNumber() ?? 2;
        $lockDate = getTransactionsLockDate()[LockTransaction::INCOME] ?? null;

        $eInvoices = EInvoice::whereIn('transaction_id', $rows->pluck('id'))->where('transaction_type', SaleReturnTransaction::class)->get()->keyBy('transaction_id');
        $ewayBills = EwayBill::whereIn('transaction_id', $rows->pluck('id'))->where('transaction_type', SaleReturnTransaction::class)->get()->keyBy('transaction_id');

        /** @var SaleReturnTransaction $row */
        foreach ($rows as $row) {
            $billing = $row->billingAddress;
            $shipping = Address::find($row->shipping_address_id) ?? $row->shippingAddress;
            $dispatch = $row->dispatchAddress;

            // Initialize address strings
            $billingAddress = '';
            $shippingAddress = '';
            $dispatchAddress = '';

            // Prepare billing address
            if (! empty($billing)) {
                $billingAddress = trim(($billing->address_1 ?? '').
                    (! empty($billing->address_2) ? ', '.$billing->address_2 : '').
                    (! empty($billing->city) ? ', '.$billing->city->name : '').
                    (! empty($billing->state) ? ', '.$billing->state->name : '').
                    (! empty($billing->country) ? ', '.$billing->country->name : '').
                    (! empty($billing->pin_code) ? ' - '.$billing->pin_code : ''));
            }

            // Prepare shipping address
            if (! empty($shipping)) {
                $shippingAddress = ($shipping->address_1 ?? null).($shipping->address_2 ? ', '.$shipping->address_2 : '')
                    .($shipping->city ? ', '.$shipping->city->name : '')
                    .($shipping->state ? ', '.$shipping->state->name : '')
                    .($shipping->country ? ', '.$shipping->country->name : '')
                    .($shipping->pin_code ? ' - '.$shipping->pin_code : '');
            }

            // Prepare dispatch address
            if (! empty($dispatch)) {
                $dispatchAddress = trim(($dispatch->address_1 ?? '').
                    (! empty($dispatch->address_2) ? ', '.$dispatch->address_2 : '').
                    (! empty($dispatch->city) ? ', '.$dispatch->city->name : '').
                    (! empty($dispatch->state) ? ', '.$dispatch->state->name : '').
                    (! empty($dispatch->country) ? ', '.$dispatch->country->name : '').
                    (! empty($dispatch->pin_code) ? ' - '.$dispatch->pin_code : ''));
            }

            $isLocked = ! empty($lockDate) && Carbon::parse($lockDate)->greaterThanOrEqualTo($row->date);

            // $einvoice = EInvoice::where('transaction_id', $row->id)->first();
            $einvoice = $eInvoices[$row->id] ?? null;
            $ewayBill = $ewayBills[$row->id] ?? null;

            $customFields = [];

            if (! empty($row->customFieldValues)) {
                foreach ($row->customFieldValues as $customFieldValue) {
                    $value = GetInputTypeWiseValue::run($customFieldValue);
                    $customFields[$customFieldValue->customField->label_name] = $value ?? $customFieldValue->value ?? '';
                }
            }

            // Get customer information
            $customer = $row->customer;

            $phoneNumber = ! empty($row->party_phone_number)
                ? $row->party_phone_number : (! empty($customer->model->phone_1)
                ? $customer->model->phone_1 : (! empty($customer->model->phone_2)
                ? $customer->model->phone_2 : ''));

            $data[] = [
                'id' => $row->id,
                'party_name' => $row->customer->name,
                'party_id' => $row->customer->id,
                'gstin' => $row->gstin,
                'mobile_number' => $row->party_phone_number ?? '',
                'phone_number' => $phoneNumber,
                'credit_note_number' => $row->full_invoice_number,
                'credit_note_date' => ! empty($row->date) ? Carbon::parse($row->date)->format('d-m-Y') : '',
                'original_invoice_number' => ! empty($row->sale->full_invoice_number) ? $row->sale->full_invoice_number : '',
                'original_invoice_id' => $row->original_inv_no ?? 0,
                'original_invoice_date' => ! empty($row->original_inv_date) ? Carbon::parse($row->original_inv_date)->format('d-m-Y') : '',
                'dispatch_address' => $dispatchAddress,
                'billing_address' => $billingAddress,
                'shipping_gstin' => $row->shipping_gstin ?? null,
                'shipping_name' => $row->shipping_name ?? null,
                'shipping_address' => $shippingAddress,
                'broker_name' => $row->brokerDetails->broker_name ?? '',
                'brokerage' => round($row->brokerage_for_sale ?? 0.00, $settingDecimal),
                'transport_name' => $row->transport->transporter_name ?? '',
                'transporter_document_number' => $row->transporter_document_number,
                'transporter_document_date' => ! empty($row->transporter_document_date) ?
                    Carbon::parse($row->transporter_document_date)->format('d-m-Y') : '',
                'transporter_vehicle_number' => $row->transporter_vehicle_number ?? null,
                'credit_period' => ! empty($row->credit_period) && ! empty($row->credit_period_type) ? getCreditPeriod($row->credit_period, $row->credit_period_type) : '',
                'irn_number' => $einvoice->irn ?? null,
                'irn_date' => ! empty($einvoice->ack_date) ? Carbon::parse($einvoice->ack_date)->format('d-m-Y') : null,
                'po_no' => $row->po_no,
                'po_date' => ! empty($row->po_date) ? Carbon::parse($row->po_date)->format('d-m-Y') : '',
                'gross_value' => round($row->gross_value ?? 0.00, $settingDecimal),
                'additional_charges' => round($row->additionalCharges->sum('total_without_tax') ?? 0.00, $settingDecimal),
                'taxable_value' => round($row->taxable_value, $settingDecimal) ?? 0.00,
                'cgst' => round($row->cgst, $settingDecimal) ?? 0.00,
                'sgst' => round($row->sgst, $settingDecimal) ?? 0.00,
                'igst' => round($row->igst, $settingDecimal) ?? 0.00,
                'cess' => round($row->cess, $settingDecimal) ?? 0.00,
                'tcs_ledger' => $row->tcsLedger->name ?? null,
                'tcs_rate' => $row->tcs_rate ?? 0.00,
                'tcs_amount' => round($row->tcs_amount, $settingDecimal) ?? 0.00,
                'add_less_amount' => round($row->addLess->sum('total') ?? 0.00, $settingDecimal),
                'round_off' => round($row->rounding_amount, $settingDecimal) ?? 0.00,
                'invoice_amount' => round($row->grand_total, $settingDecimal) ?? 0.00,
                'note' => $row->narration ?? null,
                'terms_and_conditions' => $row->term_and_condition ?? null,
                'tds_ledger' => $row->tdsLedger->name ?? null,
                'tds_rate' => $row->tds_rate ?? 0.00,
                'tds_amount' => round($row->tds_amount, $settingDecimal) ?? 0.00,
                'paid_amount' => round($row->paid_amount, $settingDecimal) ?? 0.00,
                'credit_period_due_date' => ! empty($row->credit_period_due_date) ?
                    Carbon::parse($row->credit_period_due_date)->format('d-m-Y') : '',
                'due_amount' => round($row->due_amount, $settingDecimal) ?? 0.00,
                'payment_status' => $row->payment_status ?? '',
                'created_by' => $row->createdBy->first_name ?? null,
                'created_at' => ! empty($row->updated_at) ? Carbon::parse($row->updated_at)->format('d-m-Y h:i A') : '',
                'media' => $row->getSaleReturnFileAttribute() ?? null,
                'is_locked' => $isLocked,
                'is_invoice_locked' => ! empty($row->sale) ? isLockTransaction(LockTransaction::INCOME, $row->sale->date) : false,
                'einvoice_id' => ! empty($einvoice) ? $einvoice->id : null,
                'is_einvoice_cancel' => ! empty($einvoice) ? $einvoice->is_canceled : null,
                'show_einvoice_cancel_btn' => ! empty($einvoice) ? Carbon::parse($einvoice->created_at)->diffInHours(Carbon::now()) <= 24 : false,
                'eway_bill_id' => ! empty($ewayBill) ? $ewayBill->id : null,
                'is_eway_bill_cancel' => ! empty($ewayBill) ? $ewayBill->is_canceled : null,
                'show_eway_bill_cancel_btn' => ! empty($ewayBill) ? Carbon::parse($ewayBill->created_at)->diffInHours(Carbon::now()) <= 24 : false,
            ] + $customFields;
        }

        return $data;
    }
}
