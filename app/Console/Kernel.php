<?php

namespace App\Console;

use App\Console\Commands\AssistantNeededCommand;
use App\Console\Commands\AutoSendWhatsAppReminder;
use App\Console\Commands\ChangeValidForStatusInIncomeEstimateQuoteTransaction;
use App\Console\Commands\CheckIfDeviceIsConnected;
use App\Console\Commands\CheckIfTelescopeIsEnabled;
use App\Console\Commands\CheckMissingReceiptOrPaymentForAnyTransaction;
use App\Console\Commands\CompanyWorkStatus;
use App\Console\Commands\DatabaseBackup;
use App\Console\Commands\DeleteDailyReportMessages;
use App\Console\Commands\DeleteForgotPasswordRecords;
use App\Console\Commands\DeleteMobileAssetsDownloadFile;
use App\Console\Commands\FetchDeliveryChallanDataFromVastra;
use App\Console\Commands\NotVerifiedUserDeleteEveryThreeDay;
use App\Console\Commands\PlanExpireAutoSendNotifications;
use App\Console\Commands\ProcessRecurringInvoices;
use App\Console\Commands\SendDailyOutstandingReportWhatsApp;
use App\Console\Commands\SendDailyReportWhatsAppMessage;
use App\Console\Commands\SendMonthlyEndWhatsAppMessage;
use App\Console\Commands\SendScheduledDateTimePushNotifications;
use App\Console\Commands\SendScheduledNotifications;
use App\Console\Commands\SendTrialPeriodEndWhatsAppMessage;
use App\Console\Commands\SoftDeleteCompanyDelete;
use App\Console\Commands\TrialPeriodExpiredAutoSendNotifications;
use App\Console\Commands\UpdateEndSubscriptionStatus;
use App\Console\Commands\UpdateUserWhatsappCredit;
use App\Console\Commands\ZohoCMSUpdate;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // $schedule->command('inspire')->hourly();
        $schedule->command(CompanyWorkStatus::class)->monthly()->appendOutputTo(storage_path('logs/commands.log'));
        $schedule->command(DatabaseBackup::class)->everySixHours()->appendOutputTo(storage_path('logs/commands.log'));
        $schedule->command(DeleteForgotPasswordRecords::class)->daily()->appendOutputTo(storage_path('logs/commands.log'));
        $schedule->command(DeleteMobileAssetsDownloadFile::class)->daily();
        $schedule->command(NotVerifiedUserDeleteEveryThreeDay::class)->daily();
        $schedule->command(SoftDeleteCompanyDelete::class)->daily();
        $schedule->command(SendTrialPeriodEndWhatsAppMessage::class)->dailyAt('09:00');
        $schedule->command(TrialPeriodExpiredAutoSendNotifications::class)->dailyAt('08:00');
        $schedule->command(PlanExpireAutoSendNotifications::class)->dailyAt('08:00');
        $schedule->command(SendScheduledNotifications::class)->dailyAt('08:00');
        $schedule->command(SendScheduledDateTimePushNotifications::class)->everyMinute();
        $schedule->command(SendDailyReportWhatsAppMessage::class)->everyMinute();
        $schedule->command(SendDailyOutstandingReportWhatsApp::class)->dailyAt('08:00');
        // $schedule->command(SendMonthlyEndWhatsAppMessage::class)->monthly()->at('23:59');
        $schedule->command(UpdateEndSubscriptionStatus::class)->dailyAt('01:00');
        $schedule->command(CheckIfDeviceIsConnected::class)->everyTwoHours();
        $schedule->command('telescope:prune --hours=48')->daily();
        $schedule->command(ChangeValidForStatusInIncomeEstimateQuoteTransaction::class)->dailyAt('01:00');
        $schedule->command(UpdateUserWhatsappCredit::class)->dailyAt('00:00');
        $schedule->command(AutoSendWhatsAppReminder::class)->hourly();
        $schedule->command(DeleteDailyReportMessages::class)->daily();
        $schedule->command(CheckIfTelescopeIsEnabled::class)->everyThreeHours();
        // $schedule->command(SendSMSToCustomers::class)->everyThreeHours();
        if (app()->environment() == 'production') {
            $schedule->command(ZohoCMSUpdate::class)->daily();
            $schedule->command(CheckMissingReceiptOrPaymentForAnyTransaction::class)->everyFiveMinutes();
            $schedule->command(AssistantNeededCommand::class)->monthly();
        }
        $schedule->command(ProcessRecurringInvoices::class)->dailyAt('00:01');
        $schedule->command(FetchDeliveryChallanDataFromVastra::class)->everyThirtyMinutes();
        // if (config('app.nightwatch_enabled')) {
        //     $schedule->command('nightwatch:agent')->everySecond();
        // }
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
