{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.2", "algolia/scout-extended": "^3.0", "anhskohbo/no-captcha": "^3.5", "barryvdh/laravel-dompdf": "^2.0", "barryvdh/laravel-snappy": "^1.0", "berkayk/onesignal-laravel": "^2.1", "doctrine/dbal": "^3.10.0", "embed/embed": "^4.4", "guzzlehttp/guzzle": "^7.2", "jenssegers/agent": "^2.6", "lab404/laravel-impersonate": "^1.7", "laracasts/flash": "^3.2", "laravel/framework": "^10.28", "laravel/nightwatch": "^1.11", "laravel/sanctum": "^3.3.3", "laravel/socialite": "^5.15", "laravel/telescope": "^5.10.0", "laravel/tinker": "^2.8", "laravelcollective/html": "^6.4", "league/flysystem-aws-s3-v3": "^3.0", "livewire/livewire": "^2.11", "lorisleiva/laravel-actions": "^2.7.2", "maatwebsite/excel": "^3.1", "mixpanel/mixpanel-php": "^2.11", "opcodesio/log-viewer": "^3.18.0", "owen-it/laravel-auditing": "^13.6", "picqer/php-barcode-generator": "^3.1.0", "prettus/l5-repository": "^2.9", "rappasoft/laravel-livewire-tables": "^2.11", "razorpay/razorpay": "^2.8", "revolution/laravel-google-sheets": "^6.4", "simplesoftwareio/simple-qrcode": "^4.2", "spatie/browsershot": "^5.0.10", "spatie/laravel-backup": "^9.3.3", "spatie/laravel-medialibrary": "^10.7", "spatie/laravel-permission": "^5.8", "spatie/laravel-query-builder": "^5.2", "symfony/http-client": "^7.3.1", "symfony/postmark-mailer": "^7.1.6", "tightenco/ziggy": "1.4", "yajra/laravel-datatables-oracle": "^10.3"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.8", "barryvdh/laravel-ide-helper": "^3.1.0", "fakerphp/faker": "^1.9.1", "jasonmccreary/laravel-test-assertions": "^2.5.0", "larastan/larastan": "^2.11.2", "laravel-shift/factory-generator": "dev-master", "laravel/breeze": "^1.29.1", "laravel/pint": "^1.13", "laravel/sail": "^1.18", "mockery/mockery": "^1.6", "nunomaduro/collision": "^7.0", "pestphp/pest": "^2.35.1", "pestphp/pest-plugin-laravel": "^2.2", "phpunit/phpunit": "^10.5", "spatie/laravel-ignition": "^2.0", "wulfheart/laravel-actions-ide-helper": "^0.10.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true}}, "minimum-stability": "stable", "prefer-stable": true}