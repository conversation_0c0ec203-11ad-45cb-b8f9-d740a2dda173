<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('item_custom_field_combinations', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('item_id');
            $table->foreign('item_id')->references('id')->on('item_masters')->onDelete('cascade');
            $table->double('available_quantity', 15, 2)->nullable();
            $table->unsignedBigInteger('cf_id_1');
            $table->foreign('cf_id_1')->references('id')->on('item_custom_field')->onDelete('cascade');
            $table->string('cf_value_1')->nullable();
            $table->unsignedBigInteger('cf_id_2')->nullable();
            $table->foreign('cf_id_2')->references('id')->on('item_custom_field')->onDelete('cascade');
            $table->string('cf_value_2')->nullable();
            $table->unsignedBigInteger('cf_id_3')->nullable();
            $table->foreign('cf_id_3')->references('id')->on('item_custom_field')->onDelete('cascade');
            $table->string('cf_value_3')->nullable();
            $table->unsignedBigInteger('cf_id_4')->nullable();
            $table->foreign('cf_id_4')->references('id')->on('item_custom_field')->onDelete('cascade');
            $table->string('cf_value_4')->nullable();
            $table->unsignedBigInteger('cf_id_5')->nullable();
            $table->foreign('cf_id_5')->references('id')->on('item_custom_field')->onDelete('cascade');
            $table->string('cf_value_5')->nullable();
            $table->double('purchase_rate', 15, 2)->nullable();
            $table->date('purchase_date')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('item_custom_field_combinations');
    }
};
