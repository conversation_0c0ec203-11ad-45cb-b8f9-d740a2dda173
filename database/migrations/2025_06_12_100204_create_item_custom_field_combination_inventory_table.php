<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('item_custom_field_combination_inventory', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('item_custom_field_combination_id');
            $table->foreign('item_custom_field_combination_id', 'item_custom_field_combination_id_fk')->references('id')->on('item_custom_field_combinations')->onDelete('cascade');
            $table->integer('model_id');
            $table->string('model_type');
            $table->double('quantity', 15, 2);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('item_custom_field_combination_inventory');
    }
};
