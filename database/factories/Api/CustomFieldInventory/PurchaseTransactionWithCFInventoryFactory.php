<?php

namespace Database\Factories\Api\CustomFieldInventory;

use App\Models\Ledger;
use App\Models\Master\Expense;
use App\Models\Master\ItemMaster;
use App\Models\PurchaseTransaction;
use Carbon\Carbon;
use Faker\Factory as Faker;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Model>
 */
class PurchaseTransactionWithCFInventoryFactory extends Factory
{
    protected $model = PurchaseTransaction::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $gstTaxes = getGstTaxes();
        $ledgerId = Ledger::whereCompanyId(getCurrentCompany()->id)->whereModelType(Expense::class)->first()->id;
        $purchaseInputs = [
            'voucher_number' => 'V-'.rand(1, 5000),
            'sale_number' => rand(1, 5000),
            'voucher_date' => $this->getFinancialYearDate(),
            'date_of_invoice' => $this->getFinancialYearDate(),
            'supplier_ledger_id' => array_rand(getAllChildTransactionLedgers([\App\Models\Master\Customer::class, \App\Models\Master\Supplier::class])),
            'purchase_item_type' => 2, // 2: Item Invoice, 1: accounting invoice
            'is_cgst_sgst_igst_calculated' => false,
            'is_gst_na' => true,
            'narration' => 'test',
            'term_and_condition' => 'test',
            'submit_button_value' => PurchaseTransaction::SAVE_BUTTON,
            'main_classification_nature_type' => 'Intrastate Purchase Taxable',
            'is_gst_enabled' => 0,
            'is_round_off_not_changed' => 0,
            'rounding_amount' => 0.0,
            'gross_value' => 100,
            'taxable_value' => 100,
            'grand_total' => 100,
            'billing_address' => [
                'address_1' => $this->faker->address(),
                'country_id' => 1,
                'state_id' => 1,
                'city_id' => 1,
            ],
            'shipping_address' => [
                'shipping_name' => $this->faker->name(),
                'country_id' => 1,
                'state_id' => 1,
                'city_id' => 1,
            ],
            'is_rcm_applicable' => 0,
            'cgst' => 0,
            'sgst' => 0,
            'igst' => 0,
            'cess' => 0,
            'round_off_method' => 1,
        ];

        if ($purchaseInputs['purchase_item_type'] == 2) {
            return array_merge($this->getItemInvoiceInputs($gstTaxes), $purchaseInputs);
        }

        return $purchaseInputs;
    }

    private function getItemInvoiceInputs(array $gstTaxes): array
    {
        // $ids = array_keys(getItemMasters());
        $items = ItemMaster::where('id', 1)->get()->keyBy('id');

        $purchaseItemsInputs = [];
        foreach ($items as $id => $item) {
            $gstID = array_rand($gstTaxes);
            $gstPercentage = in_array($gstTaxes[$gstID], ['0%', 'NA', 'Exempt']) ? 0 : $gstTaxes[$gstID];
            $gstPercentage = str_replace('%', '', $gstPercentage);
            $ledgerId = Ledger::whereCompanyId(getCurrentCompany()->id)->whereName(Ledger::PURCHASE)->first()->id;

            $purchaseItemsInputs[] = [
                'item_id' => $id,
                'additional_description' => $this->faker->sentence(),
                'rpu' => 100,
                'rpu_with_gst' => 100,
                'rpu_without_gst' => 100,
                'mrp' => 100,
                'with_tax' => true,
                'discount_type' => 1,
                'discount_value' => 0,
                'total_discount_amount' => 0,
                'gst_id' => $gstID,
                'gst_tax' => $gstID,
                'gst_tax_percentage' => $gstPercentage,
                'total' => 100,
                'cess' => 0,
                'ledger_id' => $ledgerId,
                'unit_id' => $item->model->unit_of_measurement,
                'quantity' => 1,
                'classification_igst_tax' => 0,
                'classification_cgst_tax' => 0,
                'classification_sgst_tax' => 0,
            ];
        }

        return ['items' => $purchaseItemsInputs];
    }

    private function getFinancialYearDate(): string
    {
        $faker = Faker::create();

        $currentYear = now()->year;
        $financialYearStart = Carbon::createFromDate($currentYear, 4, 1)->format('d-m-Y'); // Assuming financial year starts from April 1st
        $financialYearEnd = Carbon::createFromDate($currentYear + 1, 3, 31)->format('d-m-Y'); // Ends on March 31st of the next year

        return $faker->dateTimeBetween($financialYearStart, $financialYearEnd)->format('d-m-Y');
    }
}
