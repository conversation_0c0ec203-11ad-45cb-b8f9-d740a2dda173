<?php

namespace Database\Factories\Api\CustomFieldInventory;

use App\Actions\v1\Transactions\Purchase\GetPurchaseTransactionAction;
use App\Models\PurchaseTransaction;
use Barryvdh\Reflection\DocBlock\Type\Collection;
use Carbon\Carbon;
use Closure;
use Faker\Factory as Faker;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Model>
 */
class PreparePurchaseEditResponseFactory extends Factory
{
    protected $model = PurchaseTransaction::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $attributes = $this->getAttributes();

        $lastPurchase = null;
        if (isset($attributes['id'])) {
            $lastPurchase = PurchaseTransaction::findOrFail($attributes['id']);
        } else {
            $lastPurchase = PurchaseTransaction::orderBy('id', 'desc')->first();
        }

        $purchase = GetPurchaseTransactionAction::run($lastPurchase['id'], false);

        $purchaseInputs = [
            'voucher_number' => $purchase->voucher_number,
            'sale_number' => $purchase->sale_number,
            'voucher_date' => $this->getFinancialYearDate(),
            'date_of_invoice' => $this->getFinancialYearDate(),
            'supplier_ledger_id' => $purchase->supplier_ledger_id,
            'purchase_item_type' => 2, // 2: Item Invoice, 1: accounting invoice
            'is_cgst_sgst_igst_calculated' => false,
            'is_gst_na' => true,
            'narration' => 'test',
            'term_and_condition' => 'test',
            'submit_button_value' => PurchaseTransaction::SAVE_BUTTON,
            'main_classification_nature_type' => 'Intrastate Purchase Taxable',
            'is_gst_enabled' => 0,
            'is_round_off_not_changed' => 0,
            'rounding_amount' => 0.0,
            'gross_value' => 100,
            'taxable_value' => 100,
            'grand_total' => 100,
            'billing_address' => [
                'address_1' => $this->faker->address(),
                'country_id' => 1,
                'state_id' => 1,
                'city_id' => 1,
            ],
            'shipping_address' => [
                'shipping_name' => $this->faker->name(),
                'country_id' => 1,
                'state_id' => 1,
                'city_id' => 1,
            ],
            'is_rcm_applicable' => 0,
            'cgst' => 0,
            'sgst' => 0,
            'igst' => 0,
            'cess' => 0,
            'round_off_method' => 1,
        ];

        if ($purchaseInputs['purchase_item_type'] == 2) {
            return array_merge($this->getItemInvoiceInputs($purchase), $purchaseInputs);
        }

        return $purchaseInputs;
    }

    private function getItemInvoiceInputs($purchase): array
    {
        $items = $purchase['purchaseTransactionItems'];

        $purchaseItemsInputs = [];

        foreach ($items as $id => $item) {
            $purchaseItemsInputs[] = [
                'id' => $item['id'],
                'item_id' => $item['item_id'],
                'additional_description' => $item['additional_description'],
                'rpu' => $item['rpu_with_gst'],
                'rpu_with_gst' => $item['rpu_with_gst'],
                'rpu_without_gst' => $item['rpu_without_gst'],
                'mrp' => $item['mrp'],
                'with_tax' => true,
                'discount_type' => 1,
                'discount_value' => 0,
                'total_discount_amount' => 0,
                'gst_id' => $item['gst_id'],
                'gst_tax' => $item['gst_id'],
                'gst_tax_percentage' => $item['gst_tax_percentage'],
                'total' => 100,
                'cess' => 0,
                'ledger_id' => $item['ledger_id'],
                'unit_id' => $item['unit_id'],
                'quantity' => 1,
                'classification_igst_tax' => 0,
                'classification_cgst_tax' => 0,
                'classification_sgst_tax' => 0,
            ];
        }

        return ['items' => $purchaseItemsInputs];
    }

    private function getFinancialYearDate(): string
    {
        $faker = Faker::create();

        $currentYear = now()->year;
        $financialYearStart = Carbon::createFromDate($currentYear, 4, 1)->format('d-m-Y'); // Assuming financial year starts from April 1st
        $financialYearEnd = Carbon::createFromDate($currentYear + 1, 3, 31)->format('d-m-Y'); // Ends on March 31st of the next year

        return $faker->dateTimeBetween($financialYearStart, $financialYearEnd)->format('d-m-Y');
    }

    public function getAttributes()
    {
        $parent = null;

        return $this->states->pipe(function ($states) {
            return $this->for->isEmpty() ? $states : new Collection(array_merge([function () {
                return $this->parentResolvers();
            }], $states->all()));
        })->reduce(function ($carry, $state) use ($parent) {
            if ($state instanceof Closure) {
                $state = $state->bindTo($this);
            }

            return array_merge($carry, $state($carry, $parent));
        }, []);

    }
}
