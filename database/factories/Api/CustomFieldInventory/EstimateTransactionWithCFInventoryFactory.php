<?php

namespace Database\Factories\Api\CustomFieldInventory;

use App\Models\IncomeEstimateQuoteTransaction;
use App\Models\Ledger;
use App\Models\Master\Income;
use App\Models\Master\ItemMaster;
use Barryvdh\Reflection\DocBlock\Type\Collection;
use Carbon\Carbon;
use Closure;
use Faker\Factory as Faker;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Model>
 */
class EstimateTransactionWithCFInventoryFactory extends Factory
{
    protected $model = IncomeEstimateQuoteTransaction::class;

    public function definition(): array
    {
        $attributes = $this->getAttributes(); // for sales item type get value from test data
        $gstTaxes = getGstTaxes();
        $estimateQuoteInputs = [
            'title' => array_rand(getEstimateQuoteTitles()),
            'document_number' => 'INV-'.rand(1, 5000),
            'date' => $this->getFinancialYearDate(),
            'party_ledger_id' => array_rand(getAllChildTransactionLedgers([\App\Models\Master\Customer::class, \App\Models\Master\Supplier::class])),
            'valid_for' => 30,
            'valid_for_type' => 1, // 1 : days, 2 : months
            'invoice_type' => $attributes['invoice_type'] ?? 2,
            'is_cgst_sgst_igst_calculated' => 0,
            'is_gst_na' => 0,
            'narration' => 'test',
            'term_and_condition' => 'test',
            'company_gst_applicable' => 0,
            'is_gst_enabled' => 0,
            'is_round_off_not_changed' => 1,
            'main_classification_nature_type' => 'Intrastate Sales Taxable',
            'round_off_method' => 3,
            'rounding_amount' => 0.0,
            'taxable_value' => 0,
            'gross_value' => 100,
            'taxable_value' => 100,
            'grand_total' => 100,
            'billing_address' => [
                'country_id' => 1,
                'state_id' => 1,
                'city_id' => 1,
            ],
            'shipping_address' => [
                'shipping_name' => $this->faker->name(),
                'country_id' => 1,
                'state_id' => 1,
                'city_id' => 1,
            ],
            'is_rcm_applicable' => 0,
            'cgst' => 0,
            'sgst' => 0,
            'igst' => 0,
            'cess' => 0,
        ];

        if ($estimateQuoteInputs['invoice_type'] == 2) {
            return array_merge($this->getItemInvoiceInputs($gstTaxes), $estimateQuoteInputs);
        }

        return $estimateQuoteInputs;
    }

    private function getItemInvoiceInputs(array $gstTaxes): array
    {
        $items = ItemMaster::where('id', 1)->get()->keyBy('id');

        $estimateQuoteItemsInputs = [];
        foreach ($items as $id => $item) {
            $gstID = array_rand($gstTaxes);
            $gstPercentage = in_array($gstTaxes[$gstID], ['0%', 'NA', 'Exempt']) ? 0 : $gstTaxes[$gstID];
            $gstPercentage = str_replace('%', '', $gstPercentage);
            $ledgerId = Ledger::whereCompanyId(getCurrentCompany()->id)->whereName(Ledger::SALE)->first()->id;
            $estimateQuoteItemsInputs[] = [
                'item_id' => $id,
                'additional_description' => $this->faker->sentence(),
                'rpu' => 100,
                'mrp' => 100,
                'ledger_id' => $ledgerId ?? 1,
                'rpu_without_gst' => 100,
                'rpu_with_gst' => 100,
                'quantity' => 1,
                'discount_type' => 1,
                'discount_value' => 0,
                'total_discount_amount' => 0,
                'gst_id' => $gstID,
                'gst_tax' => $gstID,
                'gst_tax_percentage' => $gstPercentage,
                'classification_igst_tax' => 0,
                'classification_cgst_tax' => 0,
                'classification_sgst_tax' => 0,
                'consolidating_items_to_invoice' => false,
                'cess' => 0,
                'total' => 100,
                'unit_id' => $item->model->unit_of_measurement,
                'with_tax' => true,
            ];
        }

        return ['items' => $estimateQuoteItemsInputs];
    }

    private function getFinancialYearDate(): string
    {
        $faker = Faker::create();

        $currentYear = now()->year;
        $financialYearStart = Carbon::createFromDate($currentYear, 4, 1)->format('d-m-Y');
        $financialYearEnd = Carbon::createFromDate($currentYear + 1, 3, 31)->format('d-m-Y');

        return $faker->dateTimeBetween($financialYearStart, $financialYearEnd)->format('d-m-Y');
    }

    public function getAttributes()
    {
        $parent = null;

        return $this->states->pipe(function ($states) {
            return $this->for->isEmpty() ? $states : new Collection(array_merge([function () {
                return $this->parentResolvers();
            }], $states->all()));
        })->reduce(function ($carry, $state) use ($parent) {
            if ($state instanceof Closure) {
                $state = $state->bindTo($this);
            }

            return array_merge($carry, $state($carry, $parent));
        }, []);

    }
}
