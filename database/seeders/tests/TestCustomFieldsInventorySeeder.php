<?php

namespace Database\Seeders\tests;

use App\Actions\CustomFieldsItemMaster\StoreCustomFieldItemMaster;
use App\Models\ItemCustomField;
use App\Models\ItemCustomFieldSetting;
use Illuminate\Database\Seeder;

class TestCustomFieldsInventorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $data = [
            [
                'label_name' => 'IMEI',
                'custom_field_type' => 2,
                'enable_for_all' => 0,
                'open_in_popup' => true,
                'types' => [
                    [
                        'type' => 1, // sale
                        'is_show_in_print' => 1,
                    ],
                    [
                        'type' => 8, // purchase
                        'is_show_in_print' => 1,
                    ],
                ],
            ],
            [
                'label_name' => 'COLOR',
                'custom_field_type' => 2,
                'enable_for_all' => 0,
                'open_in_popup' => true,
                'types' => [
                    [
                        'type' => 1, // sale
                        'is_show_in_print' => 1,
                    ],
                    [
                        'type' => 8, // purchase
                        'is_show_in_print' => 1,
                    ],
                ],
            ],
        ];

        foreach ($data as $datum) {
            ItemCustomField::create([
                'company_id' => getCurrentCompany()->id,
                'label_name' => trim($datum['label_name']),
                'custom_field_type' => $datum['custom_field_type'],
                'enable_for_all' => isset($datum['enable_for_all']) ? $datum['enable_for_all'] : false,
                'open_in_popup' => isset($datum['open_in_popup']) ? $datum['open_in_popup'] : false,
            ]);
        }

        $ids = array_keys(getItemMasters());

        // New created custom fields will be assigned to all items
        $customFields = ItemCustomField::all()->keyBy('id');

        $customFields->each(function ($field) use ($ids) {
            foreach ($ids as $id) {
                ItemCustomFieldSetting::create([
                    'item_id' => $id,
                    'custom_field_id' => $field->id,
                ]);
            }
        });
    }
}
