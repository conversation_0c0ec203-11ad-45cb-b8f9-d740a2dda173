@php
    use App\Models\LockTransaction;
    $user = getLoginUser();
    $eInvoiceAPICred = getEInvAPICredentials();
    $dispatchAddress = $rows['companyDispatchAddress'];
@endphp
<div class="d-flex">
    <div class="d-flex align-items-center">
        @if ($user->can('company_edit_income') && activeSubscription() && !isOnlyForMobilePlan())
            @if ($row['is_locked'])
                <a href="javascript:void(0)" class="text-hover-primary me-2 edit-btn open-lock-transaction-alert"
                    data-bs-toggle="tooltip" title="Edit"
                    data-lock-date="{{ getTransactionsLockDate()[LockTransaction::INCOME] }}">
                    <i class="fas fa-edit text-primary"></i>
                </a>
            @else
                <a href="{{ route('company.sale-returns.edit', ['sale_return' => $row['id']]) }}"
                    class="text-hover-primary me-2 edit-btn" data-bs-toggle="tooltip" title="Edit">
                    <i class="fas fa-edit text-primary"></i>
                </a>
            @endif
        @endif
        <a href="javascript:void(0)" data-id="{{ $row['id'] }}"
            class="view-sale-return-modal text-hover-warning me-2" data-bs-toggle="tooltip" title="View">
            <i class="fas fa-info-circle text-warning"></i>
        </a>
        {{-- @if (getCompanyPdfFormat(true) == \App\Models\CompanySetting::THERMAL_PRINT)
            <a href="javascript:void(0)" data-id="{{ $row['id'] }}" data-transaction="sale-return"
            class="sale-thermal-print text-hover-primary me-2"
            data-bs-toggle="tooltip" title="Preview">
                <i class="fas fa-file-pdf text-primary"></i>
            </a>
        @else --}}
        <a href="javascript:void(0)" data-id="{{ $row['id'] }}"
            class="sale-return-pdf-preview-modal text-hover-primary me-2" data-bs-toggle="tooltip" title="Preview">
            <i class="fas fa-file-pdf text-primary"></i>
        </a>
        {{-- @endif --}}
        @if (!empty($row['media']))
            <a href="{{ route('company.sale-return.download-attachment', ['sale_return' => $row['id']]) ?? 'javascript:void(0)' }}"
                class="text-hover-success me-2" data-bs-toggle="tooltip">
                <i class="fas fa-download text-success"></i>
            </a>
        @endif
    </div>
    <div class="dropup" wire:key="{{ $row['id'] }}">
        <button wire:key="sale-{{ $row['id'] }}" type="button" title="Action" class="btn px-2 text-primary fs-3 p-0"
            id="dropdownMenuButton1" data-bs-toggle="dropdown" data-bs-boundary="viewport" aria-expanded="false">
            <i class="fa-solid fas fa-ellipsis-v text-black"></i>
        </button>
        <ul class="dropdown-menu transaction-dropdown-menu min-w-170px" aria-labelledby="dropdownMenuButton1">
            @if (activeSubscription() && !isOnlyForMobilePlan())
                @if ($user->can('company_delete_income'))
                    @if ($row['is_locked'])
                        <li>
                            <a href="javascript:void(0)"
                                data-lock-date="{{ getTransactionsLockDate()[LockTransaction::INCOME] }}"
                                class="open-lock-transaction-alert dropdown-item text-hover-danger me-1"
                                data-bs-toggle="tooltip">
                                <i class="fas fa-trash text-danger"></i>&nbsp;&nbsp;Delete
                            </a>
                        </li>
                    @else
                        <li>
                            <a href="javascript:void(0)" data-id="{{ $row['id'] }}"
                                class="sale-return-delete-btn dropdown-item text-hover-danger me-1"
                                data-bs-toggle="tooltip">
                                <i class="fas fa-trash text-danger"></i>&nbsp;&nbsp;Delete
                            </a>
                        </li>
                    @endif
                @endif
                @if ($user->can('company_add_new_income'))
                    <li>
                        <a href="{{ route('company.sale-returns.create.duplicate', ['sale_return' => $row['id']]) ?? 'javascript:void(0)' }}"
                            class="dropdown-item text-hover-info me-1" data-bs-toggle="tooltip">
                            <i class="fas fa-clone text-info"></i>&nbsp;&nbsp;Duplicate
                        </a>
                    </li>
                @endif
            @endif
            @if ($user->can('company_add_new_income') || $user->can('company_edit_income'))
                @if ($dispatchAddress && $dispatchAddress->country_id == \App\Models\Address::INDIA_COUNTRY_ID)
                    @if (!empty($row['eway_bill_id']) && $row['is_eway_bill_cancel'] == 0)
                        <li>
                            <a href="{{ route('company.get-ewaybill-pdf', $row['eway_bill_id']) }}" data-turbo="false"
                                class="dropdown-item text-hover-primary me-1 show-ewaybill" data-bs-toggle="tooltip"
                                title="View Eway Bill">
                                <i class="far fa-file-pdf text-danger"></i>&nbsp;&nbsp;E-way Bill PDF
                            </a>
                        </li>
                        @if ($row['show_eway_bill_cancel_btn'])
                            <li>
                                <a data-id="{{ $row['eway_bill_id'] }}"
                                    class="dropdown-item text-hover-primary me-1 cancel-ewaybill cursor-pointer"
                                    data-bs-toggle="tooltip" title="Cancel Eway Bill">
                                    <i class="fas fa-times text-danger fs-16"></i>&nbsp;&nbsp;Cancel E-way Bill
                                </a>
                            </li>
                        @endif
                    @else
                        <li>
                            <a href="{{ route('company.eway-bill.index', ['transactionId' => $row['id'], 'transactionType' => 'sale-return']) }}"
                                data-id="{{ $row['id'] }}" class="dropdown-item text-hover-primary me-1"
                                data-bs-toggle="tooltip">
                                <i class="fas fa-file text-primary"></i>&nbsp;&nbsp;E-way Bill
                            </a>
                        </li>
                    @endif
                    {{-- @if (!empty($eInvoiceAPICred) && $eInvoiceAPICred['is_e_invoice_applicable'] == 1 && isset($eInvoiceAPICred['einvuser']) && isset($eInvoiceAPICred['einvpwd'])) --}}
                    @if (!empty($row['einvoice_id']))
                        <li>
                            <a href="{{ route('company.get-einvoice-pdf', $row['einvoice_id']) }}" data-turbo="false"
                                class="dropdown-item text-hover-primary me-1" data-bs-toggle="tooltip"
                                title="View E-Invoice">
                                <i class="far fa-file-pdf text-danger fs-16"></i>&nbsp;&nbsp;E-Invoice PDF
                            </a>
                        </li>
                        @if ($row['is_einvoice_cancel'] == 0 && $row['show_einvoice_cancel_btn'])
                            <li>
                                <a data-id="{{ $row['einvoice_id'] }}"
                                    class="dropdown-item text-hover-primary me-1 cancel-einvoice cursor-pointer"
                                    data-bs-toggle="tooltip" title="Cancel E-Invoice">
                                    <i class="fas fa-times text-danger fs-16"></i>&nbsp;&nbsp;Cancel E-Invoice
                                </a>
                            </li>
                        @endif
                    @else
                        <li>
                            <a href="javascript:void(0)" data-id="{{ $row['id'] }}"
                                class="sale-return-e-invoice-generate-btn dropdown-item text-hover-primary me-1">
                                <i class="fas fa-file-invoice-dollar text-primary"></i>&nbsp;&nbsp;E-Invoice
                            </a>
                        </li>
                    @endif
                @endif
                {{-- @endif --}}
            @endif
            <li>
                <a href="{{ route('company.sale-returns-email', ['sale_return' => $row['id']]) ?? 'javascript:void(0)' }}"
                    class="dropdown-item text-hover-danger me-1" target="_blank" data-bs-toggle="tooltip">
                    <i class="fa-solid fa-envelope text-danger"></i>&nbsp;&nbsp;Send Mail
                </a>
            </li>
            <li>
                <a id="sendSaleReturnInvoiceToWhatsapp" data-id="{{ $row['id'] }}"
                    class="dropdown-item text-hover-success cursor-pointer me-1" data-bs-toggle="tooltip">
                    <i class="fa-brands fa-whatsapp text-success"></i>&nbsp;&nbsp;Send Auto Whatsapp
                </a>
            </li>
            <li>
                <a id="sendWhatsappModel" data-phone="{{ $row['phone_number'] }}" data-id="{{ $row['id'] }}"
                    data-type="sale-return" class="dropdown-item text-hover-success cursor-pointer me-1"
                    data-bs-toggle="tooltip">
                    <i class="fa-brands fa-whatsapp text-success"></i>&nbsp;&nbsp;Send Whatsapp Manually
                </a>
            </li>
        </ul>
    </div>
</div>
