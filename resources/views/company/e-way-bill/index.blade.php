@extends('company.layouts.app')
@section('title')
    E-way Bill
@endsection
@section('content')
    @php
        use App\Models\SaleTransaction;
        $transactionId = $data['transactionId'];
    @endphp
    <div id="kt_content_container" class="container-fluid">
        <div class="d-flex flex-column flex-lg-row">
            <div class="flex-lg-row-fluid mb-10 mb-lg-0 me-lg-7 me-xl-10 eway-bill-create-page">
                <div class="row">
                    <div class="col-12">
                        @include('layouts.errors')
                    </div>
                </div>

                <div class="card card-border-1 generate-eway-bill">
                    <div class="card-body position-relative">
                        <form method="POST" id="ewayBillGenerate">
                            @csrf
                            {{ Form::hidden('transaction_id',$data['transactionId'],['id'=>'ewaybillTransactionId']) }}
                            {{ Form::hidden('eway_bill_transaction_type', $transactionType,['id'=>'ewaybillTransactionType']) }}
                            <div class="row">
                                <div class="col-6">
                                    <h4 class="text-decoration-underline">Transaction Details</h4>
                                </div>
                                <div class="col-6 d-flex justify-content-end">
                                    <a href="{{ route('company.ewaybill-einvoice-setting.index', ['transactionId' => $transactionId, 'transactionType' => $transactionType ]) }}" class="text-decoration-underline h4 text-primary">How to generate EwayBill API credentials?</a>
                                </div>
                                <div class="col-md-3 col-12">
                                    {{ Form::label('supply_type', 'Supply Type:', ['class' => 'form-label required']) }}
                                    <div class="ms-auto d-flex flex-wrap">
                                        <div class="form-check mx-2 mt-1 align-content-center">
                                            <label class="form-label fw-bold" for="supplyTypeOutward">Outward</label>
                                            {{ Form::radio('supply_type','O', $data['supply_type'] == SaleTransaction::OUTWARD ? true : false,['class' => 'form-check-input','id'=>'supplyTypeOutward']) }}
                                        </div>
                                        <div class="form-check mx-2 mt-1 align-content-center">
                                            <label class="form-label fw-bold" for="supplyTypeInward">Inward</label>
                                            {{ Form::radio('supply_type','I', $data['supply_type'] == SaleTransaction::INWARD ? true : false,['class' => 'form-check-input','id'=>'supplyTypeInward']) }}
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-9 col-12">
                                    {{ Form::label('sub_type', 'Sub Type:', ['class' => 'form-label required']) }}
                                    <div class="ms-auto d-flex flex-wrap">
                                        <div class="form-check mx-2 mt-1 align-content-center">
                                            <label class="fw-bold" for="subTypeSupply">Supply</label>
                                            {{ Form::radio('sub_type',SaleTransaction::SUB_SUPPLY_TYPE_SUPPLY, $data['sub_type'] == SaleTransaction::SUB_SUPPLY_TYPE_SUPPLY ? true : false,
                                                ['class' => 'form-check-input sub-supply-type','id'=>'subTypeSupply']) }}
                                        </div>
                                        <div class="form-check mx-2 mt-1 align-content-center">
                                            <label class="fw-bold" for="subTypeImport">Import</label>
                                            {{ Form::radio('sub_type',SaleTransaction::SUB_SUPPLY_TYPE_IMPORT, false,
                                                ['class' => 'form-check-input sub-supply-type','id'=>'subTypeImport']) }}
                                        </div>
                                        <div class="form-check mx-2 mt-1 align-content-center">
                                            <label class="fw-bold" for="subTypeExport">Export</label>
                                            {{ Form::radio('sub_type',SaleTransaction::SUB_SUPPLY_TYPE_EXPORT, false,
                                                ['class' => 'form-check-input sub-supply-type','id'=>'subTypeExport']) }}
                                        </div>
                                        <div class="form-check mx-2 mt-1 align-content-center">
                                            <label class="fw-bold" for="subTypeJobWork">Job work</label>
                                            {{ Form::radio('sub_type',SaleTransaction::SUB_SUPPLY_TYPE_JOB_WORK, false,
                                                ['class' => 'form-check-input sub-supply-type','id'=>'subTypeJobWork']) }}
                                        </div>
                                        <div class="form-check mx-2 mt-1 align-content-center">
                                            <label class="fw-bold" for="subTypeForOwnUse">For Own Use</label>
                                            {{ Form::radio('sub_type',SaleTransaction::SUB_SUPPLY_TYPE_FOR_OWN_USE, false,
                                                ['class' => 'form-check-input sub-supply-type','id'=>'subTypeForOwnUse']) }}
                                        </div>
                                        <div class="form-check mx-2 mt-1 align-content-center">
                                            <label class="fw-bold" for="subTypeJobWorkReturn">Job work
                                                Returns</label>
                                            {{ Form::radio('sub_type',SaleTransaction::SUB_SUPPLY_TYPE_JOB_WORK_RETURN, false,
                                                ['class' => 'form-check-input sub-supply-type','id'=>'subTypeJobWorkReturn']) }}
                                        </div>
                                        <div class="form-check mx-2 mt-1 align-content-center">
                                            <label class="fw-bold" for="subTypeSalesReturns">Sales
                                                Return</label>
                                            {{ Form::radio('sub_type',SaleTransaction::SUB_SUPPLY_TYPE_SALE_RETURN, $data['sub_type'] == SaleTransaction::SUB_SUPPLY_TYPE_SALE_RETURN ? true : false,
                                                ['class' => 'form-check-input sub-supply-type','id'=>'subTypeSalesReturns']) }}
                                        </div>
                                        <div class="form-check mx-2 mt-1 align-content-center">
                                            <label class="fw-bold" for="subTypeOthers">Others</label>
                                            {{ Form::radio('sub_type',SaleTransaction::SUB_SUPPLY_TYPE_OTHER, $data['sub_type'] == SaleTransaction::SUB_SUPPLY_TYPE_OTHER ? true : false,
                                                ['class' => 'form-check-input','id'=>'subTypeOthers']) }}
                                        </div>
                                        <div class="form-check mx-2 mt-1 align-content-center">
                                            <label class="fw-bold" for="subTypeSKD">SKD/CKD</label>
                                            {{ Form::radio('sub_type',SaleTransaction::SUB_SUPPLY_TYPE_SKD, false,
                                                ['class' => 'form-check-input sub-supply-type','id'=>'subTypeSKD']) }}
                                        </div>
                                        <div class="form-check mx-2 mt-1 align-content-center">
                                            <label class="fw-bold" for="subTypeLineSales">Line Sales</label>
                                            {{ Form::radio('sub_type',SaleTransaction::SUB_SUPPLY_LINE_SALES, false,
                                                ['class' => 'form-check-input sub-supply-type','id'=>'subTypeLineSales']) }}
                                        </div>
                                        <div class="form-check mx-2 mt-1 align-content-center">
                                            <label class="fw-bold" for="subTypeRecipientNotKnown">Recipient Not Known</label>
                                            {{ Form::radio('sub_type',SaleTransaction::SUB_SUPPLY_RECIPIENT_NOT_KNOWN, false,
                                                ['class' => 'form-check-input sub-supply-type','id'=>'subTypeRecipientNotKnown']) }}
                                        </div>
                                        <div class="form-check mx-2 mt-1 align-content-center">
                                            <label class="fw-bold" for="subTypeExhibitionOrFairs">Exhibition or
                                                Fairs</label>
                                            {{ Form::radio('sub_type',SaleTransaction::SUB_SUPPLY_TYPE_FAIRS, false,
                                                ['class' => 'form-check-input sub-supply-type','id'=>'subTypeExhibitionOrFairs']) }}
                                        </div>
                                        <div class="mt-1 align-content-center d-none sub-type-others-description">
                                            {{ Form::text('sub_type_other_description', $data['sub_type'] == SaleTransaction::SUB_SUPPLY_TYPE_OTHER && !empty($data['specify']) ? $data['specify'] : null,['class'=>'form-control','id'=>'subTypeOthersDescription','placeholder'=>'Specify']) }}
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12 mt-4">
                                    <div class="row">
                                        <div class="col-lg-3 col-md-6 col-12 mt-2">
                                            {{ Form::label('document_type', 'Document Type:', ['class' => 'form-label required']) }}
                                            {{ Form::select('document_type',SaleTransaction::DOCUMENT_TYPE ?? [], $data['document_type'] ?? SaleTransaction::TAX_INVOICE, ['class' => 'form-select',  'data-control' => 'select2','placeholder'=>'Select Document Type', $data['isDocumentTypeDisabled'] ? 'readonly' : '']) }}
                                        </div>
                                        <div class="col-lg-3 col-md-6 col-12 mt-2">
                                            {{ Form::label('document_no', 'Document No:', ['class' => 'form-label required']) }}
                                            {{ Form::text('document_no',$data['docNo'] ?? null,['class'=>'form-control','readonly']) }}
                                        </div>
                                        <div class="col-lg-3 col-md-6 col-12 mt-2">
                                            {{ Form::label('document_date', 'Document Date:', ['class' => 'form-label required']) }}
                                            {{ Form::text('document_date',$data['docDate'],['class'=>'form-control','id'=>'documentDate','readonly']) }}
                                        </div>
                                        <div class="col-lg-3 col-md-6 col-12 mt-2">
                                            {{ Form::label('transaction_type', 'Transaction Type:', ['class' => 'form-label required']) }}
                                            {{ Form::select('transaction_type',SaleTransaction::E_BILL_TRANSACTION_TYPE_ARR ?? [], $data['transaction_type'],
                                                ['class' => 'form-select', 'id' => 'eWayBillDocumentType', 'data-control' => 'select2','placeholder'=>'Select Transaction Type']) }}
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12 mt-4">
                                    <div class="row">
                                        <div class="col-lg-6 col-12">
                                            <h4 class="text-decoration-underline">Bill From</h4>
                                            <div class="row">
                                                <div class="col-lg-6 col-12 mt-2">
                                                    {{ Form::label('bill_from_name', 'Name:', ['class' => 'form-label']) }}
                                                    {{ Form::text('bill_from_name',$data['fromTrdName'],['class'=>'form-control','readonly']) }}
                                                </div>
                                                <div class="col-lg-6 col-12 mt-2">
                                                    {{ Form::label('bill_from_gstin', 'GSTIN:', ['class' => 'form-label required']) }}
                                                    {{ Form::text('bill_from_gstin',$data['fromGstin'] ?? null,['class'=>'form-control','readonly']) }}
                                                </div>
                                                <div class="col-lg-6 col-12 mt-2">
                                                    {{ Form::label('bill_from_state', 'State:', ['class' => 'form-label required']) }}
                                                    {{ Form::text('bill_from_state',$data['fromStateName'] ?? null,['class'=>'form-control','readonly']) }}
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-lg-6 col-12">
                                            <h4 class="text-decoration-underline">Dispatch From</h4>
                                            <div class="row dispatch-from-div">
                                                <div class="col-lg-6 col-12 mt-2">
                                                    {{ Form::label('from_name_address_1', 'Address Line 1:', ['class' => 'form-label']) }}
                                                    {{ Form::text('from_name_address_1',$data['fromAddr1'] ?? null,
                                                        ['class'=>'form-control','readonly']) }}
                                                </div>
                                                <div class="col-lg-6 col-12 mt-2">
                                                    {{ Form::label('from_name_address_2', 'Address Line 2:', ['class' => 'form-label']) }}
                                                    {{ Form::text('from_name_address_2',$data['fromAddr2'] ?? null,
                                                        ['class'=>'form-control','readonly']) }}
                                                </div>
                                                <div class="col-lg-6 col-12 mt-2">
                                                    {{ Form::label('from_place_label', 'Place:', ['class' => 'form-label']) }}
                                                    {{-- {{ Form::text('from_place',$data['fromPlace'] ?? null,['class'=>'form-control','readonly']) }} --}}
                                                    {{ Form::select('from_place', getCities($data['fromStateId']) ?? null, $data['fromCityId'] ?? null,
                                                        ['class' => 'form-select','readonly','data-control' => 'select2','placeholder'=>'Select City', 'id' => 'from_place']) }}
                                                </div>
                                                <div class="col-lg-3 col-md-6 col-12 mt-2">
                                                    {{ Form::label('from_pincode_label', 'Pincode:', ['class' => 'form-label required']) }}
                                                    {{ Form::text('from_pincode',$data['fromPincode'] ?? null,['class'=>'form-control','readonly','id' => 'from_pincode']) }}
                                                </div>
                                                <div class="col-lg-3 col-md-6 col-12 mt-2">
                                                    {{ Form::label('from_state_label', 'State:', ['class' => 'form-label required']) }}
                                                    {{-- {{ Form::text('from_state',$data['fromStateName'] ?? null, ['class'=>'form-control','readonly']) }} --}}
                                                    {{ Form::select('from_state', getStates($data['fromCountryId']) ?? null, $data['fromStateId'] ?? null,
                                                        ['class' => 'form-select','readonly','data-control' => 'select2','placeholder'=>'Select State', 'id' => 'from_state', 'data-country-id' => $data['fromCountryId']]) }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12 mt-4">
                                    <div class="row">
                                        <div class="col-lg-6 col-12">
                                            <h4 class="text-decoration-underline">Bill To</h4>
                                            <div class="row">
                                                <div class="col-lg-6 col-12 mt-2">
                                                    {{ Form::label('bill_to_name', 'Name:', ['class' => 'form-label']) }}
                                                    {{ Form::text('bill_to_name',$data['toTrdName'] ?? null,['class'=>'form-control','readonly']) }}
                                                </div>
                                                <div class="col-lg-6 col-12 mt-2 ">
                                                    {{ Form::label('bill_to_gstin', 'GSTIN:', ['class' => 'form-label']) }}
                                                    <div class="d-flex">
                                                        {{ Form::text('bill_to_gstin', $data['toGstin'], ['class'=>'form-control','readonly']) }}
                                                        @if (empty($data['toGstin']))
                                                            <span data-bs-toggle="tooltip" data-bs-placement="top" title="This person is unregistered." class="ms-2 pt-3">
                                                                <i class="bi bi-question-circle fs-16"></i>
                                                            </span>
                                                        @endif
                                                    </div>
                                                </div>
                                                <div class="col-lg-6 col-12 mt-2">
                                                    {{ Form::label('bill_to_state', 'State:', ['class' => 'form-label']) }}
                                                    {{ Form::text('bill_to_state',$data['billToStateName'] ?? null,['class'=>'form-control','readonly']) }}
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-lg-6 col-12">
                                            <h4 class="text-decoration-underline">Ship To</h4>
                                            <div class="row ship-to-div">
                                                <div class="col-lg-6 col-12 mt-2">
                                                    {{ Form::label('to_address_1', 'Address Line 1:', ['class' => 'form-label']) }}
                                                    {{ Form::text('to_address_1',$data['toAddr1'] ?? null,['class'=>'form-control' ,'readonly']) }}
                                                </div>
                                                <div class="col-lg-6 col-12 mt-2">
                                                    {{ Form::label('to_address_2', 'Address Line 2:', ['class' => 'form-label']) }}
                                                    {{ Form::text('to_address_2',$data['toAddr2'] ?? null,['class'=>'form-control','readonly']) }}
                                                </div>
                                                <div class="col-lg-6 col-12 mt-2">
                                                    {{ Form::label('to_city_label', 'Place:', ['class' => 'form-label']) }}
                                                    {{ Form::select('to_city', getCities($data['toStateId']) ?? null, $data['toCityId'] ?? null,
                                                        ['class' => 'form-select','readonly','data-control' => 'select2','placeholder'=>'Select City','id' => 'to_city']) }}
                                                </div>
                                                <div class="col-lg-3 col-md-6 col-12 mt-2">
                                                    {{ Form::label('to_pincode_label', 'Pincode:', ['class' => 'form-label required']) }}
                                                    {{ Form::text('to_pincode',$data['toPincode'] ?? null,['class'=>'form-control','readonly','id' => 'to_pincode']) }}
                                                </div>
                                                <div class="col-lg-3 col-md-6 col-12 mt-2">
                                                    {{ Form::label('to_state_label', 'State:', ['class' => 'form-label required']) }}
                                                    {{ Form::select('to_state', getStates($data['toCountryId']) ?? null, $data['toStateId'] ?? null,
                                                        ['class' => 'form-select','readonly','data-control' => 'select2','placeholder'=>'Select State', 'id' => 'to_state', 'data-country-id' => $data['toCountryId']]) }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12 mt-4">
                                    <div class="row overflow-auto" style=" margin: 0px">
                                        <h4 class="text-decoration-underline p-0">Item Details</h4>
                                        <div class=" p-0 overflow-auto table-bordered" style="border-radius:5px;">
                                            <table class="table table-eway table-responsive mb-0 m-0 " style="min-width:1090px;">
                                                <thead class="bg-primary text-white">
                                                <tr class="fw-bolder">
                                                    <th>Product Name</th>
                                                    <th>Description</th>
                                                    <th>HSN</th>
                                                    <th>Quantity</th>
                                                    <th>Unit</th>
                                                    <th>Taxable Value</th>
                                                    <th>CGST+SGST (%)</th>
                                                    <th>IGST (%)</th>
                                                    <th>CESS</th>
                                                    <th class="pe-0">CESS non. Advol</th>
                                                </tr>
                                                </thead>
                                                <tbody>
                                                @if(!empty($data['itemList']))
                                                    @foreach($data['itemList'] as $item)
                                                        <tr>
                                                            <td>{{ Form::text('product_name[]',$item['productName'], ['class'=>'form-control','readonly']) }}</td>
                                                            {{ Form::hidden('item_id[]',$item['itemId'], ['class'=>'form-control','readonly']) }}
                                                            <td>{{ Form::text('product_description[]',$item['productDesc'], ['class'=>'form-control']) }}</td>
                                                            <td>{{ Form::text('product_hsn[]',$item['hsnCode'], ['class'=>'form-control']) }}</td>
                                                            <td>{{ Form::text('quantity[]',$item['quantity'], ['class'=>'form-control','readonly']) }}</td>
                                                            <td>{{ Form::text('quantity_unit[]',$item['qtyUnit'], ['class'=>'form-control','readonly']) }}</td>
                                                            <td>{{ Form::text('taxable_value[]',$item['taxableAmount'], ['class'=>'form-control taxable-value','readonly']) }}</td>
                                                            <td>{{ Form::text('cgst_sgst[]',$item['cgstRate']. '+' .$item['sgstRate'], ['class'=>'form-control','readonly']) }}</td>
                                                            <td>{{ Form::text('igst[]',$item['igstRate'], ['class'=>'form-control','readonly']) }}</td>
                                                            <td>{{ Form::text('cess_rate[]',$item['cessRate'], ['class'=>'form-control','readonly']) }}</td>
                                                            <td>{{ Form::text('cess_non_advol_rate[]',null, ['class'=>'form-control cess-non-advol-rate']) }}</td>
                                                        </tr>
                                                    @endforeach
                                                @endif
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12 mt-4">
                                    <div class="row overflow-auto m-0">
                                        <div class=" p-0 overflow-auto table-bordered" style="border-radius:5px;">
                                            <table class="table table-eway table-responsive mb-0 m-0 " style="min-width:1090px;">
                                                <thead class="bg-primary text-white">
                                                <tr class="fw-bolder">
                                                    <th>Total Taxable Amount</th>
                                                    <th>CGST Amount</th>
                                                    <th>SGST Amount</th>
                                                    <th>IGST Amount</th>
                                                    <th>CESS Advol Amount</th>
                                                    <th>CESS Non Advol Amount</th>
                                                    <th>Other Amount (+/-)</th>
                                                    <th>Total Inv. Amount</th>
                                                </tr>
                                                </thead>
                                                <tbody>
                                                <tr>
                                                    <td>
                                                        {{ Form::text('total_taxable',$data['totalTaxValue'] ?? null,
                                                        ['class'=>'form-control','readonly']) }}
                                                    </td>
                                                    <td>
                                                        {{ Form::text('total_cgst',$data['cgstValue'] ?? null,
                                                        ['class'=>'form-control','readonly']) }}
                                                    </td>
                                                    <td>
                                                        {{ Form::text('total_sgst',$data['sgstValue'] ?? null,
                                                        ['class'=>'form-control','readonly']) }}
                                                    </td>
                                                    <td>
                                                        {{ Form::text('total_igst',$data['igstValue'] ?? null,
                                                        ['class'=>'form-control','readonly']) }}
                                                    </td>
                                                    <td>
                                                        {{ Form::text('total_cess_advol',$data['cessValue'] ?? null,
                                                        ['class'=>'form-control','readonly']) }}
                                                    </td>
                                                    <td>
                                                        {{ Form::text('total_cess_non_advol',null,
                                                        ['class'=>'form-control','id' => 'cessNonAdvolAmount','readonly']) }}
                                                    </td>
                                                    <td>
                                                        {{ Form::text('total_other_amount',$data['otherAmount'],
                                                        ['class'=>'form-control','id' => 'otherAmount','readonly']) }}
                                                    </td>
                                                    {{ Form::hidden('total_inv_amount',$data['totInvValue'],
                                                       ['id' => 'totalInvAmount']) }}
                                                    <td>
                                                        {{ Form::text('total_invoice_amount',$data['totInvValue'] ?? null,
                                                        ['class'=>'form-control','readonly', 'id' => 'totalInvoiceAmount']) }}
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12 mt-4">
                                    <h4 class="text-decoration-underline">Transportation Details</h4>
                                    <div class="row">
                                        <div class="col-md-3 col-12">
                                            {{ Form::label('transporter_id', 'Transporter ID:', ['class' => 'form-label']) }}
                                            {{ Form::text('transporter_id',$data['transporterId'] ?? null,['class'=>'form-control eway-bill-transport-id']) }}
                                        </div>
                                        <div class="col-md-3 col-12">
                                            {{ Form::label('transporter_name', 'Transporter Name:', ['class' => 'form-label']) }}
                                            {{ Form::text('transporter_name',$data['transporterName'] ?? null,['class'=>'form-control']) }}
                                        </div>
                                        <div class="col-md-3 col-12 ">
                                            {{ Form::label('distance', 'Approximate Distance (in KM):', ['class' => 'form-label required']) }}
                                            {{ Form::text('distance',null,['class'=>'form-control']) }}
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12 mt-4">
                                    <div class="row">
                                        <div class="col-12">
                                            <div class="form-check d-flex align-items-center form-switch form-check-custom form-switch-sm">
                                                <h4 class="mb-0 text-decoration-underline">Part B</h4>
                                                {{ Form::checkbox('eway_bill_part_b', 1, null , ['class' => 'form-check-input ms-2',
                                                    'id' => 'ewayBillPartB']) }}
                                            </div>
                                        </div>
                                        <div class="col-12 d-none" id="ewayBillPartBSection">
                                            <div class="row">
                                                <div class="col-md-6 col-12">
                                                    {{ Form::label('transporter_mode', 'Mode:', ['class' => 'form-label']) }}
                                                    <div class="ms-auto d-flex flex-wrap">
                                                        <div class="form-check me-2 mt-1 align-content-center">
                                                            <label class="form-label mb-0" for="transporterModeRoad">Road</label>
                                                            {{ Form::radio('transporter_mode',SaleTransaction::TRANSPORTER_MODE_ROAD, true,
                                                            ['class' => 'form-check-input transporter-mode','id'=>'transporterModeRoad']) }}
                                                        </div>
                                                        <div class="form-check mx-2 mt-1 align-content-center">
                                                            <label class="form-label mb-0" for="transporterModeRail">Rail</label>
                                                            {{ Form::radio('transporter_mode',SaleTransaction::TRANSPORTER_MODE_RAIL, false,
                                                            ['class' => 'form-check-input transporter-mode','id'=>'transporterModeRail']) }}
                                                        </div>
                                                        <div class="form-check mx-2 mt-1 align-content-center">
                                                            <label class="form-label mb-0" for="transporterModeAir">Air</label>
                                                            {{ Form::radio('transporter_mode',SaleTransaction::TRANSPORTER_MODE_AIR, false,
                                                            ['class' => 'form-check-input transporter-mode','id'=>'transporterModeAir']) }}
                                                        </div>
                                                        <div class="form-check mx-2 mt-1 align-content-center">
                                                            <label class="form-label mb-0" for="transporterModeShip">Ship or Ship Cum Road/Rail
                                                            </label>
                                                            {{ Form::radio('transporter_mode',SaleTransaction::TRANSPORTER_MODE_SHIP, false,
                                                                ['class' => 'form-check-input transporter-mode','id'=>'transporterModeShip']) }}
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="col-md-6 col-12">
                                                    {{ Form::label('vehicle_type', 'Vehicle Type:', ['class' => 'form-label']) }}
                                                    <div class="ms-auto d-flex flex-wrap">
                                                        <div class="form-check me-2 mt-1 align-content-center">
                                                            <label class="form-label mb-0" for="vehicleTypeRegular">Regular</label>
                                                            {{ Form::radio('vehicle_type',SaleTransaction::VEHICLE_TYPE_REGULAR, true,
                                                                ['class' => 'form-check-input','id'=>'vehicleTypeRegular']) }}
                                                        </div>
                                                        <div class="form-check mx-2 mt-1 align-content-center">
                                                            <label class="form-label mb-0" for="vehicleTypeOverDimensional">
                                                                Over Dimensional Cargo</label>
                                                            {{ Form::radio('vehicle_type',SaleTransaction::VEHICLE_TYPE_OVER_DIMENSIONAL_CARGO,
                                                            false,['class' => 'form-check-input','id'=>'vehicleTypeOverDimensional']) }}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row mt-5">
                                                <div class="col-lg-3 col-6">
                                                    {{ Form::label('vehicle_no', 'Vehicle No:', ['class' => 'form-label']) }}
                                                    {{ Form::text('vehicle_no',$data['vehicle_no'] ?? null,['class'=>'form-control']) }}
                                                </div>
                                                <div class="col-lg-6 col-12 transporter">
                                                    <label class="form-label mb-0" for="vehicleTypeRegular">
                                                        Transporter Doc. No & Date</label>
                                                    <div class="d-flex">
                                                        {{ Form::text('transporter_no',$data['transDocNo'] ?? null,
                                                            ['class'=>'form-control',$data['transDocNo'] != '' ? 'readonly' : '', 'id' => 'transporter_no']) }}
                                                        <input type="text" class="form-control ms-2"
                                                               value="{{ $data['transDocDate'] ?? null }}"
                                                               name="transporter_date" id="eWayBillTransporterDate">
                                                    </div>
                                                </div>
                                                <div class="col-lg-6 col-12 rr-transport">
                                                    <label class="form-label mb-0" for="vehicleTypeRegular">RR No & Date</label>
                                                    <div class="d-flex">
                                                        {{ Form::text('rr_no',$data['rrNo'] ?? null,['class'=>'form-control', 'id' => 'rr_no']) }}
                                                        <input type="text" class="form-control ms-2"
                                                               value="{{ $data['rrDate'] ?? null }}"
                                                               name="rr_date" id="eWayRrTransporterDate">
                                                    </div>
                                                </div>
                                                <div class="col-lg-6 col-12 air-transport">
                                                    <label class="form-label mb-0" for="vehicleTypeRegular">Airway Bill No & Date</label>
                                                    <div class="d-flex">
                                                        {{ Form::text('air_no',$data['airNo'] ?? null,['class'=>'form-control', 'id' => 'air_no']) }}
                                                        <input type="text" class="form-control ms-2"
                                                               value="{{ $data['airDate'] ?? null }}"
                                                               name="air_date" id="eWayAirTransporterDate">
                                                    </div>
                                                </div>
                                                <div class="col-lg-6 col-12 bill-transport">
                                                    <label class="form-label mb-0" for="vehicleTypeRegular">Bill of lading No & Date</label>
                                                    <div class="d-flex">
                                                        {{ Form::text('bill_landing_no',$data['billLandingNo'] ?? null,['class'=>'form-control',
                                                            'id' => 'bill_landing_no']) }}
                                                        <input type="text" class="form-control ms-2"
                                                               value="{{ $data['billLandingDate'] ?? null }}"
                                                               name="bill_landing_date" id="eWayBillLandingTransporterDate">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12 mt-4">
                                    <div class="">
                                        <button type="submit" name="submit_button" class="btn btn-primary me-2">Submit</button>
                                        <a href="javascript:history.back(-1);" class="btn btn-secondary">Back</a>
                                    </div>
                                </div>

                            </div>
                        </form>
                    </div>
                </div>
            </div>

        </div>
    </div>
@endsection
