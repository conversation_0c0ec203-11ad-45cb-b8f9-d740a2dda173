<!DOCTYPE html>
<html lang="en">
@php
    $pdfSymbol = getPrintPdfCurrencySymbol($currentCompany->id);
    $dockereEnabled = dockerEnabled() && !isset($preview_enabled);
    $fontStyleName = $dockereEnabled ? 'Arial Unicode MS' : 'Arial-unicode-ms';
    $fontCssName = $dockereEnabled ? 'font-docker.css' : 'font.css';
@endphp

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/css/' . $fontCssName) }}">
    <title>{{ isset($fileName) ? $fileName : $transaction->document_number }}</title>
    @include('company.pdf.sale_pdf.print_custom_font_css_variables')
    <style>
        h1 {
            font-size: 27px;
        }

        * {
            margin: 0;
            padding: 0;
            text-indent: 0;
            font-family: "{{ $fontStyleName }}";
            font-size: 13px;
            font-weight: 400;
            box-sizing: border-box;
        }

        @page {
            margin-left: 20px !important;
            margin-right: 20px !important;
            margin-top: var(--header-space) !important;
            margin-bottom: var(--footer-space) !important;
        }

        .letter-head-table {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            width: 100%;
            box-sizing: border-box;
        }

        .main-content-table {
            border: 2px solid black;
            display: flex;
            flex-direction: column;
        }

        table {
            display: table;
            width: 100%;
            border-collapse: collapse;
        }

        .custom-table-heading {
            padding: 6px 8px;
            font-weight: bold;
            width: 100px;
            font-size: 12px;
            line-height: 12px;
        }

        .text-primary {
            color: #4f158c;
        }

        .address {
            font-size: 11px;
            /* line-height: 14px; */
            padding-left: 8px;
            padding-right: 8px;
        }

        .phone {
            font-size: 11px;
            /* line-height: 14px; */
            padding-left: 8px;
            padding-right: 8px;
        }

        td {
            vertical-align: top;
        }

        .fs-13 {
            font-size: 13px;
        }

        .fs-12 {
            font-size: 12px;
        }

        .fw-6 {
            font-weight: 600;
        }

        .whitespace-nowrap {
            white-space: nowrap;
        }

        .border-bottom {
            border-bottom: 1px solid black;
        }

        .border-right {
            border-right: 1px solid black;
        }

        .border-top {
            border-top: 1px solid black;
        }

        .border-left {
            border-left: 1px solid black;
        }

        .vertical-top {
            vertical-align: top;
        }

        .vertical-middle {
            vertical-align: middle;
        }

        .vertical-bottom {
            vertical-align: bottom;
        }

        .text-center {
            text-align: center;
        }

        .text-start {
            text-align: left;
        }

        .text-end {
            text-align: right;
        }

        .table-heading {
            padding: 3px 8px;
            text-align: left;
            position: relative;
            /* background-color: #eeeeee !important; */
        }

        .signature {
            max-width: 210px;
            height: 100px;
            margin-left: auto;
        }

        .desc {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            font-size: 12px;
            position: relative;
            padding-left: 5px;
            padding-right: 5px;
        }

        .desc::before {
            position: absolute;
            content: "(";
            top: 0;
            left: 0;
            font-size: 12px;
        }

        .desc::after {
            position: absolute;
            content: ")";
            bottom: 2px;
            right: 0;
            font-size: 12px;
        }

        .item-table tr:nth-last-child(-n+2):not(:last-child) td {
            font-size: 12px;
            border-bottom: 1px solid black;
            padding: 4px 8px 0 4px;
            height: 100% !important;
            vertical-align: top;
        }

        .d-none {
            display: none;
        }

        /*.for-preview {
                padding:20px;
                box-sizing:border-box
            }*/

        .min-width-250 {
            min-width: 250px !important;
        }

        .min-width-150 {
            min-width: 150px !important;
        }

        .pe-0 {
            padding-right: 0;
        }

        .ps-0 {
            padding-left: 0;
        }

        .col {
            flex: 1 0 0%;
        }

        .row {
            display: flex;
            flex-wrap: wrap;
        }

        .qr-code {
            max-width: 75px;
            min-width: 75px;
            height: 75px;
        }

        .mb-0 {
            margin-bottom: 0 !important;
        }

        .fw-7 {
            font-weight: 700;
        }

        .fw-5 {
            font-weight: 500;
        }

        .bg-light {
            background-color: #F5F8FA !important;
        }

        .ps-3 {
            padding-left: 0.75rem !important; /* 12px padding on the left */
        }

        .w-100 {
            width: 100%
        }

        tr td:last-child {
            border-right: none;
        }

    </style>
    @include('company.pdf.sale_pdf.print_custom_font_css')
</head>

<body>
    <div class="letter-head-table main-content-table">

            <table class="border-bottom">
                <tbody>
                    <tr>
                        @foreach (printCustomPDFLabelsForDeliveryChallan() as $key => $customLabel)
                            <td class="company-address-font-size fw-7 {{ $loop->iteration == 2 ? 'text-end' : '' }}" style="padding:4px 8px;">
                                {{ $key ?? null }}: <span class="company-address-font-size">{{ $customLabel ?? null }}</span>
                            </td>
                        @endforeach
                    </tr>
                </tbody>
            </table>
            <div class="text-center border-bottom" style="padding: 4px 2px 2px 2px">
                <h6 class="mb-0 fw-7 text-primary" style="font-size: 14px;">
                    {{ $deliveryChallanTransactionMaster->title_of_print }} ({#INVOICE_TYPE#})
                </h6>
            </div>
            <div class="row">
                <div class="col">
                </div>
                <div class="border-left" style="width:33.34%;">
                    <div>
                        <table>
                            <tbody>
                                <tr>
                                    <td class="fs-12 fw-6 header-contents-font-size" style="padding: 2px 8px 2px 8px;">
                                        {{ $changeLabel['delivery_challan_number_label'] ?? 'Challan No' }}:
                                    </td>
                                    <td class="text-end header-contents-font-size" style="padding: 2px 8px 2px 0px;  white-space: normal">
                                        {{ $transaction->challan_number }}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fs-12 fw-6 header-contents-font-size" style="padding: 2px 8px 2px 8px;">
                                        {{ $changeLabel['delivery_challan_date_label'] ?? 'Challan Date' }}:
                                    </td>
                                    <td class="text-end header-contents-font-size" style="padding: 2px 8px 2px 0px;  white-space: normal">
                                        {{ Carbon\Carbon::parse($transaction->challan_date)->format('d-m-Y') }}
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col vertical-top border-bottom pe-0 ps-0 {{ (($invoiceSetting['delivery_challan_ship_to_details'] ?? true) || isset($transaction['dispatch_address_id']) && isset($dispatchAddress) && ($invoiceSetting['delivery_challan_dispatch_from_details'] ?? false)) ? 'border-right' : '' }}">
                    <p
                        class="mb-0 text-primary border-bottom border-top fw-7 table-heading header-labels-font-size">
                        {{ $changeLabel['delivery_challan_bill_to_label'] ?? 'Bill to' }}:
                    </p>
                    <h4 class="mb-0 fw-7 header-contents-font-size" style="padding: 4px 0 1px 8px; font-size: 15px">
                        {{ strtoupper($customerDetail->name) }}
                    </h4>
                    @if (isset($billingAddress))
                        <p class="m-0 address ps-3 fs-12 fw-5 header-contents-font-size">
                            @if ($billingAddress->address_1 != null)
                                {{ strtoupper($billingAddress->address_1) }}
                            @endif
                            @if ($billingAddress->address_2 != null)
                                {{ strtoupper($billingAddress->address_2) }},
                            @endif
                            {{ $billingAddress->city_id != null ? strtoupper(getCityName($billingAddress->city_id)) . ',' : null }}
                            {{ $billingAddress->state_id != null ? strtoupper(getStateName($billingAddress->state_id)) . ',' : null }}
                            {{ strtoupper(getCountryName($billingAddress->country_id ?? null)) }},
                            {{ $billingAddress->pin_code ?? null }}
                        </p>
                    @endif
                    <p class="mb-0 fs-12 fw-5 ps-3 header-contents-font-size" style="padding: 2px 0px 0px 0px">
                        @if (!empty($transaction->party_phone_number))
                            Contact No:
                                +{{ $transaction->region_code ?? '' }} {{ $transaction->party_phone_number ?? null }}
                        @elseif (!empty($customerDetail->model->phone_1))
                            Contact No:
                                +{{ $customerDetail->model->region_code_1 ?? '' }} {{ $customerDetail->model->phone_1 ?? null }}
                        @endif
                        @if (!empty($customerDetail->model->phone_2) && ($customerDetail->model->phone_1 !== $customerDetail->model->phone_2))
                            {{ empty($customerDetail->model->phone_1) && empty($transaction->party_phone_number) ? 'Contact No:' : "|" }}
                            <span class="whitespace-nowrap header-contents-font-size">
                                +{{ $customerDetail->model->region_code_2 ?? '' }} {{ $customerDetail->model->phone_2 ?? null }}
                            </span>
                        @endif
                    </p>
                    @if (!empty($customerDetail->model->person_email))
                        <p class="mb-0 fs-12 fw-5 ps-3 header-contents-font-size" style="padding: 2px 0px 0px 0px">
                            Email: {{ $customerDetail->model->person_email ?? null }}
                        </p>
                    @endif
                    @if ($showGst)
                        <p class="mb-0 fs-12 fw-5 ps-3 header-contents-font-size" style="padding: 2px 0px 0px 0px">
                            GSTIN: {{ $transaction->gstin ?? $customerDetail->model->gstin ?? null }}
                        </p>
                    @endif
                    @if (!empty($panNumber) && $showPanNumber)
                        <p class="mb-0 fs-12 fw-5 ps-3 header-contents-font-size" style="padding: 2px 0px 0px 0px">
                            PAN: {{ $panNumber ?? null }}
                        </p>
                    @endif
                </div>
                @if ($invoiceSetting['delivery_challan_ship_to_details'] ?? true)
                    <div class="col vertical-top border-bottom {{ (isset($transaction['dispatch_address_id']) && isset($dispatchAddress) && ($invoiceSetting['delivery_challan_dispatch_from_details'] ?? false)) ? 'border-right' : '' }}">
                        <p
                            class="mb-0 text-primary border-bottom border-top fw-7 table-heading header-labels-font-size">
                            {{ $changeLabel['delivery_challan_ship_to_label'] ?? 'Ship to' }}:
                        </p>
                        <h4 class="mb-0 fw-7 header-contents-font-size" style="padding: 4px 0 1px 8px; font-size: 15px">
                            {{ strtoupper($transaction->shipping_name ?? $customerDetail->name) }}
                        </h4>
                        <p class="mb-0 address fs-12 fw-5 header-contents-font-size">
                            @if (isset($shippingAddress->address_1))
                                {{ strtoupper($shippingAddress->address_1) }},
                            @endif
                            @if (isset($shippingAddress->address_2))
                                {{ strtoupper($shippingAddress->address_2) }},
                            @endif
                            {{ isset($shippingAddress->city_id) ? strtoupper(getCityName($shippingAddress->city_id)) . ',' : ''  }}
                            {{ isset($shippingAddress->state_id) ? strtoupper(getStateName($shippingAddress->state_id)) . ',' : '' }}
                            {{ isset($shippingAddress->country_id) ? strtoupper(getCountryName($shippingAddress->country_id)) . ',' : '' }},
                            {{ $shippingAddress->pin_code ?? null }}
                        </p>
                        @if (!empty($transaction->party_phone_number))
                            <p class="mb-0 fs-12 fw-5 ps-3 header-contents-font-size" style="padding: 2px 0px 0px 0px">
                                Contact No:
                                    +{{ $transaction->region_code ?? '' }} {{ $transaction->party_phone_number ?? null }}
                            </p>
                        @endif
                        @if ($isCompanyGstApplicable && !empty($transaction->shipping_gstin))
                            <p class="mb-0 fs-12 fw-5 ps-3 header-contents-font-size" style="padding: 2px 0px 0px 0px">
                                GSTIN: {{ $transaction->shipping_gstin ?? null }}
                            </p>
                        @endif
                        @if (!empty($panNumber) && $showPanNumber)
                            <p class="mb-0 fs-12 fw-5 ps-3 header-contents-font-size" style="padding: 2px 0px 0px 0px">
                                PAN: {{ $panNumber ?? null }}
                            </p>
                        @endif
                    </div>
                @endif
                @if (isset($transaction['dispatch_address_id']) && isset($dispatchAddress) && ($invoiceSetting['delivery_challan_dispatch_from_details'] ?? false))
                    <div class="col vertical-top border-bottom pe-0 ps-0">
                        <p
                            class="mb-0 text-primary border-bottom border-top fw-7 table-heading header-labels-font-size">
                            {{ $changeLabel['delivery_challan_dispatch_from_label'] ?? 'Dispatch from' }}:
                        </p>
                        <p class="mb-0 address fs-12 fw-5 header-contents-font-size">
                            {{ isset($dispatchAddress->address_1) ? strtoupper($dispatchAddress->address_1 .',') : null }}
                            {{ isset($dispatchAddress->address_2) ? strtoupper($dispatchAddress->address_2 .',') : null }}
                            {{ isset($dispatchAddress->city_id) ?  strtoupper(getCityName($dispatchAddress->city_id).',') : null }}
                            {{ isset($dispatchAddress->state_id) ? strtoupper(getStateName($dispatchAddress->state_id).',') : null }}
                            {{ isset($dispatchAddress->country_id) ? strtoupper(getCountryName($dispatchAddress->country_id).',') : null }}
                            {{ $dispatchAddress->pin_code ?? null }}
                        </p>
                    </div>
                @endif
            </div>
            <div class="row">
                @if ((($invoiceSetting['delivery_challan_transport_details'] ?? true) && !empty($transaction->transportDetails)))
                    <div class="col {{ (($invoiceSetting['delivery_challan_broker_details'] ?? true) && !empty($transaction->brokerDetails)) || ($invoiceSetting['delivery_challan_po_number'] ?? true) || ($invoiceSetting['show_delivery_challan_po_date'] ?? true) ? 'border-right' : '' }} vertical-top border-bottom pe-0 ps-0">
                        <table>
                            <tbody>
                                @if (($invoiceSetting['delivery_challan_transport_details'] ?? true) && !empty($transaction->transportDetails))
                                    <tr>
                                        <td class="fs-12 fw-6 header-contents-font-size" style="padding: 2px 8px 0px 8px; width:35%">
                                            {{ $changeLabel['delivery_challan_transport_name_label'] ?? 'Transport Name' }}:
                                        </td>
                                        <td class="fs-12 header-contents-font-size" style="padding: 2px 8px 0px 0px">
                                            {{ $transaction->transportDetails->transporter_name ?? '' }}
                                        </td>
                                    </tr>
                                    @if($isCompanyGstApplicable)
                                    <tr>
                                        <td class="fs-12 fw-6 header-contents-font-size" style="padding: 2px 8px 0px 8px; width:35%">
                                            GSTIN:
                                        </td>
                                        <td class="fs-12 header-contents-font-size" style="padding: 2px 8px 0px 0px">
                                            {{ $transaction->transportDetails->gstin ?? '' }}
                                        </td>
                                    </tr>
                                    @endif
                                    <tr>
                                        <td class="fs-12 fw-6 header-contents-font-size" style="padding: 2px 8px 0px 8px; width:35%">
                                            {{ $changeLabel['document_no'] ?? 'Document No' }}:
                                        </td>
                                        <td class="fs-12 header-contents-font-size" style="padding: 2px 8px 0px 0px">
                                            {{ $transaction->transporter_document_number ?? '' }}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="fs-12 fw-6 header-contents-font-size" style="padding: 2px 8px 0px 8px; width:35%">
                                            {{ $changeLabel['document_date'] ?? 'Document Date' }}:
                                        </td>
                                        <td class="fs-12 header-contents-font-size" style="padding: 2px 8px 0px 0px">
                                            {{ !empty($transaction->transporter_document_date) ? \Carbon\Carbon::parse($transaction->transporter_document_date)->format('d-m-Y') : ' ' }}
                                        </td>
                                    </tr>
                                    @if (!empty($transaction->transporter_vehicle_number))
                                        <tr>
                                            <td class="fs-12 fw-6 header-contents-font-size" style="padding: 2px 8px 0px 8px; width:35%">
                                                {{ $changeLabel['delivery_challan_transport_vehicle_number_label'] ?? 'Vehicle No' }}:
                                            </td>
                                            <td class="fs-12 header-contents-font-size" style="padding: 2px 8px 0px 0px">
                                                {{ $transaction->transporter_vehicle_number ?? '' }}
                                            </td>
                                        </tr>
                                    @endif
                                @endif
                            </tbody>
                        </table>
                    </div>
                @endif
                @if ((($invoiceSetting['delivery_challan_broker_details'] ?? true) && !empty($transaction->brokerDetails)) || ($invoiceSetting['delivery_challan_po_number'] ?? true) || (isset($deliveryChallanInvoiceNumber) && !empty($deliveryChallanInvoiceNumber)))
                    <div class="col vertical-top border-bottom pe-0 ps-0">
                        <table>
                            <tbody>
                                @if (($invoiceSetting['delivery_challan_broker_details'] ?? true) && !empty($transaction->brokerDetails))
                                    <tr>
                                        <td class="fs-12 fw-6 header-contents-font-size" style="padding: 2px 8px 0px 8px; width:25%">
                                            Broker:
                                        </td>
                                        <td class="fs-12 header-contents-font-size" style="padding: 2px 8px 0px 0px">
                                            {{ $transaction->brokerDetails->broker_name ?? '' }}
                                        </td>
                                    </tr>
                                    @if($isCompanyGstApplicable)
                                        <tr>
                                            <td class="fs-12 fw-6 header-contents-font-size" style="padding: 2px 8px 0px 8px; width:25%">
                                                GSTIN:
                                            </td>
                                            <td class="fs-12 header-contents-font-size" style="padding: 2px 8px 0px 0px">
                                                {{ $transaction->brokerDetails->gstin ?? '' }}
                                            </td>
                                        </tr>
                                    @endif
                                @endif
                                @if ($invoiceSetting['delivery_challan_po_number'] ?? true)
                                    <tr>
                                        <td class="fs-12 fw-6 header-contents-font-size" style="padding: 2px 8px 0px 8px; width:25%">
                                            {{ $changeLabel['delivery_challan_po_number_label'] ?? 'PO No' }}:
                                        </td>
                                        <td class="fs-12 header-contents-font-size" style="padding: 2px 8px 0px 0px">
                                            {{ $transaction->po_no }}
                                        </td>
                                    </tr>
                                @endif
                                @if ($invoiceSetting['show_delivery_challan_po_date'] ?? true)
                                    <tr>
                                        <td class="fs-12 fw-6 header-contents-font-size" style="padding: 2px 8px 0px 8px; width:25%">
                                            {{ $changeLabel['delivery_challan_po_date_label'] ?? 'PO Date' }}:
                                        </td>
                                        <td class="fs-12 header-contents-font-size" style="padding: 2px 8px 0px 0px">
                                            {{ isset($transaction->po_date) ? \Carbon\Carbon::parse($transaction->po_date)->format('d-m-Y') : null }}
                                        </td>
                                    </tr>
                                @endif
                            </tbody>
                        </table>
                    </div>
                @endif
            </div>

            {{-- Custom Fields Section Start --}}
            @if (count($customFieldValues) > 0)
                @php
                    $customFields = collect($customFieldValues)->where('is_show_in_print', true)->values();
                @endphp
                <table cellpadding="0" class="item-table">
                    @foreach ($customFields->chunk(3) as $chunk)
                        <tr class="border-bottom">
                            @foreach ($chunk as $customField)
                                <td class="{{ $loop->last ? '' : 'border-right' }}" style="padding: 6px 8px; width:150px; ">
                                    <span style="font-weight: bold;">{{ $customField['label_name'] ?? '' }}</span> : {{ $customField['value'] ?? '' }}
                                </td>
                            @endforeach
                        </tr>
                    @endforeach
                </table>
            @endif
            {{-- Custom Fields Section End --}}

            @php
                $customFieldItemsValues = collect($transactionItems[0]['customItemsValues'])->where('is_show_in_print', true)->values();
                $customFieldItemsHeaders = collect($customFieldItemsValues)->pluck('label_name')->toArray();
                $cfNumberTypeFieldTotals = [];
            @endphp
            {{-- Item Table Section Start --}}
            @if ($itemType == \App\Models\DeliveryChallanTransaction::WITH_AMOUNT)
                <table cellpadding="0" class="item-table" style="flex-grow: 1;">
                    <tbody>
                        <tr class="border-bottom">
                            <td class="text-center table-headings-font-size border-right"
                                style="padding: 6px 8px; font-weight: bold;">
                                {{ $changeLabel['delivery_challan_sn_label'] ?? 'SN' }}
                            </td>
                            <td class="table-headings-font-size border-right whitespace-nowrap w-100"
                                style="padding: 6px 8px;font-weight: bold;min-width: 170px;">
                                {{ $changeLabel['delivery_challan_item_name_label'] ?? 'Item Name' }}
                            </td>
                            @if (count($customFieldItemsHeaders) > 0)
                                @foreach ($customFieldItemsHeaders as $customFieldItemHeader)
                                    <td class="table-headings-font-size border-right whitespace-nowrap text-start"
                                        style="padding: 6px 8px; font-weight: bold">
                                        {{ $customFieldItemHeader ?? '' }}
                                    </td>
                                @endforeach
                            @endif
                            @if($isCompanyGstApplicable && ($showPrintSettings['show_delivery_challan_hsn_sac'] ?? true))
                                <td class="table-headings-font-size border-right whitespace-nowrap text-start"
                                    style="padding: 6px 8px; font-weight: bold">
                                    {{ $changeLabel['delivery_challan_hsn_sac_label'] ?? 'HSN/SAC' }}
                                </td>
                            @endif
                            @if($isCompanyGstApplicable && ($showPrintSettings['show_delivery_challan_gst'] ?? true))
                                <td class="text-center table-headings-font-size border-right whitespace-nowrap"
                                    style="padding: 6px 8px; font-weight: bold">
                                    {{ $changeLabel['delivery_challan_gst_label'] ?? 'GST (%)' }}
                                </td>
                            @endif
                            @if($showPrintSettings['show_delivery_challan_qty'] ?? true)
                                <td class="table-headings-font-size border-right whitespace-nowrap text-end"
                                    style="padding: 6px 8px; font-weight: bold">
                                    {{ $changeLabel['delivery_challan_qty_label'] ?? 'Qty' }}
                                </td>
                            @endif
                            @if(($showPrintSettings['delivery_challan_uom_enable'] ?? true) && $transactionItems->sum('secondary_quantity') != 0.0)
                                <td class="table-headings-font-size border-right whitespace-nowrap text-end"
                                    style="padding: 6px 8px; font-weight: bold">
                                    {{ $changeLabel['delivery_challan_uom_label'] ?? 'UOM' }}
                                </td>
                            @endif
                            @if(($showPrintSettings['show_delivery_challan_mrp'] ?? true) && $transactionItems->sum('mrp') != 0.0)
                                <td class="table-headings-font-size border-right whitespace-nowrap text-end"
                                    style="padding: 6px 8px; font-weight: bold">
                                    {{ $changeLabel['delivery_challan_mrp_label'] ?? 'MRP' }}
                                </td>
                            @endif
                            @if ($isCompanyGstApplicable)
                                @if($showPrintSettings['show_delivery_challan_rate_with_gst'] ?? false)
                                    <td class="table-headings-font-size border-right whitespace-nowrap text-end"
                                        style="padding: 6px 8px; font-weight: bold">
                                        {{ $changeLabel['delivery_challan_rate_with_gst_label'] ?? 'Rate With GST' }}
                                    </td>
                                @endif
                            @endif
                            @if($showPrintSettings['show_delivery_challan_rate'] ?? true)
                                <td class="table-headings-font-size border-right whitespace-nowrap text-end"
                                    style="padding: 6px 8px; font-weight: bold">
                                    {{ $changeLabel['delivery_challan_rate_label'] ?? 'Rate' }}
                                </td>
                            @endif
                            @if(($showPrintSettings['show_delivery_challan_discount'] ?? true) && $transactionItems->sum('discount_value') != 0.0)
                                <td class="table-headings-font-size border-right whitespace-nowrap text-end"
                                    style="padding: 6px 8px; font-weight: bold">
                                    {{ $changeLabel['delivery_challan_discount_label'] ?? 'Dis.' }}
                                </td>
                            @endif
                            @if(($showPrintSettings['delivery_challan_dis_2_enable'] ?? true) && $transactionItems->sum('discount_value_2') != 0.0)
                                <td class="table-headings-font-size border-right whitespace-nowrap text-end"
                                    style="padding: 6px 8px; font-weight: bold">
                                    {{ $changeLabel['delivery_challan_dis_2_label'] ?? 'Dis. 2' }}
                                </td>
                            @endif
                            @if(($showPrintSettings['show_delivery_challan_total_discount'] ?? true) && $transactionItems->sum('total_discount_amount') != 0.0)
                                <td class="table-headings-font-size border-right whitespace-nowrap text-end"
                                    style="padding: 6px 8px; font-weight: bold">
                                    {{ $changeLabel['delivery_challan_total_discount_label'] ?? 'Total Dis.' }}
                                </td>
                            @endif
                            <td class="table-headings-font-size whitespace-nowrap text-end"
                                style="padding: 6px 8px; font-weight: bold">
                                {{ $changeLabel['delivery_challan_taxable_value_label'] ?? 'Taxable Value' }}
                            </td>
                        </tr>
                        @foreach ($transactionItems as $key => $item)
                            @php
                                $uniqueId = ++$key;
                                $ids = collect($customFieldItemsValues)->pluck('custom_field_item_id')->values();
                                $printCustomFields = collect($item['customItemsValues'])->where('is_show_in_print', true)->whereIn('custom_field_item_id', $ids)->values();
                                foreach ($item['customItemsValues'] as $customField) {
                                    $cfId = $customField['custom_field_id'];
                                    if ($customField['show_total'] && $customField['custom_field_type'] == \App\Models\ItemCustomField::CF_TYPE_NUMBER) {
                                        $value = (float) $customField['value'] ?? 0;
                                        if (! isset($cfNumberTypeFieldTotals[$cfId])) {
                                            $cfNumberTypeFieldTotals[$cfId] = 0;
                                        }
                                        $cfNumberTypeFieldTotals[$cfId] += $value;
                                    } else {
                                        $cfNumberTypeFieldTotals[$cfId] = null;
                                    }
                                }
                                $customItemsInventoryValues = $item['customItemsInventoryValues'] ?? [];
                            @endphp
                            <tr>
                                <td class="text-center table-contents-font-size border-right" style="padding: 4px 8px 0 8px">
                                    {{ $uniqueId }}
                                </td>
                                <td class="table-contents-font-size border-right" style="padding: 4px 8px 0 8px">
                                    <p class="table-contents-font-size" style="font-weight: bold">{{ $item->items->item_name ?? null }}</p>
                                    @if ($item->items->sku != null && ($invoiceSetting['show_delivery_challan_item_sku'] ?? true))
                                        <p style="" class="description-font-size">Item Code:
                                            {{ $item->items->sku ?? null }}</p>
                                    @endif
                                    <p style="word-break: break-word; " class="description-font-size">
                                        {!! !empty($item->consolidating_items_to_invoice)
                                            ? '(' . consolidatingItemsToInvoice($item->consolidating_items_to_invoice) . ')'
                                            : null !!}
                                    </p>
                                    <p style="word-break: break-word;" class="description-font-size">
                                        {!! !empty($item->additional_description) ? nl2br('(' . $item->additional_description . ')') : null !!}
                                    </p>
                                    @if(!empty($item->items->item_image) && ($invoiceSetting['show_delivery_challan_item_image'] ?? true))
                                        <img src="{{ $item->items->item_image }}" width="60"  height="60" style="margin-top: 4px">
                                    @endif
                                    @if (count($customItemsInventoryValues) > 0)
                                        @foreach ($customItemsInventoryValues as $customItemsInventoryValue)
                                            <p style="word-break: break-word; font-style: italic; color: #888888;" class="description-font-size">{{ $customItemsInventoryValue }}</p>
                                        @endforeach
                                    @endif
                                </td>
                                @if (count($printCustomFields) > 0)
                                    @foreach ($printCustomFields as $customFieldItemsValue)
                                        <td class="text-center table-contents-font-size border-right" style="padding: 4px 8px 0 8px">
                                            {{ $customFieldItemsValue['value'] ?? '' }}
                                        </td>
                                    @endforeach
                                @endif
                                @if($isCompanyGstApplicable && ($showPrintSettings['show_delivery_challan_hsn_sac'] ?? true))
                                    <td class="table-contents-font-size border-right whitespace-nowrap text-start"
                                        style="padding: 4px 8px 0 8px">
                                        {{ $item->hsn_code ?? $item->items->model->hsn_sac_code ?? null }}
                                    </td>
                                @endif
                                @if($isCompanyGstApplicable && ($showPrintSettings['show_delivery_challan_gst'] ?? true))
                                    <td class="text-center table-contents-font-size border-right whitespace-nowrap"
                                        style="padding: 4px 8px 0 8px">
                                        {{ $item->gst_tax_percentage ?? '0.0' }}
                                    </td>
                                @endif
                                @if($showPrintSettings['show_delivery_challan_qty'] ?? true)
                                    <td class="table-contents-font-size border-right whitespace-nowrap text-end"
                                        style="padding: 4px 8px 0 8px">
                                        {{ $item->primary_quantity }}
                                        @if($showPrintSettings['show_delivery_challan_unit'] ?? true)
                                            {{ $item->primary_unit_name }}
                                        @endif
                                    </td>
                                @endif
                                @if(($showPrintSettings['delivery_challan_uom_enable'] ?? true) && $transactionItems->sum('secondary_quantity') != 0.0)
                                    <td class="table-contents-font-size border-right whitespace-nowrap text-end"
                                        style="padding: 4px 8px 0 8px">
                                        {{ round( $item->secondary_quantity,2) }}
                                        @if($showPrintSettings['show_delivery_challan_unit'] ?? true)
                                            {{ $item->secondary_unit_name }}
                                        @endif
                                    </td>
                                @endif
                                @if(($showPrintSettings['show_delivery_challan_mrp'] ?? true) && $transactionItems->sum('mrp') != 0.0)
                                    <td class="table-contents-font-size border-right whitespace-nowrap text-end"
                                        style="padding: 4px 8px 0 8px">
                                        {{ !empty($item->mrp) ? $pdfSymbol.getCurrencyFormatFor3digit($item->mrp) : '-' }}
                                    </td>
                                @endif
                                @if ($isCompanyGstApplicable)
                                    @if($showPrintSettings['show_delivery_challan_rate_with_gst'] ?? false)
                                        <td class="table-contents-font-size border-right whitespace-nowrap text-end"
                                            style="padding: 4px 8px 0 8px">
                                            {{ $pdfSymbol.getCurrencyFormatFor3digit($item->rpu_with_gst) }}
                                        </td>
                                    @endif
                                @endif
                                @if($showPrintSettings['show_delivery_challan_rate'] ?? true)
                                    <td class="table-contents-font-size border-right whitespace-nowrap text-end"
                                        style="padding: 4px 8px 0 8px">
                                        {{ $pdfSymbol.getCurrencyFormatFor3digit($item->rpu_without_gst) }}
                                    </td>
                                @endif
                                @if(($showPrintSettings['show_delivery_challan_discount'] ?? true) && $transactionItems->sum('discount_value') != 0.0)
                                    <td class="table-contents-font-size border-right whitespace-nowrap text-end"
                                        style="padding: 4px 8px 0 8px">
                                        @if ($item->discount_type == \App\Models\DeliveryChallanTransaction::DISCOUNT_TYPE_AMOUNT)
                                            {{ $pdfSymbol.getCurrencyFormatFor3digit($item->discount_value) ?? '0.0' }}
                                        @else
                                            {{ $item->discount_value . '(%)' ?? '0.0(%)' }}
                                        @endif
                                    </td>
                                @endif
                                @if(($showPrintSettings['delivery_challan_dis_2_enable'] ?? true) && $transactionItems->sum('discount_value_2') != 0.0)
                                    <td class="table-contents-font-size border-right whitespace-nowrap text-end"
                                        style="padding: 4px 8px 0 8px">
                                        @if ($item->discount_type_2 == \App\Models\DeliveryChallanTransaction::DISCOUNT_TYPE_AMOUNT)
                                            {{ $pdfSymbol.getCurrencyFormatFor3digit($item->discount_value_2) ?? '0.0' }}
                                        @else
                                            {{ $item->discount_value_2 . '(%)' ?? '0.0(%)' }}
                                        @endif
                                    </td>
                                @endif
                                @if(($showPrintSettings['show_delivery_challan_total_discount'] ?? true) && $transactionItems->sum('total_discount_amount') != 0.0)
                                    <td class="table-contents-font-size border-right whitespace-nowrap text-end"
                                        style="padding: 4px 8px 0 8px">
                                        {{ $pdfSymbol.getCurrencyFormatFor3digit($item->total_discount_amount ?? '0.0') }}
                                    </td>
                                @endif
                                <td class="table-contents-font-size whitespace-nowrap text-end"
                                    style="padding: 4px 8px 0 8px">
                                    {{ $pdfSymbol.getCurrencyFormat(round($item->total ?? '0.0', getCompanyFixedDigitNumber())) }}
                                </td>
                            </tr>
                        @endforeach
                        <tr class="border-top">
                            <td class="text-center footer-contents-font-size border-right" style="padding: 6px 8px">
                            </td>
                            <td class="table-headings-font-size border-right" style="padding: 6px 8px; font-weight: bold">
                                Total
                            </td>
                            @if (count($customFieldItemsValues) > 0)
                                @foreach ($customFieldItemsValues as $customFieldItemsValue)
                                    <td class="text-center footer-contents-font-size border-right" style="padding: 6px 8px">
                                        {{ $cfNumberTypeFieldTotals[$customFieldItemsValue['custom_field_id']] ?? '' }}
                                    </td>
                                @endforeach
                            @endif
                            @if($isCompanyGstApplicable && ($showPrintSettings['show_delivery_challan_hsn_sac'] ?? true))
                                <td class="table-headings-font-size border-right whitespace-nowrap text-start"
                                    style="padding: 6px 8px; font-weight: bold">
                                </td>
                            @endif
                            @if($isCompanyGstApplicable && ($showPrintSettings['show_delivery_challan_gst'] ?? true))
                                <td class="text-center table-headings-font-size border-right whitespace-nowrap"
                                    style="padding: 6px 8px; font-weight: bold">
                                </td>
                            @endif
                            @if($showPrintSettings['show_delivery_challan_qty'] ?? true)
                                <td class="table-headings-font-size border-right whitespace-nowrap text-end"
                                    style="padding: 6px 8px; font-weight: bold">
                                    {{ getCurrencyFormat($transactionItems->sum('primary_quantity')) }}
                                </td>
                            @endif
                            @if(($showPrintSettings['delivery_challan_uom_enable'] ?? true) && $transactionItems->sum('secondary_quantity') != 0.0)
                                <td class="table-headings-font-size border-right whitespace-nowrap text-end"
                                    style="padding: 6px 8px; font-weight: bold">
                                    {{ getCurrencyFormat($transactionItems->sum('secondary_quantity')) }}
                                </td>
                            @endif
                            @if(($showPrintSettings['show_delivery_challan_mrp'] ?? true) && $transactionItems->sum('mrp') != 0.0)
                                <td class="table-headings-font-size border-right whitespace-nowrap text-end"
                                    style="padding: 6px 8px; font-weight: bold">
                                </td>
                            @endif
                            @if ($isCompanyGstApplicable)
                                @if($showPrintSettings['show_delivery_challan_rate_with_gst'] ?? false)
                                    <td class="table-headings-font-size border-right whitespace-nowrap text-end"
                                        style="padding: 6px 8px; font-weight: bold">
                                    </td>
                                @endif
                            @endif
                            @if($showPrintSettings['show_delivery_challan_rate'] ?? true)
                                <td class="table-headings-font-size border-right whitespace-nowrap text-end"
                                    style="padding: 6px 8px; font-weight: bold">
                                </td>
                            @endif
                            @if(($showPrintSettings['show_delivery_challan_discount'] ?? true) && $transactionItems->sum('discount_value') != 0.0)
                                <td class="table-headings-font-size border-right whitespace-nowrap text-end"
                                    style="padding: 6px 8px; font-weight: bold">
                                </td>
                            @endif
                            @if(($showPrintSettings['delivery_challan_dis_2_enable'] ?? true) && $transactionItems->sum('discount_value_2') != 0.0)
                                <td class="table-headings-font-size border-right whitespace-nowrap text-end"
                                    style="padding: 6px 8px; font-weight: bold">
                                </td>
                            @endif
                            @if(($showPrintSettings['show_delivery_challan_total_discount'] ?? true) && $transactionItems->sum('total_discount_amount') != 0.0)
                                <td class="table-headings-font-size border-right whitespace-nowrap text-end"
                                    style="padding: 6px 8px; font-weight: bold">
                                    {{ $pdfSymbol.getCurrencyFormat($transactionItems->sum('total_discount_amount') ?? 0.0) }}
                                </td>
                            @endif
                            <td class="table-headings-font-size whitespace-nowrap text-end"
                                style="padding: 6px 8px; font-weight: bold">
                                {{ $pdfSymbol.getCurrencyFormat($transaction->gross_value) }}
                            </td>
                        </tr>
                    </tbody>
                </table>
                {{-- Item Type WITH AMOUNT End --}}
            @else
                <table cellpadding="0" class="item-table" style="flex-grow: 1;">
                    <tbody>
                        <tr class="border-bottom">
                            <td class="text-center table-headings-font-size border-right"
                                style="padding: 6px 8px; font-weight: bold;">
                                {{ $changeLabel['delivery_challan_sn_label'] ?? 'SN' }}
                            </td>
                            <td class="table-headings-font-size border-right whitespace-nowrap w-100"
                                style="padding: 6px 8px;font-weight: bold;min-width: 170px;">
                                {{ $changeLabel['delivery_challan_item_name_label'] ?? 'Item Name' }}
                            </td>
                            @if (count($customFieldItemsHeaders) > 0)
                                @foreach ($customFieldItemsHeaders as $customFieldItemHeader)
                                    <td class="table-headings-font-size border-right whitespace-nowrap text-start"
                                        style="padding: 6px 8px; font-weight: bold">
                                        {{ $customFieldItemHeader ?? '' }}
                                    </td>
                                @endforeach
                            @endif
                            @if($showPrintSettings['show_delivery_challan_qty'] ?? true)
                                <td class="table-headings-font-size border-right whitespace-nowrap text-end"
                                    style="padding: 6px 8px; font-weight: bold">
                                    {{ $changeLabel['delivery_challan_qty_label'] ?? 'Qty' }}
                                </td>
                            @endif
                            @if(($showPrintSettings['delivery_challan_uom_enable'] ?? true) && $transactionItems->sum('secondary_quantity') != 0.0)
                                <td class="table-headings-font-size border-right whitespace-nowrap text-end"
                                    style="padding: 6px 8px; font-weight: bold">
                                    {{ $changeLabel['delivery_challan_uom_label'] ?? 'UOM' }}
                                </td>
                            @endif
                        </tr>
                        @foreach ($transactionItems as $key => $item)
                            @php
                                $uniqueId = ++$key;
                                $ids = collect($customFieldItemsValues)->pluck('custom_field_item_id')->values();
                                $printCustomFields = collect($item['customItemsValues'])->where('is_show_in_print', true)->whereIn('custom_field_item_id', $ids)->values();
                                foreach ($item['customItemsValues'] as $customField) {
                                    $cfId = $customField['custom_field_id'];
                                    if ($customField['show_total'] && $customField['custom_field_type'] == \App\Models\ItemCustomField::CF_TYPE_NUMBER) {
                                        $value = (float) $customField['value'] ?? 0;
                                        if (! isset($cfNumberTypeFieldTotals[$cfId])) {
                                            $cfNumberTypeFieldTotals[$cfId] = 0;
                                        }
                                        $cfNumberTypeFieldTotals[$cfId] += $value;
                                    } else {
                                        $cfNumberTypeFieldTotals[$cfId] = null;
                                    }
                                }
                            @endphp
                            <tr>
                                <td class="text-center footer-contents-font-size border-right" style="padding: 4px 8px 0 8px">
                                    {{ $uniqueId }}
                                </td>
                                <td class="footer-contents-font-size border-right" style="padding: 4px 8px 0 8px">
                                    <p class="table-contents-font-size" style="font-weight: bold">{{ $item->items->item_name ?? null }}</p>
                                    @if ($item->items->sku != null && ($invoiceSetting['show_delivery_challan_item_sku'] ?? true))
                                        <p style="" class="description-font-size">Item Code:
                                            {{ $item->items->sku ?? null }}</p>
                                    @endif
                                    <p style="word-break: break-word; " class="description-font-size">
                                        {!! !empty($item->consolidating_items_to_invoice)
                                            ? '(' . consolidatingItemsToInvoice($item->consolidating_items_to_invoice) . ')'
                                            : null !!}
                                    </p>
                                    <p style="word-break: break-word;" class="description-font-size">
                                        {!! !empty($item->additional_description) ? nl2br('(' . $item->additional_description . ')') : null !!}
                                    </p>
                                    @if(!empty($item->items->item_image) && ($invoiceSetting['show_delivery_challan_item_image'] ?? true))
                                        <img src="{{ $item->items->item_image }}" width="60"  height="60" style="margin-top: 4px">
                                    @endif
                                </td>
                                @if (count($printCustomFields) > 0)
                                    @foreach ($printCustomFields as $customFieldItemsValue)
                                        <td class="text-center footer-contents-font-size border-right" style="padding: 4px 8px 0 8px">
                                            {{ $customFieldItemsValue['value'] ?? '' }}
                                        </td>
                                    @endforeach
                                @endif
                                @if($showPrintSettings['show_delivery_challan_qty'] ?? true)
                                    <td class="table-headings-font-size border-right whitespace-nowrap text-end"
                                        style="padding: 4px 8px 0 8px">
                                        {{ $item->primary_quantity }}
                                        @if($showPrintSettings['show_delivery_challan_unit'] ?? true)
                                            {{ $item->primary_unit_name }}
                                        @endif
                                    </td>
                                @endif
                                @if(($showPrintSettings['delivery_challan_uom_enable'] ?? true) && $transactionItems->sum('secondary_quantity') != 0.0)
                                    <td class="table-headings-font-size border-right whitespace-nowrap text-end"
                                        style="padding: 4px 8px 0 8px">
                                        {{ round( $item->secondary_quantity,2) }}
                                        @if($showPrintSettings['show_delivery_challan_unit'] ?? true)
                                            {{ $item->secondary_unit_name }}
                                        @endif
                                    </td>
                                @endif
                            </tr>
                        @endforeach
                        <tr class="border-top">
                            <td class="text-center footer-contents-font-size border-right" style="padding: 4px 8px 0 8px">
                            </td>
                            <td class="footer-contents-font-size border-right" style="padding: 4px 8px 0 8px; font-weight: bold">
                                Total
                            </td>
                            @if (count($customFieldItemsValues) > 0)
                                @foreach ($customFieldItemsValues as $customFieldItemsValue)
                                    <td class="text-center footer-contents-font-size border-right" style="padding: 4px 8px 0 8px">
                                        {{ $cfNumberTypeFieldTotals[$customFieldItemsValue['custom_field_id']] ?? '' }}
                                    </td>
                                @endforeach
                            @endif
                            @if($showPrintSettings['show_delivery_challan_qty'] ?? true)
                                <td class="table-headings-font-size border-right whitespace-nowrap text-end"
                                    style="padding: 6px 8px; font-weight: bold">
                                    {{ getCurrencyFormat($transactionItems->sum('primary_quantity')) }}
                                </td>
                            @endif
                            @if(($showPrintSettings['delivery_challan_uom_enable'] ?? true) && $transactionItems->sum('secondary_quantity') != 0.0)
                                <td class="table-headings-font-size border-right whitespace-nowrap text-end"
                                    style="padding: 6px 8px; font-weight: bold">
                                    {{ getCurrencyFormat($transactionItems->sum('secondary_quantity')) }}
                                </td>
                            @endif
                        </tr>
                    </tbody>
                </table>
            @endif
            {{-- Item Table Section End --}}
            @if ((($showPrintSettings['show_delivery_challan_narration'] ?? true) && $transaction->narration) || (($showPrintSettings['show_delivery_challan_terms_and_conditions'] ?? true) && $transaction->term_and_condition) || $itemType == \App\Models\DeliveryChallanTransaction::WITH_AMOUNT)
                <table class="border-top" cellpadding="0">
                    <tbody>
                        <tr>
                            <td>
                                @if ($transaction->term_and_condition || $transaction->narration)
                                    <div>
                                        @if(($showPrintSettings['show_delivery_challan_narration'] ?? true) && $transaction->narration)
                                            <div class="{{ ($showPrintSettings['show_delivery_challan_terms_and_conditions'] ?? true) && $transaction->term_and_condition ? 'border-bottom' : '' }}">
                                                <h4 class="note-font-size fw-6" style="padding: 8px 0 0 8px;">
                                                    {{ $changeLabel['delivery_challan_narration'] ?? 'Notes' }}:
                                                </h4>
                                                <div style="padding: 4px 8px;">
                                                    <p class="note-font-size">
                                                        {!! nl2br($transaction->narration) !!}
                                                    </p>
                                                </div>
                                            </div>
                                        @endif
                                        @if(($showPrintSettings['show_delivery_challan_terms_and_conditions'] ?? true) && $transaction->term_and_condition)
                                            <div>
                                                <h4 class="terms-and-conditions-font-size fw-6" style="padding: 8px 0 0 8px;">
                                                    {{ $changeLabel['delivery_challan_terms_and_conditions'] ?? 'Terms and Conditions' }}:
                                                </h4>
                                                <div style="padding: 4px 8px" class="terms-and-conditions-font-size">
                                                    <p class="terms-and-conditions-font-size">
                                                        {!! nl2br($transaction->term_and_condition) !!}
                                                    </p>
                                                </div>
                                            </div>
                                        @endif
                                    </div>
                                @endif
                            </td>
                            @if ($itemType == \App\Models\DeliveryChallanTransaction::WITH_AMOUNT)
                                <td class="vertical-top" style="width: 231px; border-left: 1px solid black">
                                    <table>
                                        <tbody>
                                            @foreach ($additionalCharges as $additionalCharge)
                                                <tr class="{{ $loop->last ? 'border-bottom' : '' }}">
                                                    <td class="table-headings-font-size vertical-top fw-6" style="padding: 3px 8px 2px 8px">
                                                        {{ $additionalCharge['ledger_name'] }}
                                                    </td>
                                                    <td class="table-headings-font-size vertical-top text-end fw-6" style="padding: 3px 8px 2px 8px">
                                                        {{ $pdfSymbol.getCurrencyFormat($additionalCharge['amount'] ?? '0.0') }}
                                                    </td>
                                                </tr>
                                            @endforeach
                                            <tr class="">
                                                <td class="table-headings-font-size vertical-top fw-7" style="padding: 4px 8px 2px 8px">
                                                    {{ $changeLabel['delivery_challan_sub_total'] ?? ($isCompanyGstApplicable ? 'Taxable Value' : 'Sub Total') }}:
                                                </td>
                                                <td class="table-headings-font-size vertical-top text-end fw-7" style="padding: 4px 8px 2px 8px">
                                                    {{ $pdfSymbol.getCurrencyFormat(round($transaction->taxable_value, getCompanyFixedDigitNumber()) ?? '0.0') }}
                                                </td>
                                            </tr>
                                            @if($isCompanyGstApplicable)
                                                @if ($transaction->cgst != 0)
                                                    <tr>
                                                        <td class="table-headings-font-size vertical-top fw-6" style="padding: 2px 8px 0 8px">
                                                            {{ $changeLabel['delivery_challan_cgst'] ?? 'CGST' }}
                                                        </td>
                                                        <td class="table-headings-font-size vertical-top text-end fw-6" style="padding: 2px 8px 0 8px">
                                                            {{ $pdfSymbol.getCurrencyFormat(round($transaction->cgst, getCompanyFixedDigitNumber()) ?? '0.0') }}
                                                        </td>
                                                    </tr>
                                                @endif
                                                @if ($transaction->sgst != 0)
                                                    <tr>
                                                        <td class="table-headings-font-size vertical-top fw-6" style="padding: 2px 8px 0 8px">
                                                            {{ $changeLabel['delivery_challan_sgst'] ?? 'SGST' }}
                                                        </td>
                                                        <td class="table-headings-font-size vertical-top text-end fw-6" style="padding: 2px 8px 0 8px">
                                                            {{ $pdfSymbol.getCurrencyFormat(round($transaction->sgst, getCompanyFixedDigitNumber()) ?? '0.0') }}
                                                        </td>
                                                    </tr>
                                                @endif
                                                @if ($transaction->igst != 0)
                                                    <tr>
                                                        <td class="table-headings-font-size vertical-top fw-6" style="padding: 2px 8px 0 8px">
                                                            {{ $changeLabel['delivery_challan_igst'] ?? 'IGST' }}
                                                        </td>
                                                        <td class="table-headings-font-size vertical-top text-end fw-6" style="padding: 2px 8px 0 8px">
                                                            {{ $pdfSymbol.getCurrencyFormat(round($transaction->igst, getCompanyFixedDigitNumber()) ?? '0.0') }}
                                                        </td>
                                                    </tr>
                                                @endif
                                            @endif
                                            @if ($transaction->cess != 0)
                                                <tr>
                                                    <td class="table-headings-font-size vertical-top fw-6" style="padding: 2px 8px 0 8px">
                                                        {{ $changeLabel['delivery_challan_cess'] ?? 'CESS' }}
                                                    </td>
                                                    <td class="table-headings-font-size vertical-top text-end fw-6" style="padding: 2px 8px 0 8px">
                                                        {{ $pdfSymbol.getCurrencyFormat($transaction->cess ?? '0.0') }}
                                                    </td>
                                                </tr>
                                            @endif
                                            @if ($transaction->tcs_amount != 0)
                                                <tr>
                                                    <td class="table-headings-font-size vertical-top fw-6" style="padding: 2px 8px 0 8px">
                                                        {{ $changeLabel['delivery_challan_tcs'] ?? 'TCS' }}
                                                    </td>
                                                    <td class="table-headings-font-size vertical-top text-end fw-6" style="padding: 2px 8px 0 8px">
                                                        {{ $pdfSymbol.getCurrencyFormat($transaction->tcs_amount ?? '0.0') }}
                                                    </td>
                                                </tr>
                                            @endif
                                            @php
                                                if (empty($addLess)) {
                                                    $total = $transaction->grand_total;
                                                } else {
                                                    $addLessSum = collect($addLess)->sum('amount');
                                                    $total = $transaction->grand_total - $addLessSum;
                                                    $addLessSumTotal = collect($addLess)->where('is_show_in_print',1)->sum('amount');
                                                    $total = $total + $addLessSumTotal;
                                                }
                                            @endphp
                                            @foreach (collect($addLess)->where('is_show_in_print',1) as $addLessItem)
                                                <tr>
                                                    <td class="table-headings-font-size vertical-top fw-6" style="padding: 2px 8px 0 8px">
                                                        {{ $addLessItem['ledger_name'] }}
                                                    </td>
                                                    <td class="table-headings-font-size vertical-top text-end fw-6" style="padding: 2px 8px 0 8px">
                                                        {{ $pdfSymbol.getCurrencyFormat($addLessItem['amount'] ?? '0.0') }}
                                                    </td>
                                                </tr>
                                            @endforeach
                                            <tr>
                                                <td class="table-headings-font-size vertical-top fw-6" style="padding: 2px 8px">
                                                    {{ $changeLabel['delivery_challan_round_off'] ?? 'Round off' }}
                                                </td>
                                                <td class="table-headings-font-size vertical-top text-end fw-6" style="padding: 2px 8px">
                                                    {{ $pdfSymbol.getCurrencyFormat($transaction->rounding_amount ?? '0.0') }}
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </td>
                            @endif
                        </tr>
                    </tbody>
                </table>
            @endif
            @if ($itemType == \App\Models\DeliveryChallanTransaction::WITH_AMOUNT)
                <table cellpadding="0">
                    <tbody>
                        <tr class="{{ ($invoiceSetting['delivery_challan_received_by'] ?? true) || ($invoiceSetting['delivery_challan_delivered_by'] ?? true) || ($showPrintSettings['show_delivery_challan_authorized_signatory'] ?? true) || ($invoiceSetting['delivery_challan_signature'] ?? true) ? 'border-bottom' : '' }}">
                            <td class="border-right vertical-bottom {{ ($showPrintSettings['show_delivery_challan_in_words'] ?? true) ? 'border-top' : '' }}">
                                @if($showPrintSettings['show_delivery_challan_in_words'] ?? true)
                                    <p class="mb-0 table-contents-font-size fw-6" style="display: flex; padding: 6px 8px">
                                        {{ $changeLabel['delivery_challan_in_words'] ?? 'In Words' }}:
                                        <span class="table-contents-font-size" style="margin-left: 10px; font-weight: 400">
                                            {{ getAmountToWord($total ?? '0.0') }} Only
                                        </span>
                                    </p>
                                @endif
                            </td>
                            <td class="border-top" style="width: 231px;">
                                <table class="vertical-bottom">
                                    <tbody>
                                        <tr>
                                            <td class="text-primary total-font-size"
                                                style="padding: 3px 8px; font-weight: bold;">
                                                {{ $changeLabel['total'] ?? 'Total' }}
                                            </td>
                                            <td class="text-primary text-end total-font-size"
                                                style="padding: 3px 8px; font-weight: bold;">
                                                {{ $pdfSymbol.getCurrencyFormat($total ?? '0.0') }}
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                    </tbody>
                </table>
            @endif

            {{-- Bank Details / Signature Section Start --}}
            @if (($invoiceSetting['delivery_challan_received_by'] ?? true) || ($invoiceSetting['delivery_challan_delivered_by'] ?? true) || ($showPrintSettings['show_delivery_challan_authorized_signatory'] ?? true) || ($invoiceSetting['delivery_challan_signature'] ?? true))
                <table class="{{ $itemType == \App\Models\DeliveryChallanTransaction::WITH_AMOUNT ? '' : 'border-top' }}" cellpadding="0" style="page-break-inside: avoid !important">
                    <tbody>
                        <tr>
                            @if ($invoiceSetting['delivery_challan_received_by'] ?? true)
                                <td class="border-right">
                                    <table>
                                        <tr>
                                            <td class="footer-headings-font-size fw-6" style="padding: 4px 8px 0 8px">{{ $changeLabel['delivery_challan_recived_by'] ?? 'Received By' }}</td>
                                        </tr>
                                        <tr>
                                            <td class="footer-contents-font-size fw-6" style="padding: 4px 8px 0 8px">Name:</td>
                                        </tr>
                                        <tr>
                                            <td class="footer-contents-font-size fw-6" style="padding: 4px 8px 0 8px">Comment:</td>
                                        </tr>
                                        <tr>
                                            <td class="footer-contents-font-size fw-6" style="padding: 4px 8px 0 8px">Date:</td>
                                        </tr>
                                        <tr>
                                            <td class="footer-contents-font-size fw-6" style="padding: 4px 8px 4px 8px">Signature:</td>
                                        </tr>
                                    </table>
                                </td>
                            @endif
                            @if ($invoiceSetting['delivery_challan_delivered_by'] ?? true)
                                <td class="border-right">
                                    <table>
                                        <tr>
                                            <td class="footer-headings-font-size fw-6" style="padding: 4px 8px 0 8px">{{ $changeLabel['delivery_challan_delivered_by_label'] ?? 'Delivered By' }}</td>
                                        </tr>
                                        <tr>
                                            <td class="footer-contents-font-size fw-6" style="padding: 4px 8px 0 8px">Name:</td>
                                        </tr>
                                        <tr>
                                            <td class="footer-contents-font-size fw-6" style="padding: 4px 8px 0 8px">Comment:</td>
                                        </tr>
                                        <tr>
                                            <td class="footer-contents-font-size fw-6" style="padding: 4px 8px 0 8px">Date:</td>
                                        </tr>
                                        <tr>
                                            <td class="footer-contents-font-size fw-6" style="padding: 4px 8px 4px 8px">Signature:</td>
                                        </tr>
                                    </table>
                                </td>
                            @endif
                            @if(($showPrintSettings['show_delivery_challan_authorized_signatory'] ?? true) || ($invoiceSetting['delivery_challan_signature'] ?? true))
                                <td class="vertical-bottom border-right" style="width: 165px; position: relative">
                                    <div style="padding:4px 8px; margin-left:auto;">
                                        <div class="text-end signature" style="max-width:140px; height:70px;"></div>
                                        <p class="mb-2 verical-bottom text-end fs-12 fw-5 footer-contents-font-size">
                                            Receiver Signatory
                                        </p>
                                    </div>
                                </td>
                                <td class="vertical-bottom" style="width: 166px; position: relative">
                                    <div style="padding:4px 8px; margin-left:auto;">
                                        @if ($showPrintSettings['show_delivery_challan_authorized_signatory'] ?? true)
                                            <p class="fs-12 fw-7 footer-contents-font-size" style="margin-top:5px;">
                                                For, {{ strtoupper($currentCompany->trade_name) }}
                                            </p>
                                        @endif
                                        <div class="text-end signature" style="max-width:140px; height:70px;">
                                            @if (($invoiceSetting['delivery_challan_signature'] ?? false) &&
                                                    $currentCompany->company_signature != asset('images/preview-img.png'))
                                                <img src="{{ $currentCompany->company_signature ?? null }}" alt="company-img"
                                                    style="max-width:100%; height:100%;  object-fit:contain; margin-left:auto;">
                                            @endif
                                        </div>
                                        @if ($showPrintSettings['show_delivery_challan_authorized_signatory'] ?? true)
                                            <p class="mb-2 verical-bottom text-end fs-12 fw-5 footer-contents-font-size">
                                                {{ $changeLabel['delivery_challan_authorized_signatory'] ?? 'Authorized Signatory' }}
                                            </p>
                                        @endif
                                    </div>
                                </td>
                            @endif
                        </tr>
                    </tbody>
                </table>
            @endif
    </div>
</body>
</html>
