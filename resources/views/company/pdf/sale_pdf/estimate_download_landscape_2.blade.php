<!DOCTYPE html>
<html>
@php
    $pdfSymbol = getPrintPdfCurrencySymbol($currentCompany->id);
    $dockereEnabled = dockerEnabled() && !isset($preview_enabled);
    $fontStyleName = $dockereEnabled ? 'Arial Unicode MS' : 'Arial-unicode-ms';
    $fontCssName = $dockereEnabled ? 'font-docker.css' : 'font.css';
@endphp
<head>
    <meta charset="UTF-8" />
    <title>{{ isset($fileName) ? $fileName : $transaction->document_number }}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/css/' . $fontCssName) }}">
    @include('company.pdf.sale_pdf.print_custom_font_css_variables')
    <style>
         * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "{{ $fontStyleName }}";
            font-size: 13px;
            font-weight: 400;
        }

        @page {
            margin: 20px !important;
        }

        @media print {
            body {
                -webkit-print-color-adjust: exact;
            }
        }

        .main {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        .fw-semibold {
            font-weight: 600;
        }

        .text-black {
            color: black;
        }

        .text-end {
            text-align: end;
        }

        .text-start {
            text-align: start;
        }

        .text-center {
            text-align: center;
        }

        .mb-2px {
            margin-bottom: 2px;
        }

        .w-50 {
            width: 50%;
        }

        .w-30 {
            width: 30%;
        }
        .w-20 {
            width: 20%;
        }
        .w-80 {
            width: 80%;
        }
        .w-75 {
            width: 75%;
        }

        .w-100 {
            width: 100%;
        }

        .h-100 {
            height: 100%;
        }

        .fs-14 {
            font-size: 14px;
        }

        .py-1 {
            padding-top: 4px;
            padding-bottom: 4px;
        }

        .fs-13 {
            font-size: 13px;
        }

        .fs-12 {
            font-size: 12px;
        }

        .d-flex {
            display: flex;
        }

        .align-items-center {
            align-items: center;
        }

        .logo-img {
            width: 80px;
            height: 80px;
            min-width: 80px;
            border-radius: 50%;
            min-height: 80px;
        }

        .logo-img img {
            /* border-radius: 50%; */
        }

        .object-fit-cover {
            object-fit: cover;
        }

        .lh-1-5 {
            line-height: 1.5;
        }

        .text-nowrap {
            white-space: nowrap;
        }

        .break-text {
            word-break: break-all;
        }

        .fs-10 {
            font-size: 10px;
        }

        .fs-11 {
            font-size: 11px;
        }

        .py-2 {
            padding-top: 8px;
            padding-bottom: 8px;
        }

        .text-decoration-none {
            text-decoration: none;
        }


        .w-25 {
            width: 25%;
        }

        .ps-3 {
            padding-left: 8px;
        }

        table {
            border-collapse: collapse;
        }

        .tax-name {
            border: 1px solid black;
        }

        .border-bottom {
            border-bottom: 1px solid black;
        }

        .table-intro td,
        .bill-table td,
        .sn-table td,
        .qr-table td {
            border-right: 1px solid black;
            border-bottom: 1px solid black;
            border-left: 1px solid black;
        }

        .sn-table th,
        .tax-table th {
            border-right: 1px solid black;
            border-bottom: 1px solid black;
        }

        .sn-table th:last-child {
            border-right: 1px solid dblack !important;
        }

        .sn-table th:first-child,
        .tax-table th:first-child {
            border-left: 1px solid black !important;
        }

        .qr-table th:last-child,
        .tax-table td:last-child {
            border-right: none !important;
        }

        .tax-table td:first-child {
            border-left: none !important;
        }

        .tax-table tbody td {
            border-bottom: none !important;
        }

        .tax-table tfoot td {
            border-top: 1px solid black;
        }

        .bank-table td {
            border: none !important;
        }

        .po-table td {
            border: none !important;
        }


        .gap-2 {
            gap: 8px;
        }


        address {
            font-style: normal;
        }

        .vertical-align-top {
            vertical-align: top;
        }

        .bg-primary {
            background-color: var(--color-theme);
        }

        .text-primary {
            color: var(--color-theme);
        }

        .text-white {
            color: white;
        }

        .px-2 {
            padding-left: 8px;
            padding-right: 8px;
        }

        .mb-1 {
            margin-bottom: 4px;
        }

        .qr-img {
            width: 75px;
            max-height: 75px;
            height: 75px;
            min-width: 75px;
        }

        .px-1 {
            padding-left: 4px;
            padding-right: 4px;
        }

        .gap-3 {
            gap: 16px;
        }

        .justify-content-between {
            justify-content: space-between;
        }

        .sign {
            background-color: #f6f6f6;
            height: 70px;
            width: 130px;
            justify-content: center;
            overflow: hidden !important;
        }

        .w-102px {
            min-width: 102px;
        }

        .ms-auto {
            margin-left: auto;
        }

        .fs-15 {
            font-size: 15px;
        }

        .mb-3 {
            margin-bottom: 16px;
        }

        .vertical-align-bottom {
            vertical-align: bottom;
        }

        .py-3 {
            padding-top: 16px;
            padding-bottom: 16px;
        }

        .pb-1 {
            padding-bottom: 4px;
        }

        .flex-grow-1 {
            flex-grow: 1;
        }

        .sn-table tbody tr:last-child td {
            height: 100%;
        }

        table {
            display: table;
            width: 100%;
            max-width: 100%;
        }

        tr {
            display: table-row;
        }

        th,
        td {
            display: table-cell;
        }

        .pb-2px {
            padding-bottom: 2px;
        }

        .w-22 {
            width: 22%;
        }

        .w-28 {
            width: 28%;
        }

        .ps-1 {
            padding-left: 4px;
        }

        .pe-3 {
            padding-right: 8px;
        }

        .px-12px {
            padding-left: 8px;
            padding-right: 8px;
        }

        .flex-wrap {
            flex-wrap: wrap
        }

        .row-gap-1 {
            row-gap: 2px;
        }

        .colum-gap-2 {
            column-gap: 10px;
        }
        .border-bottom-none tfoot tr td {
            border-bottom: none !important;
        }
        .fw-medium {
            font-weight: 500;
        }
        .w-20px {
            min-width: 20px;
        }
        .position-relative {
            position: relative !important;
        }
    </style>
    @include('company.pdf.sale_pdf.print_custom_font_css')
</head>

<body>
    <div class="main">
        {{-- Invoice Type Section Start --}}
        <div class="position-relative">
            @if (($invoiceSetting['show_slogan'] ?? false) && isset($invoiceSetting['slogan']))
            <h6 class="text-center text-black fw-medium company-address-font-size mb-2px">
                    {{ $invoiceSetting['slogan'] }}
            </h6>
            @endif
            <h6 class="text-black fs-13 text-end fw-medium mb-2px" style="letter-spacing:0.3px; {{ ($invoiceSetting['show_slogan'] ?? false) && isset($invoiceSetting['slogan']) ? 'position:absolute; right:0; top:0;' : '' }}">
                ({#INVOICE_TYPE#})
            </h6>
        </div>
        <div class="tax-name">
            <p class="py-1 text-center text-black fw-semibold fs-15">
                {{ $transaction->transactionTitle->name }}
            </p>
        </div>
        {{-- Invoice Type Section End --}}
        <table class="w-100 table-intro">
            <tbody>
                <tr>
                    <td class="{{ ($invoiceSetting['estimate_po_number'] ?? true) || ($invoiceSetting['estimate_broker_details'] ?? true) ? 'w-50' : 'w-75'}} ps-3 pe-3">
                        <div class="gap-2 py-2 d-flex align-items-center">
                            @if (isset($currentCompany->company_logo) && ($invoiceSetting['estimate_logo'] ?? true) && $currentCompany->company_logo != asset('images/preview-img.png'))
                                <div class="logo-img h-100 w-100">
                                    <img src="{{ $currentCompany->company_logo ?? '' }}"
                                        alt="logo" class="h-100 w-100 " style="object-fit: contain" />
                                </div>
                            @endif
                            <div>
                                <h2 class="company-name-font-size company-font-customization fw-semibold">{{ strtoupper($currentCompany->trade_name) }}</h2>
                                @if(($invoiceSetting['prop_details'] ?? true) && isset($customProp))
                                    <p class="mt-1 fw-6 company-address-font-size">{{ $customProp->label_name.' : '.$customProp->label_value }}</p>
                                @endif
                                @if ($currentCompany->is_gst_applicable)
                                    <p class="text-black company-address-font-size fw-semibold lh-1-5">
                                        {{ $changeLabel['estimate_gstin'] ?? 'GSTIN' }}
                                        {{ '  ' . $currentCompany->companyTax->gstin ?? ($customerDetail->model->gstin ?? null) }}
                                    </p>
                                @endif
                                <P class="text-black company-address-font-size lh-1-5">
                                    {{ isset($companyBillingAddress->address_1) ? strtoupper($companyBillingAddress->address_1 .',') : null }}
                                    {{ isset($companyBillingAddress->address_2) ? strtoupper($companyBillingAddress->address_2 .',') : null }}
                                    {{ isset($companyBillingAddress->city_id) ?  strtoupper(getCityName($companyBillingAddress->city_id).',') : null }}
                                    {{ isset($companyBillingAddress->state_id) ? strtoupper(getStateName($companyBillingAddress->state_id).',') : null }}
                                    {{ isset($companyBillingAddress->country_id) ? strtoupper(getCountryName($companyBillingAddress->country_id)) : null }}
                                    {{ isset($companyBillingAddress->pin_code) ? ' - '.$companyBillingAddress->pin_code : null }}
                                </P>
                                <div class="flex-wrap row-gap-1 d-flex align-items-center colum-gap-2">
                                    @if ($invoiceSetting['estimate_mobile_number'] ?? true)
                                        <p class="text-black company-address-font-size lh-1-5 d-flex">{{ $changeLabel['estimate_tel'] ?? 'Phone No' }}:
                                            <a class="text-black d-flex company-address-font-size text-decoration-none break-text">
                                                {{  (isset($invoiceSetting['region_code']) ? '+' . $invoiceSetting['region_code'] . ' ' : '') .
                                                    (isset($invoiceSetting['alternate_phone']) ? $invoiceSetting['alternate_phone'] : '+' . $currentCompany->user->region_code . ' ' . $currentCompany->user->phone) .
                                                    (isset($invoiceSetting['region_code_2']) && isset($invoiceSetting['alternate_phone_2']) ? ', +' . $invoiceSetting['region_code_2'] . ' ' : '') .
                                                    (isset($invoiceSetting['alternate_phone_2']) ? (isset($invoiceSetting['region_code_2']) ? '' : ', ') . $invoiceSetting['alternate_phone_2'] : '')
                                                }}
                                            </a>
                                        </p>
                                    @endif
                                    @if ($invoiceSetting['estimate_email'] ?? true)
                                        <a class="text-black company-address-font-size lh-1-5 text-decoration-none d-flex break-text">
                                            {{ $invoiceSetting['alternate_email'] ?? ($currentCompany->user->email ?? null) }}
                                        </a>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </td>
                    @if (($invoiceSetting['estimate_po_number'] ?? true) || ($invoiceSetting['estimate_broker_details'] ?? true))
                        <td class="w-25 vertical-align-top">
                            <div class="py-2">
                                <table class="po-table">
                                    <tbody>
                                        @if ($invoiceSetting['estimate_po_number'] ?? true)
                                            <tr>
                                                <td class="text-black header-contents-font-size ps-3 lh-1-5 pe-3 w-50">
                                                    {{ $changeLabel['estimate_po_number_label'] ?? 'PO No.' }}
                                                </td>
                                                <td class="text-black header-contents-font-size pe-3 lh-1-5 d-flex align-items-center text-nowrap w-50">
                                                    : {{ $transaction->po_no }}
                                                </td>
                                            </tr>
                                            @endif
                                            @if($invoiceSetting['show_estimate_po_date'] ?? true)
                                            <tr>
                                                <td class="text-black header-contents-font-size ps-3 lh-1-5 pe-3 w-50">
                                                    {{ $changeLabel['estimate_po_date'] ?? 'PO Date' }}
                                                </td>
                                                <td class="text-black header-contents-font-size pe-3 lh-1-5 d-flex align-items-center text-nowrap w-50">
                                                    : {{ isset($transaction->po_date) ? \Carbon\Carbon::parse($transaction->po_date)->format('d-m-Y') : null }}
                                                </td>
                                            </tr>
                                        @endif
                                        <tr>
                                            <td class="text-black header-contents-font-size ps-3 lh-1-5 pe-3 w-50">
                                                {{ $changeLabel['estimate_valid_for'] ?? 'Valid For' }}
                                            </td>
                                            <td class="text-black header-contents-font-size pe-3 lh-1-5 d-flex align-items-center">
                                                : {{ $validFor }} {{ !empty($transaction->valid_till_date) ? ' (' . Carbon\Carbon::parse($transaction->valid_till_date)->format('d-m-Y') . ')' : '' }}
                                            </td>
                                        </tr>
                                        @if ($invoiceSetting['estimate_broker_details'] ?? true)
                                            <tr>
                                                <td class="text-black header-contents-font-size ps-3 lh-1-5 pe-3 w-50">
                                                    Broker
                                                </td>
                                                <td class="text-black header-contents-font-size pe-3 lh-1-5 d-flex align-items-center">
                                                    : {{ $transaction->brokerDetails->broker_name ?? '' }}
                                                </td>
                                            </tr>
                                            @if ($isCompanyGstApplicable)
                                            <tr>
                                                <td class="text-black header-contents-font-size ps-3 lh-1-5 pe-3 w-50">
                                                    GSTIN
                                                </td>
                                                <td class="text-black header-contents-font-size pe-3 lh-1-5 d-flex align-items-center text-nowrap w-50">
                                                    : {{ $transaction->brokerDetails->gstin ?? '' }}
                                                </td>
                                            </tr>
                                            @endif
                                        @endif
                                    </tbody>
                                </table>
                            </div>
                        </td>
                    @endif
                    <td class="w-25 vertical-align-top">
                        <div class="py-2">
                            <table class="po-table">
                                <tbody>
                                    <tr>
                                        <td class="text-black header-contents-font-size ps-3 lh-1-5 pe-3 w-50">
                                            {{ $changeLabel['estimate_invoice_number'] ?? 'Estimate No' }}:
                                        </td>
                                        <td class="text-black header-contents-font-size pe-3 lh-1-5">
                                            : {{ $transaction->document_number }}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="text-black header-contents-font-size ps-3 lh-1-5 pe-3 w-50">
                                            {{ $changeLabel['estimate_invoice_date'] ?? 'Estimate Date' }}:
                                        </td>
                                        <td class="text-black header-contents-font-size pe-3 lh-1-5 text-nowrap">
                                            : {{ Carbon\Carbon::parse($transaction->document_date)->format('d-m-Y') }}
                                        </td>
                                    </tr>
                                    @if($invoiceSetting['show_estimate_due_date'] ?? true)
                                        <tr>
                                            <td class="text-black header-contents-font-size ps-3 lh-1-5 pe-3 w-50">
                                                {{ $changeLabel['estimate_due_date'] ?? 'Due Date' }}
                                            </td>
                                            <td class="text-black header-contents-font-size pe-3 lh-1-5 text-nowrap">
                                                : {{ $dueDate }}
                                            </td>
                                        </tr>
                                    @endif
                                    <tr>
                                        <td class="text-black header-contents-font-size ps-3 lh-1-5 pe-3 w-50">
                                            Place of supply
                                        </td>
                                        <td class="text-black header-contents-font-size pe-3 lh-1-5">
                                            : {{ !isset($shippingAddress->state_id)
                                                ? (isset($ledgerShippingAddress->state_id)
                                                ? strtoupper(getStateName($ledgerShippingAddress->state_id))
                                                : '')
                                                : strtoupper(getStateName($shippingAddress->state_id)) }}
                                        </td>
                                    </tr>
                                    @if($invoiceSetting['show_estimate_credit_period'] ?? true)
                                        <tr>

                                            <td class="text-black header-contents-font-size ps-3 lh-1-5 pe-3 w-50">
                                                {{ $changeLabel['estimate_credit_period'] ?? 'Credit Period' }}
                                            </td>

                                            <td class="text-black header-contents-font-size pe-3 lh-1-5">
                                                : {{ $creditPeriod }}
                                            </td>

                                        </tr>
                                    @endif
                                </tbody>
                            </table>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
        <table class="w-100 bill-table">
            <tbody>
                <tr>
                    {{-- start billing deatils --}}
                    <td class="{{ ($invoiceSetting['estimate_ship_to_details'] ?? true) ? 'w-25' : 'w-50' }} vertical-align-top">
                        <div class="py-2">
                            <p class="text-black header-labels-font-size lh-1-5 px-12px fw-semibold">
                                {{ $changeLabel['estimate_bill_to'] ?? 'Estimate To' }}:
                            </p>
                            <p class="text-black header-contents-font-size lh-1-5 px-12px">
                                {{ strtoupper($customerDetail->name) }}
                            </p>
                            @if ($showGst)
                            <p class="text-black header-contents-font-size lh-1-5 px-12px text-nowrap">
                                GSTIN : {{ $transaction->gstin ?? $customerDetail->model->gstin ?? null }}
                            </p>
                            @endif
                            @if (isset($billingAddress))
                                <address class="text-black header-contents-font-size lh-1-5 px-12px">
                                    @if ($billingAddress->address_1 != null)
                                        {{ strtoupper($billingAddress->address_1) }}
                                    @endif
                                    @if ($billingAddress->address_2 != null)
                                        {{ strtoupper($billingAddress->address_2) }},
                                    @endif
                                    {{ $billingAddress->city_id != null ? strtoupper(getCityName($billingAddress->city_id)) . ',' : null }}
                                    {{ $billingAddress->state_id != null ? strtoupper(getStateName($billingAddress->state_id)) . ',' : null }}
                                    {{ strtoupper(getCountryName($billingAddress->country_id ?? null)) }}
                                    {{ $billingAddress->pin_code != null ? ' - ' . $billingAddress->pin_code : null }}
                                </address>
                            @endif
                            <p class="text-black header-contents-font-size lh-1-5 px-12px">
                                @if (!empty($transaction->party_phone_number))
                                    Contact No:
                                        +{{ $transaction->region_code ?? '' }} {{ $transaction->party_phone_number ?? null }}
                                @elseif (!empty($customerDetail->model->phone_1))
                                    Contact No:
                                        +{{ $customerDetail->model->region_code_1 ?? '' }} {{ $customerDetail->model->phone_1 ?? null }}
                                @endif
                                @if (!empty($customerDetail->model->phone_2) && ($customerDetail->model->phone_1 !== $customerDetail->model->phone_2))
                                    {{ empty($customerDetail->model->phone_1) && empty($transaction->party_phone_number) ? 'Contact No:' : "|" }}
                                    <span class="text-nowrap header-contents-font-size">
                                        +{{ $customerDetail->model->region_code_2 ?? '' }} {{ $customerDetail->model->phone_2 ?? null }}
                                    </span>
                                @endif
                            </p>
                            @if (!empty($customerDetail->model->person_email))
                                <p class="text-black header-contents-font-size lh-1-5 px-12px">
                                    Email: {{ $customerDetail->model->person_email ?? null }}
                                </p>
                            @endif
                            @if (!empty($panNumber) && $showPanNumber)
                                <p class="text-black header-contents-font-size lh-1-5 px-12px">
                                    PAN: {{ $panNumber ?? null }}
                                </p>
                            @endif
                        </div>
                    </td>
                    {{-- end billing deatils --}}
                    {{-- start shipping Details --}}
                    @if ($invoiceSetting['estimate_ship_to_details'] ?? true)
                        <td class="w-25 vertical-align-top">
                            <div class="py-2">
                                <p class="text-black header-labels-font-size lh-1-5 px-12px fw-semibold">
                                    {{ $changeLabel['estimate_ship_to'] ?? 'Ship to' }}:
                                </p>
                                <p class="text-black header-contents-font-size lh-1-5 px-12px">
                                    {{ strtoupper($transaction->shipping_name ?? $customerDetail->name) }}
                                </p>
                                @if ($isCompanyGstApplicable && !empty($transaction->shipping_gstin))
                                <p class="text-black header-contents-font-size lh-1-5 px-12px text-nowrap">
                                    GSTIN : {{ $transaction->shipping_gstin ?? null }}
                                </p>
                                @endif
                                <address class="text-black header-contents-font-size lh-1-5 px-12px">
                                    @if (isset($shippingAddress->address_1))
                                        {{ strtoupper($shippingAddress->address_1) }},
                                    @endif
                                    @if (isset($shippingAddress->address_2))
                                        {{ strtoupper($shippingAddress->address_2) }},
                                    @endif
                                    {{ isset($shippingAddress->city_id) ?  strtoupper(getCityName($shippingAddress->city_id)) . ',' : '' }}
                                    {{ isset($shippingAddress->state_id) ?  strtoupper(getStateName($shippingAddress->state_id)) . ',' : '' }}
                                    {{ isset($shippingAddress->country_id) ? strtoupper(getCountryName($shippingAddress->country_id)) : '' }}
                                    {{ $shippingAddress->pin_code ?? null }}
                                </address>
                                <p class="text-black header-contents-font-size lh-1-5 px-12px">
                                    @if (!empty($transaction->party_phone_number))
                                        Contact No:
                                            +{{ $transaction->region_code ?? '' }} {{ $transaction->party_phone_number ?? null }}
                                    @endif
                                </p>
                                @if (!empty($panNumber) && $showPanNumber)
                                <p class="header-contents-font-size" style="padding: 0px 0px 2px 8px; " >
                                    PAN: {{ $panNumber ?? null }}
                                </p>
                            @endif
                            </div>
                        </td>
                    @endif
                    {{-- end shipping Details --}}
                    {{-- start Transport Details --}}
                    @if (($invoiceSetting['estimate_transport_details'] ?? true) || !empty($transaction->transporter_vehicle_number))
                    <td class="vertical-align-top w-25">
                        <div class="py-2">
                            <table class="po-table">
                                <tbody>
                                    <tr>
                                        <td class="text-black header-contents-font-size ps-3 lh-1-5 pe-3 w-50">
                                            {{ $changeLabel['transport_name'] ?? 'Transport Name' }}
                                        </td>
                                        <td class="text-black header-contents-font-size pe-3 lh-1-5 d-flex align-items-center text-nowrap w-50">
                                            : {{ $transaction->transportDetails->transporter_name ?? '' }}
                                        </td>
                                    </tr>
                                    @if ($isCompanyGstApplicable)
                                    <tr>
                                        <td class="text-black header-contents-font-size ps-3 lh-1-5 pe-3 w-50">
                                            GSTIN
                                        </td>
                                        <td class="text-black header-contents-font-size pe-3 lh-1-5 d-flex align-items-center text-nowrap w-50">
                                            : {{ $transaction->transportDetails->gstin ?? '' }}
                                        </td>
                                    </tr>
                                    @endif
                                    @if (!empty($transaction->transporter_vehicle_number))
                                    <tr>
                                        <td class="text-black header-contents-font-size ps-3 lh-1-5 pe-3 w-50">
                                            {{ $changeLabel['transport_vehicle_number'] ?? 'Vehicle No' }}
                                        </td>
                                        <td class="text-black header-contents-font-size pe-3 lh-1-5 d-flex align-items-center text-nowrap w-50">
                                            : {{ $transaction->transporter_vehicle_number ?? '' }}
                                        </td>
                                    </tr>
                                    @endif
                                    <tr>
                                        <td class="text-black header-contents-font-size ps-3 lh-1-5 pe-3 w-50">
                                            {{ $changeLabel['document_no'] ?? 'Document No' }}
                                        </td>
                                        <td class="text-black header-contents-font-size pe-3 lh-1-5 d-flex align-items-center text-nowrap w-50">
                                            : {{ $transaction->transporter_document_number ?? '' }}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="text-black header-contents-font-size ps-3 lh-1-5 pe-3 w-50">
                                            {{ $changeLabel['document_date'] ?? 'Document Date' }}
                                        </td>
                                        <td class="text-black header-contents-font-size pe-3 lh-1-5 d-flex align-items-center text-nowrap w-50">
                                            : {{ !empty($transaction->transporter_document_date) ?
                                            \Carbon\Carbon::parse($transaction->transporter_document_date)->format('d-m-Y') : '' }}
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </td>
                    @endif
                    {{-- end Transport Details --}}
                    {{-- start E-way Bill / E Invoice / Custome fields Details --}}
                    @if (isset($eWayBill) || (isset($eInvoice) && !empty($eInvoice)) || !empty(printCustomPDFLabelsForEstimate()))
                        <td class="vertical-align-top w-25">
                            <div class="py-2">
                                <table class="po-table">
                                    <tbody>
                                        @if (isset($eWayBill))
                                            <tr>
                                                <td class="text-black header-contents-font-size ps-3 lh-1-5 pe-3 w-50 text-nowrap">
                                                    {{ $changeLabel['e_way_bill_no'] ?? 'E-way Bill No' }}
                                                </td>
                                                <td class="text-black header-contents-font-size pe-3 lh-1-5 d-flex align-items-center text-nowrap w-50">
                                                    : {{ $eWayBill?->eway_bill_no ?? $eWayBill->eway_bill_number ?? null }}
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="text-black header-contents-font-size ps-3 lh-1-5 pe-3 w-50 text-nowrap">
                                                    {{ $changeLabel['e_way_bill_date'] ?? 'E-way Bill Date' }}
                                                </td>
                                                <td class="text-black header-contents-font-size pe-3 lh-1-5 d-flex align-items-center text-nowrap w-50">
                                                    : {{ $eWayBill?->eway_bill_date ?? $eWayBill->eway_bill_date ?? null }}
                                                </td>
                                            </tr>
                                        @endif
                                        @if (isset($eInvoice) && !empty($eInvoice))
                                            <tr>
                                                <td class="text-black header-contents-font-size ps-3 lh-1-5 pe-3 w-50 text-nowrap">
                                                    {{ $changeLabel['ack_no'] ?? 'Ack No' }}
                                                </td>
                                                <td class="text-black header-contents-font-size pe-3 lh-1-5 d-flex align-items-center text-nowrap w-50">
                                                    : {{ !empty($eInvoice) ? $eInvoice->ack_no : '' }}
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="text-black header-contents-font-size ps-3 lh-1-5 pe-3 w-50 text-nowrap">
                                                    {{ $changeLabel['ack_date'] ?? 'Ack Date' }}
                                                </td>
                                                <td class="text-black header-contents-font-size pe-3 lh-1-5 d-flex align-items-center text-nowrap w-50">
                                                    : {{ !empty($eInvoice) ? \Carbon\Carbon::parse($eInvoice->ack_date)->format('d-m-Y') : '' }}
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="text-black header-contents-font-size ps-3 lh-1-5 pe-3 w-50 text-nowrap">
                                                    {{ $changeLabel['irn'] ?? 'IRN' }}
                                                </td>
                                                <td class="text-black header-contents-font-size pe-3 lh-1-5 d-flex align-items-center text-nowrap w-50">
                                                    : {{ !empty($eInvoice) ? $eInvoice->irn : '' }}
                                                </td>
                                            </tr>
                                        @endif
                                        @foreach (printCustomPDFLabelsForEstimate() as $key => $customLabel)
                                            <tr>
                                                <td class="text-black header-contents-font-size ps-3 lh-1-5 pe-3 w-50">
                                                    {{ $key ?? null }}
                                                </td>
                                                <td class="text-black header-contents-font-size pe-3 lh-1-5 d-flex align-items-center text-nowrap w-50">
                                                    : {{ $customLabel ?? null }}
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </td>
                    @endif
                    {{-- start E-way Bill / E Invoice / Custome fields Details --}}
                </tr>
            </tbody>
        </table>

        {{-- Custom Fields Section Start --}}
        @if (count($customFieldValues) > 0)
            @php
                $customFields = collect($customFieldValues)->where('is_show_in_print', true)->values();
            @endphp
            <table cellpadding="0" class="w-100 sn-table">
                @foreach ($customFields->chunk(3) as $chunk)
                    <tr class="border-bottom" style="border-right: 1px solid black;">
                        @foreach ($chunk as $customField)
                            <td class="fs-13" style="padding: 6px 8px; width:150px; {{ !$loop->last ? '' : 'border-right: none;' }}">
                                <span style="font-weight: bold;">{{ $customField['label_name'] ?? '' }}</span> : {{ $customField['value'] ?? '' }}
                            </td>
                        @endforeach
                    </tr>
                @endforeach
            </table>
        @endif
        {{-- Custom Fields Section End --}}

        <div class="d-flex flex-grow-1">
            <table class="w-100 sn-table">
                <thead>
                    <tr>
                        @foreach ($rearrangeItems['headings'] as $headings)
                            @if($headings['is_show_in_print'])
                                <th class="py-2 text-white bg-primary table-headings-font-size px-12px lh-1-5 text-nowrap {{ ($loop->index === $loop->count - 2) ? '' : 'border-right' }} {{ $headings['class'] }}"
                                    style="padding: 6px 8px; font-weight: bold;">
                                    {{ $headings['name'] }}
                                </th>
                            @endif
                        @endforeach
                    </tr>
                </thead>
                <tbody>
                    @foreach ($rearrangeItems['detail'] as $key => $items)
                        <tr>
                            @foreach ($items as  $key => $item)
                                @if($item['is_show_in_print'])
                                    <td class="py-1 text-black table-contents-font-size px-12px vertical-align-top text-nowrap  {{ ($loop->index === $loop->count - 2) ? '' : 'border-right' }} {{ ($key == 'item_name') ? 'min-width-150' : 'text-center'}}"
                                        style="padding: 4px 8px 0 8px;{{ ($key == 'primary_quantity' || $key == 'secondary_quantity') ? 'white-space: nowrap;' : '' }}">
                                        {{ $item['value'] }}
                                        @if($key == 'item_name')
                                            @if($item['show_sku'])
                                                <p class="description-font-size">Item Code:
                                                    {{ $item['sku'] ?? null }}</p>
                                            @endif
                                            @if($item['show_consolidating_items'])
                                                <p style="word-break: break-word; " class="description-font-size">
                                                    {!! $item['consolidating_items'] !!}
                                                </p>
                                            @endif
                                            @if($item['show_additional_description'])
                                                <p style="word-break: break-word;" class="description-font-size">
                                                     {!! $item['additional_description'] !!}
                                                </p>
                                            @endif
                                            @if (count($item['customItemsInventoryValues']) > 0)
                                                @foreach ($item['customItemsInventoryValues'] as $customItemsInventoryValue)
                                                    <p style="word-break: break-word; font-style: italic; color: #888888;" class="description-font-size">{{ $customItemsInventoryValue }}</p>
                                                @endforeach
                                            @endif
                                            @if($item['show_item_image'])
                                                <div><img src="{{ $item['item_image'] }}" width="60"  height="60" style="margin-top: 4px"></div>
                                            @endif
                                        @endif
                                    </td>
                                @endif
                            @endforeach
                        </tr>
                    @endforeach
                </tbody>
                <tfoot>
                    <tr>
                        @foreach ($rearrangeItems['footer'] as $key => $footer)
                            @if($footer['is_show_in_print'])
                                <td class="py-1 text-black table-headings-font-size px-12px lh-1-5 {{ ($loop->index === $loop->count - 2) ? '' : 'border-right' }}  fw-6 {{ ($key == 'item_name') ? 'min-width-150 ps-2' : 'text-center'}}"
                                style="">{{ $footer['value'] }}</td>
                            @endif
                        @endforeach
                    </tr>
                </tfoot>
            </table>
    </div>
        <table class="qr-table w-100">
            <tbody>
                <tr>
                    <td class="w-75 vertical-align-top">
                        <div class="d-flex">
                            @if ($isCompanyGstApplicable && !empty($checkHsnCodeExist) && ($invoiceSetting['estimate_hsn_summary'] ?? true))
                                <div style="{{ ($showPrintSettings['show_estimate_narration'] ?? true) && $transaction->narration ? 'border-right: 1px solid black;' : '' }}">
                                    <table class="tax-table w-100 {{ count(array_keys($checkHsnCodeExist)) < 2 || !$transaction->narration ? '' : 'border-bottom-none' }}"
                                        style="{{ !(($showPrintSettings['show_estimate_narration'] ?? true) && $transaction->narration) ? 'border-right: 1px solid black' : '' }}">
                                        <thead>
                                            <tr>
                                                <th
                                                    class="py-1 text-center text-white bg-primary footer-headings-font-size fw-semibold lh-1-5 px-12px">
                                                    SN
                                                </th>
                                                <th
                                                    class="py-1 text-white bg-primary footer-headings-font-size fw-semibold lh-1-5 px-12px text-start">
                                                    HSN/SAC
                                                </th>
                                                <th
                                                    class="py-1 text-white bg-primary footer-headings-font-size fw-semibold lh-1-5 px-12px text-end text-nowrap">
                                                    Taxable Amount
                                                </th>
                                                <th
                                                    class="px-2 py-1 text-white bg-primary footer-headings-font-size fw-semibold lh-1-5 text-end">
                                                    GST(%)
                                                </th>
                                                @if ($cgst != 0.0)
                                                    <th
                                                        class="px-2 py-1 text-white bg-primary footer-headings-font-size fw-semibold lh-1-5 text-end">
                                                        CGST
                                                    </th>
                                                @endif
                                                @if ($sgst != 0.0)
                                                    <th
                                                        class="px-2 py-1 text-white bg-primary footer-headings-font-size fw-semibold lh-1-5 text-end">
                                                        SGST
                                                    </th>
                                                @endif
                                                @if ($igst != 0.0)
                                                    <th
                                                        class="px-2 py-1 text-white bg-primary footer-headings-font-size fw-semibold lh-1-5 text-end">
                                                        IGST
                                                    </th>
                                                @endif
                                                <th
                                                    class="py-1 text-white bg-primary footer-headings-font-size fw-semibold lh-1-5 px-12px text-end text-nowrap">
                                                    Total Tax
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @php
                                                $uniquekey = 1;
                                            @endphp
                                            @foreach ($checkHsnCodeExist as $key => $item)
                                                @foreach ($item as $hsnCode => $data)
                                                    <tr>
                                                        <td class="text-center text-black footer-contents-font-size lh-1-5 px-12px">
                                                            {{ $uniquekey++ }}
                                                        </td>
                                                        <td class="text-black footer-contents-font-size lh-1-5 text-start px-12px">
                                                            {{ !empty($hsnCode) ? $hsnCode : '-' }}
                                                        </td>
                                                        <td class="text-black footer-contents-font-size lh-1-5 text-end px-12px">
                                                            {{ $pdfSymbol . getCurrencyFormat(round($checkTAXtExist[$key]['taxableValue'][$hsnCode], getCompanyFixedDigitNumber()) ?? 0) }}
                                                        </td>
                                                        <td class="px-2 text-black footer-contents-font-size lh-1-5 text-end">
                                                            {{ !empty($key) ? $key : '-' }}
                                                        </td>
                                                        @if ($cgst != 0.0)
                                                            <td class="px-2 text-black footer-contents-font-size lh-1-5 text-end">
                                                                {{ $pdfSymbol . getCurrencyFormat(round($checkTAXtExist[$key]['cgst'][$hsnCode] ?? 0, getCompanyFixedDigitNumber())) }}
                                                            </td>
                                                        @endif
                                                        @if ($sgst != 0.0)
                                                            <td class="px-2 text-black footer-contents-font-size lh-1-5 text-end">
                                                                {{ $pdfSymbol . getCurrencyFormat(round($checkTAXtExist[$key]['sgst'][$hsnCode] ?? 0, getCompanyFixedDigitNumber())) }}
                                                            </td>
                                                        @endif
                                                        @if ($igst != 0.0)
                                                            <td class="px-2 text-black footer-contents-font-size lh-1-5 text-end">
                                                                {{ $pdfSymbol . getCurrencyFormat(round($checkTAXtExist[$key]['igst'][$hsnCode] ?? 0, getCompanyFixedDigitNumber())) }}
                                                            </td>
                                                        @endif
                                                        @php
                                                            $totalTax =
                                                                round(
                                                                    $checkTAXtExist[$key]['cgst'][$hsnCode],
                                                                    getCompanyFixedDigitNumber(),
                                                                ) +
                                                                round(
                                                                    $checkTAXtExist[$key]['sgst'][$hsnCode],
                                                                    getCompanyFixedDigitNumber(),
                                                                ) +
                                                                round(
                                                                    $checkTAXtExist[$key]['igst'][$hsnCode],
                                                                    getCompanyFixedDigitNumber(),
                                                                );
                                                        @endphp
                                                        <td class="text-black footer-contents-font-size lh-1-5 text-end px-12px">
                                                            {{ $pdfSymbol . getCurrencyFormat(round($totalTax ?? 0, getCompanyFixedDigitNumber())) }}
                                                        </td>
                                                    </tr>
                                                @endforeach
                                            @endforeach
                                        </tbody>
                                        <tfoot>
                                            <tr>
                                                <td class="text-center text-black footer-headings-font-size lh-1-5 px-12px"></td>
                                                <td class="text-black footer-headings-font-size lh-1-5 text-start px-12px fw-semibold">
                                                    Total
                                                </td>
                                                <td class="text-black footer-headings-font-size lh-1-5 text-end px-12px fw-semibold">
                                                    {{ $pdfSymbol . getCurrencyFormat(round($transaction->taxable_value, getCompanyFixedDigitNumber())) }}
                                                </td>
                                                <td class="px-2 text-black footer-headings-font-size lh-1-5 text-end"></td>
                                                @if ($cgst != 0.0)
                                                    <td class="px-2 text-black footer-headings-font-size lh-1-5 text-end fw-semibold">
                                                        {{ $pdfSymbol . getCurrencyFormat(round($cgst, getCompanyFixedDigitNumber())) }}
                                                    </td>
                                                @endif
                                                @if ($sgst != 0.0)
                                                    <td class="px-2 text-black footer-headings-font-size lh-1-5 text-end fw-semibold">
                                                        {{ $pdfSymbol . getCurrencyFormat(round($sgst, getCompanyFixedDigitNumber())) }}
                                                    </td>
                                                @endif
                                                @if ($igst != 0.0)
                                                    <td class="px-2 text-black footer-headings-font-size lh-1-5 text-end fw-semibold">
                                                        {{ $pdfSymbol . getCurrencyFormat(round($igst, getCompanyFixedDigitNumber())) }}
                                                    </td>
                                                @endif
                                                <td class="text-black footer-headings-font-size lh-1-5 text-end px-12px fw-semibold">
                                                    @php
                                                        $grandTotalTax = $cgst + $sgst + $igst;
                                                    @endphp
                                                    {{ $pdfSymbol . getCurrencyFormat(round($grandTotalTax, getCompanyFixedDigitNumber())) }}
                                                </td>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            @endif
                            @if ((($invoiceSetting['estimate_qr_code'] ?? true) && isset($bankDetail->upi_id)) || (($invoiceSetting['estimate_bank_details'] ?? true) && !empty($bankDetail)))
                                <div class="gap-3 py-1 d-flex px-12px">
                                    @if (($invoiceSetting['estimate_qr_code'] ?? true) && isset($bankDetail->upi_id))
                                        <div>
                                            <div class="qr-img h-100 w-100">
                                                <img src="data:image/svg+xml;base64,{{ base64_encode(generateUPIQRCode($bankDetail->upi_id, $transaction->due_amount)) }}"
                                                    width="75" height="75" />
                                            </div>
                                            <p class="text-center text-black footer-contents-font-size lh-1-5" style="margin-top: 8px">
                                                {{ $bankDetail->upi_id }}
                                            </p>
                                        </div>
                                    @endif
                                    <div>
                                        @if (($invoiceSetting['estimate_bank_details'] ?? true) && !empty($bankDetail))
                                            <table class="bank-table">
                                                <tbody>
                                                    <tr>
                                                        <td class="px-1 text-black footer-headings-font-size lh-1-5 fw-semibold text-nowrap">Bank:
                                                        </td>
                                                        <td class="text-black footer-contents-font-size lh-1-5">
                                                            {{ !empty($bankDetail) ? $bankDetail->bank_name : null }}
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td class="px-1 text-black footer-headings-font-size lh-1-5 fw-semibold text-nowrap">IFSC
                                                            Code:
                                                        </td>
                                                        <td class="text-black footer-contents-font-size lh-1-5">
                                                            {{ !empty($bankDetail) ? $bankDetail->ifsc_code : null }}
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td class="px-1 text-black footer-headings-font-size lh-1-5 fw-semibold text-nowrap">A/C
                                                            Number:</td>
                                                        <td class="text-black footer-contents-font-size lh-1-5">
                                                            {{ $accountNumber }}
                                                        </td>
                                                    </tr>
                                                    @if (!empty($bankDetail) && $bankDetail->swift_code != null)
                                                        <tr>
                                                            <td class="px-1 text-black footer-headings-font-size lh-1-5 fw-semibold text-nowrap">
                                                                Swift Code:
                                                            </td>
                                                            <td class="text-black footer-contents-font-size lh-1-5">
                                                                {{ $bankDetail->swift_code }}
                                                            </td>
                                                        </tr>
                                                    @endif
                                                    @if (isset($bankDetail->account_holder_name))
                                                        <tr>
                                                            <td class="px-1 text-black footer-headings-font-size lh-1-5 fw-semibold text-nowrap">
                                                                A/C Name:
                                                            </td>
                                                            <td class="text-black footer-contents-font-size lh-1-5">
                                                                {{ $bankDetail->account_holder_name }}
                                                            </td>
                                                        </tr>
                                                    @endif
                                                    <tr>
                                                        <td class="px-1 text-black footer-headings-font-size lh-1-5 fw-semibold text-nowrap">Bank
                                                            Branch:</td>
                                                        <td class="text-black footer-contents-font-size lh-1-5">
                                                            {{ $branchName }}
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        @endif
                                    </div>
                                </div>
                            @endif
                        </div>
                        @if ($showPrintSettings['show_estimate_narration'] ?? true)
                            @if ($transaction->narration)
                                <div style="{{ $isCompanyGstApplicable && !empty($checkHsnCodeExist) && ($invoiceSetting['estimate_hsn_summary'] ?? true) ? 'border-top: 1px solid black;' : ''}}">
                                    <div class="py-1">
                                        <p class="mb-1 text-black note-font-size lh-1-5 px-12px fw-semibold">
                                            {{ $changeLabel['estimate_narration'] ?? 'Note' }}:
                                        </p>
                                        <p class="pb-1 text-black note-font-size lh-1-5 px-12px">
                                            {!! nl2br($transaction->narration) !!}
                                        </p>
                                    </div>
                                </div>
                            @endif
                        @endif
                    </td>
                    <td class="w-25 vertical-align-top ms-auto">
                        <div class="ms-auto">
                            <table class="w-100 bank-table">
                                <tbody>
                                    @foreach ($additionalCharges as $additionalCharge)
                                        <tr>
                                            <td class="px-1 text-black table-headings-font-size lh-1-5 fw-semibold pb-2px">
                                                {{ $additionalCharge['ledger_name'] }}
                                            </td>
                                            <td class="text-black w-102px pe-3 table-headings-font-size lh-1-5 fw-semibold text-end pb-2px">
                                                {{ $pdfSymbol . getCurrencyFormat($additionalCharge['amount'] ?? '0.0') }}
                                            </td>
                                        </tr>
                                    @endforeach
                                    <tr>
                                        <td class="px-1 text-black table-headings-font-size lh-1-5 fw-semibold pb-2px">
                                            {{ $changeLabel['estimate_sub_total'] ?? 'Taxable Amount' }}
                                        </td>
                                        <td class="text-black w-102px pe-3 table-headings-font-size lh-1-5 fw-semibold text-end pb-2px">
                                            {{ $pdfSymbol . getCurrencyFormat(round($transaction->taxable_value, getCompanyFixedDigitNumber())) }}
                                        </td>
                                    </tr>
                                    @if ($isCompanyGstApplicable)
                                        @if ($transaction->cgst != 0)
                                            <tr>
                                                <td class="px-1 text-black table-headings-font-size lh-1-5 fw-semibold">
                                                    {{ $changeLabel['estimate_cgst'] ?? 'CGST' }}
                                                </td>
                                                <td class="text-black w-102px pe-3 table-headings-font-size lh-1-5 fw-semibold text-end">
                                                    {{ $pdfSymbol . getCurrencyFormat(round($transaction->cgst, getCompanyFixedDigitNumber()) ?? '0.0') }}
                                                </td>
                                            </tr>
                                        @endif
                                        @if ($transaction->sgst != 0)
                                            <tr>
                                                <td class="px-1 text-black table-headings-font-size lh-1-5 fw-semibold">
                                                    {{ $changeLabel['estimate_sgst'] ?? 'SGST' }}
                                                </td>
                                                <td class="text-black w-102px pe-3 table-headings-font-size lh-1-5 fw-semibold text-end">
                                                    {{ $pdfSymbol . getCurrencyFormat(round($transaction->sgst, getCompanyFixedDigitNumber()) ?? '0.0') }}
                                                </td>
                                            </tr>
                                        @endif
                                        @if ($transaction->igst != 0)
                                            <tr>
                                                <td class="px-1 text-black table-headings-font-size lh-1-5 fw-semibold">
                                                    {{ $changeLabel['estimate_igst'] ?? 'IGST' }}
                                                </td>
                                                <td class="text-black w-102px pe-3 table-headings-font-size lh-1-5 fw-semibold text-end">
                                                    {{ $pdfSymbol . getCurrencyFormat(round($transaction->igst, getCompanyFixedDigitNumber()) ?? '0.0') }}
                                                </td>
                                            </tr>
                                        @endif
                                    @endif
                                    @if ($transaction->tcs_amount != 0)
                                        <tr>
                                            <td class="px-1 text-black table-headings-font-size lh-1-5 fw-semibold">
                                                {{ $changeLabel['estimate_tcs'] ?? 'TCS' }}
                                            </td>
                                            <td class="text-black w-102px pe-3 table-headings-font-size lh-1-5 fw-semibold text-end">
                                                {{ $pdfSymbol . getCurrencyFormat($transaction->tcs_amount ?? '0.0') }}
                                            </td>
                                        </tr>
                                    @endif
                                    @if ($transaction->cess != 0)
                                        <tr>
                                            <td class="px-1 text-black table-headings-font-size lh-1-5 fw-semibold">
                                                {{ $changeLabel['estimate_cess'] ?? 'Cess' }}
                                            </td>
                                            <td class="text-black w-102px pe-3 table-headings-font-size lh-1-5 fw-semibold text-end">
                                                {{ $pdfSymbol . getCurrencyFormat($transaction->cess ?? '0.0') }}
                                            </td>
                                        </tr>
                                    @endif
                                    <tr>
                                        <td class="px-1 pb-1 text-black table-headings-font-size lh-1-5 fw-semibold">
                                            {{ $changeLabel['estimate_round_off'] ?? 'Round off' }}
                                        </td>
                                        <td class="pb-1 text-black w-102px pe-3 table-headings-font-size lh-1-5 fw-semibold text-end">
                                            {{ $pdfSymbol . getCurrencyFormat($transaction->round_off_amount ?? '0.0') }}
                                        </td>
                                    </tr>
                                    @foreach (collect($addLess)->where('is_show_in_print',1) as $addLessItem)
                                        <tr>
                                            <td class="px-1 pb-1 text-black table-headings-font-size lh-1-5 fw-semibold">
                                                {{ $addLessItem['ledger_name'] }}
                                            </td>
                                            <td class="pb-1 text-black w-102px pe-3 table-headings-font-size lh-1-5 fw-semibold text-end">
                                                {{ $pdfSymbol . getCurrencyFormat($addLessItem['amount'] ?? '0.0') }}
                                            </td>
                                        </tr>
                                    @endforeach
                                    @php
                                        if (empty($addLess)) {
                                            $total = $transaction->grand_total;
                                        } else {
                                            $addLessSum = array_sum(array_column($addLess, 'amount'));
                                            $total =  $transaction->grand_total - $addLessSum;
                                            $addLessSumTotal = collect($addLess)->where('is_show_in_print',1)->sum('amount');
                                            $total = $total + $addLessSumTotal;
                                        }
                                    @endphp
                                    <tr>
                                        <td class="px-1 total-font-size text-primary lh-1-5 fw-semibold">
                                            {{ $changeLabel['estimate_total'] ?? 'Total' }}
                                        </td>
                                        <td class="w-102px pe-3 total-font-size text-primary lh-1-5 fw-semibold text-end">
                                            {{ $pdfSymbol . getCurrencyFormat($total ?? '0.0') }}
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
        @if (($showPrintSettings['show_estimate_terms_and_conditions'] ?? true || (($showPrintSettings['show_estimate_authorized_signatory'] ?? true) || ($invoiceSetting['estimate_signature'] ?? false))))
            <table class="qr-table">
                <tbody>
                    <tr>
                        @if ($showPrintSettings['show_estimate_terms_and_conditions'] ?? true)
                            <td class="w-80 vertical-align-top">
                                <div class="py-3">
                                    <p class="mb-1 text-black terms-and-conditions-font-size lh-1-5 px-12px fw-semibold">
                                        {{ $changeLabel['estimate_terms_and_conditions'] ?? 'Terms and Conditions' }}:
                                    </p>
                                    <p class="text-black terms-and-conditions-font-size lh-1-5 px-12px">
                                        {!! nl2br($transaction->term_and_condition) !!}
                                    </p>
                                </div>
                            </td>
                        @endif
                        @if (($showPrintSettings['show_estimate_authorized_signatory'] ?? true) || ($invoiceSetting['estimate_signature'] ?? false))
                            <td class="w-20 vertical-align-bottom">
                                <div class="sign-section ms-auto pe-3">
                                    @if ($showPrintSettings['show_estimate_authorized_signatory'] ?? true)
                                        <p class="py-2 text-black footer-contents-font-size text-end fw-semibold">
                                            For, {{ strtoupper($currentCompany->trade_name) }}
                                        </p>
                                    @endif
                                    <div class="p-2 mb-1 sign d-flex align-items-center ms-auto">
                                        <div class="d-flex">
                                            @if (($invoiceSetting['estimate_signature'] ?? false) && $currentCompany->company_signature != asset('images/preview-img.png'))
                                                <img src="{{ $currentCompany->company_signature ?? null }}" alt="signature"
                                                    class="h-100 w-100" style="object-fit:contain;"/>
                                            @endif
                                        </div>
                                    </div>
                                    @if ($showPrintSettings['show_estimate_authorized_signatory'] ?? true)
                                        <p class="mb-3 text-black footer-contents-font-size ms-auto text-end">
                                            {{ $changeLabel['estimate_authorized_signatory'] ?? 'Authorized Signatory' }}
                                        </p>
                                    @endif
                                </div>
                            </td>
                        @endif
                    </tr>
                </tbody>
            </table>
        @endif
    </div>
</body>

</html>
