<!DOCTYPE html>
<html lang="en">
    {{-- landscape a5 format pdf ui 1 --}}
@php
    $pdfSymbol = getPrintPdfCurrencySymbol($currentCompany->id);
    $dockereEnabled = dockerEnabled() && !isset($preview_enabled);
    $fontStyleName = $dockereEnabled ? 'Arial Unicode MS' : 'Arial-unicode-ms';
    $fontCssName = $dockereEnabled ? 'font-docker.css' : 'font.css';
@endphp

<head>
    <meta charset="UTF-8" />
    <title>{{ isset($fileName) ? $fileName : $transaction->full_invoice_number }}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/css/' . $fontCssName) }}">
    @include('company.pdf.sale_pdf.print_custom_font_css_variables')
    <style>
        * {
            margin: 0;
            padding: 0;
            text-indent: 0;
            font-family: "{{ $fontStyleName }}";
            font-size: 13px;
            font-weight: 400;
            box-sizing: border-box;
        }
        @page {
            margin: 12px !important;
        }
        h1 {
            font-size: 24px;
        }
        .main-table {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            width: 100%;
            box-sizing: border-box;
            border: 2px solid #A9A9A9;
            color: #181C32;
        }
        table {
            display: table;
            width: 100%;
            border-collapse: collapse;
        }
        td {
            vertical-align: top;
        }
        .text-primary {
            color: var(--color-theme);
        }
        .fs-14 {
            font-size: 14px;
        }
        .fs-11 {
            font-size: 11px;
        }
        .fw-6 {
            font-weight: 600;
        }
        .fw-7 {
            font-weight: 700;
        }
        .whitespace-nowrap {
            white-space: nowrap;
        }
        .border-bottom {
            border-bottom: 1px solid #A9A9A9 !important;
        }
        .border-right {
            border-right: 1px solid #A9A9A9 !important;
        }
        .border-top {
            border-top: 1px solid #A9A9A9 !important;
        }
        .border-left {
            border-left: 1px solid #A9A9A9;
        }
        .vertical-top {
            vertical-align: top;
        }
        .vertical-middle {
            vertical-align: middle;
        }
        .vertical-bottom {
            vertical-align: bottom;
        }
        .text-center {
            text-align: center;
        }
        .text-start {
            text-align: left;
        }
        .text-end {
            text-align: right;
        }
        .text-black {
            color: #181C32 !important;
        }
        .signature {
            max-width: 200px;
            height: 50px;
        }
        .qr-code {
            width: 75px;
            height: 75px;
            min-width: 75px;
            margin-bottom: 12px;
        }
        .px-8 {
            padding-left: 8px !important;
            padding-right: 8px !important;
        }
        .pt-2 {
            padding-top: 2px !important;
        }
        .pb-2 {
            padding-bottom: 2px !important;
        }
        .pt-4 {
            padding-top: 4px !important;
        }
        .pb-4 {
            padding-bottom: 4px !important;
        }
        .w-100 {
            width: 100%;
        }
        .mb-0 {
            margin-bottom: 0 !important;
        }
        .px-3 {
            padding-left: 16px !important;
            padding-right: 16px !important;
        }
        .text-uppercase {
            text-transform: uppercase;
        }
        .row {
            display: flex;
            flex-wrap: wrap;
        }
        .col {
            flex: 1 0 0%;
        }
        .w-25 {
            width: 25%;
        }
        .p-0 {
            padding: 0 !important;
        }
        .font-italic {
            font-style: italic;
        }
        .item-table tbody tr:last-child td{
            height: 100%;
        }
        .h-100 {
            height: 100%;
        }
    </style>
      @include('company.pdf.sale_pdf.print_custom_font_css')
</head>

<body>
    <div>
        <div class="main-table w-100">
            <h2 class="pt-4 pb-2 mb-0 text-center company-name-font-size company-font-customization">
                {{ strtoupper($currentCompany->trade_name) }}
            </h2>
            @if(($invoiceSetting['prop_details'] ?? true) && isset($customProp))
                <p class="mb-0 text-center company-address-font-size">{{ $customProp->label_name.' : '.$customProp->label_value }}</p>
            @endif
            <p class="mb-0 text-center company-address-font-size">
                {{ isset($companyBillingAddress->address_1) ? strtoupper($companyBillingAddress->address_1 .',') : null }}
                {{ isset($companyBillingAddress->address_2) ? strtoupper($companyBillingAddress->address_2 .',') : null }}
                {{ isset($companyBillingAddress->city_id) ?  strtoupper(getCityName($companyBillingAddress->city_id).',') : null }}
                {{ isset($companyBillingAddress->state_id) ? strtoupper(getStateName($companyBillingAddress->state_id).',') : null }}
                {{ isset($companyBillingAddress->country_id) ? strtoupper(getCountryName($companyBillingAddress->country_id).',') : null }}
                {{ $companyBillingAddress->pin_code ?? null }}
            </p>
            @if ($invoiceSetting['email'] ?? true)
                <a class="text-center text-black company-address-font-size">
                    {{ $invoiceSetting['alternate_email'] ?? ($currentCompany->user->email ?? null) }}
                </a>
            @endif
            <div class="text-center">
                @foreach (printCustomPDFLabelsForSale() as $key => $customLabel)
                    <span class="company-address-font-size" style="{{ $loop->first ? 'margin-right:25px;' : '' }}">
                        {{ $key ?? null }}:
                        {{ $customLabel ?? null }}
                    </span>
                @endforeach
            </div>
            <div class="text-center">
                @if ($currentCompany->is_gst_applicable)
                    <span class="company-address-font-size" style="margin-right:25px;">
                        {{ $changeLabel['gstin'] ?? 'GSTIN' }}:
                        {{ '  ' . $currentCompany->companyTax->gstin ?? null }}
                    </span>
                @endif
                @if ($invoiceSetting['mobile_number'] ?? true)
                    <span class="company-address-font-size">
                        {{ $changeLabel['tel'] ?? 'Phone no.' }} :
                        {{
                            (isset($invoiceSetting['region_code']) ? '+' . $invoiceSetting['region_code'] . ' ' : '') .
                            (isset($invoiceSetting['alternate_phone']) ? $invoiceSetting['alternate_phone'] : ('+' . $currentCompany->user->region_code . ' ' . $currentCompany->user->phone)) .
                            (isset($invoiceSetting['region_code_2']) && isset($invoiceSetting['alternate_phone_2']) ? ', +' . $invoiceSetting['region_code_2'] . ' ' : '') .
                            (isset($invoiceSetting['alternate_phone_2']) ? (isset($invoiceSetting['region_code_2']) ? '' : ', ') . $invoiceSetting['alternate_phone_2'] : '')
                        }}
                    </span>
                @endif
            </div>
            <div class="px-3 border-bottom">
                <table class="table mb-0">
                    <tr>
                        <td class="px-8 pt-4 pb-4 fs-11 text-uppercase fw-7"> ({#INVOICE_TYPE#}) </td>
                        <td class="px-8 pt-4 pb-4 fs-11 text-uppercase fw-7 text-end"> {{ $taxInvoice }} </td>
                    </tr>
                </table>
            </div>
            <div class="row border-bottom">
                <div class="px-8 pt-4 pb-4 col vertical-top">
                    <h4 class="mb-0 fw-6 header-contents-font-size">
                        {{ strtoupper($customerDetail->name) }}
                    </h4>
                    @if ($showGst)
                        <p class="mb-0 header-contents-font-size">
                            GSTIN : {{ $transaction->gstin ?? $customerDetail->model->gstin ?? null }}
                        </p>
                    @endif
                    @if (!empty($panNumber) && $showPanNumber)
                        <p class="mb-0 header-contents-font-size">
                            PAN : {{ $panNumber ?? null }}
                        </p>
                    @endif
                    @if (isset($billingAddress))
                        <p class="m-0 header-contents-font-size">
                            @if ($billingAddress->address_1 != null)
                                {{ strtoupper($billingAddress->address_1) }}
                            @endif
                            @if ($billingAddress->address_2 != null)
                                {{ strtoupper($billingAddress->address_2) }},
                            @endif
                            {{ $billingAddress->city_id != null ? strtoupper(getCityName($billingAddress->city_id)) . ',' : null }}
                            {{ $billingAddress->state_id != null ? strtoupper(getStateName($billingAddress->state_id)) . ',' : null }}
                            {{ strtoupper(getCountryName($billingAddress->country_id ?? null)) }},
                            {{ $billingAddress->pin_code ?? null }}
                        </p>
                    @endif
                    <p class="mb-0 header-contents-font-size">
                        @if (!empty($transaction->party_phone_number))
                            ph.: +{{ $transaction->region_code ?? '' }} {{ $transaction->party_phone_number ?? null }}
                        @endif
                    </p>
                </div>
                @if ($invoiceSetting['ship_to_details'] ?? true)
                    <div class="px-8 pt-4 pb-4 col vertical-top">
                        <h4 class="mb-0 fw-6 header-contents-font-size">
                            {{ strtoupper($transaction->shipping_name ?? $customerDetail->name) }}
                        </h4>
                        @if ($isCompanyGstApplicable && !empty($transaction->shipping_gstin))
                            <p class="mb-0 header-contents-font-size">
                                GSTIN : {{ $transaction->shipping_gstin ?? null }}
                            </p>
                        @endif
                        <p class="m-0 header-contents-font-size">
                            @if (isset($shippingAddress->address_1))
                                {{ strtoupper($shippingAddress->address_1) }},
                            @endif
                            @if (isset($shippingAddress->address_2))
                                {{ strtoupper($shippingAddress->address_2) }},
                            @endif
                            {{ isset($shippingAddress->city_id) ? strtoupper(getCityName($shippingAddress->city_id)) . ',' : ''  }}
                            {{ isset($shippingAddress->state_id) ? strtoupper(getStateName($shippingAddress->state_id)) . ',' : '' }}
                            {{ isset($shippingAddress->country_id) ? strtoupper(getCountryName($shippingAddress->country_id)) . ',' : '' }},
                            {{ $shippingAddress->pin_code ?? null }}
                        </p>
                        <p class="mb-0 header-contents-font-size">
                            @if (!empty($transaction->party_phone_number))
                                ph.: +{{ $transaction->region_code ?? '' }} {{ $transaction->party_phone_number ?? null }}
                            @endif
                        </p>
                    </div>
                @endif
                @if (isset($transaction['dispatch_address_id']) && isset($dispatchAddress) && (($invoiceSetting['dispatch_from_details'] ?? false)))
                    <div class="px-8 pt-4 pb-4 col vertical-top">
                        <p class="m-0 header-contents-font-size">
                            {{ isset($dispatchAddress->address_1) ? strtoupper($dispatchAddress->address_1 .',') : null }}
                            {{ isset($dispatchAddress->address_2) ? strtoupper($dispatchAddress->address_2 .',') : null }}
                            {{ isset($dispatchAddress->city_id) ?  strtoupper(getCityName($dispatchAddress->city_id).',') : null }}
                            {{ isset($dispatchAddress->state_id) ? strtoupper(getStateName($dispatchAddress->state_id).',') : null }}
                            {{ isset($dispatchAddress->country_id) ? strtoupper(getCountryName($dispatchAddress->country_id).',') : null }}
                            {{ $dispatchAddress->pin_code ?? null }}
                        </p>
                    </div>
                @endif
                <div class="px-8 pt-4 pb-4 col vertical-top">
                    <table class="table">
                        <tr>
                            <td class="p-0 fw-7 header-contents-font-size">
                                {{ $invoiceNumberLabel }}:
                            </td>
                            <td class="p-0 header-contents-font-size">
                                {{ $transaction->full_invoice_number }}
                            </td>
                        </tr>
                        <tr>
                            <td class="p-0 fw-7 header-contents-font-size">
                                {{ $invoiceDateLabel }}:
                            </td>
                            <td class="p-0 header-contents-font-size">
                                {{ $invoiceDate }}
                            </td>
                        </tr>
                        @if(isset($originalInvoiceNumber) && !empty($originalInvoiceNumber) && isset($originalInvoiceDate) && !empty($originalInvoiceDate))
                            <tr>
                                <td class="p-0 fw-7 header-contents-font-size">
                                    Original Invoice No:
                                </td>
                                <td class="p-0 header-contents-font-size">
                                    {{ Illuminate\Support\Str::limit($originalInvoiceNumber, 20, '...')  }}
                                </td>
                            </tr>
                            <tr>
                                <td class="p-0 fw-7 header-contents-font-size">
                                    Original Invoice Date:
                                </td>
                                <td class="p-0 header-contents-font-size">
                                    {{ isset($originalInvoiceDate) ? \Carbon\Carbon::parse($originalInvoiceDate)->format('d-m-Y') : null }}
                                </td>
                            </tr>
                        @endif
                        @if ($invoiceSetting['po_number'] ?? true)
                            <tr>
                                <td class="p-0 fw-7 header-contents-font-size">
                                    {{ $changeLabel['po_number_label'] ?? 'PO No' }}:
                                </td>
                                <td class="p-0 header-contents-font-size">
                                    {{ $transaction->po_no }}
                                </td>
                            </tr>
                        @endif
                        @if ($invoiceSetting['show_po_date'] ?? true)
                            <tr>
                                <td class="p-0 fw-7 header-contents-font-size">
                                    {{ $changeLabel['po_date'] ?? 'PO Date' }}:
                                </td>
                                <td class="p-0 header-contents-font-size">
                                    {{ isset($transaction->po_date) ? \Carbon\Carbon::parse($transaction->po_date)->format('d-m-Y') : null }}
                                </td>
                            </tr>
                        @endif
                        @if(isset($deliveryChallanInvoiceNumber) && !empty($deliveryChallanInvoiceNumber))
                            <tr>
                                <td class="p-0 fw-7 header-contents-font-size">
                                    {{ $changeLabel['delivery_challan'] ?? 'Delivery Challan No' }}:
                                </td>
                                <td class="p-0 header-contents-font-size">
                                    {{ Illuminate\Support\Str::limit($deliveryChallanInvoiceNumber, 20, '...')  }}
                                </td>
                            </tr>
                            <tr>
                                <td class="p-0 fw-7 header-contents-font-size">
                                    {{ $changeLabel['delivery_challan'] ?? 'Delivery Date' }}:
                                </td>
                                <td class="p-0 header-contents-font-size">
                                    {{ $deliveryChallanInvoiceDate }}
                                </td>
                            </tr>
                        @endif
                    </table>
                </div>
            </div>
            @if (!empty($eInvoice) || ($invoiceSetting['transport_details'] ?? true) || count($customFieldValues) > 0 || (isset($eWayBill) || (isset($transaction->eway_bill_number) || isset($transaction->eway_bill_date)) || (isset($transaction->transporter_vehicle_number) && ($invoiceSetting['transport_details'] ?? true))))
                <div class="row border-bottom">
                    @if (!empty($eInvoice))
                        <div class="px-8 pt-4 pb-4 col vertical-top w-25">
                            <table class="table">
                                <tr>
                                    <td class="p-0 fw-7 header-contents-font-size whitespace-nowrap">
                                        {{ $changeLabel['ack_no'] ?? 'Ack No' }}:
                                    </td>
                                    <td class="p-0 header-contents-font-size">
                                        {{ !empty($eInvoice) ? $eInvoice->ack_no : '' }}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="p-0 fw-7 header-contents-font-size whitespace-nowrap">
                                        {{ $changeLabel['ack_date'] ?? 'Ack Date' }}:
                                    </td>
                                    <td class="p-0 header-contents-font-size">
                                        {{ isset($eInvoice->ack_date) ? \Carbon\Carbon::parse($eInvoice->ack_date)->format('d-m-Y') : '' }}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="p-0 fw-7 header-contents-font-size whitespace-nowrap">
                                        {{ $changeLabel['irn'] ?? 'IRN' }}:
                                    </td>
                                    <td class="p-0 header-contents-font-size" style="word-break: break-all;">
                                        {{ !empty($eInvoice) ? $eInvoice->irn : '' }}
                                    </td>
                                </tr>
                            </table>
                        </div>
                    @endif
                    @if ($invoiceSetting['transport_details'] ?? true)
                        <div class="px-8 pt-4 pb-4 col vertical-top" style="{{ isset($eWayBill) || (isset($transaction->eway_bill_number) || isset($transaction->eway_bill_date)) || (isset($transaction->transporter_vehicle_number) && ($invoiceSetting['transport_details'] ?? true)) || (count($customFieldValues) > 0) ? '' : 'max-width: 35%' }}">
                            <table class="table">
                                <tr>
                                    <td class="p-0 fw-7 header-contents-font-size">
                                        {{ $changeLabel['transport_name'] ?? 'Transport Name' }}:
                                    </td>
                                    <td class="p-0 header-contents-font-size">
                                        {{ $transaction->transport->transporter_name ?? '' }}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="p-0 fw-7 header-contents-font-size">
                                        GSTIN:
                                    </td>
                                    <td class="p-0 header-contents-font-size">
                                        {{ $transaction->transport->gstin ?? '' }}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="p-0 fw-7 header-contents-font-size">
                                        {{ $changeLabel['document_no'] ?? 'Document No' }}:
                                    </td>
                                    <td class="p-0 header-contents-font-size">
                                        {{ $transaction->transporter_document_number ?? '' }}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="p-0 fw-7 header-contents-font-size">
                                        {{ $changeLabel['document_date'] ?? 'Document Date' }}:
                                    </td>
                                    <td class="p-0 header-contents-font-size">
                                        {{ isset($transaction->transporter_document_date) ? \Carbon\Carbon::parse($transaction->transporter_document_date)->format('d-m-Y') : ' ' }}
                                    </td>
                                </tr>
                            </table>
                        </div>
                    @endif
                    @if (isset($eWayBill) || (isset($transaction->eway_bill_number) || isset($transaction->eway_bill_date)) || (isset($transaction->transporter_vehicle_number) && ($invoiceSetting['transport_details'] ?? true)))
                        <div class="px-8 pt-4 pb-4 col vertical-top">
                            <table class="table">
                                <tr>
                                    <td class="p-0 fw-7 header-contents-font-size">
                                        {{ $changeLabel['e_way_bill_no'] ?? 'E-way Bill No' }}:
                                    </td>
                                    <td class="p-0 header-contents-font-size">
                                        {{ $eWayBill?->eway_bill_no ?? $transaction->eway_bill_number ?? null}}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="p-0 fw-7 header-contents-font-size">
                                        {{ $changeLabel['e_way_bill_date'] ?? 'E-way Bill Date' }}:
                                    </td>
                                    <td class="p-0 header-contents-font-size">
                                        {{ \Carbon\Carbon::parse($eWayBill?->eway_bill_date)->format('d-m-Y') ?? \Carbon\Carbon::parse($transaction->eway_bill_date)->format('d-m-Y') ?? null }}
                                    </td>
                                </tr>
                                @if (isset($transaction->transporter_vehicle_number) && ($invoiceSetting['transport_details'] ?? true))
                                    <tr>
                                        <td class="p-0 fw-7 header-contents-font-size">
                                            {{ $changeLabel['transport_vehicle_number'] ?? 'Vehicle No.' }}:
                                        </td>
                                        <td class="p-0 header-contents-font-size">
                                            {{ $transaction->transporter_vehicle_number ?? '' }}
                                        </td>
                                    </tr>
                                @endif
                            </table>
                        </div>
                    @endif
                    {{-- Custom Fields Section Start --}}
                    @if (count($customFieldValues) > 0)
                        @php
                            $customFields = collect($customFieldValues)->where('is_show_in_print', true)->values();
                        @endphp
                        <div class="px-8 pt-4 pb-4 col vertical-top">
                            <table class="table">
                                @foreach ($customFields as $customField)
                                    <tr>
                                        <td class="p-0 fw-7 header-contents-font-size">
                                            {{ $customField['label_name'] ?? '' }}:
                                        </td>
                                        <td class="p-0 header-contents-font-size">
                                            {{ $customField['value'] ?? '' }}
                                        </td>
                                    </tr>
                                @endforeach
                            </table>
                        </div>
                    @endif
                    {{-- Custom Fields Section End --}}
                </div>
            @endif
            {{-- Item Table Section Start --}}
            @php
                $lastFourColumns = ['total', 'gst_tax_percentage', 'gst_amount', 'total_amount'];

                // Headings Fields
                $headingsBefore = collect($rearrangeItems['headings'])
                    ->reject(fn($h) => in_array($h['key'] ?? '', $lastFourColumns) || (isset($h['is_show_in_print']) && !$h['is_show_in_print']));
                $headingsLastFour = collect($lastFourColumns)
                    ->map(fn($key) => collect($rearrangeItems['headings'])->where('is_show_in_print', true)->firstWhere('key', $key))
                    ->filter();
                $allVisibleHeadings = $headingsBefore->concat($headingsLastFour);

                // Details Fields
                $detailsBefore = collect($rearrangeItems['detail'])
                    ->map(fn($row) => collect($row)
                        ->reject(fn($v, $k) => in_array($k, $lastFourColumns) || (is_array($v) && isset($v['is_show_in_print']) && !$v['is_show_in_print']))
                        ->all())
                    ->all();
                $detailsLastFour = collect($rearrangeItems['detail'])->map(fn($row) => collect($lastFourColumns)
                    ->mapWithKeys(fn($key) => (isset($row[$key]) && $row[$key]['is_show_in_print']) ? [$key => $row[$key]] : []));

                // Footer Fields
                $footerBefore = collect($rearrangeItems['footer'])
                    ->reject(fn($f, $k) => in_array($k, $lastFourColumns) || (is_array($f) && isset($f['is_show_in_print']) && !$f['is_show_in_print']))
                    ->all();
                $footerLastFour = collect($lastFourColumns)
                    ->mapWithKeys(fn($key) => (isset($rearrangeItems['footer'][$key]) && $rearrangeItems['footer'][$key]['is_show_in_print'])
                        ? [$key => $rearrangeItems['footer'][$key]]
                        : []);
                $allFooter = collect($footerBefore)->merge($footerLastFour);
            @endphp

            <table class="item-table" cellpadding="0" style="flex-grow: 1; width: 100%; border-collapse: collapse;">
                <thead>
                    <tr class="border-bottom">
                        @foreach($allVisibleHeadings as $heading)
                            <td class="px-8 pt-4 pb-4 text-center table-headings-font-size fw-7 whitespace-nowrap {{ $heading['class'] }} {{ $loop->last ? '' : 'border-right' }}"
                                style="padding: 6px 8px; font-weight: bold;">
                                {{ $heading['name'] }}
                            </td>
                        @endforeach
                    </tr>
                </thead>
                <tbody>
                    @foreach ($detailsBefore as $index => $row)
                        @php $fullRow = collect($row)->merge($detailsLastFour[$index] ?? []); @endphp
                        <tr>
                            @foreach($fullRow as $key => $item)
                                <td class="px-8 pt-4 table-contents-font-size fw-6 {{ $loop->last ? '' : 'border-right' }} {{ ($key == 'item_name') ? 'w-100' : 'text-center whitespace-nowrap' }}"
                                    style="padding: 4px 8px 0 8px; {{ ($key == 'primary_quantity' || $key == 'secondary_quantity') ? 'white-space: nowrap;' : '' }}">
                                    {{ $item['value'] ?? '' }}
                                    @if($key == 'item_name')
                                        @if($item['show_sku'] ?? false)
                                            <p class="description-font-size">Item Code: {{ $item['sku'] ?? '' }}</p>
                                        @endif
                                        @if($item['show_consolidating_items'] ?? false)
                                            <p class="description-font-size" style="word-break: break-word;">{!! $item['consolidating_items'] !!}</p>
                                        @endif
                                        @if($item['show_additional_description'] ?? false)
                                            <p class="description-font-size" style="word-break: break-word;">{!! $item['additional_description'] !!}</p>
                                        @endif
                                        @if (count($item['customItemsInventoryValues']) > 0)
                                            @foreach ($item['customItemsInventoryValues'] as $customItemsInventoryValue)
                                                <p style="word-break: break-word; font-style: italic; color: #888888;" class="description-font-size">{{ $customItemsInventoryValue }}</p>
                                            @endforeach
                                        @endif
                                        @if($item['show_item_image'] ?? false)
                                            <div><img src="{{ $item['item_image'] }}" width="60" height="60" style="margin-top: 4px"></div>
                                        @endif
                                    @endif
                                </td>
                            @endforeach
                        </tr>
                    @endforeach
                </tbody>
                <tfoot>
                    <tr class="border-top border-bottom">
                        @foreach($allFooter as $key => $footer)
                            <td class="px-8 pt-4 table-headings-font-size fw-6 {{ $loop->last ? '' : 'border-right' }} {{ ($key == 'item_name') ? 'min-width-150 ps-2' : 'text-center' }}">
                                {{ $footer['value'] ?? '' }}
                            </td>
                        @endforeach
                    </tr>
                </tfoot>
            </table>
            {{-- Item Table Section End --}}

            <div style="display: flex;">
                <div style="display: flex; flex-direction: column; width: 100%; justify-content: space-between;">
                    @if ((($showPrintSettings['show_sale_narration'] ?? true) && $transaction->narration) || (($showPrintSettings['show_sale_terms_and_conditions'] ?? true) && $transaction->term_and_condition))
                        <div>
                            @if (($showPrintSettings['show_sale_narration'] ?? true) && $transaction->narration)
                                <div class="pt-4 pb-4">
                                    <h4 class="px-8 pt-4 note-font-size fw-7">
                                        {{ $changeLabel['narration'] ?? 'Note' }} :
                                    </h4>
                                    <p class="px-8 pt-4 note-font-size">
                                        {!! nl2br($transaction->narration) !!}
                                    </p>
                                </div>
                            @endif
                            @if (($showPrintSettings['show_sale_terms_and_conditions'] ?? true) && $transaction->term_and_condition)
                                <div class="pt-4 pb-4">
                                    <h4 class="px-8 pt-4 terms-and-conditions-font-size fw-7">
                                        {{ $changeLabel['terms_and_conditions'] ?? 'Terms and Conditions' }} :
                                    </h4>
                                    <p class="px-8 pt-4 terms-and-conditions-font-size">
                                        {!! nl2br($transaction->term_and_condition) !!}
                                    </p>
                                </div>
                            @endif
                        </div>
                    @endif
                    @php
                        if (empty($addLess)) {
                            $total = $transaction->grand_total;
                        } else {
                            $addLessSum = collect($addLess)->sum('amount');
                            $total = $transaction->grand_total - $addLessSum;
                            $addLessSumTotal = collect($addLess)->where('is_show_in_print',1)->sum('amount');
                            $total = $total + $addLessSumTotal;
                        }
                    @endphp
                    @if (($showPrintSettings['show_sale_in_words'] ?? true) || ((($invoiceSetting['qr_code'] ?? true) && isset($bankDetail->upi_id)) || (($invoiceSetting['bank_details'] ?? true) && isset($bankDetail))) || (($showPrintSettings['show_sale_authorized_signatory'] ?? true) || ($invoiceSetting['signature'] ?? false)) || ($invoiceSetting['show_payment_status'] ?? false))
                        <div class="{{ ($showPrintSettings['show_sale_in_words'] ?? true) ? '' : 'h-100' }}">
                            @if ($showPrintSettings['show_sale_in_words'] ?? true)
                                <div class="px-8 pt-4 pb-4 {{ ((($showPrintSettings['show_sale_narration'] ?? true) && $transaction->narration) || (($showPrintSettings['show_sale_terms_and_conditions'] ?? true) && $transaction->term_and_condition)) ? 'border-top' : '' }} table-contents-font-size">
                                    <span class="fw-7 table-contents-font-size" style="margin-right:4px;">
                                        {{ $changeLabel['in_words'] ?? 'In Words' }} :
                                    </span>
                                    {{ getAmountToWord($total ?? '0.0') }} Only
                                </div>
                            @endif
                            <table class="{{ ($showPrintSettings['show_sale_in_words'] ?? true) ? '' : 'h-100' }}" cellpadding="0" style="page-break-inside: avoid !important;">
                                <tr class="{{ ((($showPrintSettings['show_sale_narration'] ?? true) && $transaction->narration) || (($showPrintSettings['show_sale_terms_and_conditions'] ?? true) && $transaction->term_and_condition) || ($showPrintSettings['show_sale_in_words'] ?? true)) ? 'border-top' : '' }}">
                                    @if ((($invoiceSetting['qr_code'] ?? true) && isset($bankDetail->upi_id)) || (($invoiceSetting['bank_details'] ?? true) && isset($bankDetail)) || ($invoiceSetting['show_payment_status'] ?? false))
                                        <td class="{{ (($showPrintSettings['show_sale_authorized_signatory'] ?? true) || ($invoiceSetting['signature'] ?? false)) ? 'border-right' : '' }}">
                                            <table>
                                                <tr>
                                                    <td>
                                                        <table>
                                                            <tr>
                                                                <td>
                                                                    <div class="px-8 pt-4 pb-4"
                                                                        style="display: flex; align-items:center; gap:16px;">
                                                                        @if (($invoiceSetting['qr_code'] ?? true) && isset($bankDetail->upi_id))
                                                                            <div class="qr-code"
                                                                                style="margin-top: 5px; padding-right: 10px; white-space: nowrap;">
                                                                                <img src="data:image/svg+xml;base64,{{ base64_encode(generateUPIQRCode($bankDetail->upi_id, $transaction->due_amount)) }}"
                                                                                    width="80" height="80" />
                                                                            </div>
                                                                        @endif
                                                                        @if (($invoiceSetting['bank_details'] ?? true) && isset($bankDetail))
                                                                            <div>
                                                                                <table class="whitespace-nowrap">
                                                                                    <tr>
                                                                                        <td class="px-8 pt-2 footer-headings-font-size fw-7">
                                                                                            Bank:
                                                                                        </td>
                                                                                        <td class="pt-2 footer-contents-font-size">
                                                                                            {{ $bankDetail->bank_name ?? null }}
                                                                                        </td>
                                                                                    </tr>
                                                                                    <tr>
                                                                                        <td class="px-8 pt-2 footer-headings-font-size fw-7">
                                                                                            IFSC Code:
                                                                                        </td>
                                                                                        <td class="pt-2 footer-contents-font-size">
                                                                                            {{ $bankDetail->ifsc_code ?? null }}
                                                                                        </td>
                                                                                    </tr>
                                                                                    <tr>
                                                                                        <td class="px-8 pt-2 footer-headings-font-size fw-7">
                                                                                            A/C Number:
                                                                                        </td>
                                                                                        <td class="pt-2 footer-contents-font-size">
                                                                                            {{ $accountNumber }}
                                                                                        </td>
                                                                                    </tr>
                                                                                    <tr>
                                                                                        <td class="px-8 pt-2 footer-headings-font-size fw-7">
                                                                                            Bank Branch:
                                                                                        </td>
                                                                                        <td class="pt-2 footer-contents-font-size">
                                                                                            {{ $branchName }}
                                                                                        </td>
                                                                                    </tr>
                                                                                    @if (isset($bankDetail->account_holder_name))
                                                                                        <tr>
                                                                                            <td class="px-8 pt-2 footer-headings-font-size fw-7">
                                                                                                A/C Name:
                                                                                            </td>
                                                                                            <td class="pt-2 footer-contents-font-size">
                                                                                                {{ $bankDetail->account_holder_name }}
                                                                                            </td>
                                                                                        </tr>
                                                                                    @endif
                                                                                    @if (isset($bankDetail->swift_code))
                                                                                        <tr>
                                                                                            <td class="px-8 pt-2 footer-headings-font-size fw-7">
                                                                                                Swift Code:
                                                                                            </td>
                                                                                            <td class="pt-2 footer-contents-font-size">
                                                                                                {{ $bankDetail->swift_code }}
                                                                                            </td>
                                                                                        </tr>
                                                                                    @endif
                                                                                    @if (isset($bankDetail->upi_id))
                                                                                        <tr>
                                                                                            <td class="px-8 pt-2 footer-headings-font-size fw-7">
                                                                                                UPI ID:
                                                                                            </td>
                                                                                            <td class="pt-2 footer-contents-font-size">
                                                                                                {{ $bankDetail->upi_id }}
                                                                                            </td>
                                                                                        </tr>
                                                                                    @endif
                                                                                </table>
                                                                            </div>
                                                                        @endif
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                    <td>
                                                        @if($invoiceSetting['show_payment_status'] ?? false)
                                                            @if($transaction->payment_status == 'Partially Unpaid')
                                                                <div style="display: flex; justify-content:center; align-items:center; padding-right: 4px;">
                                                                    <div style="border:2px solid #4f158c; width:fit-content; margin:8px auto; padding:4px 8px; display:inline-block;">
                                                                        <h5 class="text-primary text-center mb-0" style="font-weight:700; font-size:14px;"> PARTLY</br>
                                                                            UNPAID</h5>
                                                                    </div>
                                                                </div>
                                                            @elseif ($transaction->payment_status == 'Paid')
                                                                <div class="flex-grow-1">
                                                                    <div style="display: flex; justify-content:center; align-items:center; padding-right: 4px;">
                                                                        <div
                                                                            style="padding:2px; width:fit-content; background: linear-gradient(to top,#43ad52,#a6d052); margin:8px auto; display:inline;">
                                                                            <div style="background-color: white; padding:4px 8px;">
                                                                                <h4 class="text-center"
                                                                                    style="font-weight:700; font-size:14px; background: -webkit-linear-gradient(#a6d052, #43ad52); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">
                                                                                    PAID</h4>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            @elseif($transaction->payment_status == 'Unpaid')
                                                                <div style="display: flex; justify-content:center; align-items:center; padding-right: 4px;" class="flex-grow-1">
                                                                    <div style="padding:2px; width:fit-content; background: linear-gradient(to top,#801520,#c30a17); margin:8px auto; display:inline;">
                                                                        <div style="background-color: white; padding:4px 8px;">
                                                                            <h4 class="text-center"
                                                                                style="font-weight:700; font-size:14px; background: -webkit-linear-gradient(#c30a17, #801520); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">
                                                                                UNPAID</h4>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            @endif
                                                        @endif
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    @endif
                                    @if(($showPrintSettings['show_sale_authorized_signatory'] ?? true) || ($invoiceSetting['signature'] ?? false))
                                        <td class="vertical-bottom border-right" style="width: 200px; position: relative">
                                            <div class="px-8 pt-4 pb-4">

                                                <div class="text-end signature">
                                                    {{-- <img src="{{ $currentCompany->company_signature ?? null }}" alt="company-img"
                                                        style="max-width:100%; height:100%;  object-fit:contain; margin-left:auto;"> --}}
                                                </div>
                                                <p class="text-center verical-bottom footer-contents-font-size">
                                                    Receiver Signatory
                                                </p>
                                            </div>
                                        </td>
                                    @endif
                                    @if(($showPrintSettings['show_sale_authorized_signatory'] ?? true) || ($invoiceSetting['signature'] ?? false))
                                        <td class="vertical-bottom" style="width: 200px; position: relative">
                                            <div class="px-8 pt-4 pb-4">
                                                @if ($showPrintSettings['show_sale_authorized_signatory'] ?? true)
                                                    <p class="text-center footer-headings-font-size fw-7">
                                                        For, {{ strtoupper($currentCompany->trade_name) }}
                                                    </p>
                                                @endif
                                                <div class="text-center signature" style="margin: 0 auto;">
                                                    @if (($invoiceSetting['signature'] ?? false) && $currentCompany->company_signature != asset('images/preview-img.png'))
                                                        <img src="{{ $currentCompany->company_signature ?? null }}" alt="company-img"
                                                            style="max-width:100%; height:100%;  object-fit:contain; margin-left:auto;">
                                                    @endif
                                                </div>
                                                @if ($showPrintSettings['show_sale_authorized_signatory'] ?? true)
                                                    <p class="text-center verical-bottom footer-contents-font-size">
                                                        {{ $changeLabel['authorized_signatory'] ?? 'Authorized Signatory' }}
                                                    </p>
                                                @endif
                                            </div>
                                        </td>
                                    @endif
                                </tr>
                            </table>
                        </div>
                    @endif
                </div>
                <div class="vertical-top border-left" style="white-space: nowrap; display: flex;
                flex-direction: column;
                justify-content: space-between; ">
                    <div>
                        <table cellpadding="0" style="flex-grow: 1;">
                            @foreach ($additionalCharges as $additionalCharge)
                                <tr>
                                    <td class="px-8 pt-4 pb-2 table-contents-font-size vertical-top">
                                        {{ $additionalCharge['ledger_name'] }}
                                    </td>
                                    <td class="px-8 pt-4 pb-2 table-contents-font-size vertical-top text-end">
                                        {{ $pdfSymbol.getCurrencyFormat($additionalCharge['amount'] ?? '0.0') }}
                                    </td>
                                </tr>
                            @endforeach
                            <tr>
                                <td class="px-8 pb-2 table-contents-font-size vertical-top fw-7">
                                    {{ $isCompanyGstApplicable ? $changeLabel['sub_total'] ?? 'Taxable Value' : $changeLabel['sub_total'] ?? 'Sub Total' }}
                                </td>
                                <td class="px-8 pb-2 table-contents-font-size vertical-top text-end fw-7">
                                    {{ $pdfSymbol.getCurrencyFormat(round($transaction->taxable_value, getCompanyFixedDigitNumber()) ?? '0.0') }}
                                </td>
                            </tr>
                            @if ($isCompanyGstApplicable)
                                @if ($transaction->cgst != 0)
                                    <tr>
                                        <td class="px-8 pb-2 table-contents-font-size vertical-top">
                                            {{ $changeLabel['cgst'] ?? 'CGST' }}
                                        </td>
                                        <td class="px-8 pb-2 table-contents-font-size vertical-top text-end">
                                            {{ $pdfSymbol.getCurrencyFormat(round($transaction->cgst, getCompanyFixedDigitNumber()) ?? '0.0') }}
                                        </td>
                                    </tr>
                                @endif
                                @if ($transaction->sgst != 0)
                                    <tr>
                                        <td class="px-8 pb-2 table-contents-font-size vertical-top">
                                            {{ $changeLabel['sgst'] ?? 'SGST' }}
                                        </td>
                                        <td class="px-8 pb-2 table-contents-font-size vertical-top text-end">
                                            {{ $pdfSymbol.getCurrencyFormat(round($transaction->sgst, getCompanyFixedDigitNumber()) ?? '0.0') }}
                                        </td>
                                    </tr>
                                @endif
                                @if ($transaction->igst != 0)
                                    <tr>
                                        <td class="px-8 pb-2 table-contents-font-size vertical-top">
                                            {{ $changeLabel['igst'] ?? 'IGST' }}
                                        </td>
                                        <td class="px-8 pb-2 table-contents-font-size vertical-top text-end">
                                            {{ $pdfSymbol.getCurrencyFormat(round($transaction->igst, getCompanyFixedDigitNumber()) ?? '0.0') }}
                                        </td>
                                    </tr>
                                @endif
                            @endif
                            @if ($transaction->cess != 0)
                            <tr>
                                <td class="px-8 pb-2 table-contents-font-size vertical-top">
                                    {{ $changeLabel['cess'] ?? 'CESS' }}
                                </td>
                                <td class="px-8 pb-2 table-contents-font-size vertical-top text-end">
                                    {{ $pdfSymbol.getCurrencyFormat($transaction->cess ?? '0.0') }}
                                </td>
                            </tr>
                            @endif
                            @if ($transaction->tcs_amount != 0)
                                <tr>
                                    <td class="px-8 pb-2 table-contents-font-size vertical-top">
                                        {{ $changeLabel['tcs'] ?? 'TCS' }}
                                    </td>
                                    <td class="px-8 pb-2 table-contents-font-size vertical-top text-end">
                                        {{ $pdfSymbol.getCurrencyFormat($transaction->tcs_amount ?? '0.0') }}
                                    </td>
                                </tr>
                            @endif
                            @foreach (collect($addLess)->where('is_show_in_print',1) as $addLessItem)
                                <tr>
                                    <td class="px-8 pb-2 table-contents-font-size vertical-top">
                                        {{ $addLessItem['ledger_name'] }}
                                    </td>
                                    <td class="px-8 pb-2 table-contents-font-size vertical-top text-end">
                                        {{ $pdfSymbol.getCurrencyFormat($addLessItem['amount'] ?? '0.0') }}
                                    </td>
                                </tr>
                            @endforeach
                            <tr>
                                <td class="px-8 pb-4 table-contents-font-size vertical-top">
                                    {{ $changeLabel['round_off'] ?? 'Round off' }}
                                </td>
                                <td class="px-8 pb-4 table-contents-font-size vertical-top text-end">
                                    {{ $pdfSymbol.getCurrencyFormat($transaction->rounding_amount ?? '0.0') }}
                                </td>
                            </tr>

                        </table>
                    </div>
                    <div>
                        <table class="vertical-bottom">
                            <tr class="border-top">
                                <td class="px-8 pt-4 pb-4 text-primary total-font-size fw-7">
                                    {{ $changeLabel['total'] ?? 'Total' }}
                                </td>
                                <td class="px-8 pt-4 pb-4 text-primary text-end total-font-size fw-7">
                                    {{ $pdfSymbol.getCurrencyFormat($total ?? '0.0') }}
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>

</html>
