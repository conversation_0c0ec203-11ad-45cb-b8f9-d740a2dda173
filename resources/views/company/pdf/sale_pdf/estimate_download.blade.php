<!DOCTYPE html>
<html lang="en">
@php
    use App\Models\CompanySetting;
    if (!isset($isA5Pdf)) {
        $estimatePDFSetting = isset(getCompanySettings()['estimate_pdf_format']) ? CompanySetting::ESTIMATE_PDF_FORMAT[getCompanySettings()['estimate_pdf_format']] : 'A4';
        $isA5Pdf = $estimatePDFSetting == CompanySetting::PDF_FORMAT[CompanySetting::A5] ? true : false;
    }
    $pdfSymbol = getPrintPdfCurrencySymbol($currentCompany->id);
    $dockereEnabled = dockerEnabled() && !isset($preview_enabled);
    $fontStyleName = $dockereEnabled ? 'Arial Unicode MS' : 'Arial-unicode-ms';
    $fontCssName = $dockereEnabled ? 'font-docker.css' : 'font.css';
@endphp
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/css/' . $fontCssName) }}">
    <title>{{ isset($fileName) ? $fileName : $transaction->document_number }}</title>
    @include('company.pdf.sale_pdf.print_custom_font_css_variables')
    <style>
        h1 {
            font-size: 27px;
        }

        * {
            margin: 0;
            padding: 0;
            text-indent: 0;
            font-family: "{{ $fontStyleName }}";
            font-size: 13px;
            font-weight: 400;
            box-sizing: border-box;
        }

        @page {
            margin: 20px;
        }

        .main-table {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            width: 100%;
            box-sizing: border-box;
            border: 2px solid black;
        }

        table {
            display: table;
            width: 100%;
            border-collapse: collapse;
        }

        .text-primary {
            color: #4f158c;
        }

        .address {
            font-size: 11px;
            /* line-height: 14px; */
            padding-left: 8px;
            padding-right: 8px;
        }

        .phone {
            font-size: 11px;
            /* line-height: 14px; */
            padding-left: 8px;
            padding-right: 8px;
        }

        td {
            vertical-align: top;
        }

        .fs-13 {
            font-size: 13px;
        }

        .fs-12 {
            font-size: 12px;
        }

        .fw-6 {
            font-weight: 600;
        }

        .whitespace-nowrap {
            white-space: nowrap;
        }

        .border-bottom {
            border-bottom: 1px solid black;
        }

        .border-right {
            border-right: 1px solid black;
        }

        .border-top {
            border-top: 1px solid black;
        }

        .border-left {
            border-left: 1px solid black;
        }

        .vertical-top {
            vertical-align: top;
        }

        .vertical-middle {
            vertical-align: middle;
        }

        .vertical-bottom {
            vertical-align: bottom;
        }

        .text-center {
            text-align: center;
        }

        .text-start {
            text-align: left;
        }

        .text-end {
            text-align: right;
        }

        .table-heading {
            padding: 3px 8px;
            text-align: left;
            position: relative;
            /* background-color: #eeeeee !important; */
        }

        .signature {
            max-width: 217px;
            height: 100px;
            margin-left:auto;
        }

        .desc {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            font-size: 12px;
            position: relative;
            padding-left: 5px;
            padding-right: 5px;
        }

        .desc::before {
            position: absolute;
            content: "(";
            top: 0;
            left: 0;
            font-size: 12px;
        }

        .desc::after {
            position: absolute;
            content: ")";
            bottom: 2px;
            right: 0;
            font-size: 12px;
        }
        .d-none{
            display: none;
        }
        .item-table tr:nth-last-child(-n+2):not(:last-child) {
            font-size: 12px;
            border-bottom: 1px solid black;
            padding: 4px 8px 0 4px;
            height: 100%;
            vertical-align: top;
        }
        .min-width-150 {
            min-width: 150px !important;
        }
        .w-100 {
            width: 100% !important;
        }
        .ps-2{
            padding-left: 8px;
        }
    </style>
    @include('company.pdf.sale_pdf.print_custom_font_css')
</head>
<body>

    <div class="main-table">
        @if(($invoiceSetting['show_slogan'] ?? false) && isset($invoiceSetting['slogan']))
            <div class="text-center border-bottom" style="padding: 4px 2px 2px 2px">
                <h6 class="mb-1 company-address-font-size">
                    {{ $invoiceSetting['slogan'] }}
                </h6>
            </div>
         @endif
        <div class="text-center border-bottom" style="padding: 4px 2px 2px 2px">
            <h6 class="fw-6 text-primary" style="font-size: 15px">
                {{ $transaction->transactionTitle->name }} ({#INVOICE_TYPE#})
            </h6>
        </div>

        {{-- Logo / Company Name Section Start --}}
        <table cellpadding="0">
            <tr>
                <td class="vertical-middle" style="padding: 10px 25px; width: 100px; max-width:100px; height: 100px">
                    @if (isset($currentCompany->company_logo) && ($invoiceSetting['estimate_logo'] ?? true) && $currentCompany->company_logo != asset('images/preview-img.png'))
                        <img src={{ $currentCompany->company_logo ?? '' }} alt="Logo" style="object-fit: contain;" width="100" >
                    @endif
                </td>
                <td class="vertical-middle text-center" style="margin-left:-50px; width:60%;">
                    <h1 class="company-name-font-size company-font-customization">{{ strtoupper($currentCompany->trade_name) }}</h1>
                    @if(($invoiceSetting['prop_details'] ?? true) && isset($customProp))
                        <p class="mt-1 fw-6 company-address-font-size">{{ $customProp->label_name.' : '.$customProp->label_value }}</p>
                    @endif
                    <p class="company-address-font-size">
                        {{ isset($companyBillingAddress->address_1) ? strtoupper($companyBillingAddress->address_1 .',') : null }}
                        {{ isset($companyBillingAddress->address_2) ? strtoupper($companyBillingAddress->address_2 .',') : null }}
                        {{ isset($companyBillingAddress->city_id) ?  strtoupper(getCityName($companyBillingAddress->city_id).',') : null }}
                        {{ isset($companyBillingAddress->state_id) ? strtoupper(getStateName($companyBillingAddress->state_id).',') : null }}
                        {{ isset($companyBillingAddress->country_id) ? strtoupper(getCountryName($companyBillingAddress->country_id).',') : null }}
                        {{ $companyBillingAddress->pin_code ?? null }}
                    </p>
                    <p class="company-address-font-size">
                        @if ($invoiceSetting['estimate_mobile_number'] ?? true)
                            {{ $changeLabel['estimate_tel'] ?? 'Tel' }}:
                            {{
                                (isset($invoiceSetting['region_code']) ? '+' . $invoiceSetting['region_code'] . ' ' : '') .
                                (isset($invoiceSetting['alternate_phone']) ? $invoiceSetting['alternate_phone'] : ('+' . $currentCompany->user->region_code . ' ' . $currentCompany->user->phone)) .
                                (isset($invoiceSetting['region_code_2']) && isset($invoiceSetting['alternate_phone_2']) ? ', +' . $invoiceSetting['region_code_2'] . ' ' : '') .
                                (isset($invoiceSetting['alternate_phone_2']) ? (isset($invoiceSetting['region_code_2']) ? '' : ', ') . $invoiceSetting['alternate_phone_2'] : '')
                            }}
                        @endif
                        @if (($invoiceSetting['estimate_mobile_number'] ?? true) && ($invoiceSetting['estimate_email'] ?? true) && (($invoiceSetting['alternate_email'] ?? ($currentCompany->user->email ?? null) != null)))
                            |
                        @endif
                        @if (isset($invoiceSetting['estimate_email']) ? $invoiceSetting['estimate_email'] : true)
                            {{ $invoiceSetting['alternate_email'] ?? ($currentCompany->user->email ?? null) }}
                        @endif
                    </p>
                    @if ($currentCompany->is_gst_applicable)
                        <p class="company-address-font-size">{{ $changeLabel['estimate_gstin'] ?? 'GSTIN' }}: {{ '  ' . $currentCompany->companyTax->gstin ?? null }}</p>
                    @endif
                    @foreach (printCustomPDFLabelsForEstimate() as $key => $customLabel)
                        <p class="company-address-font-size">{{ $key ?? null }}: {{ $customLabel ?? null }}</p>
                    @endforeach
                </td>
                <td class="vertical-middle text-end" style="padding: 10px 25px; width: 100px; max-width:100px; height: 100px">
                </td>
            </tr>
        </table>
        {{-- Logo / Company Name Section End --}}

        {{-- Bill To / Ship to / Invoice Details Section Start --}}
        <table cellpadding="0">
            <tr class="border-bottom">
                <td class="border-right vertical-top" style="width:33.33%;">
                    <p class="text-primary border-bottom border-top fw-6 table-heading header-labels-font-size">
                        {{ $changeLabel['estimate_bill_to'] ?? 'Estimate To' }} :
                    </p>
                    <h4 class="fw-6 header-contents-font-size" style="padding: 4px 0 1px 8px;">
                        {{ strtoupper($customerDetail->name) }}
                    </h4>
                    @if (isset($billingAddress))
                        <p class="address header-contents-font-size">
                            @if ($billingAddress->address_1 != null)
                                {{ strtoupper($billingAddress->address_1) }}
                            @endif
                            @if ($billingAddress->address_2 != null)
                                {{ strtoupper($billingAddress->address_2) }},<br>
                            @endif
                            {{ $billingAddress->city_id != null ? strtoupper(getCityName($billingAddress->city_id)) . ',' : null }}
                            {{ $billingAddress->state_id != null ? strtoupper(getStateName($billingAddress->state_id)) . ',' : null }}
                            {{ strtoupper(getCountryName($billingAddress->country_id ?? null)) }},
                            {{ $billingAddress->pin_code ?? null }}
                        </p>
                    @endif
                    <p class="phone header-contents-font-size">
                        @if (!empty($transaction->party_phone_number))
                            Contact No:
                                +{{ $transaction->region_code ?? '' }} {{ $transaction->party_phone_number ?? null }}
                        @elseif (!empty($customerDetail->model->phone_1))
                            Contact No:
                                +{{ $customerDetail->model->region_code_1 ?? '' }} {{ $customerDetail->model->phone_1 ?? null }}
                        @endif
                        @if (!empty($customerDetail->model->phone_2) && ($customerDetail->model->phone_1 !== $customerDetail->model->phone_2))
                            {{ empty($customerDetail->model->phone_1) && empty($transaction->party_phone_number) ? 'Contact No:' : "|" }}
                            <span class="whitespace-nowrap header-contents-font-size">
                                +{{ $customerDetail->model->region_code_2 ?? '' }} {{ $customerDetail->model->phone_2 ?? null }}
                            </span>
                        @endif
                    </p>
                    @if (!empty($customerDetail->model->person_email))
                        <p class="header-contents-font-size" style="padding: 2px 0px 0px 8px;">
                            Email: {{ $customerDetail->model->person_email ?? null }}
                        </p>
                    @endif
                    @if ($showGst)
                        <p class="header-contents-font-size" style="padding: 2px 0px 0px 8px">
                            GSTIN: {{ $transaction->gstin ?? $customerDetail->model->gstin ?? null }}
                        </p>
                    @endif
                    @if (! empty($panNumber) && $showPanNumber)
                        <p class="header-contents-font-size" style="padding: 2px 0px 0px 8px">
                            PAN: {{ $panNumber ?? null }}
                        </p>
                    @endif
                </td>
                @if ($invoiceSetting['estimate_ship_to_details'] ?? true)
                    <td class="{{ ($invoiceSetting['estimate_dispatch_from_details'] ?? false) ? 'border-right' : '' }} vertical-top" style="width:33.33%;">
                        <p class="text-primary border-bottom border-top fw-6 table-heading header-labels-font-size">
                            {{ $changeLabel['estimate_ship_to'] ?? 'Ship to' }}:
                        </p>
                        <h4 class="fw-6 header-contents-font-size" style="padding: 4px 0 1px 8px;">
                            {{ strtoupper($transaction->shipping_name ?? $customerDetail->name) }}
                        </h4>
                        <p class="address header-contents-font-size">
                            @if (isset($shippingAddress->address_1))
                                {{ strtoupper($shippingAddress->address_1) }},
                            @endif
                            @if (isset($shippingAddress->address_2))
                                {{ strtoupper($shippingAddress->address_2) }},
                            @endif
                            {{ isset($shippingAddress->city_id) ? strtoupper(getCityName($shippingAddress->city_id)) . ',' : '' }}
                            {{ isset($shippingAddress->state_id) ? strtoupper(getStateName($shippingAddress->state_id)) . ',' : '' }}
                            {{ isset($shippingAddress->country_id) ? strtoupper(getCountryName($shippingAddress->country_id)) : '' }},
                            {{ $shippingAddress->pin_code ?? null }}
                        </p>
                        <p class="phone header-contents-font-size">
                            @if (!empty($transaction->party_phone_number))
                                Contact No:
                                    +{{ $transaction->region_code ?? '' }} {{ $transaction->party_phone_number ?? null }}
                            @endif
                        </p>
                        @if ($isCompanyGstApplicable && !empty($transaction->shipping_gstin))
                            <p class="header-contents-font-size" style="padding: 2px 0px 0px 8px">
                                GSTIN: {{ $transaction->shipping_gstin ?? null }}
                            </p>
                        @endif
                        @if (! empty($panNumber) && $showPanNumber)
                            <p class="header-contents-font-size" style="padding: 0px 0px 2px 8px">
                                PAN: {{ $panNumber ?? null }}
                            </p>
                        @endif
                    </td>
                @endif
                    <td class="vertical-top border-left" style="width: 33.33%">
                        <p class="text-primary border-bottom border-top fw-6 table-heading header-labels-font-size">
                            {{ $changeLabel['estimate_invoice_details'] ?? 'Estimate Details' }}:
                        </p>
                        <div style="padding: 0 8px !important">
                        <table class="table">
                            <tr>
                                <td class="header-contents-font-size fw-6" style="padding: 4px 1px 1px 1px">
                                    {{ $changeLabel['estimate_invoice_number'] ?? 'Estimate No' }}:
                                </td>
                                <td class="header-contents-font-size text-end" style="padding: 4px 1px 1px 1px">
                                    {{ $transaction->document_number }}
                                </td>
                            </tr>
                            <tr>
                                <td class="header-contents-font-size fw-6" style="padding: 4px 1px 1px 1px">
                                    {{ $changeLabel['estimate_invoice_date'] ?? 'Estimate Date' }}:
                                </td>
                                <td class="header-contents-font-size text-end" style="padding: 4px 1px 1px 1px">
                                    {{ Carbon\Carbon::parse($transaction->document_date)->format('d-m-Y') }}
                                </td>
                            </tr>
                            @if ($invoiceSetting['estimate_po_number'] ?? true)
                                <tr>
                                    <td class="header-contents-font-size fw-6" style="padding: 4px 1px 1px 1px">
                                        {{ $changeLabel['estimate_po_number_label'] ?? 'PO No' }}:
                                    </td>
                                    <td class="header-contents-font-size text-end" style="padding: 4px 1px 1px 1px">
                                        {{ $transaction->po_no }}
                                    </td>
                                </tr>
                                @endif
                            @if ($invoiceSetting['show_estimate_po_date'] ?? true)
                                <tr>
                                    <td class="header-contents-font-size fw-6"style="padding: 4px 1px 1px 1px">
                                        {{ $changeLabel['estimate_po_date'] ?? 'PO Date' }}:
                                    </td>
                                    <td class="header-contents-font-size text-end" style="padding: 4px 1px 1px 1px">
                                        {{ isset($transaction->po_date) ? \Carbon\Carbon::parse($transaction->po_date)->format('d-m-Y') : null }}
                                    </td>
                                </tr>
                            @endif
                            <tr>
                                <td class="header-contents-font-size fw-6" style="padding: 4px 1px 4px 1px">
                                    Valid For:
                                </td>
                                <td class="header-contents-font-size text-end" style="padding: 4px 1px 4px 1px">
                                    {{ $validFor }} {{ !empty($transaction->valid_till_date) ? ' ('.Carbon\Carbon::parse($transaction->valid_till_date)->format('d-m-Y').')' : '' }}
                                </td>
                            </tr>
                        </table>
                        </div>
                    </td>
            </tr>
        </table>
        {{-- Bill To / Ship to / Invoice Details Section End --}}

        {{-- Transport Details / EInvoice / EWay Section Start --}}
            <table cellpadding="0">
                <tr class="border-bottom ">
                    @if (isset($transaction['dispatch_address_id']) && isset($dispatchAddress) && (($invoiceSetting['estimate_dispatch_from_details'] ?? false)))
                    <td class="vertical-top border-right border-bottom" style="width: 33.33%">
                        <p class="text-primary border-bottom fw-6 table-heading header-labels-font-size">
                            {{ $changeLabel['dispatch_from'] ?? 'Dispatch from' }}:
                        </p>
                        <p class="address header-contents-font-size" style="margin-top: 0.5rem ;">
                            {{ isset($dispatchAddress->address_1) ? strtoupper($dispatchAddress->address_1 .',') : null }}
                            {{ isset($dispatchAddress->address_2) ? strtoupper($dispatchAddress->address_2 .',') : null }}
                            {{ isset($dispatchAddress->city_id) ?  strtoupper(getCityName($dispatchAddress->city_id).',') : null }}
                            {{ isset($dispatchAddress->state_id) ? strtoupper(getStateName($dispatchAddress->state_id).',') : null }}
                            {{ isset($dispatchAddress->country_id) ? strtoupper(getCountryName($dispatchAddress->country_id).',') : null }}
                            {{ $dispatchAddress->pin_code ?? null }}
                        </p>
                    </td>
                    @endif
                    @if ($invoiceSetting['estimate_transport_details'] ?? true)
                        <td class="vertical-top" style="width:33.33%;" >
                            <table style="display: table">
                                <tr>
                                    <td class="header-contents-font-size fw-6" style="padding: 3px 0 0 8px;">
                                        {{ $changeLabel['estimate_transport_name'] ?? 'Transport Name' }}:
                                    </td>
                                    <td style="padding: 3px 0 0 0px;"  class="header-contents-font-size">
                                        {{ $transaction->transportDetails->transporter_name ?? '' }}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="header-contents-font-size" style="padding: 3px 0 0 8px;font-weight: bold;">
                                       {{ $changeLabel['estimate_document_no'] ?? 'Document No' }}:
                                    </td>
                                    <td style="padding: 3px 0 0 0px;" class="header-contents-font-size">
                                        {{ $transaction->transporter_document_number ?? '' }}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="header-contents-font-size" style="padding: 3px 0 0 8px;font-weight: bold;">
                                        {{ $changeLabel['estimate_document_date'] ?? 'Document Date' }}:
                                    </td>
                                    <td style="padding: 3px 0 0 0px;" class="header-contents-font-size">
                                        {{ !empty($transaction->transporter_document_date) ? \Carbon\Carbon::parse($transaction->transporter_document_date)->format('d-m-Y') : '' }}
                                    </td>
                                </tr>
                                @if(!empty($transaction->transporter_vehicle_number))
                                    <tr>
                                        <td class="header-contents-font-size vertical-top" style="padding: 3px 0 4px 8px;font-weight: bold;">
                                            {{ $changeLabel['estimate_transport_vehicle_number'] ?? 'Vehicle Number' }}:
                                        </td>
                                        <td class="header-contents-font-size vertical-top" style="padding: 3px 0 4px 0px;">
                                            {{ $transaction->transporter_vehicle_number ?? '' }}
                                        </td>
                                    </tr>
                                @endif
                            </table>
                        </td>
                    @endif
                </tr>
            </table>
        {{-- Transport Details / EInvoice / eWay Section End --}}

        {{-- Custom Fields Section Start --}}
        @if (count($customFieldValues) > 0)
            @php
                $customFields = collect($customFieldValues)->where('is_show_in_print', true)->values();
            @endphp
            <table cellpadding="0" class="item-table">
                @foreach ($customFields->chunk(3) as $chunk)
                    <tr class="border-bottom">
                        @foreach ($chunk as $customField)
                            <td class="fs-13 {{ $loop->last ? '' : 'border-right' }}" style="padding: 6px 8px; width:150px;">
                                <span style="font-weight: bold;">{{ $customField['label_name'] ?? '' }}</span> : {{ $customField['value'] ?? '' }}
                            </td>
                        @endforeach
                    </tr>
                @endforeach
            </table>
        @endif
        {{-- Custom Fields Section End --}}
        <table cellpadding="0" style="flex-grow:1;" class="item-table">
            <tr class="border-bottom">
                @foreach ($rearrangeItems['headings'] as $headings)
                    @if($headings['is_show_in_print'])
                        <td class="table-headings-font-size {{ ($loop->index === $loop->count - 2) ? '' : 'border-right' }} whitespace-nowrap  {{ $headings['class'] }}"
                            style="padding: 6px 8px; font-weight: bold;">
                            {{ $headings['name'] }}
                        </td>
                    @endif
                @endforeach
            </tr>
            @foreach ($rearrangeItems['detail'] as $items)
                <tr>
                    @foreach ($items as  $key => $item)
                        @if($item['is_show_in_print'])
                            <td class="table-contents-font-size {{ ($loop->index === $loop->count - 2) ? '' : 'border-right' }} {{ ($key == 'item_name') ? 'w-100' : 'text-center'}}"
                                style="padding: 4px 8px 0 8px; {{ ($key == 'primary_quantity' || $key == 'secondary_quantity') ? 'white-space: nowrap;' : '' }} ">
                                {{ $item['value'] }}
                                @if($key == 'item_name')
                                    @if($item['show_sku'])
                                        <p class="description-font-size">Item Code:
                                            {{ $item['sku'] ?? null }}</p>
                                    @endif
                                    @if($item['show_consolidating_items'])
                                        <p style="word-break: break-word; " class="description-font-size">
                                            {!! $item['consolidating_items'] !!}
                                        </p>
                                    @endif
                                    @if($item['show_additional_description'])
                                        <p style="word-break: break-word;" class="description-font-size">
                                            {!! $item['additional_description'] !!}
                                        </p>
                                    @endif
                                    @if (count($item['customItemsInventoryValues']) > 0)
                                        @foreach ($item['customItemsInventoryValues'] as $customItemsInventoryValue)
                                            <p style="word-break: break-word; font-style: italic; color: #888888;" class="description-font-size">{{ $customItemsInventoryValue }}</p>
                                        @endforeach
                                    @endif
                                    @if($item['show_item_image'])
                                        <div><img src="{{ $item['item_image'] }}" width="60"  height="60" style="margin-top: 4px"></div>
                                    @endif
                                @endif
                            </td>
                        @endif
                    @endforeach
                </tr>
            @endforeach
            <tr class="border-bottom">
                @foreach ($rearrangeItems['footer'] as $key => $footer)
                        @if($footer['is_show_in_print'])
                            <td class="table-headings-font-size {{ ($loop->index === $loop->count - 2) ? '' : 'border-right ' }}  fw-6 {{ ($key == 'item_name') ? 'min-width-150 ps-2' : 'text-center'}}"
                            style="">{{ $footer['value'] }}</td>
                        @endif
                @endforeach
            </tr>
        </table>

        {{-- Bank / Terms Of Payment / Broker Section Start --}}
        <table cellpadding="0" style="page-break-inside: avoid !important">
            <tr class="border-bottom">
                <td class=" {{ ((isset($invoiceSetting['estimate_qr_code']) ? $invoiceSetting['estimate_qr_code'] : false) && isset($bankDetail->upi_id)) || (isset($invoiceSetting['estimate_bank_details']) ? $invoiceSetting['estimate_bank_details'] : true)  ? 'border-right' : ''}}">
                    @if (((isset($invoiceSetting['estimate_qr_code']) ? $invoiceSetting['estimate_qr_code'] : false) && isset($bankDetail->upi_id)) || (isset($invoiceSetting['estimate_bank_details']) ? $invoiceSetting['estimate_bank_details'] : true))
                        <table>
                            <tr>
                            <td>
                                <div class=""
                                    style="display: flex;justify-content: space-between; padding-top: 4px; padding-bottom: 4px; height: 100%;">
                                    <div class="">
                                        <table style="white-space: nowrap">
                                            <tr>
                                                <td class="footer-headings-font-size fw-6" style=" padding: 2px 8px 2px 8px;">
                                                    Bank:
                                                </td>
                                                <td class="footer-contents-font-size"
                                                    style="padding: 2px 8px 2px 0px; white-space: normal">
                                                    {{ !empty($bankDetail) ? $bankDetail->bank_name : null }}
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="footer-headings-font-size fw-6" style=" padding: 2px 8px 2px 8px;">
                                                    IFSC Code:
                                                </td>
                                                <td style="padding: 2px 8px 2px 0px; " class="footer-contents-font-size">
                                                    {{ !empty($bankDetail) ? $bankDetail->ifsc_code : null }}
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="footer-headings-font-size fw-6" style="padding: 2px 8px 2px 8px;">
                                                    A/C Number:
                                                </td>
                                                <td style="padding: 2px 8px 2px 0px; " class="footer-contents-font-size">
                                                    {{ $accountNumber }}
                                                </td>
                                            </tr>
                                            @if (!empty($bankDetail) && $bankDetail->swift_code != null)
                                                <tr>
                                                    <td class="footer-headings-font-size fw-6" style="padding: 2px 8px 2px 8px;">
                                                        Swift Code:
                                                    </td>
                                                    <td style="padding: 2px 8px 2px 0px; " class="footer-contents-font-size">
                                                        {{ $bankDetail->swift_code }}
                                                    </td>
                                                </tr>
                                            @endif
                                            @if (isset($bankDetail->account_holder_name))
                                                <tr>
                                                    <td class="footer-headings-font-size fw-6" style="padding: 2px 8px 2px 8px;">
                                                        A/C Name:
                                                    </td>
                                                    <td style="padding: 2px 8px 2px 0px; " class="footer-contents-font-size">
                                                        {{ $bankDetail->account_holder_name }}
                                                    </td>
                                                </tr>
                                            @endif
                                            <tr>
                                                <td class="footer-headings-font-size fw-6" style="padding: 2px 8px 2px 8px;">
                                                    Bank Branch:
                                                </td>
                                                <td
                                                    style="padding: 2px 8px 2px 0px; white-space: normal" class="footer-contents-font-size">
                                                    {{ $branchName }}
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="padding: 2px 8px 2px 0px; " class="footer-contents-font-size"></td>
                                            </tr>
                                        </table>
                                    </div>
                                    @if ((isset($invoiceSetting['estimate_qr_code']) && $invoiceSetting['estimate_qr_code'] == 1 ? true : false) && isset($bankDetail->upi_id))
                                        <div class="qr-code"
                                            style="margin-top: 10px; padding-right: 10px; white-space: nowrap;">
                                            <img src="data:image/svg+xml;base64,{{ base64_encode(generateUPIQRCode($bankDetail->upi_id, $transaction->due_amount)) }}"
                                                width="75" height="75" />
                                            <p style="margin-top: 8px;" class="footer-contents-font-size">
                                                {{ $bankDetail->upi_id }}
                                            </p>
                                        </div>
                                    @endif
                                </div>
                            </td>
                            </tr>
                        </table>
                    @endif
                </td>
                @if(!empty($creditPeriod) || ($invoiceSetting['estimate_broker_details'] ?? true))
                    <td style="{{ (((($invoiceSetting['show_estimate_credit_period'] ?? true) || ($invoiceSetting['show_estimate_due_date'] ?? true)) && (!empty($creditPeriod))) || ($invoiceSetting['estimate_broker_details'] ?? true))  ? '' : ''  }}">
                        @if(($invoiceSetting['show_estimate_credit_period'] ?? true) || ($invoiceSetting['show_estimate_due_date'] ?? true))
                            <div style="min-height: 60px">
                                <table>
                                    @if($invoiceSetting['show_estimate_credit_period'] ?? true)
                                        <tr>
                                            <td class="header-contents-font-size whitespace-nowrap vertical-bottom fw-6"
                                                style="padding: 5px 8px 0px 8px">
                                                {{ $changeLabel['estimate_credit_period'] ?? 'Credit Period' }}:
                                            </td>
                                            <td class="header-contents-font-size whitespace-nowrap vertical-bottom" style="padding: 5px 8px 0px 0px">
                                                {{ $creditPeriod }}
                                            </td>
                                        </tr>
                                    @endif
                                    @if($invoiceSetting['show_estimate_due_date'] ?? true)
                                        <tr>
                                            <td class="header-contents-font-size whitespace-nowrap vertical-bottom fw-6"
                                                style="padding: 5px 8px 0px 8px">
                                                {{ $changeLabel['estimate_due_date'] ?? 'Due Date' }}:
                                            </td>
                                            <td class="header-contents-font-size whitespace-nowrap vertical-bottom" style="padding: 5px 8px 0px 0px">
                                                {{ $dueDate }}
                                            </td>
                                        </tr>
                                    @endif
                                </table>
                            </div>
                        @endif
                        <div>
                            @if ($invoiceSetting['estimate_broker_details'] ?? true)
                                <table>
                                    <tr class="vertical-top {{ ($invoiceSetting['show_estimate_credit_period'] ?? true) || ($invoiceSetting['show_estimate_due_date'] ?? true) ? 'border-top' : '' }}">
                                        <td class="header-contents-font-size vertical-top fw-6" style="padding: 5px 8px 0px 8px">
                                            Broker:
                                        </td>
                                        <td class="header-contents-font-size vertical-bottom"
                                            style="padding: 5px 8px 0px 0px white-space: normal">

                                            {{ $transaction->brokerDetails->broker_name ?? '' }}
                                        </td>
                                    </tr>
                                    @if($isCompanyGstApplicable)
                                    <tr>
                                        <td class="header-contents-font-size whitespace-nowrap fw-6" style="padding: 3px 8px 1px 8px">
                                            GSTIN:
                                        </td>
                                        <td class="header-contents-font-size" style="padding: 3px 8px 1px 0px;white-space: nowrap;">
                                            {{ $transaction->brokerDetails->gstin ?? '' }}
                                        </td>
                                    </tr>
                                    @endif
                                </table>
                            @endif
                        </div>
                    </td>
                @endif
                <td class="vertical-top" style="width: 200px; border-left: 1px solid black">
                    <table>
                        @foreach ($additionalCharges as $additionalCharge)
                            <tr class="{{ $loop->last ? 'border-bottom' : '' }}">
                                <td class="table-headings-font-size vertical-top" style="padding: 4px 8px 2px 8px;">{{ $additionalCharge['ledger_name'] }}</td>
                                <td class="table-headings-font-size vertical-top text-end" style="padding: 4px 8px 2px 8px;">{{ $pdfSymbol.getCurrencyFormat($additionalCharge['amount'] ?? '0.0') }}</td>
                            </tr>
                        @endforeach
                        <tr>
                            <td class="table-headings-font-size vertical-top fw-6" style="padding: 4px 8px 2px 8px">
                                {{ $isCompanyGstApplicable ? $changeLabel['estimate_sub_total'] ?? 'Taxable Value' : $changeLabel['estimate_sub_total'] ?? 'Sub Total' }}:
                            </td>
                            <td class="table-headings-font-size vertical-top text-end fw-6" style="padding: 4px 8px 2px 8px">
                                {{ $pdfSymbol . getCurrencyFormat(round($transaction->taxable_value, getCompanyFixedDigitNumber())) }}
                            </td>
                        </tr>
                        @if ($isCompanyGstApplicable)
                            <tr>
                                <td class="table-headings-font-size vertical-top fw-6" style="padding: 3px 8px 2px 8px">
                                    {{ $changeLabel['estimate_cgst'] ?? 'CGST' }}:
                                </td>
                                <td class="table-headings-font-size vertical-top text-end fw-6" style="padding: 3px 8px 2px 8px">
                                    {{ $pdfSymbol.getCurrencyFormat(round($transaction->cgst, getCompanyFixedDigitNumber()) ?? '0.0') }}
                                </td>
                            </tr>
                            <tr>
                                <td class="table-headings-font-size vertical-top fw-6" style="padding: 3px 8px 2px 8px">
                                    {{ $changeLabel['estimate_sgst'] ?? 'SGST' }}:
                                </td>
                                <td class="table-headings-font-size vertical-top text-end fw-6" style="padding: 3px 8px 2px 8px">
                                    {{ $pdfSymbol.getCurrencyFormat(round($transaction->sgst, getCompanyFixedDigitNumber()) ?? '0.0') }}
                                </td>
                            </tr>
                            <tr>
                                <td class="table-headings-font-size vertical-top fw-6" style="padding: 3px 8px 2px 8px">
                                    {{ $changeLabel['estimate_igst'] ?? 'IGST' }}:
                                </td>
                                <td class="table-headings-font-size vertical-top text-end fw-6" style="padding: 3px 8px 2px 8px">
                                    {{ $pdfSymbol.getCurrencyFormat(round($transaction->igst, getCompanyFixedDigitNumber()) ?? '0.0') }}
                                </td>
                            </tr>
                        @endif
                        @if ($transaction->tcs_amount != 0)
                            <tr>
                                <td class="table-headings-font-size vertical-top fw-6" style="padding: 3px 8px 2px 8px">
                                    {{ $changeLabel['estimate_tcs'] ?? 'TCS' }}:
                                </td>
                                <td class="table-headings-font-size vertical-top text-end fw-6" style="padding: 3px 8px 2px 8px">
                                    {{ $pdfSymbol.getCurrencyFormat($transaction->tcs_amount ?? '0.0') }}
                                </td>
                            </tr>
                        @endif
                        @if ($transaction->cess != 0)
                            <tr>
                                <td class="table-headings-font-size vertical-top fw-6" style="padding: 3px 8px 2px 8px">
                                    {{ $changeLabel['estimate_cess'] ?? 'Cess' }}:
                                </td>
                                <td class="table-headings-font-size vertical-top text-end fw-6" style="padding: 3px 8px 2px 8px">
                                    {{ $pdfSymbol.getCurrencyFormat($transaction->cess ?? '0.0') }}
                                </td>
                            </tr>
                        @endif
                        <tr>
                            <td class="table-headings-font-size vertical-top fw-6" style="padding: 3px 8px 4px 8px">
                                {{ $changeLabel['estimate_round_off'] ?? 'Round off' }}:
                            </td>
                            <td class="table-headings-font-size vertical-top text-end fw-6" style="padding: 3px 8px 4px 8px">
                                {{ $pdfSymbol.getCurrencyFormat($transaction->round_off_amount ?? '0.0') }}
                            </td>
                        </tr>
                        @php
                            if (empty($addLess)) {
                                $total = $transaction->grand_total;
                            } else {
                                $addLessSum = array_sum(array_column($addLess, 'amount'));
                                $total =  $transaction->grand_total - $addLessSum;
                                $addLessSumTotal = collect($addLess)->where('is_show_in_print',1)->sum('amount');
                                $total = $total + $addLessSumTotal;
                            }
                        @endphp
                        @foreach (collect($addLess)->where('is_show_in_print',1) as $addLessItem)
                            <tr>
                                <td class="table-headings-font-size vertical-top"
                                    style="padding: 3px 8px 4px 8px;">
                                    {{ $addLessItem['ledger_name'] }}
                                </td>
                                <td class="table-headings-font-size vertical-top text-end"
                                    style="padding: 3px 8px 4px 8px;">
                                    {{ $pdfSymbol.getCurrencyFormat($addLessItem['amount'] ?? '0.0') }}
                                </td>
                            </tr>
                        @endforeach
                    </table>
                </td>
            </tr>
        </table>
        {{-- Bank / Terms Of Payment / Broker Section End --}}

        {{-- Total Section Start --}}
        <table cellpadding="0">
            <tr class="border-bottom">
                @if($showPrintSettings['show_estimate_in_words'] ?? true)
                <td class="border-right vertical-middle">
                    <p class="table-contents-font-size fw-6" style="display: flex; padding: 6px 3px 6px 8px">
                       {{ $changeLabel['estimate_in_words'] ?? 'In Words' }}:
                        <span class="table-contents-font-size" style="margin-left: 3px; font-weight: 400">
                            {{ getAmountToWord($transaction->grand_total ?? '0.0') }} Only
                        </span>
                    </p>
                </td>
                @endif
                <td style="max-width: 200px">
                    <table class="vertical-bottom">
                        <tr>
                            <td class="text-primary total-font-size" style="padding: 6px 8px; font-weight: bold;">
                                {{ $changeLabel['estimate_total'] ?? 'Total' }}:
                            </td>
                            <td class="text-primary text-end total-font-size"
                                style="padding: 6px 8px; font-weight: bold;">
                                {{ $pdfSymbol.getCurrencyFormat($transaction->grand_total ?? '0.0') }}
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
        {{-- Total Section End --}}

        {{-- GST Details Section Start --}}
            @if ($isCompanyGstApplicable && !empty($checkHsnCodeExist) && ($invoiceSetting['estimate_hsn_summary'] ?? true))
            <table cellpadding="0">
                <tr class="border-bottom" style="width: 100%">
                    <td class="footer-headings-font-size border-right text-center" style="padding: 4px 8px; font-weight: bold">
                        SN
                    </td>
                    <td class="footer-headings-font-size border-right text-center" style="padding: 4px 8px; font-weight: bold">
                        HSN/SAC
                    </td>
                    <td class="footer-headings-font-size border-right text-center" style="padding: 4px 8px; font-weight: bold">
                        Taxable Amount
                    </td>
                    <td class="footer-headings-font-size border-right text-center" style="padding: 4px 8px; font-weight: bold">
                        GST (%)
                    </td>
                    @if ($cgst != 0.0)
                        <td class="footer-headings-font-size border-right text-center" style="padding: 4px 8px; font-weight: bold">
                            CGST
                        </td>
                    @endif
                    @if ($sgst != 0.0)
                        <td class="footer-headings-font-size border-right text-center" style="padding: 4px 8px; font-weight: bold">
                            SGST
                        </td>
                    @endif
                    @if ($igst != 0.0)
                        <td class="footer-headings-font-size border-right text-center" style="padding: 4px 8px; font-weight: bold">
                            IGST
                        </td>
                    @endif
                    <td class="footer-headings-font-size text-center" style="padding: 4px 8px; font-weight: bold">
                        Total Tax
                    </td>
                </tr>
                @php
                    $uniquekey = 1;
                @endphp
                @foreach ($checkHsnCodeExist as $key => $item)
                    @foreach ($item as $hsnCode => $data)
                        <tr>
                            <td class="footer-contents-font-size border-right text-center" style="padding: 2px 8px">
                                {{ $uniquekey++ }}
                            </td>
                            <td class="footer-contents-font-size border-right text-center" style="padding: 2px 8px">
                                {{ !empty($hsnCode) ? $hsnCode : '-' }}
                            </td>
                            <td class="footer-contents-font-size border-right text-center" style="padding: 2px 8px">
                                {{ $pdfSymbol.getCurrencyFormat(round($checkTAXtExist[$key]['taxableValue'][$hsnCode], getCompanyFixedDigitNumber()) ?? 0) }}
                            </td>
                            <td class="footer-contents-font-size border-right text-center" style="padding: 2px 8px">
                                {{ !empty($key) ? $key : '-' }}
                            </td>
                            @if ($cgst != 0.0)
                                <td class="footer-contents-font-size border-right text-center" style="padding: 2px 8px">
                                    {{ $pdfSymbol.getCurrencyFormat(round($checkTAXtExist[$key]['cgst'][$hsnCode] ?? 0, getCompanyFixedDigitNumber())) }}
                                </td>
                            @endif
                            @if ($sgst != 0.0)
                                <td class="footer-contents-font-size border-right text-center"style="padding: 2px 8px">
                                    {{ $pdfSymbol.getCurrencyFormat(round($checkTAXtExist[$key]['sgst'][$hsnCode] ?? 0, getCompanyFixedDigitNumber())) }}
                                </td>
                            @endif
                            @if ($igst != 0.0)
                                <td class="footer-contents-font-size border-right text-center"style="padding: 2px 8px">
                                    {{ $pdfSymbol.getCurrencyFormat(round($checkTAXtExist[$key]['igst'][$hsnCode] ?? 0, getCompanyFixedDigitNumber())) }}
                                </td>
                            @endif
                            @php
                                $totalTax =
                                    round($checkTAXtExist[$key]['cgst'][$hsnCode], getCompanyFixedDigitNumber()) +
                                    round($checkTAXtExist[$key]['sgst'][$hsnCode], getCompanyFixedDigitNumber()) +
                                    round($checkTAXtExist[$key]['igst'][$hsnCode], getCompanyFixedDigitNumber());
                            @endphp
                            <td class="footer-contents-font-size text-center" style="padding: 2px 8px">
                                {{ $pdfSymbol.getCurrencyFormat(round($totalTax ?? 0, getCompanyFixedDigitNumber())) }}
                            </td>
                        </tr>
                    @endforeach
                @endforeach
                <tr class="footer-headings-font-size border-bottom border-top fw-6" style="padding: 2px 8px">
                    <td class="footer-headings-font-size border-right text-center fw-6" style="padding: 4px 8px"></td>
                    <td class="footer-headings-font-size border-right text-center fw-6" style="padding: 4px 8px">
                        Total
                    </td>
                    <td class="footer-headings-font-size border-right text-center fw-6" style="padding: 4px 8px">
                        {{ $pdfSymbol . getCurrencyFormat(round($transaction->taxable_value, getCompanyFixedDigitNumber())) }}
                    </td>
                    <td class="footer-headings-font-size border-right text-center fw-6" style="padding: 4px 8px"></td>
                    @if ($cgst != 0.0)
                        <td class="footer-headings-font-size border-right text-center fw-6" style="padding: 4px 8px">
                            {{ $pdfSymbol.getCurrencyFormat(round($cgst, getCompanyFixedDigitNumber())) }}
                        </td>
                    @endif
                    @if ($sgst != 0.0)
                        <td class="footer-headings-font-size border-right text-center fw-6" style="padding: 4px 8px">
                            {{ $pdfSymbol.getCurrencyFormat(round($sgst, getCompanyFixedDigitNumber())) }}
                        </td>
                    @endif
                    @if ($igst != 0.0)
                        <td class="footer-headings-font-size border-right text-center fw-6" style="padding: 4px 8px">
                            {{ $pdfSymbol.getCurrencyFormat(round($igst, getCompanyFixedDigitNumber())) }}
                        </td>
                    @endif
                    <td class="footer-headings-font-size text-center fw-6" style="padding: 4px 8px">
                        @php
                            $grandTotalTax = $cgst + $sgst + $igst;
                        @endphp
                        {{ $pdfSymbol.getCurrencyFormat(round($grandTotalTax, getCompanyFixedDigitNumber())) }}
                    </td>
                </tr>
            </table>
            @endif
        {{-- GST Details Section End --}}

        {{-- Term and Condition / Narration / Signature Section Start --}}
        <table cellpadding="0" style="page-break-inside: avoid !important">
            <tr>
                @if ($transaction->term_and_condition || $transaction->narration)
                <td class="vertical-top border-right">
                    @if($showPrintSettings['show_estimate_terms_and_conditions'] ?? true)
                        @if ($transaction->term_and_condition)
                            <div class="">
                                <h4 class="terms-and-conditions-font-size fw-6" style="padding: 4px 8px">
                                    {{ $changeLabel['estimate_terms_and_conditions'] ?? 'Terms and Conditions' }}:
                                </h4>
                                <div style="padding: 4px 8px" class="terms-and-conditions-font-size">
                                    <p class="terms-and-conditions-font-size">
                                        {!! nl2br($transaction->term_and_condition) !!}
                                    </p>
                                </div>
                            </div>
                        @endif
                    @endif
                    @if($showPrintSettings['show_estimate_narration'] ?? true)
                        @if ($transaction->narration)
                            <div class="">
                                <h4 class="note-font-size {{ $transaction->term_and_condition ? 'border-top' : '' }} fw-6" style="padding: 4px 8px">
                                    {{ $changeLabel['estimate_narration'] ?? 'Note' }}:
                                </h4>
                                <div style="padding: 4px 8px">
                                    <p class="note-font-size">
                                        {!! nl2br($transaction->narration) !!}
                                    </p>
                                </div>
                            </div>
                        @endif
                    @endif
                </td>
                @endif
                @if(($showPrintSettings['show_estimate_authorized_signatory'] ?? true) || ($invoiceSetting['estimate_signature'] ?? true))
                    <td class="vertical-bottom" style="width: 217px; position: relative">
                        <div style="padding:4px 8px; margin-left:auto;">
                            @if ($showPrintSettings['show_estimate_authorized_signatory'] ?? true)
                            <p class="footer-contents-font-size fw-6 text-end" style="margin-top:10px;">
                                For, {{ strtoupper($currentCompany->trade_name) }}
                            </p>
                            @endif
                            <div class="text-end signature">
                                @if (isset($invoiceSetting['estimate_signature']) && $invoiceSetting['estimate_signature'] && ($currentCompany->company_signature != asset('images/preview-img.png')))
                                    <img src="{{ $currentCompany->company_signature ?? null }}" alt="company-img" style="max-width:100%; height:100%;  object-fit:contain; margin-left:auto;">
                                @endif
                            </div>
                            @if ($showPrintSettings['show_estimate_authorized_signatory'] ?? true)
                            <p class="verical-bottom text-end footer-contents-font-size" >
                                {{ $changeLabel['estimate_authorized_signatory'] ?? 'Authorized Signatory' }}
                            </p>
                            @endif
                        </div>
                    </td>
                @endif
            </tr>
        </table>
        {{-- Term and Condition / Narration / Signature Section End --}}
    </div>
</body>
</html>
