import CountryData from "../data/country.json";
import { customToFixed, formattedDate, setGSTCalculationType } from "./calculation";
import { ADVANCE_PAYMENT_TRANSACTION_TYPE, GST_TYPES_ID, ITEM_STATUS, TRANSACTION_TYPE } from "../constants";
import { rearrangeItemList } from "../store/configuration/configurationSlice";
import { formattedCustomField, isCurrentFinancialYear, PrepareForSoldQty } from "./sharedFunction";

export const findGstRate = (items, gstOptions) => {
    const rate = items?.map(item => {
        const gstRate = item.gst && gstOptions.find(option => option.value == item.gst_id);
        return {
            rate: gstRate?.rate || 0,
            total: item.total || 0,
        };
    });
    return {
        rate: rate,
    };
};

const clearItemInvoice = (setItems) => {
    return setItems([
        {
            id: 1,
            selectedItem: null,
            additional_description: "",
            mrp: 0,
            selectedLedger: null,
            quantity: 0,
            free_quantity: 0,
            multiQuantity: [0, 0, 0, 0],
            rateWithGst: 0,
            updatedRateWithGst: 0,
            rateWithoutGst: 0,
            updatedRateWithoutGst: 0,
            gst_id: 0,
            gst: 0,
            gst_name: "",
            gst_copy: 0,
            discountType: 1,
            discountValue: 0,
            discountType_2: 1,
            discountValue_2: 0,
            total: 0,
            stock: 0,
            selectedUnit: null,
            cgstValue: 0,
            igstValue: 0,
            sgstValue: 0,
            shippingValue: 0,
            cessValue: 0,
            cessRate: 0,
            with_tax: 0,
            rpu: 0,
            itemUnitOption: null,
            isShowDelete: false,
            conversationRate: null,
            secondaryUnitOfMeasurement: null,
            decimal_places: 2,
            decimal_places_for_rate: 2,
            sku: "",
            itemType: 1,
            hsn_code: "",
        },
    ]);
};
const clearAccountingInvoice = (setAccountingItems) => {
    return setAccountingItems([
        {
            id: 1,
            ledger: "",
            selectedLedger: "",
            amountWithGst: 0,
            amountWithoutGst: 0,
            updatedAmountWithGst: 0,
            updatedAmountWithoutGst: 0,
            discountType: 1,
            discountType_2: 1,
            discountValue: 0,
            discountValue_2: 0,
            gst_id: 0,
            gst: 0,
            gst_name: "",
            total: 0,
            cgstValue: 0,
            igstValue: 0,
            sgstValue: 0,
            updatedTotal: 0,
            additional_description: "",
            with_tax: 0,
            rpu: 0,
            isShowDelete: false,
            decimal_places: 2,
            decimal_places_for_rate: 2,
            is_status: ITEM_STATUS.IN_ACTIVE,
        },
    ]);
};

const matchEstimateChallanIds = items => {
    return items?.split(",") || [];
};

const formatAdvancePaymentDetail = (advance_payment) => {
    return advance_payment?.map((payment)=>{
        return {
            ...payment,
            received_amount: payment.adjusted_amount,
        }
    })
}

const formatDeliveryChallanList = (challanNos, challanList) => {
    if (!challanNos || !challanList) return [];

    const challanIds = challanNos.split(",").map(id => Number(id.trim()));

    return challanList
        .filter(item => challanIds.includes(item.id)) // Filter matching IDs
        .map(item => ({
            value: item.id,
            label: item.document_number
        }));
};

const convertIntoDropdownList = items => {
    return items?.map(item => {
        return {
            label: item.document_number || item.number,
            value: item.id,
        };
    });
};

export const clearData = ({ setStateFunctions }) => {
    const {
        setItems,
        setTransporterDetail,
        setOtherDetail,
        setBrokerDetail,
        setTaxableValue,
        setMainGrandTotal,
        setFinalAmount,
        setGrandTotal,
        setGstValue,
        gstCalculation,
        setGstCalculation,
    } = setStateFunctions;
    setItems([
        {
            id: 1,
            selectedItem: null,
            additional_description: "",
            mrp: 0,
            selectedLedger: null,
            quantity: 0,
            free_quantity: 0,
            multiQuantity: [0, 0, 0, 0],
            rateWithGst: 0,
            updatedRateWithGst: 0,
            rateWithoutGst: 0,
            updatedRateWithoutGst: 0,
            gst_id: 0,
            gst: 0,
            gst_copy: 0,
            discountType: 1,
            discountValue: 0,
            discountType_2: 1,
            discountValue_2: 0,
            total: 0,
            stock: 0,
            selectedUnit: null,
            cgstValue: 0,
            igstValue: 0,
            sgstValue: 0,
            shippingValue: 0,
            cessValue: 0,
            cessRate: 0,
            with_tax: 0,
            rpu: 0,
            itemUnitOption: null,
            isShowDelete: false,
            decimal_places: 0,
            decimal_places_for_rate: 0,
            is_status: ITEM_STATUS.IN_ACTIVE,
        },
    ]);
    setGrandTotal(0);
    setTaxableValue(0);
    setMainGrandTotal(0);
    setFinalAmount(0);
    setGstValue({
        igstValue: 0,
        cgstValue: 0,
        sgstValue: 0,
    });
    setGstCalculation({
        ...gstCalculation,
        round_of_amount: 0,
    });
};

const gstNaFilter = (gstOptions, id) => {
    const is_na = gstOptions.find(option => option?.value == id);
    return is_na?.label == "NA" ? 1 : 0;
};

const getDecimalLength = (value, min = 2) => {
    const decimalPart = value?.toString()?.split(".")[1];
    return Math.max(decimalPart?.length || 0, min);
};

export const customFieldOptimization = (data) =>{
    return data?.map(item => {
        return {
            ...item,
            ...(item?.input_type === "select" &&{value: item?.option_id}),
        };
    })
}

export const prepareItemsData = (
    items,
    gstCalculation,
    setGstCalculation,
    company,
    gstOptions,
    configurationList,
    changeTax,
    isInWord
) => {
    if (!items[0]?.selectedItem) {
        return { itemList: null, is_na: null };
    }

    let all_na = true;
    const itemList = items?.map(item => {
        if(item?.selectedItem){
            const is_na = gstNaFilter(gstOptions, item?.gst_id);
            if (is_na === 0) {
                all_na = false;
            }
        const custom_field_inventory = isInWord
        ? item?.custom_field_inventory_store?.length > 0 ?
            item?.custom_field_inventory_store?.filter(
            (subArray) =>
                Array.isArray(subArray) &&
                subArray.some((obj) => obj?.quantity && obj?.value)
            )
            : null
        : (() => {
            const selectedSource =
                item?.custom_field_inventory_store?.length > 0 &&
                (item?.custom_field_inventory_store[0]?.sale_quantity ||
                item?.custom_field_inventory_store[0]?.is_selected)
                ? item?.custom_field_inventory_store
                : item?.model_select_inventory_custom_fields;

            // If model_select_inventory_custom_fields is used and it's empty, return null
            if (
                selectedSource === item?.model_select_inventory_custom_fields &&
                (!selectedSource || selectedSource.length < 1)
            ) {
                return null;
            }

            return selectedSource
                ?.filter((item) => item?.sale_quantity || item?.is_selected)
                ?.map((item) =>
                item?.fields?.map((field) => ({
                    custom_field_id: field?.custom_field_id,
                    value: field?.value,
                    quantity: item?.sale_quantity ? item?.sale_quantity : 1,
                    purchase_rate: field?.purchase_rate,
                    purchase_date: field?.purchase_date,
                }))
                );
            })();

        const newItem = {
            item_id: item?.selectedItem,
            additional_description: configurationList?.item_table_configuration
                ?.is_additional_item_description
                ? item?.additional_description
                : null,
            ledger_id: item?.selectedLedger,
            unit_id: item?.selectedUnit,
            quantity: item?.quantity,
            free_quantity: configurationList?.item_table_configuration?.is_enable_free_quantity ? item?.free_quantity : null,
            mrp: item?.mrp,
            hsn_code: item?.hsn_code,
            rpu: item?.updatedRateWithoutGst || 0,
            rpu_with_gst: item?.updatedRateWithoutGst || 0,
            rpu_without_gst: item?.updatedRateWithoutGst || 0,
            with_tax: changeTax ? 1 : 0,
            discount_type: item?.discountType || 1,
            discount_value: item?.discountValue || 0,
            discount_type_2: item?.discountType_2 || 1,
            discount_value_2: item?.discountValue_2 || 0,
            gst_id: item?.gst_id,
            gst_tax: item?.gst_id,
            gst_tax_percentage: item?.gst,
            total: parseFloat(item?.total),
            total_discount_amount: 0,
            cess: customToFixed(item?.cessValue, 2) || 0,
            rpu_with_gst: item?.updatedRateWithGst,
            consolidating_items_to_invoice: configurationList?.item_table_configuration
                ?.consolidating_items_to_invoice
                ? item?.multiQuantity?.toString() === "0,0,0,0"
                    ? null
                    : item.multiQuantity.toString()
                : null,
            decimal_places: item?.decimal_places,
            decimal_places_for_rate: item?.decimal_places_for_rate,
            ...(item?.custom_fields?.length > 0 ? {custom_fields: item?.custom_fields
            ?.filter(customField => customField?.value && customField?.custom_field_id) // Only keep fields with value && (customField?.is_able_to_edit === undefined || customField?.is_able_to_edit)
            ?.map(customField => ({
                custom_field_id: customField?.custom_field_id,
                value: customField?.option_id ? customField?.option_id : customField?.value,
                ...(customField?.field_type && {field_type: customField?.field_type ?? null})
            }))} : {}),
            custom_fields_item_master: item?.custom_fields_item_master,
            ...( custom_field_inventory?.length > 0 ? {custom_field_inventory} : {}),
        };
        return newItem;
    }
    });

    const hasNegativeTotal = itemList.some(item => item?.total < 0);
    return { itemList, is_na: all_na ? true : false, hasNegativeTotal };
};

export const prepareLedgerData = (
    items,
    gstCalculation,
    setGstCalculation,
    gstOptions,
    configurationList,
    changeTax
) => {
    if (!items[0]?.selectedLedger) {
        return { ledgerList: null, is_na: null };
    }

    let all_na = true;
    const ledgerList = items?.map(item => {
        if(item?.selectedLedger){
            const is_na = gstNaFilter(gstOptions, item?.gst_id);
            if (is_na === 0) {
                all_na = false;
            }
            const newItem = {
            ledger_id: item?.selectedLedger,
            additional_description: configurationList?.item_table_configuration
                ?.is_additional_ledger_description
                ? item?.additional_description
                : null,
            rpu: item?.updatedRateWithGst || 0,
            rpu_with_gst: item?.updatedRateWithGst || 0,
            rpu_without_gst: item?.updatedRateWithGst || 0,
            with_tax: changeTax ? 1 : 0,
            discount_type: item?.discountType,
            discount_value: item?.discountValue,
            discount_type_2: item?.discountType_2,
            discount_value_2: item?.discountValue_2,
            gst_id: item?.gst_id,
            gst_tax: item?.gst_id,
            gst_tax_percentage: item?.gst,
            total: customToFixed(item?.total, 2),
            cess: customToFixed(item?.cessValue, 2) || 0,
            custom_fields: item?.custom_fields
            ?.filter(customField => customField?.value && (customField?.custom_field_id || customField?.custom_field_item_id) && (customField?.is_enabled === undefined || customField?.is_enabled)) // Only keep fields with value
            ?.map(customField => ({
                custom_field_item_id: customField?.custom_field_item_id,
                value: customField?.value,
            }))
        };
        return newItem;
    }
    });

    const hasNegativeLedgerTotal = ledgerList.some(item => item?.total < 0);

    return { ledgerList, is_na: all_na ? true : false, hasNegativeLedgerTotal };
};

export const checkAllAdditionalChargesNA = (additionalCharges, gstOptions) => {
    let allNA = true;
    if (
        additionalCharges.additional_detail.length === 1 &&
        additionalCharges.additional_detail[0].ac_gst_rate_id.value === 0 &&
        additionalCharges.additional_detail[0].ac_gst_rate_id.rate === 0
    ) {
        return true;
    }
    const chargesList = additionalCharges.additional_detail.map(item => {
        const isNA = gstNaFilter(
            gstOptions,
            item?.ac_gst_rate_id?.value ? item?.ac_gst_rate_id?.value : 0
        );
        if (isNA === 0) {
            allNA = false;
        }
    });
    return allNA ? true : false;
};

export const prepareDispatchData = (data, isParty ) => {
    const countryId = isParty ? data?.country_id || 101 : data?.country_id;
    const country = CountryData?.find(({ country_id }) => country_id === countryId);
    return `${country?.country_name ? country?.country_name : ""}`;
};

export const fetchGstDetail = data => {
    const country = CountryData[98];
    const state = country?.states?.find(({ state_name }) => state_name === data?.state);
    const city = state?.cities?.find(({ city_name }) => city_name === data?.city);
    const response = {
        state_id: state?.state_id,
        city_id: city?.city_id,
        country_id: country?.country_id,
    };
    return response;
};

export const prepareItemSaleData = ({
    singleSale,
    id,
    setStateFunctions,
    dispatch,
    saleCreateOrReturn,
    tableHeaderList,
    classificationOptions,
    accountingTableHeader,
    isDuplicate,
    isSaleReturnDuplicate,
    isSaleToSaleReturn,
}) => {
    const {
        setInvoiceDetail,
        setCessValue,
        setGstQuote,
        setItemType,
        setClassification,
        setItems,
        setAccountingItems,
        setAddLessChanges,
        setAdditionalCharges,
        setPartyAddress,
        gstCalculation,
        setGstCalculation,
        setBrokerDetail,
        setTransporterDetail,
        setEwayBillDetail,
        setOtherDetail,
        setGstValue,
        setTcsRate,
        setPaymentLedgerDetail,
        setGrandTotal,
        setMainGrandTotal,
        setChangeTax,
        addLessChanges,
        additionalCharges,
        setFinalAmount,
        setTaxableValue,
        setIsIGSTCalculation,
        setIsSGSTCalculation,
        setUpdateParty_quotesOptions,
        setDispatchAddressId,
        setSelectedAdvancePayment,
        setCustomHeaderListTransaction,
        setSameAsBill,
        configurationList,
        saleEdit
    } = setStateFunctions;
    if (singleSale) {
        if(!isSaleToSaleReturn){
            setInvoiceDetail({
                invoice_number: singleSale?.invoice_number
                    ? isDuplicate
                        ? singleSale?.invoice_number
                        : isSaleReturnDuplicate
                        ? singleSale?.credit_note_number
                        : singleSale?.full_invoice_number
                    : isSaleReturnDuplicate
                    ? singleSale?.credit_note_number
                    : singleSale?.full_invoice_number,
                invoice_date: singleSale?.date && formattedDate(singleSale?.date),
            });
        }
        setDispatchAddressId(singleSale?.dispatch_address_id);
        setUpdateParty_quotesOptions(
            singleSale?.estimate_quote_no && configurationList?.header?.is_enabled_estimate_quote
                ? convertIntoDropdownList(singleSale?.estimate_number_list)
                : singleSale?.delivery_challan_no && configurationList?.header?.is_enabled_delivery_challan
                ? convertIntoDropdownList(singleSale?.delivery_challan_list)
                : null
        );
        // setFinalAmount(singleSale?.grand_total);
        setCessValue(singleSale?.cess);
        setGstQuote({
            original_inv_no: singleSale?.original_inv_no ?? (isSaleToSaleReturn ? singleSale?.id : ""),
            original_inv_date: singleSale?.original_inv_date
                ? formattedDate(singleSale?.original_inv_date)
                : singleSale?.date && isSaleToSaleReturn ? formattedDate(singleSale?.date) : null,
            gstin: singleSale?.gstin,
            quotes_id: Number(singleSale?.estimate_quote_no),
            party_ledger_id: singleSale?.customer_ledger_id,
            quotes_id: isDuplicate ? [] : formatDeliveryChallanList(
                singleSale?.delivery_challan_no
                    ? singleSale?.delivery_challan_no
                    : singleSale?.estimate_quote_no
                    ? singleSale?.estimate_quote_no
                    : null,
                    singleSale?.estimate_quote_no
                ? (singleSale?.estimate_number_list)
                : singleSale?.delivery_challan_no
                ? (singleSale?.delivery_challan_list)
                : null
            ),
            mobile:{
                region_iso: singleSale?.region_iso,
                region_code: singleSale?.region_code,
                party_phone_number:  singleSale?.party_phone_number,
                phone_input:(("+"+singleSale?.region_code) || "+91") + singleSale?.party_phone_number,
            }
        });
        setTaxableValue(singleSale?.taxable_value);
        if(!isDuplicate && !isSaleReturnDuplicate && !isSaleToSaleReturn){
            setSelectedAdvancePayment(
                singleSale?.advance_payment?.map((item) => {
                    return{
                        ...item,
                        received_amount: item?.adjusted_amount,
                        advance_payment_id: item?.id
                    }
                })
            );
        }
        if (saleCreateOrReturn) {
            if (singleSale?.sale_return_item_type == 2) {
                setItems(
                    singleSale?.sale_return_items?.map((item, index) => {
                        setChangeTax(item?.with_tax);
                        const multiQuantity =
                            item?.consolidating_items_to_invoice?.split(",") || [];

                        if (multiQuantity[0] == "0") {
                            multiQuantity[0] = item?.quantity?.toFixed(item?.decimal_places || 2);
                        }

                        while (multiQuantity.length < 4) {
                            multiQuantity.push("0");
                        }

                        const unitOptions = item?.units_of_array
                            ? item?.units_of_array.map((unit) => {
                                  return {
                                      label: unit?.name,
                                      value: unit?.id,
                                  };
                              })
                            : [];
                        return {
                            id: item?.id,
                            transaction_item_id: item?.id,
                            selectedItem: item?.item_id,
                            additional_description: item?.additional_description,
                            mrp: item?.mrp,
                            hsn_code: item?.hsn_code,
                            multiQuantity: multiQuantity,
                            selectedLedger: item?.ledger_id,
                            quantity: item?.quantity?.toFixed(item?.decimal_places || 2),
                            free_quantity: item?.free_quantity,
                            rateWithGst: item?.rpu_with_gst,
                            updatedRateWithGst: item?.with_tax
                                ? item?.rpu_with_gst
                                : item?.rpu_without_gst?.toFixed(2),
                            rateWithoutGst: item?.with_tax
                                ? item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                                : item?.rpu_without_gst?.toFixed(
                                      item?.decimal_places_for_rate || 2
                                  ),
                            updatedRateWithoutGst: item?.with_tax
                                ? item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                                : item?.rpu_without_gst?.toFixed(
                                      item?.decimal_places_for_rate || 2
                                  ),
                            gst_id: item?.gst_id,
                            gst: item?.gst_tax_percentage,
                            discountType: item?.discount_type || 1,
                            discountValue: item?.discount_value,
                            discountType_2: item?.discount_type_2 || 1,
                            discountValue_2: item?.discount_value_2 ? item?.discount_value_2 : 0,
                            total: customToFixed(item?.total, 2),
                            with_tax: item?.with_tax ? 1 : 0,
                            selectedUnit: item?.unit_id,
                            itemUnitOption: unitOptions,
                            cgstValue: item?.classification_cgst_tax,
                            igstValue: item?.classification_igst_tax,
                            sgstValue: item?.classification_sgst_tax,
                            cessValue: (item?.cess_rate * item?.total) / 100,
                            cessRate: item?.cess_rate,
                            isShowDelete: singleSale?.sale_return_items?.length > 1 ? true : false,
                            decimal_places: item?.decimal_places ?? 2,
                            decimal_places_for_rate: item?.decimal_places_for_rate ?? 2,
                            secondaryUnitOfMeasurement: item?.secondary_unit_of_measurement,
                            conversationRate: item?.conversion_rate,
                            item_decimal_places: item?.item_decimal_places,
                            item_decimal_places_for_rate: item?.item_decimal_places_for_rate,
                            custom_fields: customFieldOptimization(item?.custom_fields),
                            custom_field_inventory_store: formattedCustomField(item?.custom_items_inventory_values),
                            model_inventory_custom_fields: item?.model_inventory_custom_fields,
                            model_custom_fields: item?.model_inventory_custom_fields,
                            model_select_inventory_custom_fields: item?.custom_items_inventory_values,
                            is_status: ITEM_STATUS.ACTIVE,
                        };
                    })
                );
                clearAccountingInvoice(setAccountingItems);
            } else if (singleSale?.sale_return_item_type == 1) {
                setAccountingItems(
                    singleSale?.sale_return_ledgers?.map((item, index) => {

                        setChangeTax(item?.with_tax);
                        return {
                            id: index + 1,
                            selectedLedger: item?.ledger_id,
                            additional_description: item?.additional_description,
                            rateWithGst: parseFloat(item?.rpu_with_gst),
                            updatedRateWithGst: item?.with_tax
                                ? parseFloat(item?.rpu_with_gst)
                                : parseFloat(item?.rpu_without_gst?.toFixed(2)),
                            rateWithoutGst: item?.with_tax
                                ? parseFloat(
                                      item?.rpu_with_gst?.toFixed(
                                          item?.decimal_places_for_rate || 2
                                      )
                                  )
                                : parseFloat(
                                      item?.rpu_without_gst?.toFixed(
                                          item?.decimal_places_for_rate || 2
                                      )
                                  ),
                            updatedRateWithoutGst: item?.with_tax
                                ? parseFloat(
                                      item?.rpu_with_gst?.toFixed(
                                          item?.decimal_places_for_rate || 2
                                      )
                                  )
                                : parseFloat(
                                      item?.rpu_without_gst?.toFixed(
                                          item?.decimal_places_for_rate || 2
                                      )
                                  ),
                        amountWithoutGst: item?.with_tax
                            ? parseFloat(
                                  item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              )
                            : parseFloat(
                                  item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              ),
                            updatedAmountWithoutGst: item?.rpu_without_gst,
                            gst: item?.gst_tax_percentage,
                            gst_id: item?.gst_id,
                            discountType: item?.discount_type,
                            discountValue: item?.discount_value,
                            discountType_2: item?.discount_type_2,
                            discountValue_2: item?.discount_value_2 ? item?.discount_value_2 : 0,
                            total: customToFixed(item?.total, 2),
                            cgstValue: item?.classification_cgst_tax,
                            igstValue: item?.classification_igst_tax,
                            sgstValue: item?.classification_sgst_tax,
                            cessValue: (item?.cess_rate * item?.total) / 100,
                            cessRate: item?.cess_rate,
                            with_tax: item?.with_tax ? 1 : 0,
                            rpu: item?.rpu_with_gst || 0,
                            isShowDelete:
                                singleSale?.sale_return_ledgers?.length > 1 ? true : false,
                            decimal_places: item?.decimal_places ?? 2,
                            decimal_places_for_rate: item?.decimal_places_for_rate ?? 2,
                            secondaryUnitOfMeasurement: item?.secondary_unit_of_measurement,
                            conversationRate: item?.conversion_rate,
                            item_decimal_places: item?.item_decimal_places,
                            item_decimal_places_for_rate: item?.item_decimal_places_for_rate,
                            custom_fields: customFieldOptimization(item?.custom_ledgers_values),
                            is_status: ITEM_STATUS.ACTIVE,
                        };
                    })
                );
                clearItemInvoice(setItems);
            }
            if (singleSale?.sale_return_item_type == 2) {
                dispatch(rearrangeItemList(TRANSACTION_TYPE.SALE_RETURN, 1));
            } else {
                dispatch(rearrangeItemList(TRANSACTION_TYPE.SALE_RETURN, 2));
            }
            setItemType(singleSale?.sale_return_item_type == 2 ? "item" : "accounting");
            const nature_type =
                singleSale?.sale_return_item_type == 2
                    ? singleSale?.sale_return_items &&
                      singleSale?.sale_return_items[0]?.classification_nature_type
                    : singleSale?.sale_return_ledgers &&
                      singleSale?.sale_return_ledgers[0]?.classification_nature_type;
            const nature_name = classificationOptions.find(item => item.value == nature_type);

            setClassification({
                classification_nature: nature_type,
                classification_nature_name: nature_name?.label,
                rcm_applicable:
                    singleSale?.sale_return_item_type == 2
                        ? singleSale?.sale_return_items &&
                          singleSale?.sale_return_items[0]?.classification_is_rcm_applicable == 1
                            ? true
                            : false
                        : singleSale?.sale_return_ledgers &&
                          singleSale?.sale_return_ledgers[0]?.classification_is_rcm_applicable == 1
                        ? true
                        : false,
            });
            setGSTCalculationType(
                {
                    classification_nature_name: nature_name?.label,
                },
                setIsIGSTCalculation,
                setIsSGSTCalculation
            );
        } else {
            setItemType(singleSale?.sales_item_type == 2 ? "item" : "accounting");
            const nature_type =
                singleSale?.sales_item_type == 2
                    ? singleSale?.sale_items &&
                      singleSale?.sale_items[0]?.classification_nature_type
                    : singleSale?.sale_ledgers &&
                      singleSale?.sale_ledgers[0]?.classification_nature_type;
            const nature_name = classificationOptions.find(item => item.value == nature_type);

            setClassification({
                classification_nature: nature_type,
                classification_nature_name: nature_name?.label,
                rcm_applicable:
                    singleSale?.sales_item_type == 2
                        ? singleSale?.sale_items &&
                          singleSale?.sale_items[0]?.classification_is_rcm_applicable == 1
                            ? true
                            : false
                        : singleSale?.sale_ledgers &&
                          singleSale?.sale_ledgers[0]?.classification_is_rcm_applicable == 1
                        ? true
                        : false,
            });
            setGSTCalculationType(
                {
                    classification_nature_name: nature_name?.label,
                },
                setIsIGSTCalculation,
                setIsSGSTCalculation
            );
            if (singleSale?.sales_item_type == 2) {
                setItems(
                    singleSale?.sale_items?.map((item, index) => {
                        const multiQuantity =
                            item?.consolidating_items_to_invoice?.split(",") || [];

                        if (multiQuantity[0] == "0") {
                            multiQuantity[0] = item?.quantity?.toFixed(item?.decimal_places || 2);
                        }

                        while (multiQuantity.length < 4) {
                            multiQuantity.push("0");
                        }

                        setChangeTax(item?.with_tax);
                        const unitOptions = unitOption(item?.units_of_array);

                        const filteredCf = item?.custom_items_inventory_values?.filter((item) => item?.sale_quantity || item?.quantity);
                        return {
                            id: item?.id,
                            transaction_item_id: item?.id,
                            selectedItem: item?.item_id,
                            additional_description: item?.additional_description,
                            mrp: item?.mrp,
                            hsn_code: item?.hsn_code,
                            selectedLedger: item?.ledger_id,
                            quantity: item?.quantity?.toFixed(item?.decimal_places || 2),
                            free_quantity: item?.free_quantity,
                            rateWithGst: item?.rpu_with_gst,
                            updatedRateWithGst: item?.with_tax
                                ? item?.rpu_with_gst
                                : item?.rpu_without_gst?.toFixed(2),
                            rateWithoutGst: item?.with_tax
                                ? item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                                : item?.rpu_without_gst?.toFixed(
                                      item?.decimal_places_for_rate || 2
                                  ),
                            updatedRateWithoutGst: item?.with_tax
                                ? item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                                : item?.rpu_without_gst?.toFixed(
                                      item?.decimal_places_for_rate || 2
                                  ),
                            gst_id: item?.gst_id,
                            gst: item?.gst_tax_percentage,
                            discountType: item?.discount_type,
                            discountValue: item?.discount_value,
                            discountType_2: item?.discount_type_2,
                            discountValue_2: item?.discount_value_2 ? item?.discount_value_2 : 0,
                            total: customToFixed(item?.total, 2),
                            with_tax: item?.with_tax ? 1 : 0,
                            selectedUnit: item?.unit_id,
                            itemUnitOption: unitOptions,
                            multiQuantity: multiQuantity,
                            cgstValue: item?.classification_cgst_tax,
                            igstValue: item?.classification_igst_tax,
                            sgstValue: item?.classification_sgst_tax,
                            cessValue: (item?.cess_rate * item?.total) / 100,
                            cessRate: item?.cess_rate,
                            isShowDelete: singleSale?.sale_items?.length > 1 ? true : false,
                            decimal_places: item?.decimal_places ?? 2,
                            decimal_places_for_rate: item?.decimal_places_for_rate ?? 2,
                            secondaryUnitOfMeasurement: item?.secondary_unit_of_measurement,
                            conversationRate: item?.conversion_rate,
                            item_decimal_places: item?.item_decimal_places,
                            item_decimal_places_for_rate: item?.item_decimal_places_for_rate,
                            custom_fields: customFieldOptimization(item?.custom_fields),
                            custom_field_inventory_store: formattedCustomField(isSaleToSaleReturn ? filteredCf : item?.custom_items_inventory_values, null, true),
                            model_inventory_custom_fields: item?.model_inventory_custom_fields,
                            model_custom_fields: item?.model_inventory_custom_fields,
                            model_select_inventory_custom_fields: isSaleToSaleReturn ? filteredCf : item?.custom_items_inventory_values,
                            is_status: ITEM_STATUS.ACTIVE,
                        };
                    })
                );
                clearAccountingInvoice(setAccountingItems);
            } else if (singleSale?.sales_item_type == 1) {
                setAccountingItems(
                    singleSale?.sale_ledgers?.map((item, index) => {
                        setChangeTax(item?.with_tax);
                        return {
                            id: item?.id,
                            selectedLedger: item?.ledger_id,
                            additional_description: item?.additional_description,
                            rateWithGst: parseFloat(item?.rpu_with_gst),
                            updatedRateWithGst: item?.with_tax
                                ? parseFloat(item?.rpu_with_gst)
                                : parseFloat(item?.rpu_without_gst?.toFixed(2)),
                            rateWithoutGst: item?.with_tax
                                ? parseFloat(
                                      item?.rpu_with_gst?.toFixed(
                                          item?.decimal_places_for_rate || 2
                                      )
                                  )
                                : parseFloat(
                                      item?.rpu_without_gst?.toFixed(
                                          item?.decimal_places_for_rate || 2
                                      )
                                  ),
                            updatedRateWithoutGst: item?.with_tax
                                ? parseFloat(
                                      item?.rpu_with_gst?.toFixed(
                                          item?.decimal_places_for_rate || 2
                                      )
                                  )
                                : parseFloat(
                                      item?.rpu_without_gst?.toFixed(
                                          item?.decimal_places_for_rate || 2
                                      )
                                  ),
                        amountWithoutGst: item?.with_tax
                            ? parseFloat(
                                  item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              )
                            : parseFloat(
                                  item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              ),
                            updatedAmountWithoutGst: item?.rpu_without_gst,
                            gst: item?.gst_tax_percentage,
                            gst_id: item?.gst_id,
                            discountType: item?.discount_type,
                            discountValue: item?.discount_value,
                            discountType_2: item?.discount_type_2,
                            discountValue_2: item?.discount_value_2 ? item?.discount_value_2 : 0,
                            total: customToFixed(item?.total, 2),
                            cgstValue: item?.classification_cgst_tax,
                            igstValue: item?.classification_igst_tax,
                            sgstValue: item?.classification_sgst_tax,
                            cessValue: (item?.cess_rate * item?.total) / 100,
                            cessRate: item?.cess_rate,
                            with_tax: item?.with_tax ? 1 : 0,
                            rpu: item?.rpu_with_gst || 0,
                            isShowDelete: singleSale?.sale_ledgers?.length > 1 ? true : false,
                            decimal_places: item?.decimal_places ?? 2,
                            decimal_places_for_rate: item?.decimal_places_for_rate ?? 2,
                            secondaryUnitOfMeasurement: item?.secondary_unit_of_measurement,
                            conversationRate: item?.conversion_rate,
                            item_decimal_places: item?.item_decimal_places,
                            item_decimal_places_for_rate: item?.item_decimal_places_for_rate,
                            custom_fields: customFieldOptimization(item?.custom_ledgers_values),
                            is_status: ITEM_STATUS.ACTIVE,
                        };
                    })
                );
                clearItemInvoice(setItems);
            }
            if (singleSale?.sales_item_type == 2) {
                dispatch(rearrangeItemList(TRANSACTION_TYPE.SALE, 1));

            } else {
                dispatch(rearrangeItemList(TRANSACTION_TYPE.SALE, 2));

            }
        }
        setAddLessChanges(
            singleSale?.add_less?.length == 0
                ? [
                    {
                        al_ledger_id: null,
                        al_is_show_in_print: 1,
                        al_type: 1,
                        al_value: null,
                        al_total: 0,
                    },
                ]
                : singleSale?.add_less?.map(item => {
                      return {
                          al_ledger_id: item.ledger_id,
                          al_is_show_in_print: item.is_show_in_print,
                          al_type: item.type,
                          al_value: item.type == 2 ? item.value : item.total,
                          al_total: item.total,
                          is_status: ITEM_STATUS.ACTIVE,
                      };
                  })
        );
        setCustomHeaderListTransaction(singleSale?.custom_values)
        setAdditionalCharges({
                ...additionalCharges,
                ...(id && {upload_document: singleSale?.media?.map(media => {
                    return { original_url: media?.original_url, id: media?.id };
                })}),
                note: singleSale?.narration,
                terms_and_conditions: singleSale?.term_and_condition,
                bank_id : singleSale?.bank_id,
                additional_detail:
                    !singleSale?.additional_charges || singleSale?.additional_charges?.length == 0
                        ? [
                            {
                                ac_ledger_id: null,
                                ac_type: 1,
                                ac_value: "",
                                ac_gst_rate_id: {
                                    label: "",
                                    value: 0,
                                    rate: 0,
                                },
                                ac_total: 0,
                                is_status: ITEM_STATUS.IN_ACTIVE,
                            },
                        ]
                        : singleSale?.additional_charges?.map(charge => {
                            return {
                                ac_ledger_id: charge?.ledger_id,
                                ac_type: charge?.charge_type,
                                ac_value: charge?.value,
                                ac_gst_rate_id: {
                                    value: charge?.gst_rate_id,
                                    rate: Number(charge?.gst_percentage),
                                    label: `${charge?.gst_percentage}%`,
                                },
                                ac_total: customToFixed(charge?.total_without_tax, 2),
                                is_status: ITEM_STATUS.ACTIVE,
                            };
                        }),
        });
        setSameAsBill(singleSale?.same_as_billing)
        setPartyAddress({
            billingAddress: {
                address_1: singleSale?.billing_address?.address_1,
                address_2: singleSale?.billing_address?.address_2,
                country_id: singleSale?.billing_address?.country_id,
                state_id: singleSale?.billing_address?.state_id,
                city_id: singleSale?.billing_address?.city_id,
                state_name: singleSale?.billing_address?.state_name,
                city_name: singleSale?.billing_address?.city_name,
                pin_code: singleSale?.billing_address?.pin_code,
            },
            shippingAddress: {
                address_1: singleSale?.shipping_address?.address_1,
                address_2: singleSale?.shipping_address?.address_2,
                country_id: singleSale?.shipping_address?.country_id,
                state_id: singleSale?.shipping_address?.state_id,
                city_id: singleSale?.shipping_address?.city_id,
                state_name: singleSale?.shipping_address?.state_name,
                city_name: singleSale?.shipping_address?.city_name,
                pin_code: singleSale?.shipping_address?.pin_code,
                shipping_name: singleSale?.shipping_name,
                shipping_gstin: singleSale?.shipping_gstin,
                shipping_address_id: singleSale?.shipping_address_id
            },
        });
        const roundOffAmount = customToFixed(singleSale?.rounding_amount, 2);
        setGstCalculation({
            ...gstCalculation,
            round_of_amount: roundOffAmount,
            is_round_off_not_changed: singleSale?.is_round_off_not_changed,
            ...(singleSale?.round_off_method ? {round_off_method: Number(singleSale?.round_off_method)} : {})
        });
        setBrokerDetail({
            broker_id: singleSale?.broker_id,
            broker_percentage: singleSale?.brokerage_for_sale ? singleSale?.brokerage_for_sale : "",
            brokerage_on_value: singleSale?.brokerage_on_value_type,
        });
        setTransporterDetail({
            transport_id: singleSale?.transport_id,
            transporter_document_number: singleSale?.transporter_document_number,
            transporter_document_date: singleSale?.transporter_document_date
                ? formattedDate(singleSale?.transporter_document_date)
                : null,
            transporter_vehicle_number: singleSale?.transporter_vehicle_number,
        });
        setEwayBillDetail({
            eway_bill_date: singleSale?.eway_bill_date
                ? formattedDate(singleSale?.eway_bill_date)
                : null,
            eway_bill_number: singleSale?.eway_bill_number,
        });
        setOtherDetail({
            po_number: singleSale?.po_no,
            date: singleSale?.po_date ? formattedDate(singleSale?.po_date) : null,
            creditPeriod: singleSale?.credit_period,
            creditPeriodType: singleSale?.credit_period_type || 1,
        });

        setGstValue({
            igstValue: singleSale?.igst,
            cgstValue: singleSale?.cgst,
            sgstValue: singleSale?.sgst,
        });
        setTcsRate({
            tcs_amount: singleSale?.tcs_tax_id ? customToFixed(singleSale?.tcs_amount, 2): "",
            tcs_tax_id: singleSale?.tcs_tax_id,
            tcs_rate: singleSale?.tcs_tax_id ? singleSale?.tcs_rate: "",
            tcs_calculated_on: singleSale?.tcs_calculated?.calculated_on,
        });
        setPaymentLedgerDetail(prev => ({
            ...(singleSale?.payment_details?.length > 0 && saleEdit
                ? {
                      payment_detail: singleSale.payment_details.map(item => ({
                          pd_ledger_id: item?.ledger_id,
                          pd_amount: item?.amount,
                          pd_date: isDuplicate
                              ? formattedDate()
                              : item?.date
                              ? formattedDate(item?.date)
                              : "",
                          pd_mode: item?.mode || null,
                          pd_reference_number: item?.reference_no,
                          pd_id: item?.id,
                      })),
                  }
                : {
                      payment_detail: [
                          {
                              pd_ledger_id: null,
                              pd_date: "",
                              pd_amount: "",
                              pd_mode: null,
                              pd_reference_number: "",
                              is_show_invoice_date: true,
                          },
                      ],
                  }),
            tds_amount: singleSale?.tds_tax_id ? singleSale?.tds_amount : "",
            tds_rate: singleSale?.tds_tax_id ? singleSale?.tds_rate : "",
            tds_tax_id: singleSale?.tds_tax_id,
            rounding_type: singleSale?.tds_rounding_method || 1
        }));
        setGrandTotal(singleSale?.gross_value);
        setMainGrandTotal(singleSale?.grand_total - roundOffAmount);
        setFinalAmount(singleSale?.grand_total);
    }
};

export const prepareItemSaleDataFromEstimate = ({
    id,
    singleEstimateQuote,
    estimateToSaleDetail,
    setStateFunctions,
    dispatch,
    tableHeaderList,
    classificationOptions,
    accountingTableHeader,
    estimateId,
    invoice,
    company
}) => {
    const {
        setInvoiceDetail,
        setCessValue,
        setGstQuote,
        setItemType,
        setClassification,
        setItems,
        setAccountingItems,
        setAddLessChanges,
        setAdditionalCharges,
        setPartyAddress,
        gstCalculation,
        setGstCalculation,
        setBrokerDetail,
        setTransporterDetail,
        setEwayBillDetail,
        setOtherDetail,
        setGstValue,
        setTcsRate,
        setPaymentLedgerDetail,
        setGrandTotal,
        setMainGrandTotal,
        setChangeTax,
        addLessChanges,
        additionalCharges,
        setFinalAmount,
        setTaxableValue,
        setIsIGSTCalculation,
        setIsSGSTCalculation,
        invoiceDetail,
        setCustomHeaderListTransaction,
        setSameAsBill
    } = setStateFunctions;
    if (singleEstimateQuote) {
        const isCurrentYear = isCurrentFinancialYear(company?.company)
        setInvoiceDetail({
            ...invoiceDetail,
            invoice_number: invoice?.invoiceNumber?.invoice_number,
            invoice_date: isCurrentYear ? formattedDate() : formattedDate(estimateToSaleDetail?.document_date),
        });
        setFinalAmount(estimateToSaleDetail?.grand_total);
        setCessValue(estimateToSaleDetail?.cess);
        setCustomHeaderListTransaction(estimateToSaleDetail?.custom_values);
        setGstQuote({
            original_inv_no: estimateToSaleDetail?.original_inv_no,
            original_inv_date: estimateToSaleDetail?.date
                ? formattedDate(estimateToSaleDetail?.date)
                : "",
            gstin: estimateToSaleDetail?.gstin,
            // quotes_id: Number(estimateToSaleDetail?.estimate_quote_no),
            party_ledger_id: estimateToSaleDetail?.party_ledger_id,
            quotes_id: [
                {
                    value: parseInt(estimateId),
                    label: estimateToSaleDetail?.document_number,
                },
            ],
        });
        setTaxableValue(estimateToSaleDetail?.taxable_value);

        setItemType(estimateToSaleDetail?.invoice_type == 2 ? "item" : "accounting");
        const nature_type =
        estimateToSaleDetail?.invoice_type == 2
                ? singleEstimateQuote?.allTransactionItems &&
                  singleEstimateQuote?.allTransactionItems[0]?.classification_nature_type
                : singleEstimateQuote?.allTransactionItems &&
                  singleEstimateQuote?.allTransactionItems[0]?.classification_nature_type;
        const nature_name = classificationOptions.find(item => item.value == nature_type?.id);
        setClassification({
            classification_nature: nature_type?.id,
            classification_nature_name: nature_name?.label,
            rcm_applicable:
            estimateToSaleDetail?.invoice_type == 2
                    ? singleEstimateQuote?.allTransactionItems &&
                      singleEstimateQuote?.allTransactionItems[0]?.classification_is_rcm_applicable ==
                          1
                        ? true
                        : false
                    : singleEstimateQuote?.allTransactionItems &&
                      singleEstimateQuote?.allTransactionItems[0]
                          ?.classification_is_rcm_applicable == 1
                    ? true
                    : false,
        });
        setGSTCalculationType(
            {
                classification_nature_name: nature_name?.label,
            },
            setIsIGSTCalculation,
            setIsSGSTCalculation
        );
        if (estimateToSaleDetail?.invoice_type == 2) {
            setItems(
                singleEstimateQuote?.allTransactionItems?.map((item, index) => {
                    const multiQuantity = item?.consolidating_items_to_invoice?.split(",") || [];

                    if (multiQuantity[0] == "0") {
                        multiQuantity[0] = item?.quantity?.toFixed(item?.decimal_places || 2);
                    }

                    while (multiQuantity.length < 4) {
                        multiQuantity.push("0");
                    }

                    setChangeTax(item?.with_tax);
                    const unitOptions = unitOptionWithKey(item?.unitOfArray);
                    return {
                        id: item?.id,
                        transaction_item_id: item?.id,
                        selectedItem: item?.item_id,
                        additional_description: item?.additional_description,
                        mrp: item?.mrp,
                        hsn_code: item?.hsn_code,
                        selectedLedger: item?.ledger_id,
                        quantity: item?.quantity?.toFixed(item?.decimal_places || 2),
                        free_quantity: item?.free_quantity,
                        rateWithGst: item?.rpu_with_gst,
                        updatedRateWithGst: item?.with_tax
                            ? item?.rpu_with_gst
                            : item?.rpu_without_gst?.toFixed(2),
                        rateWithoutGst: item?.with_tax
                            ? item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                            : item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2),
                        updatedRateWithoutGst: item?.with_tax
                            ? item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                            : item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2),
                        gst_id: item?.gst_id,
                        gst: item?.gst_tax_percentage,
                        discountType: item?.discount_type,
                        discountValue: item?.discount_value,
                        discountType_2: item?.discount_type_2,
                        discountValue_2: item?.discount_value_2 ? item?.discount_value_2 : 0,
                        total: customToFixed(item?.total, 2),
                        with_tax: item?.with_tax ? 1 : 0,
                        selectedUnit: item?.unit_id,
                        itemUnitOption: unitOptions,
                        multiQuantity: multiQuantity,
                        cgstValue: customToFixed(item?.classification_cgst_tax, 2),
                        igstValue: customToFixed(item?.classification_igst_tax, 2),
                        sgstValue: customToFixed(item?.classification_sgst_tax, 2),
                        cessValue: (item?.cess_rate * item?.total) / 100,
                        cessRate: item?.cess_rate,
                        isShowDelete:
                        singleEstimateQuote?.allTransactionItems?.length > 1 ? true : false,
                        decimal_places: item?.decimal_places ?? 2,
                        decimal_places_for_rate: item?.decimal_places_for_rate ?? 2,
                        secondaryUnitOfMeasurement: item?.secondary_unit_of_measurement,
                        conversationRate: item?.conversion_rate,
                        item_decimal_places: item?.item_decimal_places,
                        item_decimal_places_for_rate: item?.item_decimal_places_for_rate,
                        custom_fields: customFieldOptimization(item?.custom_fields),
                        custom_field_inventory_store: formattedCustomField(item?.custom_items_inventory_values),
                        model_inventory_custom_fields: item?.model_inventory_custom_fields,
                            model_custom_fields: item?.model_inventory_custom_fields,
                        model_select_inventory_custom_fields: item?.custom_items_inventory_values,
                        is_status: ITEM_STATUS.ACTIVE,
                    };
                })
            );
            clearAccountingInvoice(setAccountingItems)
        } else if (estimateToSaleDetail?.invoice_type == 1) {
            setAccountingItems(
                singleEstimateQuote?.allTransactionItems?.map((item, index) => {
                    setChangeTax(item?.with_tax);
                    return {
                        id: item?.id,
                        selectedLedger: item?.ledger_id,
                        additional_description: item?.additional_description,
                        rateWithGst: item?.rpu_with_gst,
                        amountWithoutGst: item?.with_tax
                            ? parseFloat(
                                  item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              )
                            : parseFloat(
                                  item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              ),
                        updatedAmountWithoutGst: item?.rpu_without_gst,
                        updatedRateWithGst: item?.with_tax
                            ? parseFloat(item?.rpu_with_gst)
                            : parseFloat(item?.rpu_without_gst?.toFixed(2)),
                        rateWithoutGst: item?.with_tax
                            ? parseFloat(
                                  item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              )
                            : parseFloat(
                                  item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              ),
                        updatedRateWithoutGst: item?.with_tax
                            ? parseFloat(
                                  item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              )
                            : parseFloat(
                                  item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              ),
                        gst: item?.gst_tax_percentage,
                        gst_id: item?.gst_id,
                        discountType: item?.discount_type,
                        discountValue: item?.discount_value,
                        discountType_2: item?.discount_type_2,
                        discountValue_2: item?.discount_value_2 ? item?.discount_value_2 : 0,
                        total: customToFixed(item?.total, 2),
                        cgstValue: item?.classification_cgst_tax,
                        igstValue: item?.classification_igst_tax,
                        sgstValue: item?.classification_sgst_tax,
                        cessValue: (item?.cess_rate * item?.total) / 100,
                        cessRate: item?.cess_rate,
                        with_tax: item?.with_tax ? 1 : 0,
                        rpu: item?.rpu_with_gst || 0,
                        isShowDelete:
                        singleEstimateQuote?.allTransactionItems?.length > 1 ? true : false,
                        decimal_places: item?.decimal_places ?? 2,
                        decimal_places_for_rate: item?.decimal_places_for_rate ?? 2,
                        secondaryUnitOfMeasurement: item?.secondary_unit_of_measurement,
                        conversationRate: item?.conversion_rate,
                        item_decimal_places: item?.item_decimal_places,
                        item_decimal_places_for_rate: item?.item_decimal_places_for_rate,
                        custom_fields: customFieldOptimization(item?.custom_ledgers_values),
                        is_status: ITEM_STATUS.ACTIVE,
                    };
                })
            );
            clearItemInvoice(setItems)
        }
        if (estimateToSaleDetail?.invoice_type == 2) {

            dispatch(rearrangeItemList(TRANSACTION_TYPE.SALE, 1));
        } else {
            dispatch(rearrangeItemList(TRANSACTION_TYPE.SALE, 2));

        }
    }
    setAddLessChanges(
        estimateToSaleDetail?.add_less?.length == 0
            ? addLessChanges
            : estimateToSaleDetail?.add_less?.map(item => {
                  return {
                      al_ledger_id: item.ledger_id,
                      al_is_show_in_print: item.is_show_in_print,
                      al_type: item.type,
                      al_value: item.type == 2 ? parseFloat(item.value) : parseFloat(item.total),
                      al_total: parseFloat(item.total),
                      is_status: ITEM_STATUS.ACTIVE,
                  };
              })
    );
    setAdditionalCharges({
        ...additionalCharges,
        note: estimateToSaleDetail?.narration,
        terms_and_conditions: estimateToSaleDetail?.term_and_condition,
        bank_id : estimateToSaleDetail?.bank_id,
        additional_detail:
            !estimateToSaleDetail?.additional_charges ||
            estimateToSaleDetail?.additional_charges?.length == 0
                ? [
                      {
                          ac_ledger_id: null,
                          ac_type: 1,
                          ac_value: "",
                          ac_gst_rate_id: {
                              label: "",
                              value: 0,
                              rate: 0,
                          },
                          ac_total: 0,
                          is_status: ITEM_STATUS.IN_ACTIVE,
                      },
                  ]
                : estimateToSaleDetail?.additional_charges?.map(charge => {
                      return {
                          ac_ledger_id: charge?.ledger_id,
                          ac_type: charge?.charge_type,
                          ac_value: charge?.value,
                          ac_gst_rate_id: {
                              value: charge?.gst_rate_id,
                              rate: Number(charge?.gst_percentage),
                              label: `${charge?.gst_percentage}%`,
                          },
                          ac_total: customToFixed(charge?.total_without_tax, 2),
                          is_status: ITEM_STATUS.ACTIVE,
                      };
                  }),
    });
    setSameAsBill(estimateToSaleDetail?.same_as_billing);
    setPartyAddress({
        billingAddress: {
            address_1: estimateToSaleDetail?.billing_address?.address_1,
            address_2: estimateToSaleDetail?.billing_address?.address_2,
            country_id: estimateToSaleDetail?.billing_address?.country_id,
            state_id: estimateToSaleDetail?.billing_address?.state_id,
            city_id: estimateToSaleDetail?.billing_address?.city_id,
            state_name: estimateToSaleDetail?.billing_address?.state_name,
            city_name: estimateToSaleDetail?.billing_address?.city_name,
            pin_code: estimateToSaleDetail?.billing_address?.pin_code,
        },
        shippingAddress: {
            address_1: estimateToSaleDetail?.shipping_address?.address_1,
            address_2: estimateToSaleDetail?.shipping_address?.address_2,
            country_id: estimateToSaleDetail?.shipping_address?.country_id,
            state_id: estimateToSaleDetail?.shipping_address?.state_id,
            city_id: estimateToSaleDetail?.shipping_address?.city_id,
            state_name: estimateToSaleDetail?.shipping_address?.state_name,
            city_name: estimateToSaleDetail?.shipping_address?.city_name,
            pin_code: estimateToSaleDetail?.shipping_address?.pin_code,
            shipping_name: estimateToSaleDetail?.shipping_name,
            shipping_gstin: estimateToSaleDetail?.shipping_gstin,
            shipping_address_id: estimateToSaleDetail?.shipping_address_id
        },
    });
    const roundOffAmount = customToFixed(estimateToSaleDetail?.rounding_amount, 2);
    setGstCalculation({
        ...gstCalculation,
        round_of_amount: roundOffAmount,
        is_round_off_not_changed: estimateToSaleDetail?.is_round_off_not_changed,
        ...(estimateToSaleDetail?.round_off_method ? {round_off_method: Number(estimateToSaleDetail?.round_off_method)} : {})
    });
    setBrokerDetail({
        broker_id: estimateToSaleDetail?.broker_id,
        broker_percentage: estimateToSaleDetail?.brokerage ? estimateToSaleDetail?.brokerage : "",
        brokerage_on_value: estimateToSaleDetail?.brokerage_on_value_type,
    });
    setTransporterDetail({
        transport_id: estimateToSaleDetail?.transport_id,
        transporter_document_number: estimateToSaleDetail?.transporter_document_number,
        transporter_document_date: estimateToSaleDetail?.transporter_document_date
            ? formattedDate(estimateToSaleDetail?.transporter_document_date)
            : null,
        transporter_vehicle_number: estimateToSaleDetail?.transporter_vehicle_number,
    });
    setEwayBillDetail({
        eway_bill_date: estimateToSaleDetail?.eway_bill_date
            ? formattedDate(estimateToSaleDetail?.eway_bill_date)
            : null,
        eway_bill_number: estimateToSaleDetail?.eway_bill_number,
    });
    setOtherDetail({
        po_number: estimateToSaleDetail?.po_no,
        date: estimateToSaleDetail?.po_date ? formattedDate(estimateToSaleDetail?.po_date) : null,
        creditPeriod: estimateToSaleDetail?.credit_period,
        creditPeriodType: estimateToSaleDetail?.credit_period_type || 1,
    });
    setGstValue({
        igstValue: estimateToSaleDetail?.igst,
        cgstValue: estimateToSaleDetail?.cgst,
        sgstValue: estimateToSaleDetail?.sgst,
    });
    setTcsRate({
        tcs_amount: estimateToSaleDetail?.tcs_tax_id ? customToFixed(estimateToSaleDetail?.tcs_amount, 2) : '',
        tcs_tax_id: estimateToSaleDetail?.tcs_tax_id,
        tcs_rate: estimateToSaleDetail?.tcs_tax_id ? estimateToSaleDetail?.tcs_rate : '',
        tcs_calculated_on: estimateToSaleDetail?.tcs_calculated?.calculated_on,
    });
    setPaymentLedgerDetail(prev => ({
        ...(estimateToSaleDetail?.payment_details?.length > 0
            ? {
                  payment_detail: estimateToSaleDetail.payment_details.map(item => ({
                      pd_ledger_id: item?.ledger_id,
                      pd_amount: item?.amount,
                      pd_date: item?.date ? formattedDate(item?.date) : "",
                      pd_mode: item?.mode || null,
                      pd_reference_number: item?.reference_no,
                      pd_id: item?.id,
                  })),
              }
            : { payment_detail:  [
                    {
                        pd_ledger_id: null,
                        pd_date: "",
                        pd_amount: "",
                        pd_mode: null,
                        pd_reference_number: "",
                        is_show_invoice_date: true,
                    }
                ] }),
        tds_amount: estimateToSaleDetail?.tds_tax_id ? estimateToSaleDetail?.tds_amount : "",
        tds_rate: estimateToSaleDetail?.tds_tax_id ? estimateToSaleDetail?.tds_rate : "",
        tds_tax_id: estimateToSaleDetail?.tds_tax_id,
        rounding_type: estimateToSaleDetail?.tds_rounding_method || 1
    }));
    setGrandTotal(estimateToSaleDetail?.gross_value);
    setMainGrandTotal(estimateToSaleDetail?.grand_total - roundOffAmount);
    setFinalAmount(estimateToSaleDetail?.grand_total);
};

const prepareData = (
    id,
    singleData,
    item_type,
    item_detail,
    setStateFunctions,
    tableHeaderList,
    accountingTableHeader,
    dispatch,
    type,
    cn_dn
) => {
    const {
        setItemType,
        setClassification,
        setItems,
        setAccountingItems,
        setAddLessChanges,
        setAdditionalCharges,
        setPartyAddress,
        gstCalculation,
        setGstCalculation,
        setBrokerDetail,
        setTransporterDetail,
        setEwayBillDetail,
        setOtherDetail,
        setGstValue,
        setTcsRate,
        setPaymentLedgerDetail,
        setGrandTotal,
        setMainGrandTotal,
        setChangeTax,
        addLessChanges,
        additionalCharges,
    } = setStateFunctions;

    setItemType(item_type == 2 ? "item" : "accounting");
    if (item_type == 2) {
        setItems(
            item_detail?.map((item, index) => {
                setChangeTax(item?.with_tax);
                const multiQuantity = item?.consolidating_items_to_invoice?.split(",") || [];

                if (multiQuantity[0] == "0") {
                    multiQuantity[0] = item?.quantity?.toFixed(item?.decimal_places || 2);
                }

                while (multiQuantity.length < 4) {
                    multiQuantity.push("0");
                }
                const unitOptions = unitOption(item?.units_of_array);
                return {
                    id: item?.id,
                    transaction_item_id: item?.id,
                    selectedItem: item?.item_id,
                    additional_description: item?.additional_description,
                    mrp: item?.mrp,
                    hsn_code: item?.hsn_code,
                    multiQuantity: multiQuantity,
                    selectedLedger: item?.ledger_id,
                    quantity: item?.quantity?.toFixed(item?.decimal_places || 2),
                    free_quantity: item?.free_quantity,
                    rateWithGst: item?.rpu_with_gst,
                    updatedRateWithGst: item?.with_tax
                        ? item?.rpu_with_gst
                        : item?.rpu_without_gst?.toFixed(2),
                    rateWithoutGst: item?.with_tax
                        ? item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                        : item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2),
                    updatedRateWithoutGst: item?.with_tax
                        ? item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                        : item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2),
                    gst_id: item?.gst_id,
                    gst: item?.gst_tax_percentage,
                    itemUnitOption: unitOptions,
                    discountType: item?.discount_type || 1,
                    discountValue: item?.discount_value,
                    discountType_2: item?.discount_type_2 || 1,
                    discountValue_2: item?.discount_value_2 ? item?.discount_value_2 : 0,
                    total: customToFixed(item?.total, 2),
                    with_tax: item?.with_tax ? 1 : 0,
                    selectedUnit: item?.unit_id,
                    cgstValue: item?.classification_cgst_tax,
                    igstValue: item?.classification_igst_tax,
                    sgstValue: item?.classification_sgst_tax,
                    cessValue: (item?.cess_rate * item?.total) / 100,
                    cessRate: item?.cess_rate,
                    isShowDelete: index === 0 ? false : true,
                    decimal_places: item?.decimal_places ?? 2,
                    decimal_places_for_rate: item?.decimal_places_for_rate ?? 2,
                    secondaryUnitOfMeasurement: item?.secondary_unit_of_measurement,
                    conversationRate: item?.conversion_rate,
                    custom_fields: customFieldOptimization(item?.custom_fields),
                    custom_field_inventory_store: formattedCustomField(item?.custom_items_inventory_values),
                    model_inventory_custom_fields: item?.model_inventory_custom_fields,
                            model_custom_fields: item?.model_inventory_custom_fields,
                    model_select_inventory_custom_fields: item?.custom_items_inventory_values,
                    is_status: ITEM_STATUS.ACTIVE,
                };
            })
        );
    } else if (item_type == 1) {
        setAccountingItems(
            item_detail?.map((item, index) => {
                setChangeTax(item?.with_tax);
                return {
                    id: item?.id,
                    selectedLedger: item?.ledger_id,
                    additional_description: item?.additional_description,
                    rateWithGst: parseFloat(item?.rpu_with_gst),
                    updatedRateWithGst: item?.with_tax
                        ? parseFloat(item?.rpu_with_gst)
                        : parseFloat(item?.rpu_without_gst?.toFixed(2)),
                    rateWithoutGst: item?.with_tax
                        ? parseFloat(
                              item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                          )
                        : parseFloat(
                              item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2)
                          ),
                    updatedRateWithoutGst: item?.with_tax
                        ? parseFloat(
                              item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                          )
                        : parseFloat(
                              item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2)
                          ),
                    amountWithoutGst: item?.with_tax
                          ? parseFloat(
                                item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                            )
                          : parseFloat(
                                item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2)
                            ),
                    updatedAmountWithoutGst: item?.rpu_without_gst,
                    gst: item?.gst_tax_percentage,
                    gst_id: item?.gst_id,
                    discountType: item?.discount_type || 1,
                    discountValue: item?.discount_value,
                    discountType_2: item?.discount_type_2 || 1,
                    discountValue_2: item?.discount_value_2 ? item?.discount_value_2 : 0,
                    total: customToFixed(item?.total, 2),
                    cgstValue: item?.classification_cgst_tax,
                    igstValue: item?.classification_igst_tax,
                    sgstValue: item?.classification_sgst_tax,
                    cessValue: (item?.cess_rate * item?.total) / 100,
                    cessRate: item?.cess_rate,
                    with_tax: item?.with_tax ? 1 : 0,
                    rpu: item?.rpu_with_gst || 0,
                    isShowDelete: index === 0 ? false : true,
                    custom_fields: customFieldOptimization(item?.custom_ledgers_values),
                    is_status: ITEM_STATUS.ACTIVE,
                };
            })
        );
    }

    setPartyAddress({
        billingAddress: {
            address_1: singleData?.billing_address?.address_1,
            address_2: singleData?.billing_address?.address_2,
            country_id: singleData?.billing_address?.country_id,
            state_id: singleData?.billing_address?.state_id,
            city_id: singleData?.billing_address?.city_id,
            state_name: singleData?.billing_address?.state_name,
            city_name: singleData?.billing_address?.city_name,
            pin_code: singleData?.billing_address?.pin_code,
        },
        shippingAddress: {
            shipping_address_id: singleData?.shipping_address_id,
            address_1: singleData?.shipping_address?.address_1,
            address_2: singleData?.shipping_address?.address_2,
            country_id: singleData?.shipping_address?.country_id,
            state_id: singleData?.shipping_address?.state_id,
            city_id: singleData?.shipping_address?.city_id,
            state_name: singleData?.shipping_address?.state_name,
            city_name: singleData?.shipping_address?.city_name,
            pin_code: singleData?.shipping_address?.pin_code,
            shipping_name: singleData?.shipping_name,
            shipping_gstin: singleData?.shipping_gstin
        },
    });
    if (type !== false) {
        setBrokerDetail({
            broker_id: singleData?.broker_id,
            broker_percentage: singleData?.brokerage_for_sale ? singleData?.brokerage_for_sale : "",
            brokerage_on_value: singleData?.brokerage_on_value_type,
        });
    }
    setTransporterDetail({
        transport_id: singleData?.transport_id,
        transporter_document_number: singleData?.transporter_document_number,
        transporter_document_date: singleData?.transporter_document_date
            ? formattedDate(singleData?.transporter_document_date)
            : "",
        transporter_vehicle_number: singleData?.transporter_vehicle_number,
    });
    setAddLessChanges(
        singleData?.add_less?.length == 0
            ? addLessChanges
            : singleData?.add_less?.map(item => {
                  return {
                      al_ledger_id: item.ledger_id,
                      al_is_show_in_print: item.is_show_in_print,
                      al_type: item.type,
                      al_value: item.type == 2 ? parseFloat(item.value) : parseFloat(item.total),
                      al_total: parseFloat(item.total),
                      is_status: ITEM_STATUS.ACTIVE,
                  };
              })
    );
    setGstValue({
        igstValue: singleData?.igst,
        cgstValue: singleData?.cgst,
        sgstValue: singleData?.sgst,
    });
    setTcsRate({
        tcs_amount: singleData?.tcs_tax_id ? customToFixed(singleData?.tcs_amount, 2) : "",
        tcs_tax_id: singleData?.tcs_tax_id,
        tcs_rate: singleData?.tcs_tax_id ? singleData?.tcs_rate : "",
        tcs_calculated_on: singleData?.tcs_calculated?.calculated_on,
    });
};

export const prepareIncomeData = ({
    singleSale,
    id,
    cn_dn,
    setStateFunctions,
    dispatch,
    tableHeaderList,
    classificationOptions,
    accountingTableHeader,
    isDuplicateDebitNote,
}) => {
    const {
        setInvoiceDetail,
        setGstQuote,
        setCessValue,
        setClassification,
        gstCalculation,
        setGstCalculation,
        setEwayBillDetail,
        setOtherDetail,
        setGstValue,
        setTcsRate,
        setPaymentLedgerDetail,
        setGrandTotal,
        setMainGrandTotal,
        setFinalAmount,
        setIsIGSTCalculation,
        setIsSGSTCalculation,
        setTaxableValue,
        setBrokerDetail,
        setDispatchAddressId,
        setSelectedAdvancePayment,
        additionalCharges,
        setAdditionalCharges,
        setCustomHeaderListTransaction,
        setSameAsBill,
        incomeEdit
    } = setStateFunctions;
    if (id || isDuplicateDebitNote) {
        const item_type = cn_dn == "cn" ? singleSale?.cn_item_type : singleSale?.dn_item_type;
        const item_ledger =
            cn_dn == "cn"
                ? singleSale?.cn_item_type == 1
                    ? singleSale?.income_credit_note_ledgers
                    : singleSale?.income_credit_note_items
                : singleSale?.dn_item_type == 1
                ? singleSale?.income_debit_note_ledgers
                : singleSale?.income_debit_note_items;
        dispatch(rearrangeItemList(cn_dn == "cn" ? TRANSACTION_TYPE.INCOME_CREDIT_NOTE : TRANSACTION_TYPE.INCOME_DEBIT_NOTE, item_type == 1 ? 2 : 1));

        prepareData(
            id,
            singleSale,
            item_type,
            item_ledger,
            setStateFunctions,
            tableHeaderList,
            accountingTableHeader,
            dispatch,
            "",
            cn_dn
        );
        if(!isDuplicateDebitNote){
            setSelectedAdvancePayment(
                singleSale?.advance_payment?.map((item) => {
                    return{
                        ...item,
                        received_amount: item?.adjusted_amount,
                        advance_payment_id: item?.id
                    }
                })
            );
        }
        setAdditionalCharges({
            ...additionalCharges,
            ...( id && {upload_document: singleSale?.media?.map(media => {
                return { original_url: media?.original_url, id: media?.id };
            })}),
            note: singleSale?.narration,
            terms_and_conditions: singleSale?.term_and_condition,
            bank_id: singleSale?.bank_id,
            additional_detail:
                !singleSale?.additional_charges || singleSale?.additional_charges?.length == 0
                    ? [
                          {
                              ac_ledger_id: null,
                              ac_type: 1,
                              ac_value: "",
                              ac_gst_rate_id: {
                                  label: "",
                                  value: 0,
                                  rate: 0,
                              },
                              ac_total: 0,
                              is_status: ITEM_STATUS.IN_ACTIVE,
                          },
                      ]
                    : singleSale?.additional_charges?.map(charge => {
                          return {
                              ac_ledger_id: charge?.ledger_id,
                              ac_type: charge?.charge_type,
                              ac_value: charge?.value,
                              ac_gst_rate_id: {
                                  value: charge?.gst_rate_id,
                                  rate: Number(charge?.gst_percentage),
                                  label: `${charge?.gst_percentage}%`,
                              },
                              ac_total: customToFixed(charge?.total_without_tax, 2),
                              is_status: ITEM_STATUS.ACTIVE,
                          };
                      }),
        });
        setCustomHeaderListTransaction(singleSale?.custom_values)
        setDispatchAddressId(singleSale?.dispatch_address_id);
        setInvoiceDetail({
            invoice_number:
                cn_dn == "dn"
                    ? isDuplicateDebitNote
                        ? singleSale?.debit_note_number
                        : singleSale?.full_invoice_number
                    : isDuplicateDebitNote
                    ? singleSale?.credit_note_number
                    : singleSale?.full_invoice_number,
            invoice_date: singleSale?.date && formattedDate(singleSale?.date),
            challan_number: singleSale?.challan_number,
        });
        setBrokerDetail({
            broker_id: singleSale?.broker_id,
            broker_percentage: singleSale?.brokerage_for_sale ? singleSale?.brokerage_for_sale : "",
            brokerage_on_value: singleSale?.brokerage_on_value_type,
        });
        setTaxableValue(singleSale?.taxable_value);
        setFinalAmount(singleSale?.grand_total);
        setCessValue(singleSale?.cess);
        setGstQuote({
            original_inv_no: singleSale?.original_inv_no,
            original_inv_date: singleSale?.original_inv_date
                ? formattedDate(singleSale?.original_inv_date)
                : "",
            gstin: singleSale?.gstin,
            quotes_id: Number(singleSale?.estimate_quote_no),
            party_ledger_id: singleSale?.customer_ledger_id || singleSale?.party_ledger_id,
             mobile:{
                region_iso: singleSale?.region_iso,
                region_code: singleSale?.region_code,
                party_phone_number:  singleSale?.party_phone_number,
                phone_input:(("+"+singleSale?.region_code) || "+91") + singleSale?.party_phone_number,
            }
        });
        const nature_type =
            item_type == 2
                ? item_ledger && item_ledger[0]?.classification_nature_type
                : item_ledger && item_ledger[0]?.classification_nature_type;
        const nature_name = classificationOptions.find(item => item.value == nature_type);
        setSameAsBill(singleSale?.same_as_billing);
        setClassification({
            classification_nature: nature_type,
            classification_nature_name: nature_name?.label,
            rcm_applicable:
                singleSale?.sales_item_type == 2
                    ? item_ledger && item_ledger[0]?.classification_is_rcm_applicable == 1
                        ? true
                        : false
                    : item_ledger && item_ledger[0]?.classification_is_rcm_applicable == 1
                    ? true
                    : false,
        });
        setGSTCalculationType(
            {
                classification_nature_name: nature_name?.label,
            },
            setIsIGSTCalculation,
            setIsSGSTCalculation
        );
        const roundOffAmount = customToFixed(singleSale?.rounding_amount, 2);
        setGstCalculation({
            ...gstCalculation,
            round_of_amount: roundOffAmount,
            is_round_off_not_changed: singleSale?.is_round_off_not_changed,
            ...(singleSale?.round_off_method ? {round_off_method: Number(singleSale?.round_off_method)} : {})
        });
        setEwayBillDetail({
            eway_bill_date: singleSale?.eway_bill_date
                ? formattedDate(singleSale?.eway_bill_date)
                : null,
            eway_bill_number: singleSale?.eway_bill_number,
        });
        setOtherDetail({
            po_number: singleSale?.po_no,
            date: singleSale?.po_date ? formattedDate(singleSale?.po_date) : null,
            creditPeriod: singleSale?.credit_period,
            creditPeriodType: singleSale?.credit_period_type || 1,
        });

        setGstValue({
            igstValue: singleSale?.igst,
            cgstValue: singleSale?.cgst,
            sgstValue: singleSale?.sgst,
        });
        setTcsRate({
            tcs_amount: singleSale?.tcs_tax_id ? customToFixed(singleSale?.tcs_amount, 2) : "",
            tcs_tax_id: singleSale?.tcs_tax_id,
            tcs_rate: singleSale?.tcs_tax_id ? singleSale?.tcs_rate : "",
            tcs_calculated_on: singleSale?.tcs_calculated?.calculated_on,
        });
        setPaymentLedgerDetail(prev => ({
            ...(singleSale?.payment_details?.length > 0 && incomeEdit
                ? {
                      payment_detail: singleSale.payment_details.map(item => ({
                          pd_ledger_id: item?.ledger_id,
                          pd_amount: item?.amount,
                          pd_date: isDuplicateDebitNote
                              ? formattedDate()
                              : item?.date
                              ? formattedDate(item?.date)
                              : "",
                          pd_mode: item?.mode || null,
                          pd_reference_number: item?.reference_no,
                          pd_id: item?.id,
                      })),
                  }
                : {
                      payment_detail: [
                          {
                              pd_ledger_id: null,
                              pd_date: "",
                              pd_amount: "",
                              pd_mode: null,
                              pd_reference_number: "",
                              is_show_invoice_date: true,
                          },
                      ],
                  }),
            tds_amount: singleSale?.tds_tax_id ? singleSale?.tds_amount : "",
            tds_rate: singleSale?.tds_tax_id ? singleSale?.tds_rate : "",
            tds_tax_id: singleSale?.tds_tax_id,
            rounding_type: singleSale?.tds_rounding_method || 1
        }));

        setGrandTotal(singleSale?.gross_value);
        setMainGrandTotal(singleSale?.grand_total - roundOffAmount);
        setFinalAmount(singleSale?.grand_total);
    }
};

export const prepareEstimateData = ({
    singleData,
    id,
    setStateFunctions,
    dispatch,
    tableHeaderList,
    classificationOptions,
    accountingTableHeader,
    isDuplicate,
}) => {
    const {
        setInvoiceDetail,
        gstQuote,
        setGstQuote,
        setCessValue,
        setClassification,
        gstCalculation,
        setGstCalculation,
        setEwayBillDetail,
        setOtherDetail,
        setGstValue,
        setTcsRate,
        setPaymentLedgerDetail,
        setGrandTotal,
        setMainGrandTotal,
        setFinalAmount,
        setIsIGSTCalculation,
        setIsSGSTCalculation,
        setTaxableValue,
        setBrokerDetail,
        setDispatchAddressId,
        additionalCharges,
        setAdditionalCharges,
        setCustomHeaderListTransaction,
        setSameAsBill
    } = setStateFunctions;
    if (id || isDuplicate) {
        const item_type = singleData?.invoice_type;
        const item_ledger =
            singleData?.invoice_type == 2
                ? singleData?.transaction_items
                : singleData?.transaction_ledgers;
        prepareData(
            id,
            singleData,
            item_type,
            item_ledger,
            setStateFunctions,
            tableHeaderList,
            accountingTableHeader,
            dispatch,
            "",
            ""
        );
        setBrokerDetail({
            broker_id: singleData?.broker_id,
            broker_percentage: singleData?.brokerage ? singleData?.brokerage : "",
            brokerage_on_value: singleData?.brokerage_on_value_type,
        });
        setDispatchAddressId(singleData?.dispatch_address_id);
        setTaxableValue(singleData?.taxable_value);
        setCustomHeaderListTransaction(singleData?.custom_values);
        setInvoiceDetail({
            invoice_number: singleData?.document_number,
            invoice_date: singleData?.document_date && formattedDate(singleData?.document_date),
            estimate_title: singleData?.title,
            document_date: singleData?.document_date && formattedDate(singleData?.document_date),
        });
        setSameAsBill(singleData?.same_as_billing);
        setAdditionalCharges({
            ...additionalCharges,
            ...( id && {upload_document: singleData?.media?.map(media => {
                return { original_url: media?.original_url, id: media?.id };
            })}),
            note: singleData?.narration,
            terms_and_conditions: singleData?.term_and_condition,
            bank_id: singleData?.bank_id,
            additional_detail:
                !singleData?.additional_charges || singleData?.additional_charges?.length == 0
                    ? [
                          {
                              ac_ledger_id: null,
                              ac_type: 1,
                              ac_value: "",
                              ac_gst_rate_id: {
                                  label: "",
                                  value: 0,
                                  rate: 0,
                              },
                              ac_total: 0,
                              is_status: ITEM_STATUS.IN_ACTIVE,
                          },
                      ]
                    : singleData?.additional_charges?.map(charge => {
                          return {
                              ac_ledger_id: charge?.ledger_id,
                              ac_type: charge?.charge_type,
                              ac_value: charge?.value,
                              ac_gst_rate_id: {
                                  value: charge?.gst_rate_id,
                                  rate: Number(charge?.gst_percentage),
                                  label: `${charge?.gst_percentage}%`,
                              },
                              ac_total: customToFixed(charge?.total_without_tax, 2),
                              is_status: ITEM_STATUS.ACTIVE,
                          };
                      }),
        });
        setCessValue(singleData?.cess);
        setFinalAmount(singleData?.grand_total);
        setGstQuote({
            ...gstQuote,
            original_inv_no: singleData?.original_inv_no,
            original_inv_date: singleData?.date ? formattedDate(singleData?.date) : "",
            gstin: singleData?.gstin,
            quotes_id: Number(singleData?.estimate_quote_no),
            party_ledger_id: singleData?.party_ledger_id,
            valid_for: singleData?.valid_for && parseFloat(singleData?.valid_for),
            valid_for_type: singleData?.valid_for_type,
            quotes_id: matchEstimateChallanIds(
                singleData?.delivery_challan_no
                    ? singleData?.delivery_challan_no
                    : singleData?.estimate_quote_no
                    ? singleData?.estimate_quote_no
                    : null
            ),
            mobile:{
                region_iso: singleData?.region_iso,
                region_code: singleData?.region_code,
                party_phone_number: singleData?.party_phone_number,
                phone_input:(("+"+singleData?.region_code) || "+91") + singleData?.party_phone_number,
            }
        });

        const nature_type =
            item_type == 2
                ? item_ledger && item_ledger[0]?.classification_nature_type
                : item_ledger && item_ledger[0]?.classification_nature_type;
        const nature_name = classificationOptions.find(item => item.value == nature_type);
        setClassification({
            classification_nature: nature_type,
            classification_nature_name: nature_name?.label,
            rcm_applicable:
                singleData?.sales_item_type == 2
                    ? item_ledger && item_ledger[0]?.classification_is_rcm_applicable == 1
                        ? true
                        : false
                    : item_ledger && item_ledger[0]?.classification_is_rcm_applicable == 1
                    ? true
                    : false,
        });
        setGSTCalculationType(
            {
                classification_nature_name: nature_name?.label,
            },
            setIsIGSTCalculation,
            setIsSGSTCalculation
        );
        const roundOffAmount = customToFixed(singleData?.round_off_amount, 2);
        setGstCalculation({
            ...gstCalculation,
            round_of_amount: roundOffAmount,
            is_round_off_not_changed: singleData?.is_round_off_not_changed,
            ...(singleData?.round_off_method ? {round_off_method: Number(singleData?.round_off_method)} : {})
        });
        setEwayBillDetail({
            eway_bill_date: singleData?.eway_bill_date
                ? formattedDate(singleData?.eway_bill_date)
                : null,
            eway_bill_number: singleData?.eway_bill_number,
        });
        setOtherDetail({
            po_number: singleData?.po_no,
            date: singleData?.po_date ? formattedDate(singleData?.po_date) : null,
            creditPeriod: singleData?.credit_period,
            creditPeriodType: singleData?.credit_period_type || 1,
        });
        setGstValue({
            igstValue: singleData?.igst,
            cgstValue: singleData?.cgst,
            sgstValue: singleData?.sgst,
        });
        setTcsRate({
            tcs_amount: singleData?.tcs_tax_id ? customToFixed(singleData?.tcs_amount, 2) : '',
            tcs_tax_id: singleData?.tcs_tax_id,
            tcs_rate: singleData?.tcs_tax_id ? singleData?.tcs_rate : '',
            tcs_calculated_on: singleData?.tcs_calculated?.calculated_on,
        });
        setPaymentLedgerDetail(prev => ({
            ...(singleData?.payment_details?.length > 0
                ? {
                      payment_detail: singleData.payment_details.map(item => ({
                          pd_ledger_id: item?.ledger_id,
                          pd_amount: item?.amount,
                          pd_date: item?.date ? formattedDate(item?.date) : "",
                          pd_mode: item?.mode || null,
                          pd_reference_number: item?.reference_no,
                          pd_id: item?.id,
                      })),
                  }
                : { payment_detail:  [
                    {
                        pd_ledger_id: null,
                        pd_date: "",
                        pd_amount: "",
                        pd_mode: null,
                        pd_reference_number: "",
                        is_show_invoice_date: true,
                    }
                ] }),
            tds_amount: singleData?.tds_tax_id ? singleData?.tds_amount : "",
            tds_rate: singleData?.tds_tax_id ? singleData?.tds_rate : "",
            tds_tax_id: singleData?.tds_tax_id,
            rounding_type: singleData?.tds_rounding_method || 1
        }));
        setGrandTotal(singleData?.gross_value);
        setMainGrandTotal(singleData?.grand_total - roundOffAmount);
        setFinalAmount(singleData?.grand_total);
        setGstQuote({
            ...gstQuote,
            gstin: singleData.gstin,
            quotes_id: Number(singleData?.estimate_quote_no),
            party_ledger_id: singleData.party_ledger_id,
            valid_for: singleData.valid_for && parseFloat(singleData.valid_for),
            valid_for_type: singleData?.valid_for_type,
        });
    }
};

export const prepareDeliveryChallanData = ({
    id,
    singleSale,
    invoice_type,
    setStateFunctions,
    dispatch,
    tableHeaderList,
    classificationOptions,
    accountingTableHeader,
    saleInvoiceId,
    invoice,
}) => {
    const {
        invoiceDetail,
        setInvoiceDetail,
        gstQuote,
        setGstQuote,
        setItemType,
        setClassification,
        setItems,
        setAdditionalCharges,
        setBrokerDetail,
        setTransporterDetail,
        setEwayBillDetail,
        otherDetail,
        setOtherDetail,
        setPaymentLedgerDetail,
        setChangeTax,
        additionalCharges,
        setInvoiceNumber,
        setPartyAddress,
        setDispatchAddressId,
        setTaxableValue,
        setIsIGSTCalculation,
        setIsSGSTCalculation,
        setAddLessChanges,
        setGstCalculation,
        setGstValue,
        setTcsRate,
        setGrandTotal,
        setMainGrandTotal,
        setFinalAmount,
        setCustomHeaderListTransaction,
        setSameAsBill,
        setCessValue
    } = setStateFunctions;
    if (singleSale) {
        const items = singleSale?.transaction_items || singleSale?.sale_items;
        setGstQuote({
            ...gstQuote,
            gstin: singleSale?.gstin,
            party_ledger_id: singleSale?.party_ledger_id || singleSale?.customer_ledger_id,
            original_inv_date:
                (singleSale?.date || singleSale?.invoice_date) &&
                formattedDate(singleSale?.date || singleSale?.invoice_date),
            mobile:{
                region_iso: singleSale?.region_iso,
                region_code: singleSale?.region_code,
                party_phone_number:  singleSale?.party_phone_number,
                phone_input:(("+"+singleSale?.region_code) || "+91") + singleSale?.party_phone_number,
            }
        });
        setCessValue(singleSale?.cess);
        setAdditionalCharges({
            ...additionalCharges,
            // ...(id &&{upload_document: singleEstimateQuote?.media?.map(media => {
            //     return { original_url: media?.original_url, id: media?.id };
            // })}),
            note: singleSale?.narration,
            terms_and_conditions: singleSale?.term_and_condition,
            ...(id && {upload_document: singleSale?.media?.map(media => {
                return { original_url: media?.original_url, id: media?.id };
            })}),
            additional_detail:
                !singleSale?.additional_charges ||
                singleSale?.additional_charges?.length == 0
                    ? [
                          {
                              ac_ledger_id: null,
                              ac_type: 1,
                              ac_value: "",
                              ac_gst_rate_id: {
                                  label: "",
                                  value: 0,
                                  rate: 0,
                              },
                              ac_total: 0,
                              is_status: ITEM_STATUS.IN_ACTIVE,
                          },
                      ]
                    : singleSale?.additional_charges?.map(charge => {
                          return {
                              ac_ledger_id: charge?.ledger_id,
                              ac_type: charge?.charge_type,
                              ac_value: charge?.value,
                              ac_gst_rate_id: {
                                  value: charge?.gst_rate_id,
                                  rate: Number(charge?.gst_percentage),
                                  label: `${charge?.gst_percentage}%`,
                              },
                              ac_total: customToFixed(charge?.total_without_tax, 2),
                              is_status: ITEM_STATUS.ACTIVE,
                          };
                      }),
        });

        setItemType(saleInvoiceId ? "item" : singleSale?.invoice_type == 2
                    ? "item"
                    : "without_amount"
        );
        setCustomHeaderListTransaction(singleSale?.custom_values)
        setDispatchAddressId(singleSale?.dispatch_address_id);
        setInvoiceNumber(saleInvoiceId ?? singleSale?.invoice_number);
        setInvoiceDetail({
            ...invoiceDetail,
            challan_date: singleSale?.challan_date ? formattedDate(singleSale?.challan_date) : formattedDate(), //singleSale?.date
            invoice_date: singleSale?.invoice_date && formattedDate(singleSale?.invoice_date),
            ...(saleInvoiceId ? {
            challan_number: invoice?.incomeDeliveryChallanInvoice?.invoice_number,
                sell_challan_number: invoice?.incomeDeliveryChallanInvoice?.invoice_number,
            } : {
            challan_number: singleSale?.challan_number,
            })
        });
        const nature_type =
            singleSale?.invoice_type == 2 || singleSale?.sales_item_type == 2
                ? items?.length > 0 && items[0]?.classification_nature_type
                : singleSale?.transaction_ledgers &&
                  singleSale?.transaction_ledgers[0]?.classification_nature_type;
        const nature_name = classificationOptions.find(item => item.value == nature_type);
        setClassification({
            classification_nature: nature_type,
            classification_nature_name: nature_name?.label,
            rcm_applicable:
                singleSale?.invoice_type == 2
                    ? items?.length > 0 && items[0]?.classification_is_rcm_applicable == 1
                        ? true
                        : false
                    : singleSale?.transaction_ledgers &&
                      singleSale?.transaction_ledgers[0]?.classification_is_rcm_applicable == 1
                    ? true
                    : false,
        });
        setGSTCalculationType(
            {
                classification_nature_name: nature_name?.label,
            },
            setIsIGSTCalculation,
            setIsSGSTCalculation
        );

        setItems(
            items?.map((item, index) => {
                setChangeTax(item?.with_tax);
                const multiQuantity = item?.consolidating_items_to_invoice?.split(",") || [];

                if (multiQuantity[0] == "0" || !multiQuantity[0]) {
                    multiQuantity[0] = item?.quantity?.toFixed(item?.decimal_places || 2);
                }

                while (multiQuantity.length < 4) {
                    multiQuantity.push("0");
                }

                const unitOptions = unitOption(item?.units_of_array);
                return {
                    id: item?.id,
                    transaction_item_id: item?.id,
                    selectedItem: item?.item_id,
                    additional_description: item?.additional_description,
                    mrp: item?.mrp,
                    hsn_code: item?.hsn_code,
                    multiQuantity: [0, 0, 0, 0],
                    selectedLedger: item?.ledger_id,
                    quantity: parseFloat(item?.quantity)?.toFixed(item?.decimal_places || 2),
                    free_quantity: item?.free_quantity,
                    rateWithGst: item?.rpu_with_gst,
                    updatedRateWithGst: item?.with_tax
                        ? item?.rpu_with_gst
                        : item?.rpu_without_gst?.toFixed(2),
                    rateWithoutGst: item?.with_tax
                        ? item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                        : item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2),
                    updatedRateWithoutGst: item?.with_tax
                        ? item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                        : item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2),
                    gst_id: item?.gst_id,
                    gst: item?.gst_tax_percentage,
                    discountType: item?.discount_type || 1,
                    discountValue: item?.discount_value || 0,
                    discountType_2: item?.discount_type_2 || 1,
                    discountValue_2: item?.discount_value_2 ? item?.discount_value_2 : 0,
                    total: customToFixed(item?.total, 2),
                    with_tax: item?.with_tax ? 1 : 0,
                    selectedUnit: item?.unit_id,
                    cgstValue: item?.classification_cgst_tax,
                    igstValue: item?.classification_igst_tax,
                    sgstValue: item?.classification_sgst_tax,
                    cessValue: (item?.cess_rate * item?.total) / 100,
                    cessRate: item?.cess_rate,
                    isShowDelete: items?.length > 0 ? true : false,
                    itemUnitOption: unitOptions,
                    decimal_places: item?.decimal_places ?? 2,
                    decimal_places_for_rate: item?.decimal_places_for_rate ?? 2,
                    secondaryUnitOfMeasurement: item?.secondary_unit_of_measurement,
                    conversationRate: item?.conversion_rate,
                    custom_fields: customFieldOptimization(item?.custom_fields),
                    custom_field_inventory_store: formattedCustomField(item?.custom_items_inventory_values),
                    model_inventory_custom_fields: item?.model_inventory_custom_fields,
                    model_custom_fields: item?.model_inventory_custom_fields,
                    model_select_inventory_custom_fields: item?.custom_items_inventory_values,
                    is_status: ITEM_STATUS.ACTIVE,
                };
            })
        );
        if(saleInvoiceId){
            dispatch(rearrangeItemList(TRANSACTION_TYPE.DELIVERY_CHALLAN, 1));
        } else if (singleSale?.invoice_type == 2) {
            dispatch(rearrangeItemList(TRANSACTION_TYPE.DELIVERY_CHALLAN, 1));
        } else if(invoice_type == "challan"){
            dispatch(rearrangeItemList(TRANSACTION_TYPE.DELIVERY_CHALLAN, 2));
        } else {
            dispatch(rearrangeItemList(TRANSACTION_TYPE.DELIVERY_CHALLAN, 1));
        }

        setBrokerDetail({
            broker_id: singleSale?.broker_id,
            broker_percentage: singleSale?.brokerage
                ? singleSale?.brokerage
                : singleSale?.brokerage_for_sale
                ? singleSale?.brokerage_for_sale
                : "",
            brokerage_on_value: singleSale?.brokerage_on_value_type,
        });
        setTransporterDetail({
            transport_id: singleSale?.transport_id,
            transporter_document_number: singleSale?.transporter_document_number,
            transporter_document_date: singleSale?.transporter_document_date
                ? formattedDate(singleSale?.transporter_document_date)
                : null,
            transporter_vehicle_number: singleSale?.transporter_vehicle_number,
        });
        setEwayBillDetail({
            eway_bill_date: singleSale?.eway_bill_date
                ? formattedDate(singleSale?.eway_bill_date)
                : null,
            eway_bill_number: singleSale?.eway_bill_number,
        });
        setOtherDetail({
            ...otherDetail,
            po_number: singleSale?.po_no,
            date: singleSale?.po_date ? formattedDate(singleSale?.po_date) : null,
            creditPeriod: singleSale?.credit_period,
            creditPeriodType: singleSale?.credit_period_type || 1,
        });
        setPaymentLedgerDetail(prev => ({
            ...(singleSale?.payment_details?.length > 0
                ? {
                      payment_detail: singleSale.payment_details.map(item => ({
                          pd_ledger_id: item?.ledger_id,
                          pd_amount: item?.amount,
                          pd_date: item?.date ? formattedDate(item?.date) : "",
                          pd_mode: item?.mode || null,
                          pd_reference_number: item?.reference_no,
                          pd_id: item?.id,
                      })),
                  }
                : { payment_detail:  [
                    {
                        pd_ledger_id: null,
                        pd_date: "",
                        pd_amount: "",
                        pd_mode: null,
                        pd_reference_number: "",
                        is_show_invoice_date: true,
                    }
                ] }),
            tds_amount: singleSale?.tds_tax_id ? singleSale?.tds_amount : "",
            tds_rate: singleSale?.tds_tax_id ? singleSale?.tds_rate : "",
            tds_tax_id: singleSale?.tds_tax_id,
            rounding_type: singleSale?.tds_rounding_method || 1
        }));
        setSameAsBill(singleSale?.same_as_billing);
        setPartyAddress({
            billingAddress: {
                address_1: singleSale?.billing_address?.address_1,
                address_2: singleSale?.billing_address?.address_2,
                country_id: singleSale?.billing_address?.country_id,
                state_id: singleSale?.billing_address?.state_id,
                city_id: singleSale?.billing_address?.city_id,
                state_name: singleSale?.billing_address?.state_name,
                city_name: singleSale?.billing_address?.city_name,
                pin_code: singleSale?.billing_address?.pin_code,
            },
            shippingAddress: {
                shipping_address_id: singleSale?.shipping_address_id,
                address_1: singleSale?.shipping_address?.address_1,
                address_2: singleSale?.shipping_address?.address_2,
                country_id: singleSale?.shipping_address?.country_id,
                state_id: singleSale?.shipping_address?.state_id,
                city_id: singleSale?.shipping_address?.city_id,
                state_name: singleSale?.shipping_address?.state_name,
                city_name: singleSale?.shipping_address?.city_name,
                pin_code: singleSale?.shipping_address?.pin_code,
                shipping_name: singleSale?.shipping_name,
                shipping_gstin: singleSale?.shipping_gstin,
            },
        });
        setTaxableValue(singleSale?.taxable_value);
        setAddLessChanges(
            singleSale?.add_less?.length > 0
                ? singleSale?.add_less?.map(item => {
                      return {
                          al_ledger_id: item.ledger_id,
                          al_is_show_in_print: item.is_show_in_print,
                          al_type: item.type,
                          al_value:
                              item.type == 2 ? parseFloat(item.value) : parseFloat(item.total),
                          al_total: parseFloat(item.total),
                          is_status: ITEM_STATUS.ACTIVE,
                      };
                  })
                : [
                    {
                        al_ledger_id: null,
                        al_is_show_in_print: 1,
                        al_type: 1,
                        al_value: null,
                        al_total: 0,
                    },
                ]
        );
        const roundOffAmount = customToFixed(singleSale?.rounding_amount, 2);
        setGstCalculation({
            round_of_amount: roundOffAmount,
            is_round_off_not_changed: singleSale?.is_round_off_not_changed,
            ...(singleSale?.round_off_method ? {round_off_method: Number(singleSale?.round_off_method)} : {})
        });
        setGstValue({
            igstValue: singleSale?.igst,
            cgstValue: singleSale?.cgst,
            sgstValue: singleSale?.sgst,
        });
        setTcsRate({
            tcs_amount: singleSale?.tcs_tax_id ? customToFixed(singleSale?.tcs_amount, 2) : "",
            tcs_tax_id: singleSale?.tcs_tax_id,
            tcs_rate: singleSale?.tcs_tax_id ? singleSale?.tcs_rate : "",
            tcs_calculated_on: singleSale?.tcs_calculated?.calculated_on,
        });
        setGrandTotal(singleSale?.gross_value || 0);
        setMainGrandTotal(singleSale?.grand_total - roundOffAmount);
        setFinalAmount(singleSale?.grand_total);
    }
};

export const fetchOriInvoiceData = ({
    fetchSaleDetail,
    saleCreateOrReturn,
    setStateFunctions,
    dispatch,
    tableHeaderList,
    accountingTableHeader,
    addLessChanges,
    additionalCharges,
    classificationOptions,
    IncomeCreateOrDebit
}) => {
    if (fetchSaleDetail || saleCreateOrReturn) {
        const {
            invoiceDetail,
            setInvoiceDetail,
            gstQuote,
            setGstQuote,
            setItemType,
            setClassification,
            setItems,
            setAccountingItems,
            setAddLessChanges,
            setAdditionalCharges,
            setPartyAddress,
            setGstCalculation,
            setBrokerDetail,
            setTransporterDetail,
            setEwayBillDetail,
            setOtherDetail,
            setGstValue,
            setTcsRate,
            setPaymentLedgerDetail,
            setGrandTotal,
            setMainGrandTotal,
            setChangeTax,
            setFinalAmount,
            setIsIGSTCalculation,
            setIsSGSTCalculation,
            setTaxableValue,
            setCessValue,
            setCustomHeaderListTransaction,
            setSameAsBill,
        } = setStateFunctions;
        setInvoiceDetail({
            ...invoiceDetail,
            // invoice_date: formattedDate(fetchSaleDetail?.date),
        });
        setCessValue(fetchSaleDetail?.cess);
        setGstQuote({
            ...gstQuote,
            gstin: fetchSaleDetail?.gstin,
            quotes_id: Number(fetchSaleDetail?.estimate_quote_no),
            party_ledger_id: fetchSaleDetail?.customer_ledger_id,
            original_inv_date: fetchSaleDetail?.date ? formattedDate(fetchSaleDetail?.date) : null,
        });
        setCustomHeaderListTransaction(fetchSaleDetail?.custom_values)
        setItemType(fetchSaleDetail?.sales_item_type == 2 ? "item" : "accounting");
        setTaxableValue(fetchSaleDetail?.taxable_value);
        const nature_type =
            fetchSaleDetail?.sales_item_type == 2
                ? fetchSaleDetail?.sale_items[0]?.classification_nature_type
                : fetchSaleDetail?.sale_ledgers[0]?.classification_nature_type;
        const nature_name = classificationOptions.find(item => item.value == nature_type);
        setClassification({
            classification_nature: nature_type,
            classification_nature_name: nature_name?.label,
            rcm_applicable:
                fetchSaleDetail?.sales_item_type == 2
                    ? fetchSaleDetail?.sale_items[0]?.classification_is_rcm_applicable == 1
                        ? true
                        : false
                    : fetchSaleDetail?.sale_ledgers[0]?.classification_is_rcm_applicable == 1
                    ? true
                    : false,
        });
        setGSTCalculationType(
            {
                classification_nature_name: nature_name?.label,
            },
            setIsIGSTCalculation,
            setIsSGSTCalculation
        );

        if (fetchSaleDetail?.sales_item_type == 2) {
            setItems(
                fetchSaleDetail?.sale_items?.map((item, index) => {
                    setChangeTax(item?.with_tax);
                    const multiQuantity = item?.consolidating_items_to_invoice?.split(",") || [];

                    if (multiQuantity[0] == "0") {
                        multiQuantity[0] = item?.quantity?.toFixed(item?.decimal_places || 2);
                    }

                    while (multiQuantity.length < 4) {
                        multiQuantity.push("0");
                    }

                    const unitOptions = unitOption(item?.units_of_array);
                    const filteredCf = item?.custom_items_inventory_values?.filter((item) => item?.sale_quantity || item?.quantity);
                    return {
                        id: item?.id,
                        transaction_item_id: item?.id,
                        selectedItem: item?.item_id,
                        additional_description: item?.additional_description,
                        mrp: item?.mrp,
                        hsn_code: item?.hsn_code,
                        multiQuantity: multiQuantity,
                        selectedLedger: item?.ledger_id,
                        quantity: item?.quantity?.toFixed(item?.decimal_places || 2),
                        free_quantity: item?.free_quantity,
                        rateWithGst: item?.rpu_with_gst,
                        updatedRateWithGst: item?.with_tax
                            ? item?.rpu_with_gst
                            : item?.rpu_without_gst?.toFixed(2),
                        rateWithoutGst: item?.with_tax
                            ? item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                            : item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2),
                        updatedRateWithoutGst: item?.with_tax
                            ? item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                            : item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2),
                        gst_id: item?.gst_id,
                        gst: item?.gst_tax_percentage,
                        itemUnitOption: unitOptions,
                        discountType: item?.discount_type || 1,
                        discountValue: item?.discount_value,
                        discountType_2: item?.discount_type_2 || 1,
                        discountValue_2: item?.discount_value_2 ? item?.discount_value_2 : 0,
                        total: customToFixed(item?.total, 2),
                        with_tax: item?.with_tax ? 1 : 0,
                        selectedUnit: item?.unit_id,
                        cgstValue: item?.classification_cgst_tax,
                        igstValue: item?.classification_igst_tax,
                        sgstValue: item?.classification_sgst_tax,
                        cessValue: (item?.cess_rate * item?.total) / 100,
                        cessRate: item?.cess_rate,
                        isShowDelete: fetchSaleDetail?.sale_items?.length > 1 ? true : false,
                        decimal_places: item?.decimal_places ?? 2,
                        decimal_places_for_rate: item?.decimal_places_for_rate ?? 2,
                        secondaryUnitOfMeasurement: item?.secondary_unit_of_measurement,
                        conversationRate: item?.conversion_rate,
                        item_decimal_places: item?.item_decimal_places,
                        item_decimal_places_for_rate: item?.item_decimal_places_for_rate,
                        custom_fields: customFieldOptimization(item?.custom_fields),
                        custom_field_inventory_store: formattedCustomField(filteredCf, null, true),
                        model_inventory_custom_fields: item?.model_inventory_custom_fields,
                        model_custom_fields: item?.model_inventory_custom_fields,
                        model_select_inventory_custom_fields: filteredCf,
                        is_status: ITEM_STATUS.ACTIVE,
                    };
                })
            );
        } else if (fetchSaleDetail?.sales_item_type == 1) {
            setAccountingItems(
                fetchSaleDetail?.sale_ledgers?.map((item, index) => {
                    setChangeTax(item?.with_tax);
                    return {
                        id: index + 1,
                        selectedLedger: item?.ledger_id,
                        additional_description: item?.additional_description,
                        rateWithGst: item?.rpu_with_gst,
                        gst: item?.gst_tax_percentage,
                        gst_id: item?.gst_id,
                        discountType: item?.discount_type || 1,
                        discountValue: item?.discount_value,
                        discountType_2: item?.discount_type_2 || 1,
                        discountValue_2: item?.discount_value_2 ? item?.discount_value_2 : 0,
                        total: customToFixed(item?.total, 2),
                        cgstValue: item?.classification_cgst_tax,
                        igstValue: item?.classification_igst_tax,
                        sgstValue: item?.classification_sgst_tax,
                        cessValue: (item?.cess_rate * item?.total) / 100,
                        cessRate: item?.cess_rate,
                        with_tax: item?.with_tax ? 1 : 0,
                        rpu: item?.rpu_with_gst || 0,
                        updatedRateWithGst: item?.with_tax
                            ? item?.rpu_with_gst
                            : item?.rpu_without_gst?.toFixed(2),
                        rateWithoutGst: item?.with_tax
                            ? item?.rpu_with_gst
                            : item?.rpu_without_gst?.toFixed(2),
                        updatedRateWithoutGst: item?.with_tax
                            ? item?.rpu_with_gst
                            : item?.rpu_without_gst?.toFixed(2),
                        amountWithoutGst: item?.with_tax
                            ? parseFloat(
                                  item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              )
                            : parseFloat(
                                  item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              ),
                              custom_fields: customFieldOptimization(item?.custom_ledgers_values),
                        is_status: ITEM_STATUS.ACTIVE,
                    };
                })
            );
        }
        if (fetchSaleDetail?.sales_item_type == 2) {
                dispatch(rearrangeItemList(IncomeCreateOrDebit ? TRANSACTION_TYPE.INCOME_CREDIT_NOTE : TRANSACTION_TYPE.INCOME_DEBIT_NOTE, 1));
        } else {
                dispatch(rearrangeItemList(IncomeCreateOrDebit ? TRANSACTION_TYPE.INCOME_CREDIT_NOTE : TRANSACTION_TYPE.INCOME_DEBIT_NOTE, 2));
        }
        setAddLessChanges(
            fetchSaleDetail?.add_less?.length > 0
                ? fetchSaleDetail?.add_less?.map(item => {
                      return {
                          al_ledger_id: item.ledger_id,
                          al_is_show_in_print: item.is_show_in_print,
                          al_type: item.type,
                          al_value:
                              item.type == 2 ? parseFloat(item.value) : parseFloat(item.total),
                          al_total: parseFloat(item.total),
                          is_status: ITEM_STATUS.ACTIVE,
                      };
                  })
                : [
                    {
                        al_ledger_id: null,
                        al_is_show_in_print: 1,
                        al_type: 1,
                        al_value: null,
                        al_total: 0,
                    },
                ]
        );
        setAdditionalCharges({
            ...additionalCharges,
            note: fetchSaleDetail?.narration,
            terms_and_conditions: fetchSaleDetail?.term_and_condition,
            bank_id: fetchSaleDetail?.bank_id,
            additional_detail:
                !fetchSaleDetail?.additional_charges ||
                fetchSaleDetail?.additional_charges?.length == 0
                    ? [
                          {
                              ac_ledger_id: null,
                              ac_type: 1,
                              ac_value: "",
                              ac_gst_rate_id: {
                                  label: "",
                                  value: 0,
                                  rate: 0,
                              },
                              ac_total: 0,
                              is_status: ITEM_STATUS.IN_ACTIVE,
                          },
                      ]
                    : fetchSaleDetail?.additional_charges?.map(charge => {
                          return {
                              ac_ledger_id: charge?.ledger_id,
                              ac_type: charge?.charge_type,
                              ac_value: charge?.value,
                              ac_gst_rate_id: {
                                  value: charge?.gst_rate_id,
                                  rate: Number(charge?.gst_percentage),
                                  label: `${charge?.gst_percentage}%`,
                              },
                              ac_total: customToFixed(charge?.total_without_tax, 2),
                              is_status: ITEM_STATUS.ACTIVE,
                          };
                      }),
        });
        setSameAsBill(fetchSaleDetail?.same_as_billing);
        setPartyAddress({
            billingAddress: {
                address_1: fetchSaleDetail?.billing_address?.address_1,
                address_2: fetchSaleDetail?.billing_address?.address_2,
                country_id: fetchSaleDetail?.billing_address?.country_id,
                state_id: fetchSaleDetail?.billing_address?.state_id,
                city_id: fetchSaleDetail?.billing_address?.city_id,
                state_name: fetchSaleDetail?.billing_address?.state_name,
                city_name: fetchSaleDetail?.billing_address?.city_name,
                pin_code: fetchSaleDetail?.billing_address?.pin_code,
            },
            shippingAddress: {
                address_1: fetchSaleDetail?.shipping_address?.address_1,
                address_2: fetchSaleDetail?.shipping_address?.address_2,
                country_id: fetchSaleDetail?.shipping_address?.country_id,
                state_id: fetchSaleDetail?.shipping_address?.state_id,
                city_id: fetchSaleDetail?.shipping_address?.city_id,
                state_name: fetchSaleDetail?.shipping_address?.state_name,
                city_name: fetchSaleDetail?.shipping_address?.city_name,
                pin_code: fetchSaleDetail?.shipping_address?.pin_code,
                shipping_name: fetchSaleDetail?.shipping_name,
                shipping_gstin: fetchSaleDetail?.shipping_gstin,
                shipping_address_id: fetchSaleDetail?.shipping_address_id
            },
        });
        const roundOffAmount = customToFixed(fetchSaleDetail?.rounding_amount, 2);
        setGstCalculation({
            round_of_amount: roundOffAmount,
            is_round_off_not_changed: fetchSaleDetail?.is_round_off_not_changed,
            ...(fetchSaleDetail?.round_off_method ? {round_off_method: Number(fetchSaleDetail?.round_off_method)} : {})
        });
        setBrokerDetail({
            broker_id: fetchSaleDetail?.broker_id,
            broker_percentage: fetchSaleDetail?.brokerage_for_sale
                ? fetchSaleDetail?.brokerage_for_sale
                : "",
            brokerage_on_value: fetchSaleDetail?.brokerage_on_value_type,
        });
        setTransporterDetail({
            transport_id: fetchSaleDetail?.transport_id,
            transporter_document_number: fetchSaleDetail?.transporter_document_number,
            transporter_document_date: fetchSaleDetail?.transporter_document_date
                ? formattedDate(fetchSaleDetail?.transporter_document_date)
                : null,
            transporter_vehicle_number: fetchSaleDetail?.transporter_vehicle_number,
        });
        setEwayBillDetail({
            eway_bill_date: fetchSaleDetail?.eway_bill_date
                ? formattedDate(fetchSaleDetail?.eway_bill_date)
                : null,
            eway_bill_number: fetchSaleDetail?.eway_bill_number,
        });
        setOtherDetail({
            po_number: fetchSaleDetail?.po_no,
            date: fetchSaleDetail?.po_date ? formattedDate(fetchSaleDetail?.po_date) : null,
            creditPeriod: fetchSaleDetail?.credit_period,
            creditPeriodType: fetchSaleDetail?.credit_period_type || 1,
        });
        setGstValue({
            igstValue: fetchSaleDetail?.igst,
            cgstValue: fetchSaleDetail?.cgst,
            sgstValue: fetchSaleDetail?.sgst,
        });
        setTcsRate({
            tcs_amount: fetchSaleDetail?.tcs_tax_id ? customToFixed(fetchSaleDetail?.tcs_amount, 2) : "",
            tcs_tax_id: fetchSaleDetail?.tcs_tax_id,
            tcs_rate: fetchSaleDetail?.tcs_tax_id ? fetchSaleDetail?.tcs_rate : "",
            tcs_calculated_on: fetchSaleDetail?.tcs_calculated?.calculated_on,
        });
        setPaymentLedgerDetail(prev => ({
            payment_detail:  [
                    {
                        pd_ledger_id: null,
                        pd_date: "",
                        pd_amount: "",
                        pd_mode: null,
                        pd_reference_number: "",
                        is_show_invoice_date: true,
                    }
                ],
            tds_amount: fetchSaleDetail?.tds_tax_id ? fetchSaleDetail?.tds_amount : "",
            tds_rate: fetchSaleDetail?.tds_tax_id ? fetchSaleDetail?.tds_rate : "",
            tds_tax_id: fetchSaleDetail?.tds_tax_id,
            rounding_type: fetchSaleDetail?.tds_rounding_method || 1
        }));
        setGrandTotal(fetchSaleDetail?.gross_value || 0);
        setMainGrandTotal(fetchSaleDetail?.grand_total - roundOffAmount);
        setFinalAmount(fetchSaleDetail?.grand_total);
        // setTimeout(() => {
        //     dispatch(getSaleById(""));
        // }, 1000);
    }
};
export const fetchExpenseOriInvoiceData = (
    fetchSaleDetail,
    {
        ExpenseCreateOrDebit,
        setStateFunctions,
        dispatch,
        tableHeaderList,
        accountingTableHeader,
        addLessChanges,
        additionalCharges,
        classificationOptions,
    }
) => {
    if (fetchSaleDetail) {
        const {
            invoiceDetail,
            setInvoiceDetail,
            gstQuote,
            setGstQuote,
            setItemType,
            setClassification,
            setItems,
            setAccountingItems,
            setAddLessChanges,
            setAdditionalCharges,
            setPartyAddress,
            setGstCalculation,
            setBrokerDetail,
            setTransporterDetail,
            setEwayBillDetail,
            setOtherDetail,
            setGstValue,
            setTcsRate,
            setPaymentLedgerDetail,
            setGrandTotal,
            setMainGrandTotal,
            setChangeTax,
            setFinalAmount,
            setIsIGSTCalculation,
            setIsSGSTCalculation,
            setTaxableValue,
            setCustomHeaderListTransaction,
            setSameAsBill,
            setCessValue,
            expenseEdit
        } = setStateFunctions;
        setGstQuote({
            ...gstQuote,
            original_inv_date:
                fetchSaleDetail?.original_inv_date || fetchSaleDetail?.voucher_date
                    ? formattedDate(
                          fetchSaleDetail?.original_inv_date || fetchSaleDetail?.voucher_date
                      )
                    : null,
        });
        setCustomHeaderListTransaction(fetchSaleDetail?.custom_values)
        setItemType(fetchSaleDetail?.purchase_item_type == 2 ? "item" : "accounting");
        setTaxableValue(fetchSaleDetail?.taxable_value);
        const nature_type =
            fetchSaleDetail?.purchase_item_type == 2
                ? fetchSaleDetail?.purchase_transaction_items[0]?.classification_nature_type
                : fetchSaleDetail?.purchase_transaction_ledger[0]?.classification_nature_type;
        const nature_name = classificationOptions.find(item => item.value == nature_type);
        setClassification({
            classification_nature: nature_type,
            classification_nature_name: nature_name?.label,
            rcm_applicable:
                fetchSaleDetail?.purchase_item_type == 2
                    ? fetchSaleDetail?.purchase_transaction_items[0]
                          ?.classification_is_rcm_applicable == 1
                        ? true
                        : false
                    : fetchSaleDetail?.purchase_transaction_ledger[0]
                          ?.classification_is_rcm_applicable == 1
                    ? true
                    : false,
        });
        setGSTCalculationType(
            {
                classification_nature_name: nature_name?.label,
            },
            setIsIGSTCalculation,
            setIsSGSTCalculation
        );
        setCessValue(fetchSaleDetail?.cess);
        if (fetchSaleDetail?.purchase_item_type == 2) {
            setItems(
                fetchSaleDetail?.purchase_transaction_items?.map((item, index) => {
                    setChangeTax(item?.with_tax);
                    const multiQuantity = item?.consolidating_items_to_invoice?.split(",") || [];

                    if (multiQuantity[0] == "0") {
                        multiQuantity[0] = item?.quantity?.toFixed(item?.decimal_places || 2);
                    }

                    while (multiQuantity.length < 4) {
                        multiQuantity.push("0");
                    }

                    const unitOptions = unitOption(item?.units_of_array);
                    const filteredCf = item?.custom_items_inventory_values?.filter((item) => item?.sale_quantity || item?.quantity);
                    return {
                        id: item?.id,
                        transaction_item_id: item?.id,
                        selectedItem: item?.item_id,
                        additional_description: item?.additional_description,
                        mrp: item?.mrp,
                        hsn_code: item?.hsn_code,
                        multiQuantity: multiQuantity,
                        selectedLedger: item?.ledger_id,
                        quantity: item?.quantity?.toFixed(item?.decimal_places || 2),
                        free_quantity: item?.free_quantity,
                        rateWithGst: item?.rpu_with_gst,
                        updatedRateWithGst: item?.with_tax
                            ? item?.rpu_with_gst
                            : item?.rpu_without_gst?.toFixed(2),
                        rateWithoutGst: item?.with_tax
                            ? item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                            : item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2),
                        updatedRateWithoutGst: item?.with_tax
                            ? item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                            : item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2),
                        gst_id: item?.gst_id,
                        gst: item?.gst_tax_percentage,
                        itemUnitOption: unitOptions,
                        discountType: item?.discount_type || 1,
                        discountValue: item?.discount_value,
                        discountType_2: item?.discount_type_2 || 1,
                        discountValue_2: item?.discount_value_2 ? item?.discount_value_2 : 0,
                        total: customToFixed(item?.total, 2),
                        with_tax: item?.with_tax ? 1 : 0,
                        selectedUnit: item?.unit_id,
                        cgstValue: item?.classification_cgst_tax,
                        igstValue: item?.classification_igst_tax,
                        sgstValue: item?.classification_sgst_tax,
                        cessValue: (item?.cess_rate * item?.total) / 100,
                        cessRate: item?.cess_rate,
                        isShowDelete:
                            fetchSaleDetail?.purchase_transaction_items?.length > 1 ? true : false,
                        decimal_places: item?.decimal_places ?? 2,
                        decimal_places_for_rate: item?.decimal_places_for_rate ?? 2,
                        secondaryUnitOfMeasurement: item?.secondary_unit_of_measurement,
                        conversationRate: item?.conversion_rate,
                        item_decimal_places: item?.item_decimal_places,
                        item_decimal_places_for_rate: item?.item_decimal_places_for_rate,
                        custom_fields: customFieldOptimization(item?.custom_fields),
                        custom_field_inventory_store: formattedCustomField(filteredCf, null, true),
                        model_inventory_custom_fields: item?.model_inventory_custom_fields,
                        model_custom_fields: item?.model_inventory_custom_fields,
                        model_select_inventory_custom_fields: filteredCf,
                        is_status: ITEM_STATUS.ACTIVE,
                    };
                })
            );
            clearAccountingInvoice(setAccountingItems)
        } else if (fetchSaleDetail?.purchase_item_type == 1) {
            setAccountingItems(
                fetchSaleDetail?.purchase_transaction_ledger?.map((item, index) => {
                    setChangeTax(item?.with_tax);
                    return {
                        id: index + 1,
                        selectedLedger: item?.ledger_id,
                        additional_description: item?.additional_description,
                        rateWithGst: item?.rpu_with_gst,
                        gst: item?.gst_tax_percentage,
                        gst_id: item?.gst_id,
                        discountType: item?.discount_type || 1,
                        discountValue: item?.discount_value,
                        discountType_2: item?.discount_type_2 || 1,
                        discountValue_2: item?.discount_value_2 ? item?.discount_value_2 : 0,
                        total: customToFixed(item?.total, 2),
                        cgstValue: item?.classification_cgst_tax,
                        igstValue: item?.classification_igst_tax,
                        sgstValue: item?.classification_sgst_tax,
                        cessValue: (item?.cess_rate * item?.total) / 100,
                        cessRate: item?.cess_rate,
                        with_tax: item?.with_tax ? 1 : 0,
                        rpu: item?.rpu_with_gst || 0,
                        updatedRateWithGst: item?.with_tax
                            ? item?.rpu_with_gst
                            : item?.rpu_without_gst?.toFixed(2),
                        rateWithoutGst: item?.with_tax
                            ? item?.rpu_with_gst
                            : item?.rpu_without_gst?.toFixed(2),
                        updatedRateWithoutGst: item?.with_tax
                            ? item?.rpu_with_gst
                            : item?.rpu_without_gst?.toFixed(2),
                        amountWithoutGst: item?.with_tax
                            ? parseFloat(
                                  item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              )
                            : parseFloat(
                                  item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              ),
                        custom_fields: customFieldOptimization(item?.custom_ledgers_values),
                        is_status: ITEM_STATUS.ACTIVE,
                    };
                })
            );
            clearItemInvoice(setItems)
        }
        if (fetchSaleDetail?.purchase_item_type == 2) {

            dispatch(rearrangeItemList(ExpenseCreateOrDebit ? TRANSACTION_TYPE.EXPENSE_CREDIT_NOTE : TRANSACTION_TYPE.EXPENSE_DEBIT_NOTE, 1));
        } else {

            dispatch(rearrangeItemList(ExpenseCreateOrDebit ? TRANSACTION_TYPE.EXPENSE_CREDIT_NOTE : TRANSACTION_TYPE.EXPENSE_DEBIT_NOTE, 2));
        }
        setAddLessChanges(
            fetchSaleDetail?.add_less?.length == 0
                ? addLessChanges
                : fetchSaleDetail?.add_less?.map(item => {
                      return {
                          al_ledger_id: item.ledger_id,
                          al_is_show_in_print: item.is_show_in_print,
                          al_type: item.type,
                          al_value:
                              item.type == 2 ? parseFloat(item.value) : parseFloat(item.total),
                          al_total: parseFloat(item.total),
                          is_status: ITEM_STATUS.ACTIVE,
                      };
                  })
        );
        setAdditionalCharges({
            ...additionalCharges,
            note: fetchSaleDetail?.narration,
            terms_and_conditions: fetchSaleDetail?.term_and_condition,
            additional_detail:
                !fetchSaleDetail?.additional_charges ||
                fetchSaleDetail?.additional_charges?.length == 0
                    ? [
                          {
                              ac_ledger_id: null,
                              ac_type: 1,
                              ac_value: "",
                              ac_gst_rate_id: {
                                  label: "",
                                  value: 0,
                                  rate: 0,
                              },
                              ac_total: 0,
                              is_status: ITEM_STATUS.IN_ACTIVE,
                          },
                      ]
                    : fetchSaleDetail?.additional_charges?.map(charge => {
                          return {
                              ac_ledger_id: charge?.ledger_id,
                              ac_type: charge?.charge_type,
                              ac_value: charge?.value,
                              ac_gst_rate_id: {
                                  value: charge?.gst_rate_id,
                                  rate: Number(charge?.gst_percentage),
                                  label: `${charge?.gst_percentage}%`,
                              },
                              ac_total: customToFixed(charge?.total_without_tax, 2),
                              is_status: ITEM_STATUS.ACTIVE,
                          };
                      }),
        });
        setSameAsBill(fetchSaleDetail?.same_as_billing);
        setPartyAddress({
            billingAddress: {
                address_1: fetchSaleDetail?.billing_address?.address_1,
                address_2: fetchSaleDetail?.billing_address?.address_2,
                country_id: fetchSaleDetail?.billing_address?.country_id,
                state_id: fetchSaleDetail?.billing_address?.state_id,
                city_id: fetchSaleDetail?.billing_address?.city_id,
                state_name: fetchSaleDetail?.billing_address?.state_name,
                city_name: fetchSaleDetail?.billing_address?.city_name,
                pin_code: fetchSaleDetail?.billing_address?.pin_code,
            },
            shippingAddress: {
                address_1: fetchSaleDetail?.dispatch_address?.address_1,
                address_2: fetchSaleDetail?.dispatch_address?.address_2,
                country_id: fetchSaleDetail?.dispatch_address?.country_id,
                state_id: fetchSaleDetail?.dispatch_address?.state_id,
                city_id: fetchSaleDetail?.dispatch_address?.city_id,
                state_name: fetchSaleDetail?.shipping_address?.state_name,
                city_name: fetchSaleDetail?.shipping_address?.city_name,
                pin_code: fetchSaleDetail?.dispatch_address?.pin_code,
                shipping_name: fetchSaleDetail?.shipping_name,
                shipping_gstin: fetchSaleDetail?.shipping_gstin,
                shipping_address_id: fetchSaleDetail?.shipping_address_id
            },
        });
        const roundOffAmount = customToFixed(fetchSaleDetail?.rounding_amount, 2);
        setGstCalculation({
            round_of_amount: roundOffAmount,
            is_round_off_not_changed: fetchSaleDetail?.is_round_off_not_changed,
            ...(fetchSaleDetail?.round_off_method ? {round_off_method: Number(fetchSaleDetail?.round_off_method)} : {})
        });
        setBrokerDetail({
            broker_id: fetchSaleDetail?.broker_id,
            broker_percentage: fetchSaleDetail?.brokerage_for_sale
                ? fetchSaleDetail?.brokerage_for_sale
                : "",
            brokerage_on_value: fetchSaleDetail?.brokerage_on_value_type,
        });
        setTransporterDetail({
            transport_id: fetchSaleDetail?.transport_id,
            transporter_document_number: fetchSaleDetail?.transporter_document_number,
            transporter_document_date: fetchSaleDetail?.transporter_document_date
                ? formattedDate(fetchSaleDetail?.transporter_document_date)
                : null,
            transporter_vehicle_number: fetchSaleDetail?.transporter_vehicle_number,
        });
        setEwayBillDetail({
            eway_bill_date: fetchSaleDetail?.eway_bill_date
                ? formattedDate(fetchSaleDetail?.eway_bill_date)
                : null,
            eway_bill_number: fetchSaleDetail?.eway_bill_number,
        });
        setOtherDetail({
            po_number: fetchSaleDetail?.po_no,
            date: fetchSaleDetail?.po_date ? formattedDate(fetchSaleDetail?.po_date) : null,
            creditPeriod: fetchSaleDetail?.credit_period,
            creditPeriodType: fetchSaleDetail?.credit_period_type || 1,
        });
        setGstValue({
            igstValue: fetchSaleDetail?.igst,
            cgstValue: fetchSaleDetail?.cgst,
            sgstValue: fetchSaleDetail?.sgst,
        });
        setTcsRate({
            tcs_amount: fetchSaleDetail?.tcs_tax_id ? customToFixed(fetchSaleDetail?.tcs_amount, 2) : "",
            tcs_tax_id: fetchSaleDetail?.tcs_tax_id,
            tcs_rate: fetchSaleDetail?.tcs_tax_id ? fetchSaleDetail?.tcs_rate : "",
            tcs_calculated_on: fetchSaleDetail?.tcs_calculated?.calculated_on,
        });
        setPaymentLedgerDetail(prev => ({
                // ...prev,
                ...(fetchSaleDetail?.payment_details?.length > 0 && expenseEdit
                    ? {
                          payment_detail: fetchSaleDetail.payment_details.map(item => ({
                              pd_ledger_id: item?.ledger_id,
                              pd_amount: item?.amount,
                              pd_date: item?.date ? formattedDate(item?.date) : "",
                              pd_mode: item?.mode || null,
                              pd_reference_number: item?.reference_no,
                            //   pd_id: item?.id,
                          })),
                      }
                    : { payment_detail:  [
                        {
                            pd_ledger_id: null,
                            pd_date: "",
                            pd_amount: "",
                            pd_mode: null,
                            pd_reference_number: "",
                            is_show_invoice_date: true,
                        }
                    ] }),
                // tds_amount: fetchSaleDetail?.tds_tax_id ? fetchSaleDetail?.tds_amount : "",
                // tds_rate: fetchSaleDetail?.tds_tax_id ? fetchSaleDetail?.tds_rate : "",
                // tds_tax_id: fetchSaleDetail?.tds_tax_id,
                tds_amount: (fetchSaleDetail?.tds_tax_id || fetchSaleDetail?.ledger_of_tds) ? fetchSaleDetail?.tds_amount : "",
                tds_rate: (fetchSaleDetail?.tds_tax_id || fetchSaleDetail?.ledger_of_tds) ? fetchSaleDetail?.tds_rate : "",
                tds_tax_id: fetchSaleDetail?.tds_tax_id || fetchSaleDetail?.ledger_of_tds,
                rounding_type: fetchSaleDetail?.tds_rounding_method || 1
        }));
        setGrandTotal(fetchSaleDetail?.gross_value || fetchSaleDetail?.taxable_value || 0);
        setMainGrandTotal(fetchSaleDetail?.grand_total - roundOffAmount);
        setFinalAmount(fetchSaleDetail?.grand_total);
        // setTimeout(() => {
        //     dispatch(getSaleById(""));
        // }, 1000);
    }
};
export const fetchPurchaseOrderNumber = ({
    fetchPurchaseDetail,
    purchaseOrderToPurchase,
    setStateFunctions,
    dispatch,
    tableHeaderList,
    accountingTableHeader,
    addLessChanges,
    additionalCharges,
    classificationOptions,
    purchaseOrderId,
    bookPurchase,
    company
}) => {
    if (fetchPurchaseDetail) {
        const {
            invoiceDetail,
            setInvoiceDetail,
            gstQuote,
            setGstQuote,
            setItemType,
            setClassification,
            setItems,
            setAccountingItems,
            setAddLessChanges,
            setAdditionalCharges,
            setPartyAddress,
            setGstCalculation,
            setBrokerDetail,
            setTransporterDetail,
            setEwayBillDetail,
            setOtherDetail,
            setGstValue,
            setTcsRate,
            setPaymentLedgerDetail,
            setGrandTotal,
            setMainGrandTotal,
            setChangeTax,
            setFinalAmount,
            setIsIGSTCalculation,
            setIsSGSTCalculation,
            setTaxableValue,
            setCessValue,
            setUpdatePurchaseNumberOptions,
            setCustomHeaderListTransaction,
            setSameAsBill
        } = setStateFunctions;
        if (!purchaseOrderId) {
            const isCurrentYear = isCurrentFinancialYear(company?.company)
            setInvoiceDetail({
                ...invoiceDetail,
                // invoice_date: purchaseOrderToPurchase?.date && formattedDate(purchaseOrderToPurchase?.date),
                invoice_date: isCurrentYear ? formattedDate() : formattedDate(purchaseOrderToPurchase?.date),
            });
        }
        setGstQuote({
            ...gstQuote,
            gstin: purchaseOrderToPurchase?.gstin,
            quotes_id: purchaseOrderToPurchase?.estimate_quote_no,
            party_ledger_id: purchaseOrderToPurchase?.party_ledger_id,
        });
        setCustomHeaderListTransaction(purchaseOrderToPurchase?.custom_values)
        setUpdatePurchaseNumberOptions(
            purchaseOrderToPurchase?.purchase_order_no
                ? convertIntoDropdownList(purchaseOrderToPurchase?.purchase_order_list)
                : null
        );
        if (purchaseOrderId) {
            setGstQuote({
                ...gstQuote,
                gstin: purchaseOrderToPurchase?.gstin,
                party_ledger_id: purchaseOrderToPurchase?.party_ledger_id,
                purchase_number: [
                    {
                        value: parseInt(purchaseOrderId),
                        label: purchaseOrderToPurchase?.full_order_number,
                    },
                ],
            });
        }
        setItemType(purchaseOrderToPurchase?.order_type == 2 ? "item" : "accounting");
        const nature_type =
            fetchPurchaseDetail?.order_type == 2
                ? fetchPurchaseDetail?.allTransactionItems[0]?.classification_nature_type
                : fetchPurchaseDetail?.allTransactionItems[0]?.classification_nature_type;
        const nature_name = classificationOptions.find(item => item.value == nature_type?.id);
        setClassification({
            classification_nature: nature_type?.id,
            classification_nature_name: nature_name?.label,
            rcm_applicable:
                fetchPurchaseDetail?.order_type == 2
                    ? fetchPurchaseDetail?.allTransactionItems[0]?.classification_is_rcm_applicable ==
                      1
                        ? true
                        : false
                    : fetchPurchaseDetail?.allTransactionItems[0]
                          ?.classification_is_rcm_applicable == 1
                    ? true
                    : false,
        });
        setGSTCalculationType(
            {
                classification_nature_name: nature_name?.label,
            },
            setIsIGSTCalculation,
            setIsSGSTCalculation
        );
        if (purchaseOrderToPurchase?.order_type == 2) {
            setItems(
                fetchPurchaseDetail?.allTransactionItems?.map((item, index) => {
                    setChangeTax(item?.with_tax);
                    const multiQuantity = item?.consolidating_items_to_invoice?.split(",") || [];

                    if (multiQuantity[0] == "0") {
                        multiQuantity[0] = item?.quantity?.toFixed(item?.decimal_places || 2);
                    }

                    while (multiQuantity.length < 4) {
                        multiQuantity.push("0");
                    }

                    const unitOptions = unitOptionWithKey(item?.unitOfArray);
                    return {
                        id: item?.id,
                        transaction_item_id: item?.id,
                        selectedItem: item?.item_id,
                        additional_description: item?.additional_description,
                        mrp: item?.mrp,
                        hsn_code: item?.hsn_code,
                        multiQuantity: multiQuantity,
                        selectedLedger: item?.ledger_id,
                        quantity: item?.quantity?.toFixed(item?.decimal_places || 2),
                        free_quantity: item?.free_quantity,
                        rateWithGst: item?.rpu_with_gst,
                        updatedRateWithGst: item?.with_tax
                            ? item?.rpu_with_gst
                            : item?.rpu_without_gst?.toFixed(2),
                        rateWithoutGst: item?.with_tax
                            ? item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                            : item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2),
                        updatedRateWithoutGst: item?.with_tax
                            ? item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                            : item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2),
                        gst_id: item?.gst_id,
                        gst: item?.gst_tax_percentage,
                        discountType: item?.discount_type || 1,
                        discountValue: item?.discount_value,
                        discountType_2: item?.discount_type_2 || 1,
                        discountValue_2: item?.discount_value_2 ? item?.discount_value_2 : 0,
                        total: customToFixed(item?.total, 2),
                        with_tax: item?.with_tax ? 1 : 0,
                        selectedUnit: item?.unit_id,
                        cgstValue: item?.classification_cgst_tax,
                        igstValue: item?.classification_igst_tax,
                        sgstValue: item?.classification_sgst_tax,
                        cessValue: (item?.cess_rate * item?.total) / 100,
                        cessRate: item?.cess_rate,
                        isShowDelete:
                        fetchPurchaseDetail?.allTransactionItems?.length > 1 ? true : false,
                        itemUnitOption: unitOptions,
                        decimal_places: item?.decimal_places ?? 2,
                        decimal_places_for_rate: item?.decimal_places_for_rate ?? 2,
                        secondaryUnitOfMeasurement: item?.secondary_unit_of_measurement,
                        conversationRate: item?.conversion_rate,
                        item_decimal_places: item?.item_decimal_places,
                        item_decimal_places_for_rate: item?.item_decimal_places_for_rate,
                        custom_fields: customFieldOptimization(item?.custom_fields),
                        custom_field_inventory_store: formattedCustomField(item?.custom_items_inventory_values),
                        model_inventory_custom_fields: item?.model_inventory_custom_fields,
                        model_custom_fields: item?.model_inventory_custom_fields,
                        model_select_inventory_custom_fields: item?.custom_items_inventory_values,
                        is_status: ITEM_STATUS.ACTIVE,
                    };
                })
            );
        } else if (purchaseOrderToPurchase?.order_type == 1) {
            setAccountingItems(
                fetchPurchaseDetail?.allTransactionItems?.map((item, index) => {
                    setChangeTax(item?.with_tax);
                    return {
                        id: item?.id,
                        selectedLedger: item?.ledger_id,
                        additional_description: item?.additional_description,
                        rateWithGst: item?.rpu_with_gst,
                        gst: item?.gst_tax_percentage,
                        gst_id: item?.gst_id,
                        discountType: item?.discount_type || 1,
                        discountValue: item?.discount_value,
                        discountType_2: item?.discount_type_2 || 1,
                        discountValue_2: item?.discount_value_2 ? item?.discount_value_2 : 0,
                        total: customToFixed(item?.total, 2),
                        cgstValue: item?.classification_cgst_tax,
                        igstValue: item?.classification_igst_tax,
                        sgstValue: item?.classification_sgst_tax,
                        cessValue: (item?.cess_rate * item?.total) / 100,
                        cessRate: item?.cess_rate,
                        with_tax: item?.with_tax ? 1 : 0,
                        rpu: item?.rpu_with_gst || 0,
                        updatedRateWithGst: item?.with_tax
                            ? item?.rpu_with_gst
                            : item?.rpu_without_gst?.toFixed(2),
                        rateWithoutGst: item?.with_tax
                            ? item?.rpu_with_gst
                            : item?.rpu_without_gst?.toFixed(2),
                        updatedRateWithoutGst: item?.with_tax
                            ? item?.rpu_with_gst
                            : item?.rpu_without_gst?.toFixed(2),
                        amountWithoutGst: item?.with_tax
                            ? item?.rpu_with_gst?.toFixed(2)
                            : item?.rpu_without_gst?.toFixed(2),
                        custom_fields: customFieldOptimization(item?.custom_ledgers_values),
                        is_status: ITEM_STATUS.ACTIVE,
                    };
                })
            );
        }
        if (purchaseOrderToPurchase?.order_type == 2) {

            dispatch(rearrangeItemList(TRANSACTION_TYPE.PURCHASE, 1));
        } else {

            dispatch(rearrangeItemList(TRANSACTION_TYPE.PURCHASE, 2));
        }
        setAddLessChanges(
            purchaseOrderToPurchase?.add_less?.length == 0
                ? addLessChanges
                : purchaseOrderToPurchase?.add_less?.map(item => {
                      return {
                          al_ledger_id: item.ledger_id,
                          al_is_show_in_print: item.is_show_in_print,
                          al_type: item.type,
                          al_value:
                              item.type == 2 ? parseFloat(item.value) : parseFloat(item.total),
                          al_total: parseFloat(item.total),
                          is_status: ITEM_STATUS.ACTIVE,
                      };
                  })
        );
        setAdditionalCharges({
            ...additionalCharges,
            upload_document: !bookPurchase
                ? purchaseOrderToPurchase?.media?.map(media => {
                      return { original_url: media?.original_url, id: media?.id };
                  })
                : "",
            note: purchaseOrderToPurchase?.narration,
            terms_and_conditions: purchaseOrderToPurchase?.term_and_condition,
            additional_detail:
                !purchaseOrderToPurchase?.additional_charges ||
                purchaseOrderToPurchase?.additional_charges?.length == 0
                    ? [
                          {
                              ac_ledger_id: null,
                              ac_type: 1,
                              ac_value: "",
                              ac_gst_rate_id: {
                                  label: "",
                                  value: 0,
                                  rate: 0,
                              },
                              ac_total: 0,
                              is_status: ITEM_STATUS.IN_ACTIVE,
                          },
                      ]
                    : purchaseOrderToPurchase?.additional_charges?.map(charge => {
                          return {
                              ac_ledger_id: charge?.ledger_id,
                              ac_type: charge?.charge_type,
                              ac_value: charge?.value,
                              ac_gst_rate_id: {
                                  value: charge?.gst_rate_id,
                                  rate: Number(charge?.gst_percentage),
                                  label: `${charge?.gst_percentage}%`,
                              },
                              ac_total: customToFixed(charge?.total_without_tax, 2),
                              is_status: ITEM_STATUS.ACTIVE,
                          };
                      }),
        });
        setSameAsBill(purchaseOrderToPurchase?.same_as_billing);
        setPartyAddress({
            billingAddress: {
                address_1: purchaseOrderToPurchase?.billing_address?.address_1 || "",
                address_2: purchaseOrderToPurchase?.billing_address?.address_2 || "",
                country_id: purchaseOrderToPurchase?.billing_address?.country_id || "",
                state_id: purchaseOrderToPurchase?.billing_address?.state_id || "",
                city_id: purchaseOrderToPurchase?.billing_address?.city_id || "",
                state_name: purchaseOrderToPurchase?.billing_address?.state_name || "",
                city_name: purchaseOrderToPurchase?.billing_address?.city_name || "",
                pin_code: purchaseOrderToPurchase?.billing_address?.pin_code || "",
            },
            shippingAddress: {
                address_1: purchaseOrderToPurchase?.shipping_address?.address_1 || "",
                address_2: purchaseOrderToPurchase?.shipping_address?.address_2 || "",
                country_id: purchaseOrderToPurchase?.shipping_address?.country_id || "",
                state_id: purchaseOrderToPurchase?.shipping_address?.state_id || "",
                city_id: purchaseOrderToPurchase?.shipping_address?.city_id || "",
                state_name: purchaseOrderToPurchase?.shipping_address?.state_name,
                city_name: purchaseOrderToPurchase?.shipping_address?.city_name,
                pin_code: purchaseOrderToPurchase?.shipping_address?.pin_code || "",
                shipping_name: purchaseOrderToPurchase?.shipping_name,
                shipping_gstin: purchaseOrderToPurchase?.shipping_gstin,
                shipping_address_id: purchaseOrderToPurchase?.shipping_address_id
            },
        });
        const roundOffAmount = customToFixed(purchaseOrderToPurchase?.round_off_amount, 2);
        setGstCalculation({
            round_of_amount: roundOffAmount,
            is_round_off_not_changed: purchaseOrderToPurchase?.is_round_off_not_changed,
            ...(purchaseOrderToPurchase?.round_off_method ? {round_off_method: Number(purchaseOrderToPurchase?.round_off_method)} : {})
        });
        setBrokerDetail({
            broker_id: purchaseOrderToPurchase?.broker_id,
            broker_percentage: purchaseOrderToPurchase?.brokerage_for_sale
                ? purchaseOrderToPurchase?.brokerage_for_sale : purchaseOrderToPurchase?.brokerage ? purchaseOrderToPurchase?.brokerage
                : null,
            brokerage_on_value: purchaseOrderToPurchase?.brokerage_on_value_type,
        });
        setTransporterDetail({
            transport_id: purchaseOrderToPurchase?.transport_id,
            transporter_document_number: purchaseOrderToPurchase?.transporter_document_number,
            transporter_document_date: purchaseOrderToPurchase?.transporter_document_date
                ? formattedDate(purchaseOrderToPurchase?.transporter_document_date)
                : null,
            transporter_vehicle_number: purchaseOrderToPurchase?.transporter_vehicle_number,
        });
        setEwayBillDetail({
            eway_bill_date: purchaseOrderToPurchase?.eway_bill_date
                ? formattedDate(purchaseOrderToPurchase?.eway_bill_date)
                : null,
            eway_bill_number: purchaseOrderToPurchase?.eway_bill_number,
        });
        setOtherDetail({
            po_number: purchaseOrderToPurchase?.po_no,
            date: purchaseOrderToPurchase?.po_date ? formattedDate(purchaseOrderToPurchase?.po_date) : null,
            creditPeriod: purchaseOrderToPurchase?.credit_period,
            creditPeriodType: purchaseOrderToPurchase?.credit_period_type || 1,
        });
        setGstValue({
            igstValue: purchaseOrderToPurchase?.igst,
            cgstValue: purchaseOrderToPurchase?.cgst,
            sgstValue: purchaseOrderToPurchase?.sgst,
        });
        setTcsRate({
            tcs_amount: purchaseOrderToPurchase?.tcs_tax_id ? customToFixed(purchaseOrderToPurchase?.tcs_amount, 2) : '',
            tcs_tax_id: purchaseOrderToPurchase?.tcs_tax_id,
            tcs_rate: purchaseOrderToPurchase?.tcs_tax_id ? purchaseOrderToPurchase?.tcs_rate : '',
            tcs_calculated_on: purchaseOrderToPurchase?.tcs_calculated?.calculated_on,
        });
        setPaymentLedgerDetail(prev => ({
            ...(purchaseOrderToPurchase?.payment_details?.length > 0
                ? {
                      payment_detail: purchaseOrderToPurchase.payment_details.map(item => ({
                          pd_ledger_id: item?.ledger_id,
                          pd_amount: item?.amount,
                          pd_date: item?.date ? formattedDate(item?.date) : "",
                          pd_mode: item?.mode || null,
                          pd_reference_number: item?.reference_no,
                          pd_id: item?.id,
                      })),
                  }
                : { payment_detail:  [
                    {
                        pd_ledger_id: null,
                        pd_date: formattedDate(),
                        pd_amount: "",
                        pd_mode: null,
                        pd_reference_number: "",
                        is_show_invoice_date: true,
                    }
                ] }),
            tds_amount: purchaseOrderToPurchase?.tds_tax_id ? purchaseOrderToPurchase?.tds_amount : "",
            tds_rate: purchaseOrderToPurchase?.tds_tax_id ? purchaseOrderToPurchase?.tds_rate : "",
            tds_tax_id: purchaseOrderToPurchase?.tds_tax_id,
            rounding_type: purchaseOrderToPurchase?.tds_rounding_method || 1
        }));
        setGrandTotal(purchaseOrderToPurchase?.gross_value);
        setMainGrandTotal(purchaseOrderToPurchase?.grand_total - roundOffAmount);
        setTaxableValue(purchaseOrderToPurchase?.taxable_value);
        setCessValue(purchaseOrderToPurchase?.cess);
        setFinalAmount(purchaseOrderToPurchase?.grand_total);
        // setTimeout(() => {
        //     dispatch(getSaleById(""));
        // }, 1000);
    }
};

export const fetchPurchaseReturnInvoiceDetail = ({
    getInvoiceDetail,
    setStateFunctions,
    dispatch,
    tableHeaderList,
    accountingTableHeader,
    classificationOptions,
}) => {
    if (getInvoiceDetail) {
        const {
            gstQuote,
            setGstQuote,
            setItemType,
            setClassification,
            setItems,
            setAccountingItems,
            addLessChanges,
            setAddLessChanges,
            additionalCharges,
            setAdditionalCharges,
            setPartyAddress,
            setGstCalculation,
            setBrokerDetail,
            setTransporterDetail,
            setEwayBillDetail,
            setOtherDetail,
            setGstValue,
            setTcsRate,
            setPaymentLedgerDetail,
            setGrandTotal,
            setTaxableValue,
            setMainGrandTotal,
            setChangeTax,
            setFinalAmount,
            setIsIGSTCalculation,
            setIsSGSTCalculation,
            localDispatchAddress,
            setLocalDispatchAddress,
            setCessValue,
            setCustomHeaderListTransaction,
            setSameAsBill,
            incomeEdit
        } = setStateFunctions;

        setItemType(getInvoiceDetail?.purchase_item_type == 2 ? "item" : "accounting");
        setCustomHeaderListTransaction(getInvoiceDetail?.custom_values)
        setGstQuote({
            ...gstQuote,
            original_inv_date:
                getInvoiceDetail?.date_of_invoice &&
                formattedDate(getInvoiceDetail?.date_of_invoice),
            party_ledger_id: getInvoiceDetail?.supplier_ledger_id,
        });
        const nature_type =
            getInvoiceDetail?.purchase_item_type == 2
                ? getInvoiceDetail?.purchase_transaction_items[0]?.classification_nature_type
                : getInvoiceDetail?.purchase_transaction_ledger[0]?.classification_nature_type;
        const nature_name = classificationOptions.find(item => item.value == nature_type);
        setClassification({
            classification_nature: nature_type,
            classification_nature_name: nature_name?.label,
            rcm_applicable:
                getInvoiceDetail?.purchase_item_type == 2
                    ? getInvoiceDetail?.purchase_transaction_items[0]
                          ?.classification_is_rcm_applicable == 1
                        ? true
                        : false
                    : getInvoiceDetail?.purchase_transaction_ledger[0]
                          ?.classification_is_rcm_applicable == 1
                    ? true
                    : false,
        });
        setGSTCalculationType(
            {
                classification_nature_name: nature_name?.label,
            },
            setIsIGSTCalculation,
            setIsSGSTCalculation
        );
        if (getInvoiceDetail?.purchase_item_type == 2) {
            setItems(
                getInvoiceDetail?.purchase_transaction_items?.map((item, index) => {
                    setChangeTax(item?.with_tax);
                    const multiQuantity = item?.consolidating_items_to_invoice?.split(",") || [];

                    if (multiQuantity[0] == "0") {
                        multiQuantity[0] = item?.quantity?.toFixed(item?.decimal_places || 2);
                    }

                    while (multiQuantity.length < 4) {
                        multiQuantity.push("0");
                    }

                    const unitOptions = unitOption(item?.units_of_array);
                    return {
                        id: item?.id,
                        transaction_item_id: item?.id,
                        selectedItem: item?.item_id,
                        additional_description: item?.additional_description,
                        mrp: item?.mrp,
                        hsn_code: item?.hsn_code,
                        multiQuantity: multiQuantity,
                        selectedLedger: item?.ledger_id,
                        quantity: item?.quantity?.toFixed(item?.decimal_places || 2),
                        free_quantity: item?.free_quantity,
                        rateWithGst: item?.rpu_with_gst,
                        updatedRateWithGst: item?.with_tax
                            ? item?.rpu_with_gst
                            : item?.rpu_without_gst?.toFixed(2),
                        rateWithoutGst: item?.with_tax
                            ? item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                            : item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2),
                        updatedRateWithoutGst: item?.with_tax
                            ? item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                            : item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2),
                        gst_id: item?.gst_id,
                        gst: item?.gst_tax_percentage,
                        discountType: item?.discount_type || 1,
                        discountValue: item?.discount_value,
                        discountType_2: item?.discount_type_2 || 1,
                        discountValue_2: item?.discount_value_2 ? item?.discount_value_2 : 0,
                        total: customToFixed(item?.total, 2),
                        with_tax: item?.with_tax ? 1 : 0,
                        selectedUnit: item?.unit_id,
                        cgstValue: item?.classification_cgst_tax,
                        igstValue: item?.classification_igst_tax,
                        sgstValue: item?.classification_sgst_tax,
                        cessValue: (item?.cess_rate * item?.total) / 100,
                        cessRate: item?.cess_rate,
                        itemUnitOption: unitOptions,
                        isShowDelete:
                            getInvoiceDetail?.purchase_transaction_items?.length > 1 ? true : false,
                        decimal_places: item?.decimal_places ?? 2,
                        decimal_places_for_rate: item?.decimal_places_for_rate ?? 2,
                        secondaryUnitOfMeasurement: item?.secondary_unit_of_measurement,
                        conversationRate: item?.conversion_rate,
                        item_decimal_places: item?.item_decimal_places,
                        item_decimal_places_for_rate: item?.item_decimal_places_for_rate,
                        custom_fields: customFieldOptimization(item?.custom_fields),
                        custom_field_inventory_store: formattedCustomField(item?.custom_items_inventory_values),
                        model_inventory_custom_fields: item?.model_inventory_custom_fields,
                        model_custom_fields: item?.model_inventory_custom_fields,
                        model_select_inventory_custom_fields: item?.custom_items_inventory_values,
                        is_status: ITEM_STATUS.ACTIVE,
                    };
                })
            );
            clearAccountingInvoice(setAccountingItems)
        } else if (getInvoiceDetail?.purchase_item_type == 1) {
            setAccountingItems(
                getInvoiceDetail?.purchase_transaction_ledger?.map((item, index) => {
                    setChangeTax(item?.with_tax);
                    return {
                        id: index + 1,
                        selectedLedger: item?.ledger_id,
                        additional_description: item?.additional_description,
                        rateWithGst: item?.rpu_with_gst,
                        gst: item?.gst_tax_percentage,
                        gst_id: item?.gst_id,
                        discountType: item?.discount_type || 1,
                        discountValue: item?.discount_value,
                        discountType_2: item?.discount_type_2 || 1,
                        discountValue_2: item?.discount_value_2 ? item?.discount_value_2 : 0,
                        total: customToFixed(item?.total, 2),
                        cgstValue: item?.classification_cgst_tax,
                        igstValue: item?.classification_igst_tax,
                        sgstValue: item?.classification_sgst_tax,
                        cessValue: (item?.cess_rate * item?.total) / 100,
                        cessRate: item?.cess_rate,
                        with_tax: item?.with_tax ? 1 : 0,
                        rpu: item?.rpu_with_gst || 0,
                        updatedRateWithGst: item?.with_tax
                            ? item?.rpu_with_gst
                            : item?.rpu_without_gst?.toFixed(2),
                        rateWithoutGst: item?.with_tax
                            ? item?.rpu_with_gst
                            : item?.rpu_without_gst?.toFixed(2),
                        updatedRateWithoutGst: item?.with_tax
                            ? item?.rpu_with_gst
                            : item?.rpu_without_gst?.toFixed(2),
                        amountWithoutGst: item?.with_tax
                            ? parseFloat(
                                  item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              )
                            : parseFloat(
                                  item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              ),
                        custom_fields: customFieldOptimization(item?.custom_ledgers_values),
                        is_status: ITEM_STATUS.ACTIVE,
                    };
                })
            );
            clearItemInvoice(setItems)
        }
        if (getInvoiceDetail?.purchase_item_type == 2) {

            dispatch(rearrangeItemList(TRANSACTION_TYPE.PURCHASE_RETURN, 1));
        } else {

            dispatch(rearrangeItemList(TRANSACTION_TYPE.PURCHASE_RETURN, 2));
        }
        setAddLessChanges(
            getInvoiceDetail?.add_less?.length == 0
                ? addLessChanges
                : getInvoiceDetail?.add_less?.map(item => {
                      return {
                          al_ledger_id: item.ledger_id,
                          al_is_show_in_print: item.is_show_in_print,
                          al_type: item.type,
                          al_value:
                              item.type == 2 ? parseFloat(item.value) : parseFloat(item.total),
                          al_total: parseFloat(item.total),
                          is_status: ITEM_STATUS.ACTIVE,
                      };
                  })
        );
        setAdditionalCharges({
            upload_document: [],
            note: getInvoiceDetail?.narration,
            terms_and_conditions: getInvoiceDetail?.term_and_condition,
            additional_detail:
                !getInvoiceDetail?.additional_charges ||
                getInvoiceDetail?.additional_charges?.length === 0
                    ? [
                          {
                              ac_ledger_id: null,
                              ac_type: 1,
                              ac_value: null,
                              ac_gst_rate_id: {
                                  label: "",
                                  value: 0,
                                  rate: 0,
                              },
                              ac_total: 0,
                              is_status: ITEM_STATUS.IN_ACTIVE,
                          },
                      ]
                    : getInvoiceDetail?.additional_charges.map(charge => {
                          return {
                              ac_ledger_id: charge?.ledger_id,
                              ac_type: charge?.charge_type,
                              ac_value: charge?.value,
                              ac_gst_rate_id: {
                                  value: charge?.gst_rate_id,
                                  rate: Number(charge?.gst_percentage),
                                  label: `${charge?.gst_percentage}%`,
                              },
                              ac_total: customToFixed(charge?.total_without_tax, 2),
                              is_status: ITEM_STATUS.ACTIVE,
                          };
                      }),
        });
        setSameAsBill(getInvoiceDetail?.same_as_billing);
        setPartyAddress({
            billingAddress: {
                address_1: getInvoiceDetail?.billing_address?.address_1 || "",
                address_2: getInvoiceDetail?.billing_address?.address_2 || "",
                country_id: getInvoiceDetail?.billing_address?.country_id || "",
                state_id: getInvoiceDetail?.billing_address?.state_id || "",
                city_id: getInvoiceDetail?.billing_address?.city_id || "",
                state_name: getInvoiceDetail?.billing_address?.state_name,
                city_name: getInvoiceDetail?.billing_address?.city_name,
                pin_code: getInvoiceDetail?.billing_address?.pin_code || "",
            },
            shippingAddress: {
                address_1: getInvoiceDetail?.dispatch_address?.address_1 || "",
                address_2: getInvoiceDetail?.dispatch_address?.address_2 || "",
                country_id: getInvoiceDetail?.dispatch_address?.country_id || "",
                state_id: getInvoiceDetail?.dispatch_address?.state_id || "",
                city_id: getInvoiceDetail?.dispatch_address?.city_id || "",
                state_name: getInvoiceDetail?.dispatch_address?.state_name,
                city_name: getInvoiceDetail?.dispatch_address?.city_name,
                pin_code: getInvoiceDetail?.dispatch_address?.pin_code || "",
                shipping_name: getInvoiceDetail?.shipping_name,
                shipping_gstin: getInvoiceDetail?.shipping_gstin,
                shipping_address_id: getInvoiceDetail?.shipping_address_id
            },
        });
        const roundOffAmount = customToFixed(getInvoiceDetail?.rounding_amount, 2);
        setGstCalculation({
            round_of_amount: roundOffAmount,
            is_round_off_not_changed: getInvoiceDetail?.is_round_off_not_changed,
            ...(getInvoiceDetail?.round_off_method ? {round_off_method: Number(getInvoiceDetail?.round_off_method)} : {})
        });
        setBrokerDetail({
            broker_id: getInvoiceDetail?.broker_id,
            broker_percentage: getInvoiceDetail?.brokerage_for_sale
                ? getInvoiceDetail?.brokerage_for_sale
                : "",
            brokerage_on_value: getInvoiceDetail?.brokerage_on_value_type,
        });
        setTransporterDetail({
            transport_id: getInvoiceDetail?.transport_id,
            transporter_document_number: getInvoiceDetail?.transporter_document_number,
            transporter_document_date: getInvoiceDetail?.transporter_document_date
                ? formattedDate(getInvoiceDetail?.transporter_document_date)
                : null,
            transporter_vehicle_number: getInvoiceDetail?.transporter_vehicle_number,
        });
        setEwayBillDetail({
            eway_bill_date: getInvoiceDetail?.eway_bill_date
                ? formattedDate(getInvoiceDetail?.eway_bill_date)
                : null,
            eway_bill_number: getInvoiceDetail?.eway_bill_number,
        });
        setOtherDetail({
            po_number: getInvoiceDetail?.po_no,
            date: getInvoiceDetail?.po_date ? formattedDate(getInvoiceDetail?.po_date) : null,
            creditPeriod: getInvoiceDetail?.credit_period,
            creditPeriodType: getInvoiceDetail?.credit_period_type || 1,
        });
        setGstValue({
            igstValue: getInvoiceDetail?.igst,
            cgstValue: getInvoiceDetail?.cgst,
            sgstValue: getInvoiceDetail?.sgst,
        });
        setTcsRate({
            tcs_amount: getInvoiceDetail?.tcs_tax_id ? customToFixed(getInvoiceDetail?.tcs_amount, 2) : '',
            tcs_tax_id: getInvoiceDetail?.tcs_tax_id,
            tcs_rate: getInvoiceDetail?.tcs_tax_id ? getInvoiceDetail?.tcs_rate : '',
            tcs_calculated_on: getInvoiceDetail?.tcs_calculated?.calculated_on,
        });
        setPaymentLedgerDetail(prev => ({
                // ...prev,
                ...(getInvoiceDetail?.payment_details?.length > 0 && incomeEdit
                    ? {
                          payment_detail: getInvoiceDetail.payment_details.map(item => ({
                              pd_ledger_id: item?.ledger_id,
                              pd_amount: item?.amount,
                              pd_date: item?.date ? formattedDate(item?.date) : "",
                              pd_mode: item?.mode || null,
                              pd_reference_number: item?.reference_no,
                            //   pd_id: item?.id,
                          })),
                      }
                    : { payment_detail: [
                        {
                            pd_ledger_id: null,
                            pd_date: formattedDate(),
                            pd_amount: "",
                            pd_mode: null,
                            pd_reference_number: "",
                            is_show_invoice_date: true,
                        }
                    ] }),
                tds_amount: getInvoiceDetail?.ledger_of_tds ? getInvoiceDetail?.tds_amount : "",
                tds_rate: getInvoiceDetail?.ledger_of_tds ? getInvoiceDetail?.tds_rate : "",
                tds_tax_id: getInvoiceDetail?.ledger_of_tds,
                rounding_type: getInvoiceDetail?.tds_rounding_method || 1
        }));
        setCessValue(getInvoiceDetail?.cess)
        setGrandTotal(getInvoiceDetail?.gross_value);
        setMainGrandTotal(getInvoiceDetail?.grand_total - roundOffAmount);
        setFinalAmount(getInvoiceDetail?.grand_total);
        setTaxableValue(getInvoiceDetail?.taxable_value);
        // setTimeout(() => {
        //     dispatch(getSaleById(""));
        // }, 1000);
    }
};
export const fetchPurchaseOrderData = ({
    singleData,
    id,
    configurationList,
    isDuplicate,
    purchaseReturnEdit,
    setStateFunctions,
    dispatch,
    tableHeaderList,
    accountingTableHeader,
    addLessChanges,
    additionalCharges,
    classificationOptions,
}) => {
    if (singleData) {
        const {
            invoiceDetail,
            setInvoiceDetail,
            gstQuote,
            setGstQuote,
            setItemType,
            setClassification,
            setItems,
            setAccountingItems,
            setAddLessChanges,
            setAdditionalCharges,
            setPartyAddress,
            setGstCalculation,
            setBrokerDetail,
            setTransporterDetail,
            setEwayBillDetail,
            setOtherDetail,
            setGstValue,
            setTcsRate,
            setPaymentLedgerDetail,
            setGrandTotal,
            setMainGrandTotal,
            setChangeTax,
            setFinalAmount,
            setIsIGSTCalculation,
            setIsSGSTCalculation,
            setTaxableValue,
            setCessValue,
            setCustomHeaderListTransaction,
            setSameAsBill
        } = setStateFunctions;
        const getInvoiceNumber = () => {
            if (purchaseReturnEdit) {
                return singleData?.full_order_number;
            }
            if (isDuplicate) {
                return singleData?.order_number;
            }
            if (!id && !configurationList?.document_prefix?.suffix && !configurationList?.document_prefix?.prefix) {
                return singleData?.full_order_number;
            }
            return singleData?.order_number;
        };

        setFinalAmount(singleData?.grand_total);
        setCustomHeaderListTransaction(singleData?.custom_values)
        setInvoiceDetail({
            ...invoiceDetail,
            purchase_title: singleData?.title,
            invoice_number: getInvoiceNumber(),
            invoice_date: singleData?.order_date && formattedDate(singleData?.order_date),
        });
        setTaxableValue(singleData?.taxable_value);
        setGstQuote({
            ...gstQuote,
            gstin: singleData?.gstin,
            quotes_id: parseFloat(singleData?.estimate_quote_no),
            party_ledger_id: singleData?.party_ledger_id,
            valid_for_type: singleData?.valid_for_type,
            valid_for: singleData?.valid_for && parseFloat(singleData?.valid_for),
            quotes_id: matchEstimateChallanIds(
                singleData?.delivery_challan_no
                    ? singleData?.delivery_challan_no
                    : singleData?.estimate_quote_no
                    ? singleData?.estimate_quote_no
                    : null
            ),
        });
        setItemType(singleData?.order_type == 2 ? "item" : "accounting");
        const nature_type =
            singleData?.order_type == 2
                ? singleData?.transaction_items[0]?.classification_nature_type
                : singleData?.transaction_ledgers[0]?.classification_nature_type;
        const nature_name = classificationOptions.find(item => item.value == nature_type);
        setClassification({
            classification_nature: nature_type,
            classification_nature_name: nature_name?.label,
            rcm_applicable:
                singleData?.order_type == 2
                    ? singleData?.transaction_items[0]?.classification_is_rcm_applicable == 1
                        ? true
                        : false
                    : singleData?.transaction_ledgers[0]?.classification_is_rcm_applicable == 1
                    ? true
                    : false,
        });
        setGSTCalculationType(
            {
                classification_nature_name: nature_name?.label,
            },
            setIsIGSTCalculation,
            setIsSGSTCalculation
        );
        if (singleData?.order_type == 2) {
            setItems(
                singleData?.transaction_items?.map((item, index) => {
                    setChangeTax(item?.with_tax);
                    const multiQuantity = item?.consolidating_items_to_invoice?.split(",") || [];

                    if (multiQuantity[0] == "0") {
                        multiQuantity[0] = item?.quantity?.toFixed(item?.decimal_places || 2);
                    }

                    while (multiQuantity.length < 4) {
                        multiQuantity.push("0");
                    }

                    const unitOptions = unitOption(item?.units_of_array);
                    return {
                        id: item?.id,
                        transaction_item_id: item?.id,
                        selectedItem: item?.item_id,
                        additional_description: item?.additional_description,
                        mrp: item?.mrp,
                        hsn_code: item?.hsn_code,
                        multiQuantity: multiQuantity,
                        selectedLedger: item?.ledger_id,
                        quantity: item?.quantity?.toFixed(item?.decimal_places || 2),
                        free_quantity: item?.free_quantity,
                        rateWithGst: item?.rpu_with_gst,
                        updatedRateWithGst: item?.with_tax
                            ? parseFloat(item?.rpu_with_gst)
                            : parseFloat(item?.rpu_without_gst?.toFixed(2)),
                        rateWithoutGst: item?.with_tax
                            ? item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                            : item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2),
                        updatedRateWithoutGst: item?.with_tax
                            ? parseFloat(
                                  item?.rpu_with_gst.toFixed(item?.decimal_places_for_rate ?? 2)
                              )
                            : parseFloat(
                                  item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate ?? 2)
                              ),
                        gst_id: item?.gst_id,
                        gst: item?.gst_tax_percentage,
                        discountType: item?.discount_type || 1,
                        discountValue: item?.discount_value,
                        discountType_2: item?.discount_type_2 || 1,
                        discountValue_2: item?.discount_value_2 ? item?.discount_value_2 : 0,
                        total: customToFixed(item?.total, 2),
                        with_tax: item?.with_tax ? 1 : 0,
                        selectedUnit: item?.unit_id,
                        itemUnitOption: unitOptions,
                        cgstValue: item?.classification_cgst_tax,
                        igstValue: item?.classification_igst_tax,
                        sgstValue: item?.classification_sgst_tax,
                        cessValue: (item?.cess_rate * item?.total) / 100,
                        cessRate: item?.cess_rate,
                        isShowDelete: singleData?.transaction_items?.length > 1 ? true : false,
                        decimal_places: item?.decimal_places ?? 2,
                        decimal_places_for_rate: item?.decimal_places_for_rate ?? 2,
                        secondaryUnitOfMeasurement: item?.secondary_unit_of_measurement,
                        conversationRate: item?.conversion_rate,
                        item_decimal_places: item?.item_decimal_places,
                        item_decimal_places_for_rate: item?.item_decimal_places_for_rate,
                        custom_fields: customFieldOptimization(item?.custom_fields),
                        custom_field_inventory_store: formattedCustomField(item?.custom_items_inventory_values),
                        model_inventory_custom_fields: item?.model_inventory_custom_fields,
                        model_custom_fields: item?.model_inventory_custom_fields,
                        model_select_inventory_custom_fields: item?.custom_items_inventory_values,
                        is_status: ITEM_STATUS.ACTIVE,
                    };
                })
            );
        } else if (singleData?.order_type == 1) {
            setAccountingItems(
                singleData?.transaction_ledgers?.map((item, index) => {
                    setChangeTax(item?.with_tax);
                    return {
                        id: index + 1,
                        selectedLedger: item?.ledger_id,
                        additional_description: item?.additional_description,
                        rateWithGst: parseFloat(item?.rpu_with_gst),
                        updatedRateWithGst: item?.with_tax
                            ? parseFloat(item?.rpu_with_gst)
                            : parseFloat(item?.rpu_without_gst?.toFixed(2)),
                        rateWithoutGst: item?.with_tax
                            ? parseFloat(
                                  item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              )
                            : parseFloat(
                                  item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              ),
                        updatedRateWithoutGst: item?.with_tax
                            ? parseFloat(
                                  item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              )
                            : parseFloat(
                                  item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              ),
                        amountWithoutGst: item?.with_tax
                            ? parseFloat(
                                  item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              )
                            : parseFloat(
                                  item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              ),
                        updatedAmountWithoutGst: item?.rpu_without_gst,
                        gst: item?.gst_tax_percentage,
                        gst_id: item?.gst_id,
                        discountType: item?.discount_type || 1,
                        discountValue: item?.discount_value,
                        discountType_2: item?.discount_type_2 || 1,
                        discountValue_2: item?.discount_value_2 ? item?.discount_value_2 : 0,
                        total: customToFixed(item?.total, 2),
                        cgstValue: item?.classification_cgst_tax,
                        igstValue: item?.classification_igst_tax,
                        sgstValue: item?.classification_sgst_tax,
                        cessValue: (item?.cess_rate * item?.total) / 100,
                        cessRate: item?.cess_rate,
                        with_tax: item?.with_tax ? 1 : 0,
                        rpu: item?.rpu_with_gst || 0,
                        isShowDelete: singleData?.transaction_ledgers?.length > 1 ? true : false,
                        custom_fields: customFieldOptimization(item?.custom_ledgers_values),
                        is_status: ITEM_STATUS.ACTIVE,
                    };
                })
            );
        }
        if (singleData?.order_type == 2) {

            dispatch(rearrangeItemList(TRANSACTION_TYPE.PURCHASE_ORDER, 1));
        } else {

            dispatch(rearrangeItemList(TRANSACTION_TYPE.PURCHASE_ORDER, 2));
        }
        setAddLessChanges(
            singleData?.add_less?.length == 0
                ? addLessChanges
                : singleData?.add_less?.map(item => {
                      return {
                          al_ledger_id: item.ledger_id,
                          al_is_show_in_print: item.is_show_in_print,
                          al_type: item.type,
                          al_value:
                              item.type == 2 ? parseFloat(item.value) : parseFloat(item.total),
                          al_total: parseFloat(item.total),
                          is_status: ITEM_STATUS.ACTIVE,
                      };
                  })
        );
        setAdditionalCharges({
            ...additionalCharges,
            ...(id &&{upload_document: singleData?.media?.map(media => {
                return { original_url: media?.original_url, id: media?.id };
            })}),
            note: singleData?.narration,
            terms_and_conditions: singleData?.term_and_condition,
            additional_detail:
                !singleData?.additional_charges || singleData?.additional_charges?.length == 0
                    ? [
                          {
                              ac_ledger_id: null,
                              ac_type: 1,
                              ac_value: "",
                              ac_gst_rate_id: {
                                  label: "",
                                  value: 0,
                                  rate: 0,
                              },
                              ac_total: 0,
                              is_status: ITEM_STATUS.IN_ACTIVE,
                          },
                      ]
                    : singleData?.additional_charges?.map(charge => {
                          return {
                              ac_ledger_id: charge?.ledger_id,
                              ac_type: charge?.charge_type,
                              ac_value: parseFloat(charge?.value),
                              ac_gst_rate_id: {
                                  value: charge?.gst_rate_id,
                                  rate: Number(charge?.gst_percentage),
                                  label: `${charge?.gst_percentage}%`,
                              },
                              ac_total: customToFixed(charge?.total_without_tax, 2),
                              is_status: ITEM_STATUS.ACTIVE,
                          };
                      }),
        });
        setSameAsBill(singleData?.same_as_billing);
        setPartyAddress({
            billingAddress: {
                address_1: singleData?.billing_address?.address_1,
                address_2: singleData?.billing_address?.address_2,
                country_id: singleData?.billing_address?.country_id,
                state_id: singleData?.billing_address?.state_id,
                city_id: singleData?.billing_address?.city_id,
                state_name: singleData?.billing_address?.state_name,
                city_name: singleData?.billing_address?.city_name,
                pin_code: singleData?.billing_address?.pin_code,
            },
            shippingAddress: {
                address_1: singleData?.shipping_address?.address_1,
                address_2: singleData?.shipping_address?.address_2,
                country_id: singleData?.shipping_address?.country_id,
                state_id: singleData?.shipping_address?.state_id,
                city_id: singleData?.shipping_address?.city_id,
                state_name: singleData?.shipping_address?.state_name,
                city_name: singleData?.shipping_address?.city_name,
                pin_code: singleData?.shipping_address?.pin_code,
                shipping_name: singleData?.shipping_name,
                shipping_gstin: singleData?.shipping_gstin,
                shipping_address_id: singleData?.shipping_address_id,
            },
        });
        const roundOffAmount = customToFixed(singleData?.round_off_amount, 2);
        setGstCalculation({
            round_of_amount: roundOffAmount,
            is_round_off_not_changed: singleData?.is_round_off_not_changed,
            ...(singleData?.round_off_method ? {round_off_method: Number(singleData?.round_off_method)} : {})
        });
        setBrokerDetail({
            broker_id: singleData?.broker_id,
            broker_percentage: singleData?.brokerage ? singleData?.brokerage : "",
            brokerage_on_value: singleData?.brokerage_on_value_type,
        });
        setTransporterDetail({
            transport_id: singleData?.transport_id,
            transporter_document_number: singleData?.transporter_document_number,
            transporter_document_date: singleData?.transporter_document_date
                ? formattedDate(singleData?.transporter_document_date)
                : "",
            transporter_vehicle_number: singleData?.transporter_vehicle_number,
        });
        setEwayBillDetail({
            eway_bill_date: singleData?.eway_bill_date
                ? formattedDate(singleData?.eway_bill_date)
                : "",
            eway_bill_number: singleData?.eway_bill_number,
        });
        setOtherDetail({
            po_number: singleData?.po_no,
            date: singleData?.po_date ? formattedDate(singleData?.po_date) : null,
            creditPeriod: singleData?.credit_period,
            creditPeriodType: singleData?.credit_period_type || 1,
        });
        setGstValue({
            igstValue: singleData?.igst,
            cgstValue: singleData?.cgst,
            sgstValue: singleData?.sgst,
        });
        setTcsRate({
            tcs_amount: singleData?.tcs_tax_id ? customToFixed(singleData?.tcs_amount, 2) : '',
            tcs_tax_id: singleData?.tcs_tax_id,
            tcs_rate: singleData?.tcs_tax_id ? singleData?.tcs_rate : '',
            tcs_calculated_on: singleData?.tcs_calculated?.calculated_on,
        });
        setPaymentLedgerDetail(prev => ({
            ...(singleData?.payment_details?.length > 0
                ? {
                      payment_detail: singleData.payment_details.map(item => ({
                          pd_ledger_id: item?.ledger_id,
                          pd_amount: item?.amount,
                          pd_date: item?.date ? formattedDate(item?.date) : "",
                          pd_mode: item?.mode || null,
                          pd_reference_number: item?.reference_no,
                          pd_id: item?.id,
                      })),
                  }
                : { payment_detail:  [
                    {
                        pd_ledger_id: null,
                        pd_date: "",
                        pd_amount: "",
                        pd_mode: null,
                        pd_reference_number: "",
                        is_show_invoice_date: true,
                    }
                ] }),
            tds_amount: singleData?.tds_tax_id ? singleData?.tds_amount : "",
            tds_rate: singleData?.tds_tax_id ? singleData?.tds_rate : "",
            tds_tax_id: singleData?.tds_tax_id,
            rounding_type: singleData?.tds_rounding_method || 1
        }));
        setGrandTotal(singleData?.gross_value);
        setMainGrandTotal(singleData?.grand_total - roundOffAmount);
        setFinalAmount(singleData?.grand_total);
        setCessValue(singleData?.cess);
        // setTimeout(() => {
        //     dispatch(getSaleById(""));
        // }, 1000);
    }
};
export const fetchPurchaseReturnData = ({
    singleData,
    id,
    setStateFunctions,
    dispatch,
    tableHeaderList,
    accountingTableHeader,
    addLessChanges,
    additionalCharges,
    classificationOptions,
    isDuplicate
}) => {
    if (singleData) {
        const {
            invoiceDetail,
            setInvoiceDetail,
            gstQuote,
            setGstQuote,
            setItemType,
            setClassification,
            setItems,
            setAccountingItems,
            setAddLessChanges,
            setAdditionalCharges,
            setPartyAddress,
            setGstCalculation,
            setBrokerDetail,
            setTransporterDetail,
            setEwayBillDetail,
            setOtherDetail,
            setGstValue,
            setTcsRate,
            setPaymentLedgerDetail,
            setGrandTotal,
            setMainGrandTotal,
            setChangeTax,
            setFinalAmount,
            setIsIGSTCalculation,
            setIsSGSTCalculation,
            setTaxableValue,
            purchaseInvoice,
            setPurchaseInvoice,
            setExpenseDetail,
            setInvoiceNumber,
            setCessValue,
            setSelectedAdvancePayment,
            setCustomHeaderListTransaction,
            setSameAsBill,
            incomeEdit
        } = setStateFunctions;
        setFinalAmount(singleData?.grand_total);
        setInvoiceNumber(singleData?.original_inv_no);
        setSameAsBill(singleData?.same_as_billing);
        setPurchaseInvoice({
            ...purchaseInvoice,
            voucher_number: singleData?.voucher_number,
            voucher_date: singleData?.voucher_date ? formattedDate(singleData?.voucher_date) : null,
        });
        setExpenseDetail({
            note_number: singleData?.supplier_purchase_return_number,
            note_date: singleData?.supplier_purchase_return_date
                ? formattedDate(singleData?.supplier_purchase_return_date)
                : null,
        });
        setCustomHeaderListTransaction(singleData?.custom_values)
        setTaxableValue(singleData?.taxable_value);
        setGstQuote({
            ...gstQuote,
            gstin: singleData?.gstin,
            quotes_id: parseFloat(singleData?.estimate_quote_no),
            party_ledger_id: singleData?.supplier_id,
            valid_for_type: singleData?.valid_for_type,
            valid_for: singleData?.valid_for && parseFloat(singleData?.valid_for),
            original_inv_no: singleData?.original_inv_no,
            original_inv_date: singleData?.original_inv_date
                ? formattedDate(singleData?.original_inv_date)
                : null,
        });
        if(!isDuplicate){
            setSelectedAdvancePayment(
                singleData?.advance_payment?.map((item) => {
                    return{
                        ...item,
                        received_amount: item?.adjusted_amount,
                        advance_payment_id: item?.id
                    }
                })
            );
        }
        setItemType(singleData?.pr_item_type == 2 ? "item" : "accounting");
        const nature_type =
            singleData?.pr_item_type == 2
                ? singleData?.purchase_return_items[0]?.classification_nature_type
                : singleData?.purchase_return_ledgers[0]?.classification_nature_type;
        const nature_name = classificationOptions.find(item => item.value == nature_type);
        setClassification({
            classification_nature: nature_type,
            classification_nature_name: nature_name?.label,
            rcm_applicable:
                singleData?.pr_item_type == 2
                    ? singleData?.purchase_return_items[0]?.classification_is_rcm_applicable == 1
                        ? true
                        : false
                    : singleData?.purchase_return_ledgers[0]?.classification_is_rcm_applicable == 1
                    ? true
                    : false,
        });
        setGSTCalculationType(
            {
                classification_nature_name: nature_name?.label,
            },
            setIsIGSTCalculation,
            setIsSGSTCalculation
        );
        if (singleData?.pr_item_type == 2) {
            setItems(
                singleData?.purchase_return_items?.map((item, index) => {
                    setChangeTax(item?.with_tax);
                    const multiQuantity = item?.consolidating_items_to_invoice?.split(",") || [];

                    if (multiQuantity[0] == "0") {
                        multiQuantity[0] = item?.quantity?.toFixed(item?.decimal_places || 2);
                    }

                    while (multiQuantity.length < 4) {
                        multiQuantity.push("0");
                    }

                    const unitOptions = unitOption(item?.units_of_array);
                    return {
                        id: item?.id,
                        transaction_item_id: item?.id,
                        selectedItem: item?.item_id,
                        additional_description: item?.additional_description,
                        mrp: item?.mrp,
                        hsn_code: item?.hsn_code,
                        multiQuantity: multiQuantity,
                        selectedLedger: item?.ledger_id,
                        quantity: item?.quantity?.toFixed(item?.decimal_places || 2),
                        free_quantity: item?.free_quantity,
                        rateWithGst: item?.rpu_with_gst,
                        updatedRateWithGst: item?.with_tax
                            ? item?.rpu_with_gst
                            : item?.rpu_without_gst?.toFixed(2),
                        rateWithoutGst: item?.with_tax
                            ? item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                            : item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2),
                        updatedRateWithoutGst: item?.with_tax
                            ? item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                            : item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2),
                        gst_id: item?.gst_id,
                        gst: item?.gst_tax_percentage,
                        discountType: item?.discount_type || 1,
                        discountValue: item?.discount_value,
                        discountType_2: item?.discount_type_2 || 1,
                        discountValue_2: item?.discount_value_2 ? item?.discount_value_2 : 0,
                        total: customToFixed(item?.total, 2),
                        with_tax: item?.with_tax ? 1 : 0,
                        selectedUnit: item?.unit_id,
                        cgstValue: item?.classification_cgst_tax,
                        igstValue: item?.classification_igst_tax,
                        sgstValue: item?.classification_sgst_tax,
                        cessValue: (item?.cess_rate * item?.total) / 100,
                        cessRate: item?.cess_rate,
                        itemUnitOption: unitOptions,
                        isShowDelete: singleData?.purchase_return_items?.length > 1 ? true : false,
                        decimal_places: item?.decimal_places ?? 2,
                        decimal_places_for_rate: item?.decimal_places_for_rate ?? 2,
                        secondaryUnitOfMeasurement: item?.secondary_unit_of_measurement,
                        conversationRate: item?.conversion_rate,
                        item_decimal_places: item?.item_decimal_places,
                        item_decimal_places_for_rate: item?.item_decimal_places_for_rate,
                        custom_fields: customFieldOptimization(item?.custom_fields),
                        custom_field_inventory_store: formattedCustomField(item?.custom_items_inventory_values),
                        model_inventory_custom_fields: item?.model_inventory_custom_fields,
                        model_custom_fields: item?.model_inventory_custom_fields,
                        model_select_inventory_custom_fields: item?.custom_items_inventory_values,
                        is_status: ITEM_STATUS.ACTIVE,
                    };
                })
            );
        } else if (singleData?.pr_item_type == 1) {
            setAccountingItems(
                singleData?.purchase_return_ledgers?.map((item, index) => {
                    setChangeTax(item?.with_tax);
                    return {
                        id: index + 1,
                        selectedLedger: item?.ledger_id,
                        additional_description: item?.additional_description,
                        rateWithGst: parseFloat(item?.rpu_with_gst),
                        updatedRateWithGst: item?.with_tax
                            ? parseFloat(item?.rpu_with_gst)
                            : parseFloat(item?.rpu_without_gst?.toFixed(2)),
                        rateWithoutGst: item?.with_tax
                            ? parseFloat(
                                  item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              )
                            : parseFloat(
                                  item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              ),
                        updatedRateWithoutGst: item?.with_tax
                            ? parseFloat(
                                  item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              )
                            : parseFloat(
                                  item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              ),
                        amountWithoutGst: item?.with_tax
                            ? parseFloat(
                                  item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              )
                            : parseFloat(
                                  item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              ),
                        updatedAmountWithoutGst: item?.rpu_without_gst,
                        gst: item?.gst_tax_percentage,
                        gst_id: item?.gst_id,
                        discountType: item?.discount_type || 1,
                        discountValue: item?.discount_value,
                        discountType_2: item?.discount_type_2 || 1,
                        discountValue_2: item?.discount_value_2 ? item?.discount_value_2 : 0,
                        total: customToFixed(item?.total, 2),
                        cgstValue: item?.classification_cgst_tax,
                        igstValue: item?.classification_igst_tax,
                        sgstValue: item?.classification_sgst_tax,
                        cessValue: (item?.cess_rate * item?.total) / 100,
                        cessRate: item?.cess_rate,
                        with_tax: item?.with_tax ? 1 : 0,
                        rpu: item?.rpu_with_gst || 0,
                        isShowDelete:
                            singleData?.purchase_return_ledgers?.length > 1 ? true : false,
                        custom_fields: customFieldOptimization(item?.custom_ledgers_values),
                        is_status: ITEM_STATUS.ACTIVE,
                    };
                })
            );
        }
        if (singleData?.pr_item_type == 2) {

            dispatch(rearrangeItemList(TRANSACTION_TYPE.PURCHASE_RETURN, 1));
        } else {

            dispatch(rearrangeItemList(TRANSACTION_TYPE.PURCHASE_RETURN, 2));
        }
        setAddLessChanges(
            singleData?.add_less?.length == 0
                ? addLessChanges
                : singleData?.add_less?.map(item => {
                      return {
                          al_ledger_id: item.ledger_id,
                          al_is_show_in_print: item.is_show_in_print,
                          al_type: item.type,
                          al_value:
                              item.type == 2 ? parseFloat(item.value) : parseFloat(item.total),
                          al_total: parseFloat(item.total),
                          is_status: ITEM_STATUS.ACTIVE,
                      };
                  })
        );
        setAdditionalCharges({
            ...additionalCharges,
            ...(id &&{upload_document: singleData?.media?.map(media => {
                return { original_url: media?.original_url, id: media?.id };
            })}),
            note: singleData?.narration,
            terms_and_conditions: singleData?.term_and_condition,
            additional_detail:
                !singleData?.additional_charges || singleData?.additional_charges?.length == 0
                    ? [
                          {
                              ac_ledger_id: null,
                              ac_type: 1,
                              ac_value: "",
                              ac_gst_rate_id: {
                                  label: "",
                                  value: 0,
                                  rate: 0,
                              },
                              ac_total: 0,
                              is_status: ITEM_STATUS.IN_ACTIVE,
                          },
                      ]
                    : singleData?.additional_charges?.map(charge => {
                          return {
                              ac_ledger_id: charge?.ledger_id,
                              ac_type: charge?.charge_type,
                              ac_value: charge?.value,
                              ac_gst_rate_id: {
                                  value: charge?.gst_rate_id,
                                  rate: Number(charge?.gst_percentage),
                                  label: `${charge?.gst_percentage}%`,
                              },
                              ac_total: customToFixed(charge?.total_without_tax, 2),
                              is_status: ITEM_STATUS.ACTIVE,
                          };
                      }),
        });
        setPartyAddress({
            billingAddress: {
                address_1: singleData?.billing_address?.address_1,
                address_2: singleData?.billing_address?.address_2,
                country_id: singleData?.billing_address?.country_id,
                state_id: singleData?.billing_address?.state_id,
                city_id: singleData?.billing_address?.city_id,
                state_name: singleData?.billing_address?.state_name,
                city_name: singleData?.billing_address?.city_name,
                pin_code: singleData?.billing_address?.pin_code,
            },
            shippingAddress: {
                address_1: singleData?.dispatch_address?.address_1,
                address_2: singleData?.dispatch_address?.address_2,
                country_id: singleData?.dispatch_address?.country_id,
                state_id: singleData?.dispatch_address?.state_id,
                city_id: singleData?.dispatch_address?.city_id,
                state_name: singleData?.dispatch_address?.state_name,
                city_name: singleData?.dispatch_address?.city_name,
                pin_code: singleData?.dispatch_address?.pin_code,
                shipping_name: singleData?.shipping_name,
                shipping_gstin: singleData?.shipping_gstin,
                shipping_address_id: singleData?.shipping_address_id
            },
        });
        const roundOffAmount = customToFixed(singleData?.rounding_amount, 2);
        setGstCalculation({
            round_of_amount: roundOffAmount,
            is_round_off_not_changed: singleData?.is_round_off_not_changed,
            ...(singleData?.round_off_method ? {round_off_method: Number(singleData?.round_off_method)} : {})
        });
        setBrokerDetail({
            broker_id: singleData?.broker_id,
            broker_percentage: singleData?.brokerage_for_sale ? singleData?.brokerage_for_sale : "",
            brokerage_on_value: singleData?.brokerage_on_value_type,
        });
        setTransporterDetail({
            transport_id: singleData?.transport_id,
            transporter_document_number: singleData?.transporter_document_number,
            transporter_document_date: singleData?.transporter_document_date
                ? formattedDate(singleData?.transporter_document_date)
                : null,
            transporter_vehicle_number: singleData?.transporter_vehicle_number,
        });
        setEwayBillDetail({
            eway_bill_date: singleData?.eway_bill_date
                ? formattedDate(singleData?.eway_bill_date)
                : null,
            eway_bill_number: singleData?.eway_bill_number,
        });
        setOtherDetail({
            po_number: singleData?.po_no,
            date: singleData?.po_date ? formattedDate(singleData?.po_date) : null,
            creditPeriod: singleData?.credit_period,
            creditPeriodType: singleData?.credit_period_type || 1,
        });
        setGstValue({
            igstValue: singleData?.igst,
            cgstValue: singleData?.cgst,
            sgstValue: singleData?.sgst,
        });
        setTcsRate({
            tcs_amount: singleData?.tcs_tax_id ? customToFixed(singleData?.tcs_amount, 2) : '',
            tcs_tax_id: singleData?.tcs_tax_id,
            tcs_rate: singleData?.tcs_tax_id ? singleData?.tcs_rate : '',
            tcs_calculated_on: singleData?.tcs_calculated?.calculated_on,
        });
        setPaymentLedgerDetail(prev => ({
                ...(singleData?.payment_details?.length > 0 && incomeEdit
                    ? {
                          payment_detail: singleData.payment_details.map(item => ({
                              pd_ledger_id: item?.ledger_id,
                              pd_amount: item?.amount,
                              pd_date: item?.date ? formattedDate(item?.date) : "",
                              pd_mode: item?.mode || null,
                              pd_reference_number: item?.reference_no,
                              pd_id: item?.id,
                          })),
                      }
                    : { payment_detail:  [
                        {
                            pd_ledger_id: null,
                            pd_date: formattedDate(),
                            pd_amount: "",
                            pd_mode: null,
                            pd_reference_number: "",
                            is_show_invoice_date: true,
                        }
                    ] }),
                tds_amount: (singleData?.tds_tax_id || singleData?.ledger_of_tds) ? singleData?.tds_amount : "",
                tds_rate: (singleData?.tds_tax_id || singleData?.ledger_of_tds) ? singleData?.tds_rate : "",
                tds_tax_id: singleData?.tds_tax_id || singleData?.ledger_of_tds,
                rounding_type: singleData?.tds_rounding_method || 1
        }));
        setCessValue(singleData?.cess);
        setGrandTotal(singleData?.gross_value);
        setMainGrandTotal(singleData?.grand_total - roundOffAmount);
        setFinalAmount(singleData?.grand_total);
    }
};

export const prepareExpenseCreditDebitNote = ({
    singleSale,
    id,
    cn_dn,
    setStateFunctions,
    dispatch,
    tableHeaderList,
    classificationOptions,
    accountingTableHeader,
    isCreditDuplicate
}) => {
    const {
        setInvoiceDetail,
        setCessValue,
        setGstQuote,
        setItemType,
        setClassification,
        setItems,
        setAccountingItems,
        setAddLessChanges,
        setAdditionalCharges,
        setPartyAddress,
        gstCalculation,
        setGstCalculation,
        setBrokerDetail,
        setTransporterDetail,
        setEwayBillDetail,
        setOtherDetail,
        setGstValue,
        setTcsRate,
        setPaymentLedgerDetail,
        setGrandTotal,
        setMainGrandTotal,
        setChangeTax,
        addLessChanges,
        additionalCharges,
        setFinalAmount,
        setTaxableValue,
        setIsIGSTCalculation,
        setIsSGSTCalculation,
        localDispatchAddress,
        setLocalDispatchAddress,
        setSelectedAdvancePayment,
        setCustomHeaderListTransaction,
        setSameAsBill,
        expenseEdit
    } = setStateFunctions;
    if (singleSale) {
        const item_type =
            cn_dn == "cn" ? singleSale?.expense_cn_item_type : singleSale?.dn_item_type;
        setInvoiceDetail({
            invoice_number: singleSale?.voucher_number,
            invoice_date: singleSale?.voucher_date && formattedDate(singleSale?.voucher_date),
            debit_note_date:
                singleSale?.supplier_purchase_return_date &&
                formattedDate(singleSale?.supplier_purchase_return_date),
            debit_note_number: singleSale?.supplier_purchase_return_number,
        });
        setCustomHeaderListTransaction(singleSale?.custom_values)
        setFinalAmount(singleSale?.grand_total);
        setCessValue(singleSale?.cess);
        setGstQuote({
            original_inv_no: singleSale?.original_inv_no,
            original_inv_date: singleSale?.original_inv_date
                ? formattedDate(singleSale?.original_inv_date)
                : null,
            gstin: singleSale?.gstin,
            party_ledger_id: singleSale?.supplier_id,
        });
        if(!isCreditDuplicate){
            setSelectedAdvancePayment(
                singleSale?.advance_payment?.map((item) => {
                    return{
                        ...item,
                        received_amount: item?.adjusted_amount,
                        advance_payment_id: item?.id
                    }
                })
            );
        }
        setTaxableValue(singleSale?.taxable_value);
        if (cn_dn == "cn") {
            if (item_type == 2) {
                setItems(
                    singleSale?.expense_credit_note_items?.map((item, index) => {
                        const multiQuantity =
                            item?.consolidating_items_to_invoice?.split(",") || [];

                        if (multiQuantity[0] == "0") {
                            multiQuantity[0] = item?.quantity?.toFixed(item?.decimal_places || 2);
                        }

                        while (multiQuantity.length < 4) {
                            multiQuantity.push("0");
                        }

                        setChangeTax(item?.with_tax);
                        const unitOptions = unitOption(item?.units_of_array);
                        return {
                            id: item?.id,
                            transaction_item_id: item?.id,
                            selectedItem: item?.item_id,
                            additional_description: item?.additional_description,
                            mrp: item?.mrp,
                            hsn_code: item?.hsn_code,
                            multiQuantity: multiQuantity,
                            selectedLedger: item?.ledger_id,
                            quantity: item?.quantity?.toFixed(item?.decimal_places || 2),
                            free_quantity: item?.free_quantity,
                            rateWithGst: item?.rpu_with_gst,
                            updatedRateWithGst: item?.with_tax
                                ? item?.rpu_with_gst
                                : item?.rpu_without_gst?.toFixed(2),
                            rateWithoutGst: item?.with_tax
                                ? item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                                : item?.rpu_without_gst?.toFixed(
                                      item?.decimal_places_for_rate || 2
                                  ),
                            updatedRateWithoutGst: item?.with_tax
                                ? item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                                : item?.rpu_without_gst?.toFixed(
                                      item?.decimal_places_for_rate || 2
                                  ),
                            gst_id: item?.gst_id,
                            gst: item?.gst_tax_percentage,
                            discountType: item?.discount_type,
                            discountValue: item?.discount_value,
                            discountType_2: item?.discount_type_2,
                            discountValue_2: item?.discount_value_2 ? item?.discount_value_2 : 0,
                            total: customToFixed(item?.total, 2),
                            with_tax: item?.with_tax ? 1 : 0,
                            selectedUnit: item?.unit_id,
                            itemUnitOption: unitOptions,
                            cgstValue: item?.classification_cgst_tax,
                            igstValue: item?.classification_igst_tax,
                            sgstValue: item?.classification_sgst_tax,
                            cessValue: (item?.cess_rate * item?.total) / 100,
                            cessRate: item?.cess_rate,
                            isShowDelete:
                                singleSale?.expense_credit_note_items?.length > 1 ? true : false,
                            decimal_places: item?.decimal_places ?? 2,
                            decimal_places_for_rate: item?.decimal_places_for_rate ?? 2,
                            secondaryUnitOfMeasurement: item?.secondary_unit_of_measurement,
                            conversationRate: item?.conversion_rate,
                            item_decimal_places: item?.item_decimal_places,
                            item_decimal_places_for_rate: item?.item_decimal_places_for_rate,
                            custom_fields: customFieldOptimization(item?.custom_fields),
                            custom_field_inventory_store: formattedCustomField(item?.custom_items_inventory_values),
                            model_inventory_custom_fields: item?.model_inventory_custom_fields,
                            model_custom_fields: item?.model_inventory_custom_fields,
                            model_select_inventory_custom_fields: item?.custom_items_inventory_values,
                            is_status: ITEM_STATUS.ACTIVE,
                        };
                    })
                );
            } else if (item_type == 1) {
                setAccountingItems(
                    singleSale?.expense_credit_note_ledgers?.map((item, index) => {
                        setChangeTax(item?.with_tax);
                        return {
                            id: index + 1,
                            selectedLedger: item?.ledger_id,
                            additional_description: item?.additional_description,
                            rateWithGst: parseFloat(item?.rpu_with_gst),
                            updatedRateWithGst: item?.with_tax
                                ? parseFloat(item?.rpu_with_gst)
                                : parseFloat(item?.rpu_without_gst?.toFixed(2)),
                            rateWithoutGst: item?.with_tax
                                ? parseFloat(
                                      item?.rpu_with_gst?.toFixed(
                                          item?.decimal_places_for_rate || 2
                                      )
                                  )
                                : parseFloat(
                                      item?.rpu_without_gst?.toFixed(
                                          item?.decimal_places_for_rate || 2
                                      )
                                  ),
                            updatedRateWithoutGst: item?.with_tax
                                ? parseFloat(
                                      item?.rpu_with_gst?.toFixed(
                                          item?.decimal_places_for_rate || 2
                                      )
                                  )
                                : parseFloat(
                                      item?.rpu_without_gst?.toFixed(
                                          item?.decimal_places_for_rate || 2
                                      )
                                  ),
                        amountWithoutGst: item?.with_tax
                            ? parseFloat(
                                  item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              )
                            : parseFloat(
                                  item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              ),
                            updatedAmountWithoutGst: item?.rpu_without_gst,
                            gst: item?.gst_tax_percentage,
                            gst_id: item?.gst_id,
                            discountType: item?.discount_type,
                            discountValue: item?.discount_value,
                            discountType_2: item?.discount_type_2,
                            discountValue_2: item?.discount_value_2 ? item?.discount_value_2 : 0,
                            total: customToFixed(item?.total, 2),
                            cgstValue: item?.classification_cgst_tax,
                            igstValue: item?.classification_igst_tax,
                            sgstValue: item?.classification_sgst_tax,
                            cessValue: (item?.cess_rate * item?.total) / 100,
                            cessRate: item?.cess_rate,
                            with_tax: item?.with_tax ? 1 : 0,
                            rpu: item?.rpu_with_gst || 0,
                            isShowDelete:
                                singleSale?.sale_return_ledgers?.length > 1 ? true : false,
                            secondaryUnitOfMeasurement: item?.secondary_unit_of_measurement,
                            conversationRate: item?.conversion_rate,
                            item_decimal_places: item?.item_decimal_places,
                            item_decimal_places_for_rate: item?.item_decimal_places_for_rate,
                            custom_fields: customFieldOptimization(item?.custom_ledgers_values),
                            is_status: ITEM_STATUS.ACTIVE,
                        };
                    })
                );
            }
            if (item_type == 2) {
                dispatch(rearrangeItemList(cn_dn == "cn" ? TRANSACTION_TYPE.EXPENSE_CREDIT_NOTE : TRANSACTION_TYPE.EXPENSE_DEBIT_NOTE, 1));
            } else {
                dispatch(rearrangeItemList(cn_dn == "cn" ? TRANSACTION_TYPE.EXPENSE_CREDIT_NOTE : TRANSACTION_TYPE.EXPENSE_DEBIT_NOTE, 2));
            }
            setItemType(item_type == 2 ? "item" : "accounting");
            const nature_type =
                item_type == 2
                    ? singleSale?.expense_credit_note_items &&
                      singleSale?.expense_credit_note_items[0]?.classification_nature_type
                    : singleSale?.expense_credit_note_ledgers &&
                      singleSale?.expense_credit_note_ledgers[0]?.classification_nature_type;
            const nature_name = classificationOptions.find(item => item.value == nature_type);

            setClassification({
                classification_nature: nature_type,
                classification_nature_name: nature_name?.label,
                rcm_applicable:
                    singleSale?.expense_cn_item_type == 2
                        ? singleSale?.expense_credit_note_items &&
                          singleSale?.expense_credit_note_items[0]
                              ?.classification_is_rcm_applicable == 1
                            ? true
                            : false
                        : singleSale?.expense_credit_note_ledgers &&
                          singleSale?.expense_credit_note_ledgers[0]
                              ?.classification_is_rcm_applicable == 1
                        ? true
                        : false,
            });
            setGSTCalculationType(
                {
                    classification_nature_name: nature_name?.label,
                },
                setIsIGSTCalculation,
                setIsSGSTCalculation
            );
        } else {
            setItemType(item_type == 2 ? "item" : "accounting");
            const nature_type =
                item_type == 2
                    ? singleSale?.debit_note_items &&
                      singleSale?.debit_note_items[0]?.classification_nature_type
                    : singleSale?.debit_note_ledgers &&
                      singleSale?.debit_note_ledgers[0]?.classification_nature_type;
            const nature_name = classificationOptions.find(item => item.value == nature_type);
            setClassification({
                classification_nature: nature_type,
                classification_nature_name: nature_name?.label,
                rcm_applicable:
                    item_type == 2
                        ? singleSale?.debit_note_items &&
                          singleSale?.debit_note_items[0]?.classification_is_rcm_applicable == 1
                            ? true
                            : false
                        : singleSale?.debit_note_ledgers &&
                          singleSale?.debit_note_ledgers[0]?.classification_is_rcm_applicable == 1
                        ? true
                        : false,
            });
            setGSTCalculationType(
                {
                    classification_nature_name: nature_name?.label,
                },
                setIsIGSTCalculation,
                setIsSGSTCalculation
            );
            if (item_type == 2) {
                setItems(
                    singleSale?.debit_note_items?.map((item, index) => {
                        setChangeTax(item?.with_tax);
                        const multiQuantity =
                            item?.consolidating_items_to_invoice?.split(",") || [];

                        if (multiQuantity[0] == "0") {
                            multiQuantity[0] = item?.quantity?.toFixed(item?.decimal_places || 2);
                        }

                        while (multiQuantity.length < 4) {
                            multiQuantity.push("0");
                        }

                        const unitOptions = unitOption(item?.units_of_array);
                        return {
                            id: item?.id,
                            transaction_item_id: item?.id,
                            selectedItem: item?.item_id,
                            additional_description: item?.additional_description,
                            mrp: item?.mrp,
                            hsn_code: item?.hsn_code,
                            multiQuantity: multiQuantity,
                            selectedLedger: item?.ledger_id,
                            quantity: item?.quantity?.toFixed(item?.decimal_places || 2),
                            free_quantity: item?.free_quantity,
                            rateWithGst: item?.rpu_with_gst,
                            updatedRateWithGst: item?.with_tax
                                ? item?.rpu_with_gst
                                : item?.rpu_without_gst?.toFixed(2),
                            rateWithoutGst: item?.with_tax
                                ? item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                                : item?.rpu_without_gst?.toFixed(
                                      item?.decimal_places_for_rate || 2
                                  ),
                            updatedRateWithoutGst: item?.with_tax
                                ? item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                                : item?.rpu_without_gst?.toFixed(
                                      item?.decimal_places_for_rate || 2
                                  ),
                            gst_id: item?.gst_id,
                            gst: item?.gst_tax_percentage,
                            discountType: item?.discount_type,
                            discountValue: item?.discount_value,
                            discountType_2: item?.discount_type_2,
                            discountValue_2: item?.discount_value_2 ? item?.discount_value_2 : 0,
                            total: customToFixed(item?.total, 2),
                            with_tax: item?.with_tax ? 1 : 0,
                            selectedUnit: item?.unit_id,
                            itemUnitOption: unitOptions,
                            cgstValue: item?.classification_cgst_tax,
                            igstValue: item?.classification_igst_tax,
                            sgstValue: item?.classification_sgst_tax,
                            cessValue: (item?.cess_rate * item?.total) / 100,
                            cessRate: item?.cess_rate,
                            isShowDelete: singleSale?.debit_note_items?.length > 1 ? true : false,
                            decimal_places: item?.decimal_places ?? 2,
                            decimal_places_for_rate: item?.decimal_places_for_rate ?? 2,
                            secondaryUnitOfMeasurement: item?.secondary_unit_of_measurement,
                            conversationRate: item?.conversion_rate,
                            item_decimal_places: item?.item_decimal_places,
                            item_decimal_places_for_rate: item?.item_decimal_places_for_rate,
                            custom_fields: customFieldOptimization(item?.custom_fields),
                            custom_field_inventory_store: formattedCustomField(item?.custom_items_inventory_values),
                            model_inventory_custom_fields: item?.model_inventory_custom_fields,
                            model_custom_fields: item?.model_inventory_custom_fields,
                            model_select_inventory_custom_fields: item?.custom_items_inventory_values,
                            is_status: ITEM_STATUS.ACTIVE,
                        };
                    })
                );
            } else if (item_type == 1) {
                setAccountingItems(
                    singleSale?.debit_note_ledgers?.map((item, index) => {
                        setChangeTax(item?.with_tax);
                        return {
                            id: item?.id,
                            selectedLedger: item?.ledger_id,
                            additional_description: item?.additional_description,
                            rateWithGst: parseFloat(item?.rpu_with_gst),
                            updatedRateWithGst: item?.with_tax
                                ? parseFloat(item?.rpu_with_gst)
                                : parseFloat(item?.rpu_without_gst?.toFixed(2)),
                            rateWithoutGst: item?.with_tax
                                ? parseFloat(
                                      item?.rpu_with_gst?.toFixed(
                                          item?.decimal_places_for_rate || 2
                                      )
                                  )
                                : parseFloat(
                                      item?.rpu_without_gst?.toFixed(
                                          item?.decimal_places_for_rate || 2
                                      )
                                  ),
                            updatedRateWithoutGst: item?.with_tax
                                ? parseFloat(
                                      item?.rpu_with_gst?.toFixed(
                                          item?.decimal_places_for_rate || 2
                                      )
                                  )
                                : parseFloat(
                                      item?.rpu_without_gst?.toFixed(
                                          item?.decimal_places_for_rate || 2
                                      )
                                  ),
                        amountWithoutGst: item?.with_tax
                            ? parseFloat(
                                  item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              )
                            : parseFloat(
                                  item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              ),
                            updatedAmountWithoutGst: item?.rpu_without_gst,
                            gst: item?.gst_tax_percentage,
                            gst_id: item?.gst_id,
                            discountType: item?.discount_type,
                            discountValue: item?.discount_value,
                            discountType_2: item?.discount_type_2,
                            discountValue_2: item?.discount_value_2 ? item?.discount_value_2 : 0,
                            total: customToFixed(item?.total, 2),
                            cgstValue: item?.classification_cgst_tax,
                            igstValue: item?.classification_igst_tax,
                            sgstValue: item?.classification_sgst_tax,
                            cessValue: (item?.cess_rate * item?.total) / 100,
                            cessRate: item?.cess_rate,
                            with_tax: item?.with_tax ? 1 : 0,
                            rpu: item?.rpu_with_gst || 0,
                            isShowDelete: singleSale?.sale_ledgers?.length > 1 ? true : false,
                            secondaryUnitOfMeasurement: item?.secondary_unit_of_measurement,
                            conversationRate: item?.conversion_rate,
                            item_decimal_places: item?.item_decimal_places,
                            item_decimal_places_for_rate: item?.item_decimal_places_for_rate,
                            custom_fields: customFieldOptimization(item?.custom_ledgers_values),
                            is_status: ITEM_STATUS.ACTIVE,
                        };
                    })
                );
            }
            if (item_type == 2) {

                dispatch(rearrangeItemList(cn_dn ? TRANSACTION_TYPE.EXPENSE_CREDIT_NOTE : TRANSACTION_TYPE.EXPENSE_DEBIT_NOTE, 1));
            } else {

                dispatch(rearrangeItemList(cn_dn ? TRANSACTION_TYPE.EXPENSE_CREDIT_NOTE : TRANSACTION_TYPE.EXPENSE_DEBIT_NOTE, 2));
            }
        }
        setAddLessChanges(
            singleSale?.add_less?.length == 0
                ? addLessChanges
                : singleSale?.add_less?.map(item => {
                      return {
                          al_ledger_id: item.ledger_id,
                          al_is_show_in_print: item.is_show_in_print,
                          al_type: item.type,
                          al_value:
                              item.type == 2 ? parseFloat(item.value) : parseFloat(item.total),
                          al_total: parseFloat(item.total),
                          is_status: ITEM_STATUS.ACTIVE,
                      };
                  })
        );
        setAdditionalCharges({
            ...additionalCharges,
            ...(id && {upload_document: singleSale?.media?.map(media => {
                return { original_url: media?.original_url, id: media?.id };
            })}),
            note: singleSale?.narration,
            terms_and_conditions: singleSale?.term_and_condition,
            additional_detail:
                !singleSale?.additional_charges || singleSale?.additional_charges?.length == 0
                    ? [
                          {
                              ac_ledger_id: null,
                              ac_type: 1,
                              ac_value: "",
                              ac_gst_rate_id: {
                                  label: "",
                                  value: 0,
                                  rate: 0,
                              },
                              ac_total: 0,
                              is_status: ITEM_STATUS.IN_ACTIVE,
                          },
                      ]
                    : singleSale?.additional_charges?.map(charge => {
                          return {
                              ac_ledger_id: charge?.ledger_id,
                              ac_type: charge?.charge_type,
                              ac_value: charge?.value,
                              ac_gst_rate_id: {
                                  value: charge?.gst_rate_id,
                                  rate: Number(charge?.gst_percentage),
                                  label: `${charge?.gst_percentage}%`,
                              },
                              ac_total: customToFixed(charge?.total_without_tax, 2),
                              is_status: ITEM_STATUS.ACTIVE,
                          };
                      }),
        });
        setSameAsBill(singleSale?.same_as_billing);
        setPartyAddress({
            billingAddress: {
                address_1: singleSale?.billing_address?.address_1,
                address_2: singleSale?.billing_address?.address_2,
                country_id: singleSale?.billing_address?.country_id,
                state_id: singleSale?.billing_address?.state_id,
                city_id: singleSale?.billing_address?.city_id,
                state_name: singleSale?.billing_address?.state_name,
                city_name: singleSale?.billing_address?.city_name,
                pin_code: singleSale?.billing_address?.pin_code,
            },
            shippingAddress: {
                address_1: singleSale?.dispatch_address?.address_1,
                address_2: singleSale?.dispatch_address?.address_2,
                country_id: singleSale?.dispatch_address?.country_id,
                state_id: singleSale?.dispatch_address?.state_id,
                city_id: singleSale?.dispatch_address?.city_id,
                state_name: singleSale?.dispatch_address?.state_name,
                city_name: singleSale?.dispatch_address?.city_name,
                pin_code: singleSale?.dispatch_address?.pin_code,
                shipping_name: singleSale?.shipping_name,
                shipping_gstin: singleSale?.shipping_gstin,
                shipping_address_id: singleSale?.shipping_address_id
            },
        });
        const roundOffAmount = customToFixed(singleSale?.rounding_amount, 2);
        setGstCalculation({
            ...gstCalculation,
            round_of_amount: roundOffAmount,
            is_round_off_not_changed: singleSale?.is_round_off_not_changed,
            ...(singleSale?.round_off_method ? {round_off_method: Number(singleSale?.round_off_method)} : {})
        });
        setBrokerDetail({
            broker_id: singleSale?.broker_id,
            broker_percentage: singleSale?.brokerage_for_sale ?? "",
            brokerage_on_value: singleSale?.brokerage_on_value_type,
        });
        setTransporterDetail({
            transport_id: singleSale?.transport_id,
            transporter_document_number: singleSale?.transporter_document_number,
            transporter_document_date: singleSale?.transporter_document_date
                ? formattedDate(singleSale?.transporter_document_date)
                : "",
            transporter_vehicle_number: singleSale?.transporter_vehicle_number,
        });
        setEwayBillDetail({
            eway_bill_date: singleSale?.eway_bill_date
                ? formattedDate(singleSale?.eway_bill_date)
                : "",
            eway_bill_number: singleSale?.eway_bill_number,
        });
        setOtherDetail({
            po_number: singleSale?.po_no,
            date: singleSale?.po_date ? formattedDate(singleSale?.po_date) : null,
            creditPeriod: singleSale?.credit_period,
            creditPeriodType: singleSale?.credit_period_type || 1,
        });
        setGstValue({
            igstValue: singleSale?.igst,
            cgstValue: singleSale?.cgst,
            sgstValue: singleSale?.sgst,
        });
        setTcsRate({
            tcs_amount: singleSale?.tcs_tax_id ? customToFixed(singleSale?.tcs_amount, 2) : '',
            tcs_tax_id: singleSale?.tcs_tax_id,
            tcs_rate: singleSale?.tcs_tax_id ? singleSale?.tcs_rate : '',
            tcs_calculated_on: singleSale?.tcs_calculated?.calculated_on,
        });
        setPaymentLedgerDetail(prev => ({
                ...(singleSale?.payment_details?.length > 0 && expenseEdit
                    ? {
                          payment_detail: singleSale.payment_details.map(item => ({
                              pd_ledger_id: item?.ledger_id,
                              pd_amount: item?.amount,
                              pd_date: item?.date ? formattedDate(item?.date) : null,
                              pd_mode: item?.mode || null,
                              pd_reference_number: item?.reference_no,
                              pd_id: item?.id,
                          })),
                      }
                    : { payment_detail:  [
                        {
                            pd_ledger_id: null,
                            pd_date: "",
                            pd_amount: "",
                            pd_mode: null,
                            pd_reference_number: "",
                            is_show_invoice_date: true,
                        }
                    ] }),
                tds_amount: (singleSale?.tds_tax_id || singleSale?.ledger_of_tds) ? singleSale?.tds_amount : "",
                tds_rate: (singleSale?.tds_tax_id || singleSale?.ledger_of_tds) ? singleSale?.tds_rate : "",
                tds_tax_id: singleSale?.tds_tax_id || singleSale?.ledger_of_tds,
                rounding_type: singleSale?.tds_rounding_method || 1
        }));
        setGrandTotal(singleSale?.gross_value);
        setMainGrandTotal(singleSale?.grand_total - roundOffAmount);
        setFinalAmount(singleSale?.grand_total);
    }
};

export const prepareItemData = (items, setItems, setChangeTax) => {
    setItems(
        items?.map((item, index) => {
            const unitOptions = unitOption(item?.units_of_array);
            setChangeTax(item?.with_tax);
            return {
                // id: item?.id,
                // selectedItem: item?.item_id,
                // additional_description: item?.additional_description,
                // multiQuantity: [0, 0, 0, 0],
                // quantity: parseFloat(item?.quantity)?.toFixed(item?.decimal_places || 2),
                // selectedUnit: item?.unit_id,
                // isShowDelete: items?.length > 0 ? true : false,
                // decimal_places: item?.decimal_places,
                // itemUnitOption: unitOptions,
                id: item?.id,
                transaction_item_id: item?.id,
                selectedItem: item?.item_id,
                additional_description: item?.additional_description,
                mrp: item?.mrp,
                hsn_code: item?.hsn_code,
                multiQuantity: [0, 0, 0, 0],
                selectedLedger: item?.ledger_id,
                quantity: parseFloat(item?.quantity)?.toFixed(item?.decimal_places || 2),
                free_quantity: item?.free_quantity,
                rateWithGst: item?.rpu_with_gst,
                updatedRateWithGst: item?.with_tax
                    ? item?.rpu_with_gst
                    : item?.rpu_without_gst?.toFixed(2),
                rateWithoutGst: item?.with_tax
                    ? item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                    : item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2),
                updatedRateWithoutGst: item?.with_tax
                    ? item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                    : item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2),
                gst_id: item?.gst_id,
                gst: item?.gst_tax_percentage,
                discountType: item?.discount_type || 1,
                discountValue: item?.discount_value || 0,
                discountType_2: item?.discount_type_2 || 1,
                discountValue_2: item?.discount_value_2 ? item?.discount_value_2 : 0,
                total: customToFixed(item?.total, 2),
                with_tax: item?.with_tax ? 1 : 0,
                selectedUnit: item?.unit_id,
                cgstValue: item?.classification_cgst_tax,
                igstValue: item?.classification_igst_tax,
                sgstValue: item?.classification_sgst_tax,
                cessValue: (item?.cess_rate * item?.total) / 100,
                cessRate: item?.cess_rate,
                isShowDelete: items?.length > 0 ? true : false,
                itemUnitOption: unitOptions,
                decimal_places: item?.decimal_places ?? 2,
                decimal_places_for_rate: item?.decimal_places_for_rate ?? 2,
                secondaryUnitOfMeasurement: item?.secondary_unit_of_measurement,
                conversationRate: item?.conversion_rate,
                custom_fields: customFieldOptimization(item?.custom_fields),
                custom_field_inventory_store: formattedCustomField(item?.custom_items_inventory_values),
                model_inventory_custom_fields: item?.model_inventory_custom_fields,
                model_custom_fields: item?.model_inventory_custom_fields,
                model_select_inventory_custom_fields: item?.custom_items_inventory_values,
            };
        })
    );
};

export const preparePurchaseData = (
    singleData,
    {
        id,
        setStateFunctions,
        dispatch,
        tableHeaderList,
        accountingTableHeader,
        addLessChanges,
        additionalCharges,
        classificationOptions,
        isDuplicate
    },
) => {
    if (singleData) {
        const {
            setPurchaseInvoice,
            gstQuote,
            setGstQuote,
            setItemType,
            setClassification,
            setItems,
            setAccountingItems,
            setAddLessChanges,
            setAdditionalCharges,
            setPartyAddress,
            setGstCalculation,
            setBrokerDetail,
            setTransporterDetail,
            setEwayBillDetail,
            setOtherDetail,
            setGstValue,
            setTcsRate,
            setPaymentLedgerDetail,
            setGrandTotal,
            setMainGrandTotal,
            setChangeTax,
            setFinalAmount,
            setInvoiceDetail,
            setIsIGSTCalculation,
            setIsSGSTCalculation,
            setTaxableValue,
            setCessValue,
            setUpdatePurchaseNumberOptions,
            setSelectedAdvancePayment,
            setCustomHeaderListTransaction,
            setSameAsBill
        } = setStateFunctions;

        setUpdatePurchaseNumberOptions(
            singleData?.purchase_order_no
                ? convertIntoDropdownList(singleData?.purchase_order_list)
                : null
        );
        let filteredOptions = convertIntoDropdownList(singleData?.purchase_order_list);

        const purchaseOrderIds = singleData?.purchase_order_no
            ? singleData.purchase_order_no.split(",").map(id => id.trim())
            : [];

        const selectedPurchaseOrders = purchaseOrderIds
            .map(id => filteredOptions.find(option => option.value == id))
            .filter(Boolean);

        setGstQuote({
            ...gstQuote,
            gstin: singleData?.gstin,
            party_ledger_id: singleData?.supplier_ledger_id,
            valid_for_type: singleData?.valid_for_type,
            valid_for: singleData?.valid_for && parseFloat(singleData?.valid_for),
            original_inv_no: singleData?.purchase_order_no,
            purchase_number: isDuplicate ? [] : selectedPurchaseOrders,
        });
        setInvoiceDetail({
            invoice_number: singleData?.sale_number,
            invoice_date: singleData?.date_of_invoice && formattedDate(singleData?.date_of_invoice),
        });
        setCustomHeaderListTransaction(singleData?.custom_values)
        setPurchaseInvoice({
            voucher_number: singleData?.voucher_number,
            voucher_date: singleData?.voucher_date && formattedDate(singleData?.voucher_date),
        });
        setItemType(singleData?.purchase_item_type == 2 ? "item" : "accounting");
        const nature_type =
            singleData?.purchase_item_type == 2
                ? singleData?.purchase_transaction_items[0]?.classification_nature_type
                : singleData?.purchase_transaction_ledger[0]?.classification_nature_type;
        const nature_name = classificationOptions.find(item => item.value == nature_type);
        setClassification({
            classification_nature: nature_type,
            classification_nature_name: nature_name?.label,
            rcm_applicable:
                singleData?.purchase_item_type == 2
                    ? singleData?.purchase_transaction_items[0]?.classification_is_rcm_applicable ==
                      1
                        ? true
                        : false
                    : singleData?.purchase_transaction_ledger[0]
                          ?.classification_is_rcm_applicable == 1
                    ? true
                    : false,
        });
        setGSTCalculationType(
            {
                classification_nature_name: nature_name?.label,
            },
            setIsIGSTCalculation,
            setIsSGSTCalculation
        );
        if (singleData?.purchase_item_type == 2) {
            setItems(
                singleData?.purchase_transaction_items?.map((item, index) => {
                    setChangeTax(item?.with_tax);
                    const multiQuantity = item?.consolidating_items_to_invoice?.split(",") || [];

                    if (multiQuantity[0] == "0") {
                        multiQuantity[0] = item?.quantity?.toFixed(item?.decimal_places || 2);
                    }

                    while (multiQuantity.length < 4) {
                        multiQuantity.push("0");
                    }

                    const unitOptions = unitOption(item?.units_of_array);
                    return {
                        id: item?.id,
                        transaction_item_id: item?.id,
                        selectedItem: item?.item_id,
                        additional_description: item?.additional_description,
                        mrp: item?.mrp,
                        hsn_code: item?.hsn_code,
                        multiQuantity: multiQuantity,
                        selectedLedger: item?.ledger_id,
                        quantity: item?.quantity?.toFixed(item?.decimal_places || 2),
                        free_quantity: item?.free_quantity,
                        rateWithGst: item?.rpu_with_gst,
                        updatedRateWithGst: item?.with_tax
                            ? item?.rpu_with_gst
                            : item?.rpu_without_gst?.toFixed(2),
                        rateWithoutGst: item?.with_tax
                            ? item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                            : item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2),
                        updatedRateWithoutGst: item?.with_tax
                            ? item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                            : item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2),
                        gst_id: item?.gst_id,
                        gst: item?.gst_tax_percentage,
                        discountType: item?.discount_type || 1,
                        discountValue: item?.discount_value,
                        discountType_2: item?.discount_type_2 || 1,
                        discountValue_2: item?.discount_value_2 ? item?.discount_value_2 : 0,
                        total: customToFixed(item?.total, 2),
                        with_tax: item?.with_tax ? 1 : 0,
                        selectedUnit: item?.unit_id,
                        cgstValue: item?.classification_cgst_tax,
                        igstValue: item?.classification_igst_tax,
                        sgstValue: item?.classification_sgst_tax,
                        cessValue: (item?.cess_rate * item?.total) / 100,
                        cessRate: item?.cess_rate,
                        isShowDelete:
                            singleData?.purchase_transaction_items?.length > 1 ? true : false,
                        itemUnitOption: unitOptions,
                        decimal_places: item?.decimal_places ?? 2,
                        decimal_places_for_rate: item?.decimal_places_for_rate ?? 2,
                        secondaryUnitOfMeasurement: item?.secondary_unit_of_measurement,
                        conversationRate: item?.conversion_rate,
                        item_decimal_places: item?.item_decimal_places,
                        item_decimal_places_for_rate: item?.item_decimal_places_for_rate,
                        custom_fields: customFieldOptimization(item?.custom_fields),
                        custom_field_inventory_store: formattedCustomField(item?.custom_items_inventory_values),
                        model_inventory_custom_fields: item?.model_inventory_custom_fields,
                        model_custom_fields: item?.model_inventory_custom_fields,
                        model_select_inventory_custom_fields: item?.custom_items_inventory_values,
                        is_status: ITEM_STATUS.ACTIVE,
                    };
                })
            );
        } else if (singleData?.purchase_item_type == 1) {
            setAccountingItems(
                singleData?.purchase_transaction_ledger?.map((item, index) => {
                    setChangeTax(item?.with_tax);
                    return {
                        id: index + 1,
                        selectedLedger: item?.ledger_id,
                        additional_description: item?.additional_description,
                        rateWithGst: parseFloat(item?.rpu_with_gst),
                        updatedRateWithGst: item?.with_tax
                            ? parseFloat(item?.rpu_with_gst)
                            : parseFloat(item?.rpu_without_gst?.toFixed(2)),
                        rateWithoutGst: item?.with_tax
                            ? parseFloat(
                                  item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              )
                            : parseFloat(
                                  item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              ),
                        updatedRateWithoutGst: item?.with_tax
                            ? parseFloat(
                                  item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              )
                            : parseFloat(
                                  item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              ),
                        amountWithoutGst: item?.with_tax
                            ? parseFloat(
                                  item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              )
                            : parseFloat(
                                  item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              ),
                        updatedAmountWithoutGst: item?.with_tax
                            ? parseFloat(
                                  item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              )
                            : parseFloat(
                                  item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              ),
                        gst: item?.gst_tax_percentage,
                        gst_id: item?.gst_id,
                        discountType: item?.discount_type || 1,
                        discountValue: item?.discount_value,
                        discountType_2: item?.discount_type_2 || 1,
                        discountValue_2: item?.discount_value_2 ? item?.discount_value_2 : 0,
                        total: customToFixed(item?.total, 2),
                        cgstValue: item?.classification_cgst_tax,
                        igstValue: item?.classification_igst_tax,
                        sgstValue: item?.classification_sgst_tax,
                        cessValue: (item?.cess_rate * item?.total) / 100,
                        cessRate: item?.cess_rate,
                        with_tax: item?.with_tax ? 1 : 0,
                        rpu: item?.rpu_with_gst || 0,
                        isShowDelete:
                            singleData?.purchase_transaction_ledger?.length > 1 ? true : false,
                        custom_fields: customFieldOptimization(item?.custom_ledgers_values),
                        is_status: ITEM_STATUS.ACTIVE,
                    };
                })
            );
        }
        if (singleData?.purchase_item_type == 2) {

            dispatch(rearrangeItemList(TRANSACTION_TYPE.PURCHASE, 1));
        } else {

            dispatch(rearrangeItemList(TRANSACTION_TYPE.PURCHASE, 2));
        }
        setAddLessChanges(
            singleData?.add_less?.length == 0
                ? addLessChanges
                : singleData?.add_less?.map(item => {
                      return {
                          al_ledger_id: item.ledger_id,
                          al_is_show_in_print: item.is_show_in_print,
                          al_type: item.type,
                          al_value:
                              item.type == 2 ? parseFloat(item.value) : parseFloat(item.total),
                          al_total: parseFloat(item.total),
                          is_status: ITEM_STATUS.ACTIVE,
                      };
                  })
        );
        setAdditionalCharges({
            ...additionalCharges,
            ...(id && {upload_document: singleData?.media?.map(media => {
                return { original_url: media?.original_url, id: media?.id };
            })}),
            note: singleData?.narration,
            terms_and_conditions: singleData?.term_and_condition,
            additional_detail:
                singleData?.additional_charges?.length == 0
                    ? [
                          {
                              ac_ledger_id: null,
                              ac_type: 1,
                              ac_value: "",
                              ac_gst_rate_id: {
                                  label: "",
                                  value: 0,
                                  rate: 0,
                              },
                              ac_total: 0,
                              is_status: ITEM_STATUS.IN_ACTIVE,
                          },
                      ]
                    : singleData?.additional_charges?.map(charge => {
                          return {
                              ac_ledger_id: charge?.ledger_id,
                              ac_type: charge?.charge_type,
                              ac_value: charge?.value,
                              ac_gst_rate_id: {
                                  value: charge?.gst_rate_id,
                                  rate: Number(charge?.gst_percentage),
                                  label: `${charge?.gst_percentage}%`,
                              },
                              ac_total: customToFixed(charge?.total_without_tax, 2),
                              is_status: ITEM_STATUS.ACTIVE,
                          };
                      }),
        });
        setSameAsBill(singleData?.same_as_billing);
        setPartyAddress({
            billingAddress: {
                address_1: singleData?.billing_address?.address_1,
                address_2: singleData?.billing_address?.address_2,
                country_id: singleData?.billing_address?.country_id,
                state_id: singleData?.billing_address?.state_id,
                city_id: singleData?.billing_address?.city_id,
                state_name: singleData?.billing_address?.state_name,
                city_name: singleData?.billing_address?.city_name,
                pin_code: singleData?.billing_address?.pin_code,
            },
            shippingAddress: {
                shipping_address_id: singleData?.shipping_address_id,
                address_1: singleData?.dispatch_address?.address_1,
                address_2: singleData?.dispatch_address?.address_2,
                country_id: singleData?.dispatch_address?.country_id,
                state_id: singleData?.dispatch_address?.state_id,
                city_id: singleData?.dispatch_address?.city_id,
                state_name: singleData?.dispatch_address?.state_name,
                city_name: singleData?.dispatch_address?.city_name,
                pin_code: singleData?.dispatch_address?.pin_code,
                shipping_name: singleData?.shipping_name,
                shipping_gstin: singleData?.shipping_gstin,
            },
        });
        const roundOffAmount = customToFixed(singleData?.rounding_amount, 2);
        setGstCalculation({
            round_of_amount: roundOffAmount,
            is_gst_na: singleData?.is_gst_na,
            is_round_off_not_changed: singleData?.is_round_off_not_changed,
            ...(singleData?.round_off_method ? {round_off_method: Number(singleData?.round_off_method)} : {})
        });
        setBrokerDetail({
            broker_id: singleData?.broker_id,
            broker_percentage: singleData?.brokerage_for_sale ? singleData?.brokerage_for_sale : "",
            brokerage_on_value: singleData?.brokerage_on_value_type,
        });
        setTransporterDetail({
            transport_id: singleData?.transport_id,
            transporter_document_number: singleData?.transporter_document_number,
            transporter_document_date: singleData?.transporter_document_date
                ? formattedDate(singleData?.transporter_document_date)
                : "",
            transporter_vehicle_number: singleData?.transporter_vehicle_number,
        });
        setEwayBillDetail({
            eway_bill_date: singleData?.eway_bill_date
                ? formattedDate(singleData?.eway_bill_date)
                : "",
            eway_bill_number: singleData?.eway_bill_number,
        });
        setOtherDetail({
            po_number: singleData?.po_no,
            date: singleData?.po_date ? formattedDate(singleData?.po_date) : null,
            creditPeriod: singleData?.credit_period,
            creditPeriodType: singleData?.credit_period_type || 1,
        });
        if(!isDuplicate){
            setSelectedAdvancePayment(
                singleData?.advance_payment?.map((item) => {
                    return{
                        ...item,
                        received_amount: item?.adjusted_amount,
                        advance_payment_id: item?.id
                    }
                })
            );
        }
        setGstValue({
            igstValue: singleData?.igst,
            cgstValue: singleData?.cgst,
            sgstValue: singleData?.sgst,
        });
        setTcsRate({
            tcs_amount: singleData?.tcs_tax_id ? customToFixed(singleData?.tcs_amount, 2) : '',
            tcs_tax_id: singleData?.tcs_tax_id,
            tcs_rate: singleData?.tcs_tax_id ? singleData?.tcs_rate : '',
            tcs_calculated_on: singleData?.tcs_calculated?.calculated_on,
        });
        setPaymentLedgerDetail(prev => ({
            ...(singleData?.payment_details?.length > 0
                ? {
                      payment_detail: singleData.payment_details.map(item => ({
                          pd_ledger_id: item?.ledger_id,
                          pd_amount: item?.amount,
                          pd_date: item?.date ? formattedDate(item?.date) : "",
                          pd_mode: item?.mode || null,
                          pd_reference_number: item?.reference_no,
                          pd_id: item?.id,
                      })),
                  }
                : {
                      payment_detail: [
                          {
                              pd_ledger_id: null,
                              pd_date:
                                  singleData?.date_of_invoice &&
                                  formattedDate(singleData?.date_of_invoice),
                              pd_amount: "",
                              pd_mode: null,
                              pd_reference_number: "",
                              is_show_invoice_date: true,
                          },
                      ],
                  }),
            tds_amount: (singleData?.tds_tax_id || singleData?.ledger_of_tds) ? singleData?.tds_amount : "",
            tds_rate: (singleData?.tds_tax_id || singleData?.ledger_of_tds) ? singleData?.tds_rate : "",
            tds_tax_id: singleData?.tds_tax_id || singleData?.ledger_of_tds,
            rounding_type: singleData?.tds_rounding_method || 1
        }));
        setTaxableValue(singleData?.taxable_value);
        setGrandTotal(singleData?.gross_value);
        setCessValue(singleData?.cess);
        setMainGrandTotal(singleData?.grand_total - roundOffAmount);
        setFinalAmount(singleData?.grand_total);
        // setTimeout(() => {
        //     dispatch(getSaleById(""));
        // }, 1000);
    }
};
export const preparePurchaseToSale = (
    singleData,
    {
        id,
        setStateFunctions,
        dispatch,
        tableHeaderList,
        accountingTableHeader,
        addLessChanges,
        additionalCharges,
        classificationOptions,
        isDuplicate
    },
) => {
    if (singleData) {
        const {
            gstQuote,
            setGstQuote,
            setItemType,
            setClassification,
            setItems,
            setAccountingItems,
            setAddLessChanges,
            setAdditionalCharges,
            setPartyAddress,
            setGstCalculation,
            setBrokerDetail,
            setTransporterDetail,
            setEwayBillDetail,
            setOtherDetail,
            setGstValue,
            setTcsRate,
            setPaymentLedgerDetail,
            setGrandTotal,
            setMainGrandTotal,
            setChangeTax,
            setFinalAmount,
            setIsIGSTCalculation,
            setIsSGSTCalculation,
            setTaxableValue,
            setCessValue,
            setSelectedAdvancePayment,
            setCustomHeaderListTransaction,
            setSameAsBill
        } = setStateFunctions;

        setItemType(singleData?.purchase_item_type == 2 ? "item" : "accounting");
        const nature_type =
            singleData?.purchase_item_type == 2
                ? singleData?.purchase_transaction_items[0]?.classification_nature_type
                : singleData?.purchase_transaction_ledger[0]?.classification_nature_type;
        const PURCHASE_TO_SALE_NATURE_TYPE_MAP = {
           19: 1, 20: 2, 21: 3, 22: 4, 23: 5, 24: 6, 25: 7, 26: 8,
            27: 9, 28: 10, 29: 11, 30: 12, 31: 13, 32: 14, 33: 15, 34: 16
        };
        const convert_nature_type = PURCHASE_TO_SALE_NATURE_TYPE_MAP[nature_type] || nature_type;
        const nature_name = classificationOptions.find(item => item.value == convert_nature_type);
        setClassification({
            classification_nature: convert_nature_type,
            classification_nature_name: nature_name?.label,
            rcm_applicable:
                singleData?.purchase_item_type == 2
                    ? singleData?.purchase_transaction_items[0]?.classification_is_rcm_applicable ==
                      1
                        ? true
                        : false
                    : singleData?.purchase_transaction_ledger[0]
                          ?.classification_is_rcm_applicable == 1
                    ? true
                    : false,
        });
        setGSTCalculationType(
            {
                classification_nature_name: nature_name?.label,
            },
            setIsIGSTCalculation,
            setIsSGSTCalculation
        );
        if (singleData?.purchase_item_type == 2) {
            setItems(
                singleData?.purchase_transaction_items?.map((item, index) => {
                    setChangeTax(item?.with_tax);
                    const multiQuantity = item?.consolidating_items_to_invoice?.split(",") || [];

                    if (multiQuantity[0] == "0") {
                        multiQuantity[0] = item?.quantity?.toFixed(item?.decimal_places || 2);
                    }

                    while (multiQuantity.length < 4) {
                        multiQuantity.push("0");
                    }

                    const unitOptions = unitOption(item?.units_of_array);
                    return {
                        id: item?.id,
                        transaction_item_id: item?.id,
                        selectedItem: item?.item_id,
                        additional_description: item?.additional_description,
                        mrp: item?.mrp,
                        hsn_code: item?.hsn_code,
                        multiQuantity: multiQuantity,
                        selectedLedger: item?.ledger_id,
                        quantity: item?.quantity?.toFixed(item?.decimal_places || 2),
                        free_quantity: item?.free_quantity,
                        rateWithGst: item?.rpu_with_gst,
                        updatedRateWithGst: item?.with_tax
                            ? item?.rpu_with_gst
                            : item?.rpu_without_gst?.toFixed(2),
                        rateWithoutGst: item?.with_tax
                            ? item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                            : item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2),
                        updatedRateWithoutGst: item?.with_tax
                            ? item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                            : item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2),
                        gst_id: item?.gst_id,
                        gst: item?.gst_tax_percentage,
                        discountType: item?.discount_type || 1,
                        discountValue: item?.discount_value,
                        discountType_2: item?.discount_type_2 || 1,
                        discountValue_2: item?.discount_value_2 ? item?.discount_value_2 : 0,
                        total: customToFixed(item?.total, 2),
                        with_tax: item?.with_tax ? 1 : 0,
                        selectedUnit: item?.unit_id,
                        cgstValue: item?.classification_cgst_tax,
                        igstValue: item?.classification_igst_tax,
                        sgstValue: item?.classification_sgst_tax,
                        cessValue: (item?.cess_rate * item?.total) / 100,
                        cessRate: item?.cess_rate,
                        isShowDelete:
                            singleData?.purchase_transaction_items?.length > 1 ? true : false,
                        itemUnitOption: unitOptions,
                        decimal_places: item?.decimal_places ?? 2,
                        decimal_places_for_rate: item?.decimal_places_for_rate ?? 2,
                        secondaryUnitOfMeasurement: item?.secondary_unit_of_measurement,
                        conversationRate: item?.conversion_rate,
                        item_decimal_places: item?.item_decimal_places,
                        item_decimal_places_for_rate: item?.item_decimal_places_for_rate,
                        custom_fields: customFieldOptimization(item?.custom_fields),
                        custom_field_inventory_store: formattedCustomField(item?.custom_items_inventory_values, true),
                        model_inventory_custom_fields: item?.model_inventory_custom_fields,
                            model_custom_fields: item?.model_inventory_custom_fields,
                        model_select_inventory_custom_fields: PrepareForSoldQty(item?.custom_items_inventory_values),
                        is_status: ITEM_STATUS.ACTIVE,
                    };
                })
            );
        } else if (singleData?.purchase_item_type == 1) {
            setAccountingItems(
                singleData?.purchase_transaction_ledger?.map((item, index) => {
                    setChangeTax(item?.with_tax);
                    return {
                        id: index + 1,
                        selectedLedger: item?.ledger_id,
                        additional_description: item?.additional_description,
                        rateWithGst: parseFloat(item?.rpu_with_gst),
                        updatedRateWithGst: item?.with_tax
                            ? parseFloat(item?.rpu_with_gst)
                            : parseFloat(item?.rpu_without_gst?.toFixed(2)),
                        rateWithoutGst: item?.with_tax
                            ? parseFloat(
                                  item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              )
                            : parseFloat(
                                  item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              ),
                        updatedRateWithoutGst: item?.with_tax
                            ? parseFloat(
                                  item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              )
                            : parseFloat(
                                  item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              ),
                        amountWithoutGst: item?.with_tax
                            ? parseFloat(
                                  item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              )
                            : parseFloat(
                                  item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              ),
                        updatedAmountWithoutGst: item?.with_tax
                            ? parseFloat(
                                  item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              )
                            : parseFloat(
                                  item?.rpu_without_gst?.toFixed(item?.decimal_places_for_rate || 2)
                              ),
                        gst: item?.gst_tax_percentage,
                        gst_id: item?.gst_id,
                        discountType: item?.discount_type || 1,
                        discountValue: item?.discount_value,
                        discountType_2: item?.discount_type_2 || 1,
                        discountValue_2: item?.discount_value_2 ? item?.discount_value_2 : 0,
                        total: customToFixed(item?.total, 2),
                        cgstValue: item?.classification_cgst_tax,
                        igstValue: item?.classification_igst_tax,
                        sgstValue: item?.classification_sgst_tax,
                        cessValue: (item?.cess_rate * item?.total) / 100,
                        cessRate: item?.cess_rate,
                        with_tax: item?.with_tax ? 1 : 0,
                        rpu: item?.rpu_with_gst || 0,
                        isShowDelete:
                            singleData?.purchase_transaction_ledger?.length > 1 ? true : false,
                        custom_fields: customFieldOptimization(item?.custom_ledgers_values),
                        is_status: ITEM_STATUS.ACTIVE,
                    };
                })
            );
        }
        if (singleData?.purchase_item_type == 2) {

            dispatch(rearrangeItemList(TRANSACTION_TYPE.SALE, 1));
        } else {

            dispatch(rearrangeItemList(TRANSACTION_TYPE.SALE, 2));
        }
        setAddLessChanges(
            singleData?.add_less?.length == 0
                ? addLessChanges
                : singleData?.add_less?.map(item => {
                      return {
                          al_ledger_id: item.ledger_id,
                          al_is_show_in_print: item.is_show_in_print,
                          al_type: item.type,
                          al_value:
                              item.type == 2 ? parseFloat(item.value) : parseFloat(item.total),
                          al_total: parseFloat(item.total),
                          is_status: ITEM_STATUS.ACTIVE,
                      };
                  })
        );
        setAdditionalCharges({
            ...additionalCharges,
            ...(id && {upload_document: singleData?.media?.map(media => {
                return { original_url: media?.original_url, id: media?.id };
            })}),
            note: singleData?.narration,
            terms_and_conditions: singleData?.term_and_condition,
            additional_detail:
                singleData?.additional_charges?.length == 0
                    ? [
                          {
                              ac_ledger_id: null,
                              ac_type: 1,
                              ac_value: "",
                              ac_gst_rate_id: {
                                  label: "",
                                  value: 0,
                                  rate: 0,
                              },
                              ac_total: 0,
                              is_status: ITEM_STATUS.IN_ACTIVE,
                          },
                      ]
                    : singleData?.additional_charges?.map(charge => {
                          return {
                              ac_ledger_id: charge?.ledger_id,
                              ac_type: charge?.charge_type,
                              ac_value: charge?.value,
                              ac_gst_rate_id: {
                                  value: charge?.gst_rate_id,
                                  rate: Number(charge?.gst_percentage),
                                  label: `${charge?.gst_percentage}%`,
                              },
                              ac_total: customToFixed(charge?.total_without_tax, 2),
                              is_status: ITEM_STATUS.ACTIVE,
                          };
                      }),
        });
        setSameAsBill(singleData?.same_as_billing);
        const roundOffAmount = customToFixed(singleData?.rounding_amount, 2);
        setGstCalculation({
            round_of_amount: roundOffAmount,
            is_gst_na: singleData?.is_gst_na,
            is_round_off_not_changed: singleData?.is_round_off_not_changed,
            ...(singleData?.round_off_method ? {round_off_method: Number(singleData?.round_off_method)} : {})
        });
        if(!isDuplicate){
            setSelectedAdvancePayment(
                singleData?.advance_payment?.map((item) => {
                    return{
                        ...item,
                        received_amount: item?.adjusted_amount,
                        advance_payment_id: item?.id
                    }
                })
            );
        }
        setGstValue({
            igstValue: singleData?.igst,
            cgstValue: singleData?.cgst,
            sgstValue: singleData?.sgst,
        });
        setTcsRate({
            tcs_amount: singleData?.tcs_tax_id ? customToFixed(singleData?.tcs_amount, 2) : '',
            tcs_tax_id: singleData?.tcs_tax_id,
            tcs_rate: singleData?.tcs_tax_id ? singleData?.tcs_rate : '',
            tcs_calculated_on: singleData?.tcs_calculated?.calculated_on,
        });
        setPaymentLedgerDetail(prev => ({
            payment_detail: [
                {
                    pd_ledger_id: null,
                    pd_date:
                        singleData?.date_of_invoice && formattedDate(singleData?.date_of_invoice),
                    pd_amount: "",
                    pd_mode: null,
                    pd_reference_number: "",
                    is_show_invoice_date: true,
                },
            ],
            tds_amount:
                singleData?.tds_tax_id || singleData?.ledger_of_tds ? singleData?.tds_amount : "",
            tds_rate:
                singleData?.tds_tax_id || singleData?.ledger_of_tds ? singleData?.tds_rate : "",
            tds_tax_id: singleData?.tds_tax_id || singleData?.ledger_of_tds,
            rounding_type: singleData?.tds_rounding_method || 1
        }));
        setTaxableValue(singleData?.taxable_value);
        setGrandTotal(singleData?.gross_value);
        setCessValue(singleData?.cess);
        setMainGrandTotal(singleData?.grand_total - roundOffAmount);
        setFinalAmount(singleData?.grand_total);
        // setTimeout(() => {
        //     dispatch(getSaleById(""));
        // }, 1000);
    }
};
export const preparePurchaseOcrData = (
    singleData,
    {
        setStateFunctions,
        dispatch,
        tableHeaderList,
        accountingTableHeader,
        addLessChanges,
        additionalCharges,
        classificationOptions,
        isDuplicate
    },
) => {
    if (singleData) {
        const {
            ocrInvoiceDetail,
            setOcrInvoiceDetail,
            gstQuote,
            setGstQuote,
            setItemType,
            setClassification,
            setItems,
            setAccountingItems,
            setAddLessChanges,
            setAdditionalCharges,
            setPartyAddress,
            setGstCalculation,
            setBrokerDetail,
            setTransporterDetail,
            setEwayBillDetail,
            setOtherDetail,
            setGstValue,
            setTcsRate,
            setPaymentLedgerDetail,
            setGrandTotal,
            setMainGrandTotal,
            setChangeTax,
            setFinalAmount,
            invoiceDetail,
            setInvoiceDetail,
            setIsIGSTCalculation,
            setIsSGSTCalculation,
            setTaxableValue,
            setCessValue,
            setUpdatePurchaseNumberOptions,
            setSelectedAdvancePayment,
            setPurchaseInvoice,
            setShippingAddress,
            setSelectShippingAddress
        } = setStateFunctions;

        setGstValue({
            igstValue: singleData?.igst,
            cgstValue: singleData?.cgst,
            sgstValue: singleData?.sgst,
        });
        if(singleData?.party_id !== -1){
            setGstQuote({
                ...gstQuote,
                party_ledger_id: singleData?.party_id,
                gstin: singleData?.billing_to?.party_gst,
            })
        }
        setGstQuote({
            ...gstQuote,
            gstin: singleData?.billing_to?.party_gst && singleData?.billing_to?.party_gst.slice(0, 16),
        })

        setInvoiceDetail({
            ...invoiceDetail,
            invoice_number: singleData?.invoice_number,
            invoice_date: singleData?.invoice_date,
        })

        setPartyAddress({
                billingAddress: {
                    address_1: singleData?.billing_to?.address_1 || "",
                    address_2: singleData?.billing_to?.address_2 || "",
                    country_id: singleData?.billing_to?.country || "",
                    state_id: singleData?.billing_to?.state || "",
                    city_id: singleData?.billing_to?.city || "",
                    state_name: singleData?.billing_to?.state_name || "",
                    city_name: singleData?.billing_to?.city_name || "",
                    pin_code: singleData?.billing_to?.pincode || "",
                    shipping_gstin: singleData?.billing_to?.gstin,
                },
                shippingAddress: {
                    address_1: singleData?.shipping_to?.address_1 || "",
                    address_2: singleData?.shipping_to?.address_2 || "",
                    country_id: singleData?.shipping_to?.country || "",
                    state_id: singleData?.shipping_to?.state || "",
                    city_id: singleData?.shipping_to?.city || "",
                    state_name: singleData?.shipping_to?.state_name || "",
                    city_name: singleData?.shipping_to?.city_name || "",
                    pin_code: singleData?.shipping_to?.pincode || "",
                    shipping_name: singleData?.shipping_to?.party_name,
                    shipping_gstin: singleData?.shipping_to?.party_gst,
                },
            })

            setShippingAddress([{
                address_1: singleData?.shipping_to?.address_1 || "",
                address_2: singleData?.shipping_to?.address_2 || "",
                country_id: singleData?.shipping_to?.country || "",
                state_id: singleData?.shipping_to?.state || "",
                city_id: singleData?.shipping_to?.city || "",
                pin_code: singleData?.shipping_to?.pincode || "",
                shipping_name: singleData?.shipping_to?.party_name,
                shipping_gstin: singleData?.shipping_to?.gstin,
            }]);

            setSelectShippingAddress(0);

            setGstValue({
                igstValue: singleData?.bill_amount?.igst,
                cgstValue: singleData?.bill_amount?.cgst,
                sgstValue: singleData?.bill_amount?.sgst,
            });

        if(singleData?.bill_amount?.igst !== 0){
            setIsIGSTCalculation(true);
            setIsSGSTCalculation(false);
        }else{
            setIsSGSTCalculation(true);
            setIsIGSTCalculation(false);
        }

        setOcrInvoiceDetail({
            ...ocrInvoiceDetail,
            voucher_number: singleData?.voucher_number,
            voucher_date: singleData?.voucher_date && formattedDate(singleData?.voucher_date),
        });
        // if (singleData?.purchase_item_type == 2) {
            if(singleData?.items?.length > 0){
                setItems(
                    singleData?.items?.map((item, index) => {
                        // setChangeTax(item?.with_tax);
                        const multiQuantity = item?.consolidating_items_to_invoice?.split(",") || [];

                        if (multiQuantity[0] == "0") {
                            multiQuantity[0] = item?.quantity?.toFixed(item?.decimal_places || 2);
                        }

                        while (multiQuantity.length < 4) {
                            multiQuantity.push("0");
                        }

                        const unitOptions = unitOption(item?.unit_of_array);
                        const itemDetails = item?.item_model;
                        const itemUnit = item?.unit_of_array?.length > 0 && item?.unit_of_array?.find((unit, index) =>
                                    unit?.code == item?.uom
                                );
                        return {
                                id: itemDetails?.id,
                                selectedItem: item?.item_id === -1 ? item?.item_name : item?.item_id,
                                item_name: item?.item_name,
                                additional_description: item?.item_description,
                                mrp: itemDetails?.mrp || 0,
                                hsn_code: item?.hsn_code || item?.hsn,
                                multiQuantity: multiQuantity,
                                selectedLedger: itemDetails?.expense_ledger_id,
                                quantity: item?.qty?.toFixed(item?.decimal_places || 2),
                                free_quantity: item?.free_quantity,
                                rateWithGst: item?.rpu_with_gst,
                                updatedRateWithGst: item?.rate,
                                updatedRateWithoutGst: item?.rate,
                                gst_id: item?.tax_rate ? item?.tax_rate : itemDetails?.gst_tax_id,
                                gst: item?.tax_rate ? item?.tax_rate : itemDetails?.gst_tax_id,
                                cessRate: itemDetails?.gst_cess_rate || 0,
                                discountType: itemDetails?.discount_type || 1,
                                discountValue: itemDetails?.discount_value,
                                discountType_2: item?.discount_type_2 || 1,
                                discountValue_2: item?.discount_value_2 ? item?.discount_value_2 : 0,
                                total: item?.total,
                                with_tax: item?.with_tax === true ? 1 : 0,
                                selectedUnit: itemUnit?.id || "",
                                cgstValue: item?.classification_cgst_tax,
                                igstValue: item?.classification_igst_tax,
                                sgstValue: item?.classification_sgst_tax,
                                cessValue: (item?.cess_rate * item?.total) / 100,
                                cessRate: item?.cess_rate,
                                isShowDelete:
                                singleData?.items?.items?.length > 1 ? true : false,
                                itemUnitOption: unitOptions,
                                decimal_places: itemDetails?.decimal_places ?? 2,
                                decimal_places_for_rate: itemDetails?.decimal_places_for_rate ?? 2,
                                secondaryUnitOfMeasurement: itemDetails?.secondary_unit_of_measurement,
                                conversationRate: itemDetails?.conversion_rate,
                                custom_fields: customFieldOptimization(item?.custom_fields),
                                is_status: ITEM_STATUS.ACTIVE,
                            ...(item?.item_id === -1 && { notExistsItems: true }),
                            custom_field_inventory_store: formattedCustomField(item?.custom_items_inventory_values),
                            model_inventory_custom_fields: item?.model_inventory_custom_fields,
                            model_custom_fields: item?.model_inventory_custom_fields,
                            model_select_inventory_custom_fields: item?.custom_items_inventory_values,
                        };
                    })
                );
            }
        if(singleData?.ledgers?.length > 0){
            setAccountingItems(
                singleData?.ledgers?.map((ledger, index) => {
                    return {
                        id: index + 1,
                        selectedLedger: ledger?.ledger_id === -1 ? ledger?.ledger_name : ledger?.ledger_id,
                        additional_description: ledger?.ledger_description,
                        rateWithGst: ledger?.rpu_with_gst,
                        gst: ledger?.gst_tax_percentage,
                        gst_id: ledger?.gst_id,
                        discountType: ledger?.discount_type || 1,
                        discountValue: ledger?.discount,
                        discountType_2: ledger?.discount_type_2 || 1,
                        discountValue_2: ledger?.discount_value_2 ? ledger?.discount_value_2 : 0,
                        total: customToFixed(ledger?.total, 2),
                        cgstValue: ledger?.classification_cgst_tax,
                        igstValue: ledger?.classification_igst_tax,
                        sgstValue: ledger?.classification_sgst_tax,
                        cessValue: (ledger?.cess_rate * ledger?.total) / 100,
                        cessRate: ledger?.cess_rate,
                        with_tax: ledger?.with_tax ? 1 : 0,
                        rpu: ledger?.rpu_with_gst || 0,
                        updatedRateWithGst: ledger?.rate?.toFixed(2),
                        rateWithoutGst: ledger?.rate?.toFixed(2),
                        updatedRateWithoutGst: ledger?.rate?.toFixed(2),
                        amountWithoutGst: ledger?.rate,
                        is_status: ITEM_STATUS.ACTIVE,
                        ...(ledger?.ledger_id === -1 && { notExistsLedger: true }),
                    }})
            )
        }

        const totalSum = singleData?.items?.reduce((acc, item) => acc + item?.total, 0);

        setTaxableValue(singleData?.bill_amount?.taxable_value);
        setGrandTotal(totalSum || 0);
        setCessValue(singleData?.bill_amount?.cess);
        setMainGrandTotal(singleData?.bill_amount?.total_price - 0);
        setFinalAmount(singleData?.bill_amount?.total_price);
    }
};

export const convertToFormData = (data, formData = new FormData(), parentKey) => {
    if (data && typeof data === "object" && !(data instanceof Date) && !(data instanceof File)) {
        // Skip if the object is empty or all properties are null/empty/undefined
        Object.keys(data).forEach(key => {
            const value = data[key];
            const newKey = parentKey ? `${parentKey}[${key}]` : key;

            if (
                value === null ||
                value === undefined ||
                (typeof value === "string" && value.trim() === "") ||
                (Array.isArray(value) && value.length === 0) ||
                (typeof value === "object" && Object.keys(value).length === 0)
            ) {
                // Skip empty/null/undefined values
                return;
            }

            if (Array.isArray(value)) {
                value.forEach((item, index) => {
                    convertToFormData(item, formData, `${newKey}[${index}]`);
                });
            } else if (typeof value === "object") {
                convertToFormData(value, formData, newKey);
            } else {
                formData.append(newKey, value);
            }
        });
    } else if (data !== null && data !== undefined && data !== "") {
        // Append primitive values if they are not null/undefined/empty
        formData.append(parentKey, data);
    }
    return formData;
};

export const convertToFormLedgerData = (data, formData = new FormData(), parentKey) => {
    if (data && typeof data === "object" && !(data instanceof Date) && !(data instanceof File)) {
        // Skip if the object is empty or all properties are null/empty/undefined
        Object.keys(data).forEach(key => {
            const value = data[key];
            const newKey = parentKey ? `${parentKey}[${key}]` : key;

            if (
                (parentKey !== "shipping_address" && value === null) ||
                value === undefined ||
                (typeof value === "string" && value.trim() === "") ||
                (Array.isArray(value) && value.length === 0) ||
                (typeof value === "object" && Object.keys(value).length === 0)
            ) {
                // Skip empty/null/undefined values
                return;
            }

            if (Array.isArray(value)) {
                value.forEach((item, index) => {
                    convertToFormData(item, formData, `${newKey}[${index}]`);
                });
            } else if (typeof value === "object") {
                convertToFormData(value, formData, newKey);
            } else {
                formData.append(newKey, value);
            }
        });
    } else if (data !== null && data !== undefined && data !== "") {
        // Append primitive values if they are not null/undefined/empty
        formData.append(parentKey, data);
    }
    return formData;
};

export const unitOption = unitDetail => {
    return unitDetail && unitDetail?.length > 0
        ? unitDetail.map((unit) => {
              return {
                  label: unit?.full_name,
                  value: unit?.id,
              };
          })
        : [];
};

export const unitOptionWithKey = unitDetail => {
    return unitDetail
    ? Object.entries(unitDetail || {}).map(([key, value]) => {
          return {
              label: value,
              value: key,
          };
      })
    : [];
};

export const convertNameIntoCamelCase = name => {
    return name
        ?.toLowerCase()
        .replace(/\b\w/g, char => char.toUpperCase())
        .replace(/\b([a-z])(?=\.)/g, char => char.toUpperCase());
};

export const areAllProductsExemptOrNA = (items, gstField = "gst_id") => {
    if (!Array.isArray(items) || items.length === 0) {
        return { isNa: false, isExempt: false };
    }

    const selectedItems = items.filter(item =>
        item.selectedLedger
    );

    if (selectedItems.length === 0) {
        return { isNa: false, isExempt: false };
    }

    const isNa = selectedItems.every(item => item[gstField] === GST_TYPES_ID.GST_NA);
    const isExempt = selectedItems.every(item => item[gstField] === GST_TYPES_ID.GST_EXEMPT);

    return { isNa, isExempt };
};

export const checkAllAdditionalChargesNaAndExempt = additionalCharges => {
    if (!additionalCharges?.additional_detail?.length) {
        return { isAdditionalChargesNa: true, isAdditionalChargesExempt: true };
    }

    if (
        additionalCharges.additional_detail.length === 1 &&
        additionalCharges.additional_detail[0].ac_gst_rate_id.value === 0 &&
        additionalCharges.additional_detail[0].ac_gst_rate_id.rate === 0
    ) {
        return { isAdditionalChargesNa: true, isAdditionalChargesExempt: true };
    }

    let isAdditionalChargesNa = true;
    let isAdditionalChargesExempt = true;

    additionalCharges.additional_detail.forEach(item => {
        const gstValue = item?.ac_gst_rate_id?.value;

        // Check for NA
        if (item?.ac_ledger_id && gstValue != null && gstValue !== GST_TYPES_ID.GST_NA) {
            isAdditionalChargesNa = false;
        }

        // Check for Exempt
        if (item?.ac_ledger_id && gstValue != null && gstValue !== GST_TYPES_ID.GST_EXEMPT) {
            isAdditionalChargesExempt = false;
        }
    });

    return { isAdditionalChargesNa, isAdditionalChargesExempt };
};

export const calculateGSTFlags = (
    company,
    isNa,
    isExempt,
    isAdditionalChargesNa,
    isAdditionalChargesExempt
) => {
    // If the company is not GST applicable, set defaults
    if (!company?.company?.is_gst_applicable) {
        return {
            is_cgst_sgst_igst_calculated: 0,
            is_gst_na: 1,
        };
    }

    // Case 1: If both items and additional charges are NA
    if (isNa && isAdditionalChargesNa) {
        return {
            is_gst_na: 1,
            is_cgst_sgst_igst_calculated: 0,
        };
    }

    // Case 2: If both items and additional charges are Exempt
    if (isExempt && isAdditionalChargesExempt) {
        return {
            is_gst_na: 0,
            is_cgst_sgst_igst_calculated: 0,
        };
    }

    // Case 3: For all other cases
    return {
        is_gst_na: 0,
        is_cgst_sgst_igst_calculated: 1,
    };
};

export const prepareDeleteTransactions = data => {
    const transactions = [];

    if (data?.deliveryChallanTransactions?.length > 0) {
        data.deliveryChallanTransactions.forEach(item => {
            transactions.push({
                type: ADVANCE_PAYMENT_TRANSACTION_TYPE.DELIVERY_CHALLAN,
                date: item?.challan_date,
                voucher_number: item?.challan_number,
                id: item?.id,
            });
        });
    }

    if (data?.saleReturnTransactions?.length > 0) {
        data.saleReturnTransactions.forEach(item => {
            transactions.push({
                type: ADVANCE_PAYMENT_TRANSACTION_TYPE.SALE_RETURN,
                date: item?.date,
                voucher_number: item?.full_invoice_number,
                amount: item?.total,
                id: item?.id,
            });
        });
    }

    if (data?.incomeDebitNoteTransactions?.length > 0) {
        data.incomeDebitNoteTransactions.forEach(item => {
            transactions.push({
                type: ADVANCE_PAYMENT_TRANSACTION_TYPE.INCOME_DEBIT_NOTE,
                date: item?.date,
                voucher_number: item?.full_invoice_number,
                amount: item?.total,
                id: item?.id,
            });
        });
    }
    if (data?.saleTransactions?.length > 0) {
        data.saleTransactions.forEach(item => {
            transactions.push({
                type: ADVANCE_PAYMENT_TRANSACTION_TYPE.SALE,
                date: item?.date,
                voucher_number: item?.full_invoice_number,
                amount: item?.total,
                id: item?.id,
            });
        });
    }
    if (data?.purchaseReturnTransactions?.length > 0) {
        data.purchaseReturnTransactions.forEach(item => {
            transactions.push({
                type: ADVANCE_PAYMENT_TRANSACTION_TYPE.PURCHASE_RETURN,
                date: item?.date,
                voucher_number: item?.voucher_number,
                amount: item?.total,
                id: item?.id,
            });
        });
    }
    if (data?.expenseCreditNoteTransactions?.length > 0) {
        data.expenseCreditNoteTransactions.forEach(item => {
            transactions.push({
                type: ADVANCE_PAYMENT_TRANSACTION_TYPE.EXPENSE_CREDIT_NOTE,
                date: item?.date,
                voucher_number: item?.voucher_number,
                amount: item?.total,
                id: item?.id,
            });
        });
    }
    if (data?.purchaseTransactions?.length > 0) {
        data.purchaseTransactions.forEach(item => {
            transactions.push({
                type: ADVANCE_PAYMENT_TRANSACTION_TYPE.PURCHASE,
                date: item?.date,
                voucher_number: item?.voucher_number,
                amount: item?.total,
                id: item?.id,
            });
        });
    }
    if (data?.receiptTransactions?.length > 0) {
        data.receiptTransactions.forEach(item => {
            transactions.push({
                type: ADVANCE_PAYMENT_TRANSACTION_TYPE.RECEIPT,
                date: item?.date,
                voucher_number: item?.voucher_number,
                amount: item?.total,
                id: item?.id,
            });
        });
    }
    if (data?.paymentTransactions?.length > 0) {
        data.paymentTransactions.forEach(item => {
            transactions.push({
                type: ADVANCE_PAYMENT_TRANSACTION_TYPE.PAYMENT,
                date: item?.date,
                voucher_number: item?.payment_voucher_number,
                amount: item?.total_paid_amount,
                id: item?.id,
            });
        });
    }
    if (data?.journalTransactionsDebit?.length > 0) {
        data.journalTransactionsDebit.forEach(item => {
            transactions.push({
                type: ADVANCE_PAYMENT_TRANSACTION_TYPE.JOURNAL,
                date: item?.date,
                voucher_number: item?.voucher_number,
                amount: item?.total,
                id: item?.id,
            });
        });
    }
    if (data?.journalTransactionsCredit?.length > 0) {
        data.journalTransactionsCredit.forEach(item => {
            transactions.push({
                type: ADVANCE_PAYMENT_TRANSACTION_TYPE.JOURNAL,
                date: item?.date,
                voucher_number: item?.voucher_number,
                amount: item?.total,
                id: item?.id,
            });
        });
    }
    return transactions;
};

export const prepareTransactionsBills = dataArray => {
    const transactions = [];

    if (!Array.isArray(dataArray)) {
        return transactions;
    }

    dataArray.forEach(receivedBills => {
        if (Array.isArray(receivedBills) && receivedBills.length > 0) {
            receivedBills.forEach(item => {
                transactions.push({
                    transaction_type: item.transaction_type,
                    id: item.transaction_id,
                    document_number: item.document_number,
                    adjusted_amount: item.adjusted_amount,
                    date: item.document_date,
                    total_amount: item.total_amount,
                    pending_amount: item.pending_amount,
                    received_amount: '',
                    type: item.type,
                });
            });
        }
    });

    return transactions;
};

export const prepareSaveAndNewData = (newData, setGstQuote, setPartyAddress, additionalCharges, setAdditionalCharges, setItemType, saveAndNewPartyDetail) => {
    if (!newData) return;

    setGstQuote({
        gstin: newData?.gstin,
        party_ledger_id: newData?.party_ledger_id || newData?.supplier_id || newData?.customer_ledger_id || newData?.supplier_ledger_id,
        mobile: {
            region_iso: newData?.region_iso,
            region_code: newData?.region_code,
            party_phone_number: newData?.party_phone_number,
            phone_input: (("+" + newData?.region_code) || "+91") + newData?.party_phone_number,
        },
    });

    setAdditionalCharges({
        ...additionalCharges,
        note: newData?.narration,
        terms_and_conditions: newData?.term_and_condition,
    });

    setItemType(newData?.sales_item_type === 2 || newData?.expense_cn_item_type === 2 || newData?.dn_item_type === 2 || newData?.cn_item_type === 2 || newData?.invoice_type === 2 || newData?.purchase_item_type === 2 || newData?.order_type === 2 || newData?.pr_item_type === 2 || newData?.sale_return_item_type === 2  ? "item" : "accounting")

    const timeoutId = setTimeout(() => {
        localStorage.removeItem("saveAndNewData");
    }, 2000);

    return () => {
        clearTimeout(timeoutId);
    };
};

export const prepareRecurringMasterData = ({
    partyDetails,
    dispatch,
    recurringEditData,
    setRecurringInvoiceDetails,
    setBrokerDetail,
    setTransporterDetail,
    setOtherDetail,
    setItems,
    setChangeTax,
    setAccountingItems,
    setItemType,
    setAddLessChanges,
    setAdditionalCharges,
    setGstCalculation,
    setGstValue,
    setTcsRate,
    setGrandTotal,
    setMainGrandTotal,
    setFinalAmount,
    gstCalculation,
    addLessChanges,
    additionalCharges,
    recurringEdit,
    setIsIGSTCalculation,
    setIsSGSTCalculation,
    setClassification,
    classificationOptions,
    setCessValue,
    setTaxableValue,
    paymentLedgerDetail,
    setPaymentLedgerDetail,
    setCustomHeaderListTransaction
}) => {

    const party_groups = recurringEditData?.party_groups?.length > 0 ? partyDetails.party_groups?.filter(party =>
        recurringEditData?.party_groups.some(groupId => party.value == groupId)
    ) : [];

    const parties = recurringEditData?.partis?.length > 0 ? partyDetails.parties?.filter(party =>
        recurringEditData?.partis.some(partiesId => party.value == partiesId)
    ) : [];

    setRecurringInvoiceDetails({
        template_name: recurringEditData?.template_name,
        recurrence_on: recurringEditData?.recurrence_on === 0 ? "approvalBasis" : "autoApprove",
        start_date: recurringEditData?.start_date ? formattedDate(recurringEditData?.start_date) : "",
        end_on: recurringEditData?.end_on === 1 ? "date" : recurringEditData?.end_on === 2 ? "occurrences" : "never",
        end_date: recurringEditData?.end_date ? formattedDate(recurringEditData?.end_date) : "",
        occurrence: recurringEditData?.occurrence,
        party_groups: party_groups,
        parties: parties,
        frequency_details: {
            repeat_frequency: recurringEditData?.frequency_details?.repeat_frequency === 10 ? "Custom" : recurringEditData?.frequency_details?.repeat_frequency,
            repeat_on: recurringEditData?.frequency_details?.repeat_on,
            on_day: recurringEditData?.frequency_details?.on_day,
            custom_type: recurringEditData?.frequency_details?.custom_type || 1,
            custom_value: recurringEditData?.frequency_details?.custom_value,
        }
    });
    setBrokerDetail({
        broker_id: recurringEditData?.broker_id,
        broker_percentage: recurringEditData?.brokerage_for_sale,
        brokerage_on_value: recurringEditData?.brokerage_on_value_type,
    });
    setTransporterDetail({
        transport_id: recurringEditData?.transport_id,
        transporter_document_number: recurringEditData?.transporter_document_number,
        transporter_document_date: recurringEditData?.transporter_document_date ? formattedDate(recurringEditData?.transporter_document_date) : "",
        transporter_vehicle_number: recurringEditData?.transporter_vehicle_number,
    });

    setOtherDetail({
        po_number: recurringEditData?.po_no,
        date: recurringEditData?.po_date ? formattedDate(recurringEditData?.po_date) : null,
        creditPeriod: recurringEditData?.credit_period,
        creditPeriodType: recurringEditData?.credit_period_type || 1,
    });
    setCustomHeaderListTransaction(recurringEditData?.custom_values)
    if (recurringEditData?.recurring_invoice_type == 2) {
        setItems(
            recurringEditData?.recurring_invoice_items?.map((item, index) => {
                setChangeTax(item?.with_tax);
                const multiQuantity =
                    item?.consolidating_items_to_invoice?.split(",") || [];

                if (multiQuantity[0] == "0") {
                    multiQuantity[0] = item?.quantity?.toFixed(item?.decimal_places || 2);
                }

                while (multiQuantity.length < 4) {
                    multiQuantity.push("0");
                }

                const unitOptions = item?.units_of_array
                    ? item?.units_of_array.map((unit) => {
                        return {
                            label: unit?.full_name,
                            value: unit?.id,
                        };
                    })
                    : [];
                return {
                    id: item?.id,
                    selectedItem: item?.item_id,
                    additional_description: item?.additional_description,
                    mrp: item?.mrp,
                    hsn_code: item?.hsn_code,
                    multiQuantity: multiQuantity,
                    selectedLedger: item?.ledger_id,
                    quantity: item?.quantity?.toFixed(item?.decimal_places || 2),
                    free_quantity: item?.free_quantity,
                    rateWithGst: item?.rpu_with_gst?.toFixed(item?.decimal_places || 2),
                    updatedRateWithGst: item?.with_tax
                        ? item?.rpu_with_gst
                        : item?.rpu_without_gst?.toFixed(item?.decimal_places || 2),
                    rateWithoutGst: item?.with_tax
                        ? item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                        : item?.rpu_without_gst?.toFixed(
                            item?.decimal_places_for_rate || 2
                        ),
                    updatedRateWithoutGst: item?.with_tax
                        ? item?.rpu_with_gst?.toFixed(item?.decimal_places_for_rate || 2)
                        : item?.rpu_without_gst?.toFixed(
                            item?.decimal_places_for_rate || 2
                        ),
                    gst_id: item?.gst_id,
                    gst: item?.gst_tax_percentage,
                    discountType: item?.discount_type || 1,
                    discountValue: item?.discount_value,
                    discountType_2: item?.discount_type_2 || 1,
                    discountValue_2: item?.discount_value_2 ? item?.discount_value_2 : 0,
                    total: customToFixed(item?.total, 2),
                    with_tax: item?.with_tax ? 1 : 0,
                    selectedUnit: item?.unit_id,
                    itemUnitOption: unitOptions,
                    cgstValue: item?.classification_cgst_tax,
                    igstValue: item?.classification_igst_tax,
                    sgstValue: item?.classification_sgst_tax,
                    cessValue: (item?.cess_rate * item?.total) / 100,
                    cessRate: item?.cess_rate,
                    isShowDelete: recurringEditData?.recurring_invoice_items?.length > 1 ? true : false,
                    decimal_places: item?.decimal_places ?? 2,
                    decimal_places_for_rate: item?.decimal_places_for_rate ?? 2,
                    secondaryUnitOfMeasurement: item?.secondary_unit_of_measurement,
                    conversationRate: item?.conversion_rate,
                    isAdditionalDescriptionUpdate: item?.is_additional_description_update,
                    custom_fields: customFieldOptimization(item?.custom_fields),
                    custom_field_inventory_store: formattedCustomField(item?.custom_items_inventory_values),
                    model_inventory_custom_fields: item?.model_inventory_custom_fields,
                            model_custom_fields: item?.model_inventory_custom_fields,
                    model_select_inventory_custom_fields: item?.custom_items_inventory_values,
                    is_status: ITEM_STATUS.ACTIVE,
                };
            })
        );
        clearAccountingInvoice(setAccountingItems);
    } else if (recurringEditData?.recurring_invoice_type == 1) {
        setAccountingItems(
            recurringEditData?.recurring_invoice_ledgers?.map((item, index) => {
                setChangeTax(item?.with_tax);
                return {
                    id: index + 1,
                    selectedLedger: item?.ledger_id,
                    additional_description: item?.additional_description,
                    rateWithGst: parseFloat(item?.rpu_with_gst),
                    updatedRateWithGst: item?.with_tax
                        ? parseFloat(item?.rpu_with_gst)
                        : parseFloat(item?.rpu_without_gst?.toFixed(2)),
                    rateWithoutGst: item?.with_tax
                        ? parseFloat(
                            item?.rpu_with_gst?.toFixed(
                                item?.decimal_places_for_rate || 2
                            )
                        )
                        : parseFloat(
                            item?.rpu_without_gst?.toFixed(
                                item?.decimal_places_for_rate || 2
                            )
                        ),
                    updatedRateWithoutGst: item?.with_tax
                        ? parseFloat(
                            item?.rpu_with_gst?.toFixed(
                                item?.decimal_places_for_rate || 2
                            )
                        )
                        : parseFloat(
                            item?.rpu_without_gst?.toFixed(
                                item?.decimal_places_for_rate || 2
                            )
                        ),
                    amountWithoutGst: item?.with_tax
                        ? item?.rpu_with_gst
                        : item?.rpu_without_gst,
                    updatedAmountWithoutGst: item?.rpu_without_gst,
                    gst: item?.gst_tax_percentage,
                    gst_id: item?.gst_id,
                    discountType: item?.discount_type,
                    discountValue: item?.discount_value,
                    discountType_2: item?.discount_type_2,
                    discountValue_2: item?.discount_value_2 ? item?.discount_value_2 : 0,
                    total: item?.total,
                    cgstValue: item?.classification_cgst_tax,
                    igstValue: item?.classification_igst_tax,
                    sgstValue: item?.classification_sgst_tax,
                    cessValue: (item?.cess_rate * item?.total) / 100,
                    cessRate: item?.cess_rate,
                    with_tax: item?.with_tax ? 1 : 0,
                    rpu: item?.rpu_with_gst || 0,
                    isShowDelete:
                        recurringEditData?.recurring_invoice_ledgers?.length > 1 ? true : false,
                    decimal_places: item?.decimal_places ?? 2,
                    decimal_places_for_rate: item?.decimal_places_for_rate ?? 2,
                    secondaryUnitOfMeasurement: item?.secondary_unit_of_measurement,
                    conversationRate: item?.conversion_rate,
                    isAdditionalDescriptionUpdate: item?.is_additional_description_update,
                    custom_fields: customFieldOptimization(item?.custom_ledgers_values),
                    is_status: ITEM_STATUS.ACTIVE,
                };
            })
        );
        clearItemInvoice(setItems);
    }

    if (recurringEditData?.recurring_invoice_type == 2) {
        dispatch(rearrangeItemList( TRANSACTION_TYPE.RECURRING, 1));
    } else {
        dispatch(rearrangeItemList(TRANSACTION_TYPE.RECURRING, 2));
    }

    setItemType(recurringEditData?.recurring_invoice_type == 2 || recurringEditData?.sales_item_type == 2 ? "item" : "accounting");

    const nature_type =
        recurringEditData?.recurring_invoice_type == 2
            ? recurringEditData?.recurring_invoice_items &&
            recurringEditData?.recurring_invoice_items[0]?.classification_nature_type
            : recurringEditData?.recurring_invoice_ledgers &&
            recurringEditData?.recurring_invoice_ledgers[0]?.classification_nature_type;
    const nature_name = classificationOptions.find(item => item.value == nature_type);

    setClassification({
        classification_nature: nature_type,
        classification_nature_name: nature_name?.label,
        rcm_applicable:
            recurringEditData?.recurring_invoice_type == 2
                ? recurringEditData?.recurring_invoice_items &&
                recurringEditData?.recurring_invoice_items[0]?.classification_is_rcm_applicable == 1
                    ? true
                    : false
                : recurringEditData?.recurring_invoice_ledgers &&
                recurringEditData?.recurring_invoice_ledgers[0]?.classification_is_rcm_applicable == 1
                ? true
                : false,
    });
    setGSTCalculationType(
        {
            classification_nature_name: nature_name?.label,
        },
        setIsIGSTCalculation,
        setIsSGSTCalculation
    );
    setAddLessChanges(
        recurringEditData?.add_less?.length == 0
            ? addLessChanges
            : recurringEditData?.add_less?.map(item => {
                return {
                    al_ledger_id: item.ledger_id,
                    al_is_show_in_print: item.is_show_in_print,
                    al_type: item.type,
                    al_value: item.type == 2 ? item.value : item.total,
                    al_total: item.total,
                    is_status: ITEM_STATUS.ACTIVE,
                };
            })
    );

    setAdditionalCharges({
        ...additionalCharges,
        ...(recurringEdit && {
            upload_document: recurringEditData?.media?.map(media => {
                return { original_url: media?.original_url, id: media?.id };
            })
        }),
        note: recurringEditData?.narration?.body,
        terms_and_conditions: recurringEditData?.term_and_condition,
        additional_detail:
            !recurringEditData?.additional_charges || recurringEditData?.additional_charges?.length == 0
                ? [
                    {
                        ac_ledger_id: null,
                        ac_type: 1,
                        ac_value: "",
                        ac_gst_rate_id: {
                            label: "",
                            value: 0,
                            rate: 0,
                        },
                        ac_total: 0,
                        is_status: ITEM_STATUS.IN_ACTIVE,
                    },
                ]
                : recurringEditData?.additional_charges?.map(charge => {
                    return {
                        ac_ledger_id: charge?.ledger_id,
                        ac_type: charge?.charge_type,
                        ac_value: charge?.value,
                        ac_gst_rate_id: {
                            value: charge?.gst_rate_id,
                            rate: Number(charge?.gst_percentage),
                            label: `${charge?.gst_percentage}%`,
                        },
                        ac_total: customToFixed(charge?.total_without_tax, 2),
                        ac_total_without_tax: customToFixed(charge?.total_without_tax, 2),
                        is_status: ITEM_STATUS.ACTIVE,
                    };
                }),
    });

    const roundOffAmount = customToFixed(recurringEditData?.rounding_amount, 2);
    setGstCalculation({
        ...gstCalculation,
        round_of_amount: roundOffAmount,
        is_round_off_not_changed: recurringEditData?.is_round_off_not_changed,
        ...(recurringEditData?.round_off_method ? {round_off_method: Number(recurringEditData?.round_off_method)} : {})
    });

    setGstValue({
        igstValue: recurringEditData?.igst,
        cgstValue: recurringEditData?.cgst,
        sgstValue: recurringEditData?.sgst,
    });
    setTcsRate({
        tcs_amount: recurringEditData?.tcs_tax_id ? customToFixed(recurringEditData?.tcs_amount, 2) : "",
        tcs_tax_id: recurringEditData?.tcs_tax_id,
        tcs_rate: recurringEditData?.tcs_tax_id ? recurringEditData?.tcs_rate : "",
        tcs_calculated_on: recurringEditData?.tcs_calculated?.calculated_on,
    });
    setPaymentLedgerDetail({
        ...paymentLedgerDetail,
        tds_amount: recurringEditData?.tds_tax_id ? recurringEditData?.tds_amount : "",
        tds_rate: recurringEditData?.tds_tax_id ? recurringEditData?.tds_rate : "",
        tds_tax_id: recurringEditData?.tds_tax_id,
        rounding_type: recurringEditData?.tds_rounding_method || 1
    });
    setCessValue(recurringEditData?.cess);
    setGrandTotal(recurringEditData?.gross_value);
    setMainGrandTotal(recurringEditData?.grand_total - roundOffAmount);
    setFinalAmount(recurringEditData?.grand_total);
    setTaxableValue(recurringEditData?.taxable_value);
}
