import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import axiosApi from "../../config/apiConfig";
import { apiBaseURL, LedgerType, toastType, transactionTypeMap } from "../../constants";
import { addToast, errorToast } from "../actions/toastAction";
import {
    fetchLedgerGroupList,
    getLedgerGroupDetail,
    getLedgerModelDetail,
    getTcsTdsGroupList,
} from "../ledger/ledgerSlice";
import { fetchGstRateList, fetchTcsList } from "../rate/rateSlice";
import { unitOption } from "../../shared/prepareData";
import { calculateTotal, getCalculatedQuantity, updateCustomFieldCalculation } from "../../shared/calculation";
import { checkPathName } from "../../shared/sharedFunction";

const initialState = {
    item: [],
    getItemById: [],
    itemModelDetail: [],
    unit: [],
    getSingleItemById: {},
    getIncomeExpense: {},
    itemGroupList: {},
    status: "",
    cessRateData: [],
    itemConfigurationData: {},
};

export const fetchItemList = createAsyncThunk("item/fetchItemList", async data => {
    const response = await axiosApi.post(apiBaseURL.ITEM_LIST, data, {
        headers: {
            "Content-Type": "application/json",
        },
    });
    return response.data.data;
});
export const fetchPartyIdBaseItemList = createAsyncThunk("item/fetchItemListPartyIdBase", async (id) => {
    const data = id ? { party_id: id } : {};
    const response = await axiosApi.post(apiBaseURL.ITEM_LIST, data, {
        headers: {
            "Content-Type": "application/json",
        },
    });
    return response.data.data;
});
export const fetchItemUomList = createAsyncThunk("item/fetchItemUomList", async () => {
    const response = await axiosApi.get(apiBaseURL.UNIT_LIST, {
        headers: {
            "Content-Type": "application/json",
        },
    });
    return response.data.data;
});

export const fetchItemGroupList = createAsyncThunk("item/fetchItemGroupList", async () => {
    const response = await axiosApi.get(apiBaseURL.ITEM_GROUP_LIST, {
        headers: {
            "Content-Type": "application/json",
        },
    });
    return response.data.data;
});

export const itemSlice = createSlice({
    name: "Item",
    initialState,
    reducers: {
        getItemById: (state, action) => {
            state.getLedgerById = action.payload;
        },
        itemModelDetail: (state, action) => {
            state.itemModelDetail = action.payload;
        },
        getSingleItemById: (state, action) => {
            state.getSingleItemById = action.payload;
        },
        getIncomeExpense: (state, action) => {
            state.getIncomeExpense = action.payload;
        },
        status: (state, action) => {
            state.status = action.payload;
        },
        cessRateData: (state, action) => {
            state.cessRateData = action.payload;
        },
        itemConfigurationData: (state, action) => {
            state.itemConfigurationData = action.payload;
        },
    },
    extraReducers: builder => {
        builder.addCase(fetchItemList.fulfilled, (state, action) => {
            const combinedItems = [...state.item, ...action.payload];

            const uniqueItems = Array.from(
                new Map(combinedItems.map(item => [item.id, item])).values()
            );

            state.item = uniqueItems;
        });
        builder.addCase(fetchPartyIdBaseItemList.fulfilled, (state, action) => {
            const combinedItems = [...state.item, ...action.payload];

            const uniqueItems = Array.from(
                new Map(combinedItems.map(item => [item.id, item])).values()
            );

            state.item = uniqueItems;
        });
        builder.addCase(fetchItemUomList.fulfilled, (state, action) => {
            state.unit = action.payload;
        });
        builder.addCase(fetchItemGroupList.fulfilled, (state, action) => {
            state.itemGroupList = action.payload;
        });
    },
});

export const {
    getLedgerById,
    itemModelDetail,
    getItemById,
    getSingleItemById,
    getIncomeExpense,
    item,
    status,
    cessRateData,
    itemConfigurationData
} = itemSlice.actions;

export const addItem =
    (
        formdata,
        close,
        items,
        setItems,
        index,
        setIndex,
        gstQuote,
        isPurchase,
        openClassificationModel,
        setIsManageCustomItemMaster,
        setIsManageMultiQtyCustomItemMaster,
        navigate,
        changeTax,
        isChangeGst,
        setIsEditCalculation,
        setIsDisable,
        isNotExistItems,
        setNotExistItems,
        company,
        setIsChangeType,
        isInWord
    ) =>
    async dispatch => {
        try {
            const response = await axiosApi.post(apiBaseURL.ITEM, formdata, {
                withCredentials: true,
            });
            const custom_fields_with_single_qty = response?.data?.data?.custom_fields_value?.filter(item => item.custom_field_type == 1);
            const custom_fields_with_multy_qty = response?.data?.data?.custom_fields_value?.filter(item => item.custom_field_type == 2);
            const getCustomFieldTransactionType = Object.keys(transactionTypeMap).find(path =>
                  checkPathName(path)
            ) ? transactionTypeMap[Object.keys(transactionTypeMap).find(path => checkPathName(path))] : "";
            dispatch(
                addToast({
                    text: response?.data?.message,
                    type: toastType.ADD_TOAST,
                })
            );
            if (navigate == "save") {
                window.location.href = "/company/item-masters";
            } else if (navigate == "saveAndNew") {
                window.location.reload();
            }
            if (!navigate) {
                dispatch(fetchItemList({ ids: [response.data.data.id] }));
                dispatch(
                    fetchPriceListOfItems([response.data.data.id], gstQuote?.party_ledger_id, null, null, getCustomFieldTransactionType)
                );
            }

            if (isNotExistItems) {
                if (setItems && index >= 0) {
                    const unitOptionDetail = unitOption(response.data.data.units_of_array);
                    let item = [...items];
                    item = item.map((it) =>
                        it.item_name === response.data.data.item_name
                            ? {
                                  ...it,
                                  notExistsItems: false,
                                  selectedItem: response.data.data.id,
                                  item_name: response.data.data.item_name,
                                  selectedLedger: response.data.data.model.expense_ledger_id ?? "",
                                  itemUnitOption: unitOptionDetail,
                                  selectedUnit: response.data.data?.model?.unit_of_measurement ?? "",
                              }
                            : it
                    );
                    if (setItems) {
                        setItems(item);
                    }
                    if (setIsChangeType) {
                        setIsChangeType(true);
                    }
                    if (setIndex) {
                        setIndex(0);
                    }
                    if(custom_fields_with_single_qty?.length > 0 && setIsManageCustomItemMaster && isInWord){
                        setIsManageCustomItemMaster(true);
                    }else if(custom_fields_with_multy_qty?.length > 0 && setIsManageMultiQtyCustomItemMaster && isInWord){
                        setIsManageMultiQtyCustomItemMaster(true);
                    }
                    const check_custom_field_validation = isInWord ? !custom_fields_with_multy_qty?.length > 0 && !custom_fields_with_single_qty?.length > 0 : true;
                    if (openClassificationModel && company?.company?.is_gst_applicable && check_custom_field_validation) {
                        openClassificationModel();
                    }
                    if (openClassificationModel && isChangeGst && check_custom_field_validation) {
                        openClassificationModel();
                    }
                    if(close){
                        close(response.data.data.id);
                    }
                    return response.data.data
                }
                setNotExistItems("");
            } else {
                if (setItems && index >= 0) {
                let item = [...items];
                const mergedCustomFields = updateCustomFieldCalculation(response?.data?.data?.custom_fields_value)

                const quantityFromCustomField = getCalculatedQuantity(mergedCustomFields);
                const finalQuantity =
                    quantityFromCustomField != null
                    ? parseFloat(quantityFromCustomField || 1)?.toFixed(
                    response.data.data.model.decimal_places_for_rate ?? 2
                    )
                    : parseFloat(items[index]?.quantity || 1).toFixed(
                    response.data.data.model.decimal_places_for_rate ?? 2
                );
                const unitOptionDetail = unitOption(response.data.data.units_of_array);
                item[index].notExistsItems = false;
                item[index].selectedItem = response.data.data.id;
                item[index].item_name = response.data.data.item_name;
                item[index].hsn_code = response.data.data.model.hsn_sac_code;
                item[index].quantity = (parseFloat(finalQuantity) || 1).toFixed(response.data.data.model.decimal_places ?? 2);
                item[index].additional_description = response.data.data.model.description ?? "";
                item[index].updatedRateWithGst = isPurchase
                    ? parseFloat(response.data.data.model.purchase_price_with_gst || 0)?.toFixed(response.data.data.model?.decimal_places_for_rate ?? 2) ?? 0
                    : parseFloat(response.data.data.model.selling_price_with_gst || 0)?.toFixed(response.data.data.model?.decimal_places_for_rate ?? 2) ?? 0;
                item[index].updatedRateWithoutGst = isPurchase
                    ? changeTax ? parseFloat(response.data.data.model.purchase_price_with_gst || 0)?.toFixed(response.data.data.model?.decimal_places_for_rate ?? 2) ?? 0 : parseFloat(response.data.data.model.purchase_price_without_gst || 0)?.toFixed(response.data.data.model?.decimal_places_for_rate ?? 2) ?? 0
                    : changeTax ? parseFloat(response.data.data.model.selling_price_with_gst || 0)?.toFixed(response.data.data.model?.decimal_places_for_rate ?? 2) ?? 0 : parseFloat(response.data.data.model.selling_price_without_gst || 0)?.toFixed(response.data.data.model?.decimal_places_for_rate ?? 2) ?? 0;
                item[index].rateWithGst = isPurchase
                    ? parseFloat(response.data.data.model.purchase_price_with_gst || 0)?.toFixed(response.data.data.model?.decimal_places_for_rate ?? 2) ?? 0
                    : parseFloat(response.data.data.model.selling_price_with_gst || 0)?.toFixed(response.data.data.model?.decimal_places_for_rate ?? 2) ?? 0;
                item[index].rateWithoutGst = isPurchase
                    ? parseFloat(response.data.data.model.purchase_price_without_gst || 0)?.toFixed(response.data.data.model?.decimal_places_for_rate ?? 2) ?? 0
                    : parseFloat(response.data.data.model.selling_price_without_gst || 0)?.toFixed(response.data.data.model?.decimal_places_for_rate ?? 2) ?? 0;
                item[index].conversationRate = response.data.data.model.conversion_rate ?? "";
                item[index].decimal_places = response.data.data.model.decimal_places ?? 2;
                item[index].decimal_places_for_rate =
                    response.data.data.model.decimal_places_for_rate ?? 2;
                item[index].gst_id = response.data.data.model.gst_tax_id ?? 0;
                item[index].gst = response.data.data.model.gst_tax_rate ?? 0;
                item[index].cessRate = response.data.data.model.cess_rate ?? 0;
                item[index].discountType = isPurchase
                    ? response.data.data.model.purchase_discount_type ?? 1
                    : response.data.data.model.discount_type ?? 1;
                item[index].discountValue = isPurchase
                    ? response.data.data.model.purchase_discount_value ?? 0
                    : response.data.data.model.discount_value ?? 0;
                item[index].selectedLedger = isPurchase
                    ? response.data.data.model.expense_ledger_id
                    : response.data.data.model.income_ledger_id ?? "";
                item[index].secondaryUnitOfMeasurement =
                    response.data.data.model.secondary_unit_of_measurement ?? "";
                item[index].mrp = response.data.data.model.mrp ?? 0;
                (item[index].selectedUnit = response.data.data.model?.unit_of_measurement ?? null),
                    (item[index].itemUnitOption = unitOptionDetail);
                item[index].total = isPurchase
                    ? response.data.data.model.purchase_price_without_gst || 0
                    : (response.data.data.model.selling_price_without_gst || 0) ?? 0;
                item[index].itemType = response.data.data.item_type ?? 0;
                item[index].custom_fields = mergedCustomFields;
                item[index].model_custom_fields = response.data.data.model_inventory_custom_fields;
                item[index].custom_field_inventory_store = [];
                item[index].model_select_inventory_custom_fields = [];
                item[index].model_inventory_custom_fields = response.data.data?.model_inventory_custom_fields;

                const calculatedTotal = calculateTotal(item[index], false, changeTax, false);

                item[index].total = calculatedTotal.total;
                item[index].updatedTotal = calculatedTotal.total;
                item[index].sgstValue = calculatedTotal.sgst;
                item[index].cgstValue = calculatedTotal.sgst;
                item[index].cessValue = calculatedTotal.cess;
                if (setItems) {
                    setItems(item);
                }
                if(setIsEditCalculation){
                    setIsEditCalculation(true);
                }
                if (setIndex) {
                    setIndex(0);
                }
                if(custom_fields_with_single_qty?.length > 0 && setIsManageCustomItemMaster && isInWord){
                        setIsManageCustomItemMaster(true);
                }else if(custom_fields_with_multy_qty?.length > 0 && setIsManageMultiQtyCustomItemMaster && isInWord){
                        setIsManageMultiQtyCustomItemMaster(true);
                }
                const check_custom_field_validation = isInWord ? !custom_fields_with_multy_qty?.length > 0 && !custom_fields_with_single_qty?.length > 0 : true;
                if (openClassificationModel && isChangeGst && check_custom_field_validation) {
                    openClassificationModel();
                }
                }
            }
            if (setIsDisable) {
                setIsDisable(false);
            };
            if(close){
                close(response.data.data.id);
            }
        } catch ({ response }) {
            if (setIsDisable) {
                setIsDisable(false);
            };
            dispatch(
                errorToast({
                    text: response?.data?.message,
                    type: toastType.ERROR,
                })
            );
        } finally {
            if (setIsDisable) {
                setIsDisable(false);
            };
        }
    };
export const fetchItemById = (id, setLoader) => async dispatch => {
    try {
        if (!id) return;

        if (setLoader) {
            setLoader(true);
        };
        const response = await axiosApi.get(apiBaseURL.ITEM + "/" + id, {
            withCredentials: true,
        });
        dispatch(getItemById(response.data.data));
        if(setLoader){
            setLoader(false);
        }
    } catch (error) {

        if (error?.response?.data?.status) {
            return dispatch(status(error?.response?.data?.status));
        }
        if(setLoader){
            setLoader(false);
        }
        throw new Error(error);
    } finally {
        if(setLoader){
            setLoader(false);
        }
    };
};

export const fetchItemListForScan = (data, itemOptionDetail) => async () => {
    const response = await axiosApi.post(apiBaseURL.ITEM_LIST, data, {
        headers: {
            "Content-Type": "application/json",
        },
    });

    let updatedItemOptionDetail = [...itemOptionDetail];

    if (itemOptionDetail?.length > 0) {
        const newItems = response.data.data.map(item => ({
            label: item.name,
            value: item.id,
            sku: item.sku || null,
            stock: item.closing_stock,
            selling_price: item.selling_price,
            purchase_cost: item.last_purchase_cost,
            unit: item.unit,
        }));
        updatedItemOptionDetail = await [...itemOptionDetail, ...newItems];
    }

    return updatedItemOptionDetail;
};

export const fetchIncomeExpenseById = id => async dispatch => {
    try {
        if (!id) return;
        const response = await axiosApi.get(apiBaseURL.INCOME_EXPENSE_TYPE + "/" + id, {
            withCredentials: true,
        });
        dispatch(getIncomeExpense(response.data.data));
    } catch (err) {
        throw new Error(err);
    }
};
export const fetchSingleItemById = id => async dispatch => {
    try {
        if (!id) return;
        const response = await axiosApi.get(apiBaseURL.ITEM_DETAILS + "/" + id, {
            withCredentials: true,
        });
        dispatch(getSingleItemById(response.data.data));
    } catch (err) {
        throw new Error(err);
    }
};

export const fetchPriceListOfItems =
    (ids, ledgerId, setShowItemPriceChangedModal, setIsConfirmed, transactionType, transaction_item_id) => async dispatch => {
        try {
            const response = await axiosApi.post(
                apiBaseURL.PRICE_LIST,
                { item_ids: ids, customer_id: ledgerId, transaction_type: transactionType, transaction_item_id: transaction_item_id || null },
                {
                    withCredentials: true,
                }
            );
            const modalShow = response.data?.data?.modal_show;
            if (setShowItemPriceChangedModal) {
                setShowItemPriceChangedModal(modalShow);
                if (setIsConfirmed) {
                    if (!modalShow) {
                        setIsConfirmed(true);
                    } else {
                        setIsConfirmed(false);
                    }
                }
            } else {
                if (setIsConfirmed) {
                    setIsConfirmed(true);
                }
            }
            dispatch(getSingleItemById(response.data?.data?.items));
            return response.data?.data?.items;
        } catch (err) {
            throw new Error(err);
        }
    };

export const updateItem =
    (id, formData, close, items, setItems, index, isPurchase, navigate, changeTax, setIsEditCalculation, setIsDisable, transactionId, setIsCheckGstType, section) =>
    async dispatch => {
        try {
            const response = await axiosApi.post(apiBaseURL.ITEM + "/" + id, formData, {
                withCredentials: true,
            });
            dispatch(
                addToast({
                    text: response?.data?.message,
                    type: toastType.ADD_TOAST,
                })
            );

            if (section === "low-stock") {
                window.location.href = "/company/low-stock-report";
            } else if (navigate == "save") {
                window.location.href = "/company/item-masters";
            } else if (navigate == "saveAndNew") {
                window.location.reload();
            }
            if (close) {
                close(response.data.data.id);
            }
            if (!navigate) {
                dispatch(fetchItemList({ ids: [response.data.data.id] }));
                dispatch(fetchSingleItemById(response.data.data.id));
            }
            if (setItems && index >= 0 && !transactionId) {
                let item = [...items];
                const mergedCustomFields = updateCustomFieldCalculation(response?.data?.data?.custom_fields_value)
                const quantityFromCustomField = getCalculatedQuantity(mergedCustomFields);
                const finalQuantity =
                    quantityFromCustomField != null
                    ? parseFloat(quantityFromCustomField || 1)?.toFixed(
                    response.data.data.model.decimal_places_for_rate ?? 2
                    )
                    : parseFloat(items[index]?.quantity || 1).toFixed(
                    response.data.data.model.decimal_places_for_rate ?? 2
                );
                const unitOptionDetail = unitOption(response.data.data.units_of_array);
                item[index].selectedItem = response.data.data.id;
                item[index].quantity = (parseFloat(finalQuantity) || 1).toFixed(response.data.data.model.decimal_places ?? 2);
                item[index].additional_description = response.data.data.model.description ?? "";
                item[index].updatedRateWithGst = isPurchase
                    ? parseFloat(response.data.data.model.purchase_price_with_gst || 0)?.toFixed(response.data.data.model?.decimal_places_for_rate ?? 2) ?? 0
                    : parseFloat(response.data.data.model.selling_price_with_gst || 0)?.toFixed(response.data.data.model?.decimal_places_for_rate ?? 2) ?? 0;
                item[index].updatedRateWithoutGst = isPurchase
                    ? changeTax ? parseFloat(response.data.data.model.purchase_price_with_gst || 0)?.toFixed(response.data.data.model?.decimal_places_for_rate ?? 2) ?? 0 :  parseFloat(response.data.data.model.purchase_price_without_gst || 0)?.toFixed(response.data.data.model?.decimal_places_for_rate ?? 2) ?? 0
                    : changeTax ? parseFloat(response.data.data.model.selling_price_with_gst || 0)?.toFixed(response.data.data.model?.decimal_places_for_rate ?? 2) ?? 0 : parseFloat(response.data.data.model.selling_price_without_gst || 0)?.toFixed(response.data.data.model?.decimal_places_for_rate ?? 2) ?? 0;
                item[index].rateWithGst = isPurchase
                    ? parseFloat(response.data.data.model.purchase_price_with_gst || 0)?.toFixed(response.data.data.model?.decimal_places_for_rate ?? 2) ?? 0
                    : parseFloat(response.data.data.model.selling_price_with_gst || 0)?.toFixed(response.data.data.model?.decimal_places_for_rate ?? 2) ?? 0;
                item[index].rateWithoutGst = isPurchase
                    ? parseFloat(response.data.data.model.purchase_price_without_gst || 0)?.toFixed(response.data.data.model?.decimal_places_for_rate ?? 2) ?? 0
                    : parseFloat(response.data.data.model.selling_price_without_gst || 0)?.toFixed(response.data.data.model?.decimal_places_for_rate ?? 2) ?? 0;
                item[index].conversationRate = response.data.data.model.conversion_rate ?? "";
                item[index].decimal_places = response.data.data.model.decimal_places ?? 2;
                item[index].decimal_places_for_rate =
                    response.data.data.model.decimal_places_for_rate ?? 2;
                item[index].item_decimal_places= response.data.data.model.decimal_places ?? 2,
                item[index].item_decimal_places_for_rate= response.data.data.model.decimal_places_for_rate ?? 2,
                item[index].gst_id = response.data.data.model.gst_tax_id ?? 0;
                item[index].gst = response.data.data.model.gst_tax_rate ?? 0;
                item[index].cessRate = response.data.data.model.cess_rate ?? 0;
                item[index].discountType = isPurchase
                    ? response.data.data.model.purchase_discount_type ?? 1
                    : response.data.data.model.discount_type ?? 1;
                item[index].discountValue = isPurchase
                    ? response.data.data.model.purchase_discount_value ?? 0
                    : response.data.data.model.discount_value ?? 0;
                item[index].selectedLedger = isPurchase
                    ? response.data.data.model.expense_ledger_id ?? ""
                    : response.data.data.model.income_ledger_id ?? "";
                item[index].secondaryUnitOfMeasurement =
                    response.data.data.model.secondary_unit_of_measurement ?? "";
                item[index].mrp = response.data.data.model.mrp ?? 0;
                item[index].hsn_code = response.data.data.model.hsn_sac_code ?? 0;
                (item[index].selectedUnit = response.data.data.model?.unit_of_measurement ?? null),
                    (item[index].itemUnitOption = unitOptionDetail);
                item[index].total = isPurchase
                    ? response.data.data.model.purchase_price_without_gst ?? 0
                    : response.data.data.model.selling_price_without_gst ?? 0;
                item[index].itemType = response.data.data.item_type ?? 0;
                item[index].custom_fields = mergedCustomFields;
                const calculatedTotal = calculateTotal(item[index], false, changeTax, false);
                item[index].total = calculatedTotal.total;
                item[index].updatedTotal = calculatedTotal.total;
                item[index].sgstValue = calculatedTotal.sgst;
                item[index].cgstValue = calculatedTotal.sgst;
                item[index].cessValue = calculatedTotal.cess;
                setItems(item);
                if(setIsEditCalculation){
                    setIsEditCalculation(true);
                }
                if(setIsCheckGstType){
                    setIsCheckGstType(true);
                }
            }
            if (setItems && index >= 0 && transactionId) {
                let item = [...items];
                const mergedCustomFields = updateCustomFieldCalculation(response?.data?.data?.transaction_item_custom_fields_value)
                item[index].decimal_places = response.data.data.model.decimal_places ?? 2;
                item[index].decimal_places_for_rate =
                    response.data.data.model.decimal_places_for_rate ?? 2;
                item[index].item_decimal_places= response.data.data.model.decimal_places ?? 2,
                item[index].item_decimal_places_for_rate= response.data.data.model.decimal_places_for_rate ?? 2,
                item[index].custom_fields = item[index].custom_fields?.map(existingField => {
                    const updatedField = mergedCustomFields.find(f => f.custom_field_id === existingField.custom_field_id);
                    if (updatedField) {
                        return {
                            ...existingField,
                            is_able_to_edit: updatedField.is_able_to_edit,
                        };
                    }
                    return existingField;
                });
                setItems(item);
            }
            if (setIsDisable) {
                setIsDisable(false);
            };
        } catch ({ response }) {
            if (setIsDisable) {
                setIsDisable(false);
            };
            dispatch(
                errorToast({
                    text: response?.data?.message || "Error occurred while updating item.",
                    type: toastType.ERROR,
                })
            );
        } finally {
            if (setIsDisable) {
                setIsDisable(false);
            };
        }
    };
export const getItemModelDetail = () => async dispatch => {
    try {
        const response = await axiosApi.get(apiBaseURL.ITEM_DETAIL);
        dispatch(itemModelDetail(response.data?.data));
        return response.data?.data
    } catch (err) {
        throw new Error(err);
    }
};

export const addItemGroup =
    (
        formData,
        formik,
        close,
        isLedger,
        checkGroupLedgerType,
        itemFormik,
        setFields,
        ledgerDetailOptions,
        isPurchase,
        isItem,
        setIsCreatedItemGroup,
        isCall,
        isAllLedger,
        groupLedgerList
    ) =>
    async dispatch => {
        try {
            const response = await axiosApi.post(
                isLedger ? apiBaseURL.LEDGER_GROUP : apiBaseURL.ITEM_GROUP,
                formData,
                {
                    withCredentials: true,
                }
            );
            if (close) {
                close();
            }
            const group = response.data?.data?.parent_group;
            if (group == "addless" || isCall) {
                dispatch(fetchLedgerGroupList());
                dispatch(getLedgerModelDetail(LedgerType.ADD_LESS_LEDGER));
                itemFormik.setFieldValue("calculated_on", 2);
                itemFormik.setFieldValue("rounding_method", 1);
            } else if (group == "Taxes - TCS") {
                dispatch(getTcsTdsGroupList(isPurchase ? 2 : 1, true));
                itemFormik.setFieldValue("calculated_on", 2);
                itemFormik.setFieldValue("rounding_method", 1);
            }
            dispatch(
                addToast({
                    text: response?.data?.message,
                    type: toastType.ADD_TOAST,
                })
            );
            if (setFields) {
                setFields([
                    {
                        value: response.data.data.parent_group,
                        label: response.data.data.parent_group,
                    },
                ]);
            }
            if (isLedger) {
                dispatch(fetchLedgerGroupList());
                const isGroupInList = groupLedgerList?.find((groupname)=> groupname.label == group);
                if(isGroupInList){
                    dispatch(getLedgerGroupDetail(response.data.data.parent_id));
                    itemFormik.setFieldValue("parent_action", group);
                    itemFormik.setFieldValue("group_id", response.data.data.id);
                    itemFormik.setFieldValue("parent_id", response.data.data.parent_id);
                    itemFormik.setFieldValue("new_group_name", response.data.data.name);
                    // }else{
                        //     itemFormik.setFieldValue("parent_action", itemFormik.values?.parent_action);
                    }
            }
            if (isLedger && checkGroupLedgerType) {
                if (checkGroupLedgerType === "item") {
                    dispatch(getLedgerModelDetail(LedgerType.ITEM_LEDGER));
                } else if (checkGroupLedgerType === "party") {
                    dispatch(getLedgerModelDetail(LedgerType.PARTY_LEDGER));
                } else if (checkGroupLedgerType === "additional") {
                    dispatch(getLedgerModelDetail(LedgerType.ADDITIONAL_LEDGER));
                } else if (checkGroupLedgerType === "addless") {
                    dispatch(getLedgerModelDetail(LedgerType.ADD_LESS_LEDGER));
                } else if (checkGroupLedgerType === "payment") {
                    dispatch(getLedgerModelDetail(LedgerType.PAYMENT_LEDGER));
                } else if (checkGroupLedgerType === "tcs") {
                    dispatch(fetchTcsList(isPurchase ? {id:2} : {id:1}));
                    dispatch(getTcsTdsGroupList(isPurchase ? 2 : 1, true)); // added by Ridham for Purchase not TCS set
                } else if (checkGroupLedgerType === "tds") {
                    dispatch(fetchTcsList(isPurchase ? {id:1} : {id:2}));
                    dispatch(getTcsTdsGroupList(isPurchase ? 1 : 2, false)); // added by Ridham for Purchase not TCS set
                }
            } else {
                dispatch(getItemModelDetail());
            }
            if (itemFormik) {
                const matchedGroup = ledgerDetailOptions?.find(
                    group => group.label === response.data.data.parent_group
                );
                if ((matchedGroup && isAllLedger) || isItem) {
                    itemFormik.setFieldValue("group_id", response.data.data.id);
                    itemFormik.setFieldValue("parent_id", response.data.data.parent_id);
                    itemFormik.setFieldValue("new_group_name", response.data.data.name);
                    itemFormik.setFieldValue("parent_action", group);
                    if(setIsCreatedItemGroup){
                        setIsCreatedItemGroup(true);
                    }
                    if (
                        group == "Customers" ||
                        group == "Supplier" ||
                        group == "Fixed Asset" ||
                        group == "Fixed Asset 1" ||
                        group == "Income" ||
                        group == "Expense" ||
                        group == "Secured Loan" ||
                        group == "Unsecured Loan" ||
                        group == "Taxes - GST" ||
                        group == "Taxes - TDS" ||
                        group == "Taxes - TCS" ||
                        group == "TDS Receivable" ||
                        group == "TCS Receivable"
                    ) {
                        dispatch(
                            getLedgerGroupDetail(
                                response.data.data.parent_id ?? response.data.data.id
                            )
                        );
                    }
                }
                // if (setCreatedGroup) {
                //     setCreatedGroup(response.data.data.parent_id);
                // }
                if (formik) {
                    formik.setFieldValue("parent_id", response.data.data.id);
                }
            }
        } catch ({response}) {
            dispatch(
                errorToast({
                    text: response?.data?.message,
                    type: toastType.ERROR,
                })
            );
        }
    };

export const deleteMedia = async id => {
    try {
        if (!id) return;
        const response = await axiosApi.get(apiBaseURL.DELETE_MEDIA + "/" + id, {
            withCredentials: true,
        });
        addToast({
            text: response?.data?.message,
            type: toastType.ADD_TOAST,
        });
        return response?.data;
    } catch ({ response }) {
        errorToast({
            text: response?.data?.message,
            type: toastType.ERROR,
        });
    }
};

export const getCessRate = () => async dispatch => {
    try {
        const response = await axiosApi.get(apiBaseURL.CESS_RATE, {
            withCredentials: true,
        });
        dispatch(cessRateData(response.data.data));
        return response.data;
    } catch (err) {
        throw new Error(err);
    }
};

export const addCessRate = (params) => async dispatch => {
    try {
        const response = await axiosApi.post(`${apiBaseURL.CESS_RATE}`, params, {
            withCredentials: true
        });

        dispatch(
            addToast({
                text: response?.data?.message,
                type: toastType.ADD_TOAST,
            })
        );
        dispatch(getCessRate());
        dispatch(fetchGstRateList());
        return response.data;
    } catch ({ response }) {
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            })
        );
    }
};

export const updateCessRate = (id, params) => async dispatch => {
    try {
        const response = await axiosApi.post(`${apiBaseURL.CESS_RATE}/${id}`, params, {
            withCredentials: true
        });

        dispatch(
            addToast({
                text: response?.data?.message,
                type: toastType.ADD_TOAST,
            })
        );
        dispatch(getCessRate());
        dispatch(fetchGstRateList());
        return response.data;
    } catch ({ response }) {
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            })
        );
    }
};

export const deleteCessRate = (id) => async dispatch => {
    try {
        if (!id) return;
        const response = await axiosApi.get(`${apiBaseURL.CESS_RATE}/${id}/destroy`);
        dispatch(
            addToast({
                text: response?.data?.message,
                type: toastType.ADD_TOAST,
            })
        );
        dispatch(getCessRate());
        dispatch(fetchGstRateList());
        return response.data
    } catch ({ response }) {
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            })
        );
    }
};

export const getItemConfiguration = () => async dispatch => {
    try {
        const response = await axiosApi.get(apiBaseURL.ITEM_MASTERS_CONFIGURATION, {
            withCredentials: true,
        });

        dispatch(itemConfigurationData(response.data.data));
        return response.data;
    } catch (err) {
        throw new Error(err);
    }
};

export const updateItemConfiguration = (params) => async dispatch => {
    try {
        const response = await axiosApi.post(`${apiBaseURL.ITEM_MASTERS_CONFIGURATION}`, params, {
            withCredentials: true
        });

        dispatch(
            addToast({
                text: response?.data?.message,
                type: toastType.ADD_TOAST,
            })
        );
        dispatch(getItemConfiguration());
        return response.data;
    } catch ({ response }) {
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            })
        );
    }
};

export default itemSlice.reducer;
