import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import axiosApi from "../../config/apiConfig";
import { apiBaseURL, toastType } from "../../constants";
import { addToast, errorToast } from "../actions/toastAction";
import { prepareDeleteTransactions } from "../../shared/prepareData";

const initialState = {
    getPurchaseById: [],
    getPurchaseDetailByOrderNumber: [],
    getPurchaseOcrById: [],
    status: ''
};

export const purchaseSlice = createSlice({
    name: "Purchase",
    initialState,
    reducers: {
        getPurchaseById: (state, action) => {
            state.getPurchaseById = action.payload;
        },
        getPurchaseDetailByOrderNumber: (state, action) => {
            state.getPurchaseDetailByOrderNumber = action.payload;
        },
        getPurchaseOcrById: (state, action) => {
            state.getPurchaseOcrById = action.payload;
        },
        status: (state, action) => {
            state.status = action.payload;
        },
    },
});

export const { getPurchaseById, getPurchaseDetailByOrderNumber, getPurchaseOcrById, status } = purchaseSlice.actions;

export const addPurchase = (formdata, saveType, submit_button, setIsDisable, submitData, submit_button_type) => async dispatch => {
    try {
        const response = await axiosApi.post(apiBaseURL.PURCHASE_TRANSACTION, formdata, {
            withCredentials: true,
        });
        if (submit_button === 3) {
            localStorage.setItem("print_sale_id", response.data?.data?.lastTransactionId);
            localStorage.setItem("transaction_type", response.data?.data?.transactionType);
        }

        if (submit_button_type === "saveAndNew") {
            localStorage.setItem('saveAndNewData', JSON.stringify(submitData));
            window.location.reload();
        };

        dispatch(
            addToast({
                text: response?.data?.message,
                type: toastType.ADD_TOAST,
            }),
        );
        if (saveType === "saveAndNew") {
            window.location.reload();
        } else if (saveType === "duplicate") {
            window.location.href = `${window.location.origin}/company/purchases/create`;
        } else {
            window.location.href = `${window.location.origin}/company/purchases`;
            localStorage.removeItem("saveAndNewData");
        }
    } catch ({ response }) {
        if (setIsDisable) {
            setIsDisable(false);
        };
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            }),
        );
    }
};

export const fetchPurchaseById = (id, setLoader) => async dispatch => {
    try {
        if (!id) return;
        if (setLoader) {
            setLoader(true);
        };
        const response = await axiosApi.get(apiBaseURL.PURCHASE_TRANSACTION_DETAIL + "/" + id, {
            withCredentials: true,
        });
        if(setLoader){
            setLoader(false);
        }
        dispatch(getPurchaseById(response.data.data));
    } catch ({ response }) {
        if (response?.data?.status) {
            dispatch(status(response?.data?.status));
        }
        if(setLoader){
            setLoader(false);
        }
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            }),
        );
    } finally {
        if(setLoader){
            setLoader(false);
        }
    };
};

export const deletePurchase = (id, setShowDeleteWarningModel) => async dispatch => {
    try {
        if (!id) return;
        const response = await axiosApi.get(`${apiBaseURL.PURCHASE_TRANSACTION}/${id}/delete`, {
            withCredentials: true,
        });
           if (response?.data?.data?.checkTransactionExists) {
               const transactions = prepareDeleteTransactions(response?.data?.data);
               setShowDeleteWarningModel({ show: true, transactions });
           } else {
               dispatch(
                   addToast({
                       text: response?.data?.message,
                       type: toastType.ADD_TOAST,
                   })
               );
               window.location.href = `${window.location.origin}/company/purchases`;
           }
    } catch ({ response }) {
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            })
        );
    }
};

export const getPurchaseOrderFromMultipleNumber =
    (ids, invoiceType, invoiceNumber, taxType, id) => async dispatch => {
        try {
            const resposne = await axiosApi.post(
                apiBaseURL.MULTI_PURCHASE_ORDER,
                {
                    ids: ids,
                    invoiceType: invoiceType,
                    invoiceNumber: invoiceNumber,
                    ...(taxType && {
                        withTax: taxType ? 1: 0
                    }),
                    ...(id && {
                        transactionId: id
                    })
                },
                {
                    withCredentials: true,
                },
            );
            dispatch(getPurchaseDetailByOrderNumber(resposne.data.data));
        } catch ({ response }) {
            dispatch(
                errorToast({
                    text: response?.data?.message,
                    type: toastType.ERROR,
                }),
            );
        }
    };

export const fetchDuplicatePurchaseById = (id, setLoader) => async dispatch => {
    try {
        if (!id) return;
        if (setLoader) {
            setLoader(true);
        };
        const response = await axiosApi.get(
            apiBaseURL.PURCHASE_TRANSACTION + "/" + id + "/duplicate",
            {
                withCredentials: true,
            },
        );
        if(setLoader){
            setLoader(false);
        }
        dispatch(getPurchaseById(response.data.data));
    } catch ({ response }) {
        if (response?.data?.status) {
            dispatch(status(response?.data?.status));
        }
        if(setLoader){
            setLoader(false);
        }
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            }),
        );
    } finally {
        if(setLoader){
            setLoader(false);
        }
    }
};
export const fetchPurchaseOcrById = (id, setOcrResponse) => async dispatch => {
    try {
        if (!id) return;
        const response = await axiosApi.get(
            `${apiBaseURL.PURCHASE_OCR}/${id}/purchase`,
            {
                withCredentials: true,
            },
        );
        dispatch(getPurchaseOcrById(response.data.data));
        if (setOcrResponse) {
            setOcrResponse(response.data.data)
        }
    } catch ({ response }) {
        if (response?.data?.status) {
            dispatch(status(response?.data?.status));
        }
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            }),
        );
    }
};

export const updatePurchase = (id, formdata, saveType, submit_button, setIsDisable) => async dispatch => {
    try {
        const response = await axiosApi.post(`${apiBaseURL.PURCHASE_TRANSACTION}/${id}`, formdata, {
            withCredentials: true,
        });
        if (submit_button === 3) {
            localStorage.setItem("print_sale_id", response.data?.data?.lastTransactionId);
            localStorage.setItem("transaction_type", response.data?.data?.transactionType);
        }
        dispatch(
            addToast({
                text: response?.data?.message,
                type: toastType.ADD_TOAST,
            }),
        );
        if (saveType === "saveAndNew") {
            window.location.reload();
        } else {
            window.location.href = `${window.location.origin}/company/purchases`;
        }

    } catch ({ response }) {
        if (setIsDisable) {
            setIsDisable(false);
        };
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            }),
        );
    }
};

export default purchaseSlice.reducer;


export const createOCRPurchase = (formdata, saveType, submit_button, setIsDisable) => async dispatch => {
    try {
        const response = await axiosApi.post(`${apiBaseURL.OCR_PURCHASE}`, formdata, {
            withCredentials: true,
        });
        if (submit_button === 3) {
            localStorage.setItem("print_sale_id", response.data?.data?.lastTransactionId);
            localStorage.setItem("transaction_type", response.data?.data?.transactionType);
        }
        dispatch(
            addToast({
                text: response?.data?.message,
                type: toastType.ADD_TOAST,
            }),
        );
        if (saveType === "saveAndNew") {
            window.location.reload();
        } else {
            window.location.href = `${window.location.origin}/company/import-documents`;
        }
        if (setIsDisable) {
            setIsDisable(false);
        };
    } catch ({ response }) {
        if (setIsDisable) {
            setIsDisable(false);
        };
        dispatch(
            errorToast({
                text: response?.data?.message,
                type: toastType.ERROR,
            }),
        );
    }
};
