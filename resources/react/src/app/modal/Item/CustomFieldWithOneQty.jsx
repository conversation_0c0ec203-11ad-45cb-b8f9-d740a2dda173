import { useEffect, useMemo, useState } from "react";
import { <PERSON><PERSON>, Modal } from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import Close from "../../../assets/images/svg/close";
import { FormInput } from "../../../components/ui/Input";
import { errorToast } from "../../../store/actions/toastAction";
import { toastType } from "../../../constants";
import { calculateTotal, customToFixed } from "../../../shared/calculation";

const rowsPerPage = 20;

const CustomFieldWithOneQty = ({
    show,
    handleClose,
    openClassificationModel,
    customFieldWithOneQty,
    items,
    setItems,
    itemIndex,
    isInWord,
    changeTax
}) => {
    const dispatch = useDispatch();
    const { configuration, company } = useSelector((state) => state);
    const saleConfiguration = configuration?.configuration;

  const [tempFieldValues, setTempFieldValues] = useState([]);
  const [tempMultiFieldValues, setTempMultiFieldValues] = useState([])
  const [customFieldDetail, setCustomFieldDetail] = useState([]);
  const [customFieldSelectInventoryDetail, setCustomFieldSelectInventoryDetail] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchFilters, setSearchFilters] = useState({});
  const [customFieldLength, setCustomFieldLength] = useState(
    (items?.[itemIndex]?.custom_field_inventory_store?.length || 0) < 20 ? 20 : items?.[itemIndex]?.custom_field_inventory_store?.length
  );

  useEffect(() => {
    setTempFieldValues(customFieldWithOneQty?.custom_field_inventory_store || []);
  }, [itemIndex, customFieldWithOneQty?.custom_field_inventory_store]);

useEffect(() => {
    const currentItemId = customFieldWithOneQty?.item_master?.id || customFieldWithOneQty?.selectedItem;
    const defaultCustomFields =
        customFieldWithOneQty?.model_inventory_custom_fields?.length > 0 && customFieldWithOneQty?.model_inventory_custom_fields?.[0]?.fields
            ? customFieldWithOneQty?.model_inventory_custom_fields
            : customFieldWithOneQty?.model_select_inventory_custom_fields?.length > 0 ? customFieldWithOneQty?.model_select_inventory_custom_fields : customFieldWithOneQty?.custom_field_inventory_store;

    const inventoryFields =
        customFieldWithOneQty?.model_custom_fields ||
        customFieldWithOneQty?.model_inventory_custom_fields ||
        [];
    setCustomFieldDetail(inventoryFields);

    const otherRowsSelectedCombinations = new Set();
    const currentRowSelectedCombinations = new Set();

    items?.forEach((itm, idx) => {
        const selectedItemId = itm?.item_master?.id || itm?.selectedItem;
        const selectedFields = itm?.model_inventory_custom_fields?.length > 0 && itm?.model_inventory_custom_fields?.[0].fields ? itm?.model_inventory_custom_fields : itm?.model_select_inventory_custom_fields || [];
        selectedFields.forEach(entry => {
            if (selectedItemId === currentItemId && entry?.is_selected) {
                if (idx === itemIndex) {
                    currentRowSelectedCombinations.add(entry.combination_id);
                } else {
                    otherRowsSelectedCombinations.add(entry.combination_id);
                }
            }
        });
    });

    // Determine combinations for current item
    const baseCombinations =
        (customFieldWithOneQty?.model_inventory_custom_fields?.length &&
        customFieldWithOneQty?.model_inventory_custom_fields?.length > 0 && customFieldWithOneQty?.model_inventory_custom_fields?.[0]?.fields
            ? customFieldWithOneQty?.model_inventory_custom_fields
            : defaultCustomFields) || [];
        let finalCombinations = [];
        if(baseCombinations[0]?.fields) {
            finalCombinations = baseCombinations.map(entry => {
                const combinationId = entry.combination_id;
                const isSelected = currentRowSelectedCombinations.has(combinationId);
                const isDisabled = otherRowsSelectedCombinations.has(combinationId);
                return {
                    ...entry,
                    is_selected: isSelected,
                    is_disabled: isDisabled,
                };
            });
        }else{
            finalCombinations = baseCombinations?.length > 0 ? baseCombinations.map((combination, index) => {
            const combinationId = combination?.combination_id;
            const isSelected = currentRowSelectedCombinations?.has(combinationId);
            const isDisabled = otherRowsSelectedCombinations?.has(combinationId);

        return combination?.map(field => ({
            ...field,
            is_selected: isSelected,
            is_disabled: isDisabled
        }));
    }) : [];
    }
    const updatedItems = [...items];
    updatedItems[itemIndex] = {
        ...updatedItems[itemIndex],
        model_inventory_custom_fields: finalCombinations,
        custom_field_inventory_store: finalCombinations,
    };

    // setItems(updatedItems);
    setTempMultiFieldValues(updatedItems)
    setCustomFieldSelectInventoryDetail(finalCombinations);
}, [customFieldWithOneQty]);

    const getDisabledCombinationIds = () => {
        const currentItemId = customFieldWithOneQty?.item_master?.id || customFieldWithOneQty?.selectedItem;
        const disabledIds = [];

        items.forEach((itm, idx) => {
            if (idx !== itemIndex && itm?.selectedItem === currentItemId) {
                itm?.model_inventory_custom_fields?.forEach((row) => {
                    if (row?.is_selected) {
                        disabledIds.push(row.combination_id);
                    }
                });
            }
        });

        return disabledIds;
    };

    const handleChange = (e, rowIndex, fieldId, colIndex) => {
        const { value } = e.target;
        const updatedTemp = [...tempFieldValues]; // already a copy

        while (updatedTemp.length <= rowIndex) updatedTemp.push([]);
        while ((updatedTemp[rowIndex] || []).length <= colIndex) updatedTemp[rowIndex].push({});

        updatedTemp[rowIndex][colIndex] = {
            custom_field_id: fieldId,
            quantity: 1,
            value,
        };
        setTempFieldValues(updatedTemp);
    };

    const handleCheckboxChange = (e, rowIndex) => {
        const checked = e.target.checked;

        const updatedItems = [...items];
        const fullList = [...customFieldSelectInventoryDetail];
        const combinationId = fullList[rowIndex]?.combination_id;

        const disabledCombinationIds = getDisabledCombinationIds();

        if (!checked && fullList[rowIndex]?.is_selected) {
            // Allow unchecking
        } else if (disabledCombinationIds.includes(combinationId)) {
            return;
        }

        fullList[rowIndex] = { ...fullList[rowIndex], is_selected: checked };

        updatedItems[itemIndex] = {
            ...updatedItems[itemIndex],
            model_inventory_custom_fields: fullList,
            custom_field_inventory_store: fullList,
        };

        // setItems(updatedItems);
        setTempMultiFieldValues(updatedItems);
        setCustomFieldSelectInventoryDetail(fullList);
    };

    const handleSearchChange = (e, key) => {
        setSearchFilters((prev) => ({ ...prev, [key]: e.target.value }));
        setCurrentPage(1);
    };

    const allRowsSelectedInvoice = useMemo(() => {
        return customFieldSelectInventoryDetail.map((row, rowIndex) => ({
            ...row,
            rowIndex,
            rowData: row.fields || [],
        }));
    }, [customFieldSelectInventoryDetail]);

    const filteredRowsSelectedInvoice = useMemo(() => {
      if (isInWord) return allRowsSelectedInvoice;

      return allRowsSelectedInvoice.filter((row) => {
        const fieldMatch = Object.entries(searchFilters).every(([key, filterValue]) => {
          if (!filterValue || !key) return true;

          // Match custom field
          const match = row.rowData?.some((cell) => {
            return String(cell.custom_field_id) === key &&
                   (cell?.value ?? "").toLowerCase().includes(filterValue.toLowerCase());
          });

          // Match system fields
          if (key === "__purchase_rate") {
            return row.purchase_rate?.toString().includes(filterValue);
          }
          if (key === "__purchase_date") {
            return row.purchase_date?.toString().includes(filterValue);
          }
          if (key === "__available_qty") {
            return row.available_quantity?.toString().includes(filterValue);
          }
          if (key === "__sale_qty") {
            return row.sale_quantity?.toString().includes(filterValue);
          }

          return match;
        });

        return fieldMatch;
      });
    }, [searchFilters, allRowsSelectedInvoice, isInWord]);

    const paginatedRowsSelectedInvoice = useMemo(() => {
        return filteredRowsSelectedInvoice.slice(
            (currentPage - 1) * rowsPerPage,
            currentPage * rowsPerPage
        );
    }, [filteredRowsSelectedInvoice, currentPage]);

    const paginatedRowsManualInput = useMemo(() => {
        return Array.from({ length: customFieldLength })
            .slice((currentPage - 1) * rowsPerPage, currentPage * rowsPerPage)
            .map((_, indexOffset) => (currentPage - 1) * rowsPerPage + indexOffset);
    }, [customFieldLength, currentPage]);

    const handleCloseModel = () => {
        handleClose();
        if (
            saleConfiguration?.header?.is_change_gst_details &&
            company?.company?.is_gst_applicable
        ) {
            openClassificationModel();
        }
    };

    const handleSubmit = e => {
        e.preventDefault();
        const data = tempFieldValues;
        if (isInWord) {
            for (let i = 0; i < data.length - 1; i++) {
                const isRowIEmpty = data[i].every(cell => !cell.value?.toString().trim());
                if (isRowIEmpty) continue;

                for (let j = i + 1; j < data.length; j++) {
                    const isRowJEmpty = data[j].every(cell => !cell.value?.toString().trim());
                    if (isRowJEmpty) continue;

                    const isDuplicate = data[i].every((cell, colIndex) => {
                        const valA = cell.value?.toString().trim();
                        const valB = data[j][colIndex]?.value?.toString().trim();
                        return valA === valB;
                    });

                    if (isDuplicate) {
                        const duplicateVal = data[i][0]?.value || "N/A";
                        return dispatch(
                            errorToast({
                                text: `Duplicate row found (Row ${i + 1} and Row ${
                                    j + 1
                                }) with value "${duplicateVal}"`,
                                type: toastType.ERROR,
                            })
                        );
                    }
                }
            }
            const updatedItems = [...items];
            updatedItems[itemIndex] = {
                ...updatedItems[itemIndex],
                custom_field_inventory_store: data,
                model_inventory_custom_fields: data,
            };
            const updatedItemsDetail = updatedItems.map((item, idx) => {
                // Step 1: calculate item_quantity based on your earlier logic
                const inventoryStore = item?.custom_field_inventory_store || [];
                const outWordinventoryStore = item?.model_select_inventory_custom_fields || [];
                let item_quantity;
                item_quantity =
                    (isInWord &&
                    inventoryStore &&
                    (item?.model_custom_fields?.length > 0 || inventoryStore?.length > 0)
                        ? customToFixed(
                              inventoryStore.reduce((sum, group) => {
                                  return (
                                      sum +
                                      (Array.isArray(group) && group.length > 0
                                          ? group[0]?.quantity || 0
                                          : 0)
                                  );
                              }, 0),
                              item?.decimal_places
                          )
                        : item?.model_select_inventory_custom_fields?.length > 0
                        ? customToFixed(
                              outWordinventoryStore.reduce((sum, group) => {
                                  return sum + (parseFloat(group?.sale_quantity) || 0);
                              }, 0),
                              item?.decimal_places
                          )
                        : null) ?? 0.00;
                // Step 2: calculate total and update item
                const updatedItem = {
                    ...item,
                    quantity: parseFloat(item_quantity),
                    is_change_quantity: true,
                };
                const calculatedTotal = calculateTotal(updatedItem, false, changeTax, false);
                updatedItem.total = calculatedTotal.total;
                updatedItem.updatedTotal = calculatedTotal.total;
                updatedItem.sgstValue = calculatedTotal.sgst;
                updatedItem.cgstValue = calculatedTotal.sgst;
                updatedItem.cessValue = calculatedTotal.cess;
                return updatedItem;
            });
            setItems(updatedItemsDetail);
        } else {
            const updatedItems = tempMultiFieldValues.map((item, idx) => {
                // Step 1: calculate item_quantity based on your earlier logic
                const customFieldType = item?.model_custom_fields?.[0]?.custom_field_type;
                const inventoryStore = item?.custom_field_inventory_store || [];
                const outWordinventoryStore = item?.model_select_inventory_custom_fields || [];
                let item_quantity;
                const selectedStoreGroups = inventoryStore.filter(
                    field => field?.is_selected === true
                );

                const inWordSingleLength = selectedStoreGroups.length ?? item?.quantity;
                if (inWordSingleLength > 0) {
                    const updatedItem = {
                        ...item,
                        quantity: customToFixed(inWordSingleLength, item?.decimal_places),
                        is_change_quantity: true,
                    };

                    const calculatedTotal = calculateTotal(updatedItem, false, changeTax, false);

                    updatedItem.total = calculatedTotal.total;
                    updatedItem.updatedTotal = calculatedTotal.total;
                    updatedItem.sgstValue = calculatedTotal.sgst;
                    updatedItem.cgstValue = calculatedTotal.sgst;
                    updatedItem.cessValue = calculatedTotal.cess;
                    return updatedItem;
                }
                const updatedItem = {
                    ...item,
                    quantity: parseFloat(item_quantity),
                    is_change_quantity: true,
                };

                const calculatedTotal = calculateTotal(updatedItem, false, changeTax, false);

                updatedItem.total = calculatedTotal.total;
                updatedItem.updatedTotal = calculatedTotal.total;
                updatedItem.sgstValue = calculatedTotal.sgst;
                updatedItem.cgstValue = calculatedTotal.sgst;
                updatedItem.cessValue = calculatedTotal.cess;
                return updatedItem;
            });

            // Step 3: update state
            setItems(updatedItems);
        }
        handleCloseModel();
    };

    return (
        <Modal show={show} onHide={handleCloseModel} centered className="custom-table-field-test-serial-modal">
            <div className="modal-header py-1 ps-4 pe-3">
                <button className="ms-auto btn btn-icon btn-sm btn-active-light-primary ms-2 close-btn" onClick={handleCloseModel}>
                    <Close />
                </button>
            </div>
            <Modal.Body className="px-4 py-2">
                <div className="w-100 overflow-auto">
                    <div className="custom-field-table-modal overflow-auto custom-table-field-test-serial mb-1">
                        <table className="w-100">
                            <thead>
                                <tr>
                                    {isInWord ? (
                                        <>
                                            <th className="w-30px">Sr No.</th>
                                            {customFieldDetail.map((field) => (
                                                <th key={field.id}>{field.label_name}</th>
                                            ))}
                                        </>
                                    ) : (
                                        <>
                                            <th></th>
                                            <th className="w-30px">Sr No.</th>
                                            {customFieldDetail.map((field) => (
                                                <th key={field.id}>{field.label_name}</th>
                                            ))}
                                            <th>Purchase Rate</th>
                                            <th>Purchase Date</th>
                                        </>
                                    )}
                                </tr>
                                {!isInWord && (
                                    <tr>
                                        <th></th>
                                        <th className="w-30px"></th>
                                        {customFieldDetail.map((field) => (
                                            <th key={`search-${field.id}`}>
                                                <FormInput
                                                    type="text"
                                                    className="form-control search-box h-26px"
                                                    placeholder="Search"
                                                    value={searchFilters[field.id] || ""}
                                                    onChange={(e) => handleSearchChange(e, field.id)}
                                                />
                                            </th>
                                        ))}
                                        <th>
                                            <FormInput
                                                type="text"
                                                className="form-control search-box h-26px"
                                                placeholder="Rate"
                                                value={searchFilters["__purchase_rate"] || ""}
                                                onChange={(e) => handleSearchChange(e, "__purchase_rate")}
                                            />
                                        </th>
                                        <th>
                                            <FormInput
                                                type="text"
                                                className="form-control search-box h-26px"
                                                placeholder="Date"
                                                value={searchFilters["__purchase_date"] || ""}
                                                onChange={(e) => handleSearchChange(e, "__purchase_date")}
                                            />
                                        </th>
                                    </tr>
                                )}
                            </thead>
                            <tbody>
                                {isInWord
                                    ? paginatedRowsManualInput.map((rowIndex) => (
                                        <tr key={rowIndex}>
                                            <td className="text-start w-30px" style={{ padding: "2px 10px" }}>{rowIndex + 1}</td>
                                            {customFieldDetail.map((field, colIndex) => (
                                                <td key={field.id}>
                                                    <FormInput
                                                        type="text"
                                                        value={tempFieldValues[rowIndex]?.[colIndex]?.value || ""}
                                                        placeholder={field.label_name}
                                                        className="form-control search-box"
                                                        onChange={(e) => handleChange(e, rowIndex, field.id, colIndex)}
                                                    />
                                                </td>
                                            ))}
                                        </tr>
                                    ))
                                    : paginatedRowsSelectedInvoice.map((row, rowIndex) => {
                                        const disabled = getDisabledCombinationIds().includes(row.combination_id) || row?.is_disabled;
                                        const isChecked = row.is_selected ? true : disabled ? true : row?.is_disabled;

                                        return (
                                            <tr key={row.rowIndex} className={disabled ? "react-input-disabled" : ""}>
                                                <td className="text-start">
                                                    <input
                                                        type="checkbox"
                                                        className="form-check-input"
                                                        checked={isChecked}
                                                        onChange={(e) => handleCheckboxChange(e, rowIndex)}
                                                        disabled={disabled}
                                                    />
                                                </td>
                                                <td className="text-start w-30px">{row.rowIndex + 1}</td>
                                                {customFieldDetail.map((field) => {
                                                    const matched = row.rowData.find((f) => f.custom_field_id === field.id);
                                                    return <td key={field.id} className="text-start">{matched?.value || ""}</td>;
                                                })}
                                                <td className="text-start">{row.purchase_rate}</td>
                                                <td className="text-start">{row.purchase_date}</td>
                                            </tr>
                                        );
                                    })}
                            </tbody>
                        </table>
                    </div>

                    {isInWord && (
                        <button
                            className="btn btn-primary btn-sm mt-2 mb-1"
                            type="button"
                            onClick={() => setCustomFieldLength((prev) => prev + 1)}
                            style={{ padding: "4px 14px 4px 10px", fontSize: "11px" }}
                        >
                            ADD <span className="font-semibold fs-4 lh-1">+</span>
                        </button>
                    )}

                    <div className="overflow-auto justify-content-center d-flex mb-3">
                        <nav>
                            <ul className="pagination cursor-pointer">
                                <li className={`page-item ${currentPage === 1 ? "disabled" : ""}`} onClick={() => currentPage > 1 && setCurrentPage((p) => p - 1)}>
                                    <span className="page-link">&lsaquo;</span>
                                </li>
                                {Array.from({ length: Math.ceil((isInWord ? customFieldLength : filteredRowsSelectedInvoice.length) / rowsPerPage) }).map((_, idx) => (
                                    <li key={idx} className={`page-item ${currentPage === idx + 1 ? "active" : ""}`} onClick={() => setCurrentPage(idx + 1)}>
                                        <span className="page-link">{idx + 1}</span>
                                    </li>
                                ))}
                                <li
                                    className={`page-item ${currentPage === Math.ceil((isInWord ? customFieldLength : filteredRowsSelectedInvoice.length) / rowsPerPage) ? "disabled" : ""}`}
                                    onClick={() => currentPage < Math.ceil((isInWord ? customFieldLength : filteredRowsSelectedInvoice.length) / rowsPerPage) && setCurrentPage((p) => p + 1)}
                                >
                                    <span className="page-link">&rsaquo;</span>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>

                <div className="d-flex gap-3">
                    <Button variant="primary" type="submit" onClick={handleSubmit} className="btn-sm fs-13"> Save</Button>
                    <Button variant="secondary" onClick={handleCloseModel} className="btn-sm fs-13" >Close</Button>
                </div>
            </Modal.Body>
        </Modal>
    );
};

export default CustomFieldWithOneQty;
