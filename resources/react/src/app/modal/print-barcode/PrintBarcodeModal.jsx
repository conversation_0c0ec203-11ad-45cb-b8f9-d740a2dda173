import React, { useContext, useEffect, useState } from 'react'
import { Modal, Row } from 'react-bootstrap'
import print from "../../../assets/images/print.svg";
import PrintBarcodeSettingsModal from './PrintBarcodeSettingsModal';
import { StateContext } from '../../../context/StateContext';
import { useSelector } from 'react-redux';

const initialMarginStateLimit = {
    pageMarginTopBottomLimit: '',
    pageMarginLeftRightLimit: '',
    barcodeBetweenRowsLimit: '',
    barcodeBetweenColumnsLimit: '',
};

const PrintBarcodeModal = ({ showModal, handleClose, formState, labelView, tableData, headerCheckboxes, printRef, handlePrint, handleDownload, loading, printLoading, LabelBarcodeDetails }) => {
    const [isPrint, setIsPrint] = useState(false);
    const [isBorder, setIsBorder] = useState(true);
    const [isPrintSettings, setIsPrintSettings] = useState(false);
    const { barcodeSize, setBarcodeSize } = useContext(StateContext);
    const [maxMarginLimit, setMaxMarginLimit] = useState(initialMarginStateLimit);
    const [maxCombinedMarginLimit, setMaxCombinedMarginLimit] = useState({ maxTopBottom: "", maxLeftRight: "", maxRows: "", maxColumns: "" });
    const { printBarcode } = useSelector(state => state);

    useEffect(() => {
        if (printBarcode?.barcodeSettingData) {
            setIsBorder(printBarcode?.barcodeSettingData?.with_border_enabled);
        }
    }, [printBarcode?.barcodeSettingData]);

    const pageStyle = `
        @media print {
          @page {
            margin-top: ${barcodeSize.pageMarginTop}mm;
            margin-bottom: ${barcodeSize.pageMarginBottom}mm;
            background: #f5f8fa;
          }
        }
      `;

    useEffect(() => {
        if (formState.selectedPrinter === "regular_printer") {

            const getMarginValues = (label) => {

                const spacingRow = parseFloat(printBarcode?.barcodeSettingData?.label_spacing_row || 0);
                const spacingCol = parseFloat(printBarcode?.barcodeSettingData?.label_spacing_column || 0);

                switch (label) {
                    case 65:

                        const incrementTopBottom = +(13 * (1.5 - (spacingRow || 0))).toFixed(2);
                        const incrementLeftRight = +(4 * (3 - (spacingCol || 0))).toFixed(2);

                        setMaxCombinedMarginLimit({ maxTopBottom: 4.5 + incrementTopBottom, maxLeftRight: 7 + incrementLeftRight, maxRows: 1.5, maxColumns: 3 })
                        return { rows: printBarcode?.barcodeSettingData?.label_spacing_row || 0, Columns: printBarcode?.barcodeSettingData?.label_spacing_column || 0 };
                    case 48:

                        const incrementTopBottom48 = +(12 * (1.5 - (spacingRow || 0))).toFixed(2);
                        const incrementLeftRight48 = +(3 * (3 - (spacingCol || 0))).toFixed(2);

                        setMaxCombinedMarginLimit({ maxTopBottom: 3 + incrementTopBottom48, maxLeftRight: 8 + incrementLeftRight48, maxRows: 1.5, maxColumns: 3 })
                        return { rows: printBarcode?.barcodeSettingData?.label_spacing_row || 0, Columns: printBarcode?.barcodeSettingData?.label_spacing_column || 0 };
                    default:

                        const incrementTopBottom24 = +(8 * (2.5 - (spacingRow || 0))).toFixed(2);
                        const incrementLeftRight24 = +(2 * (4 - (spacingCol || 0))).toFixed(2);

                        setMaxCombinedMarginLimit({ maxTopBottom: 4 + incrementTopBottom24, maxLeftRight: 9 + incrementLeftRight24, maxRows: 2.5, maxColumns: 4 })
                        return { rows: printBarcode?.barcodeSettingData?.label_spacing_row || 0, Columns: printBarcode?.barcodeSettingData?.label_spacing_column || 0 };
                }
            };

            const margins = getMarginValues(labelView);

            const barcodeMargins = {
                pageMarginTopBottomLimit: 6,
                pageMarginLeftRightLimit: 20,
                barcodeBetweenRowsLimit: margins.rows,
                barcodeBetweenColumnsLimit: margins.Columns,
            };

            const barcodeMarginsSize = {
                pageMarginTop: printBarcode?.barcodeSettingData?.roll_margin_top || 0,
                pageMarginBottom: printBarcode?.barcodeSettingData?.roll_margin_bottom || 0,
                pageMarginLeft: printBarcode?.barcodeSettingData?.roll_margin_left || 0,
                pageMarginRight: printBarcode?.barcodeSettingData?.roll_margin_right || 0,
                barcodeBetweenRows: margins.rows,
                barcodeBetweenColumns: margins.Columns,
            };


            setMaxMarginLimit(prev => ({ ...prev, ...barcodeMargins }));
            setBarcodeSize(prev => ({ ...prev, ...barcodeMarginsSize }));
        } else if (formState.selectedPrinter === "label_printer") {
            const getMarginValues = (label, labelView) => {

                const spacingCol = parseFloat(printBarcode?.barcodeSettingData?.label_spacing_column || 0);

                if (labelView > 1) {
                    const incrementLeftRight = +((labelView - 1) * (5 - (spacingCol || 0))).toFixed(2);
                    setMaxCombinedMarginLimit({ maxTopBottom: 10, maxLeftRight: 10 + incrementLeftRight, maxRows: 1, maxColumns: 5 })
                    return { rows: printBarcode?.barcodeSettingData?.label_spacing_row || 0, Columns: printBarcode?.barcodeSettingData?.label_spacing_column || 0 };
                } else {
                    setMaxCombinedMarginLimit({ maxTopBottom: 10, maxLeftRight: 10, maxRows: 1, maxColumns: 5 })
                    return { rows: printBarcode?.barcodeSettingData?.label_spacing_row || 0, Columns: printBarcode?.barcodeSettingData?.label_spacing_column || 0 };
                }
            };

            const margins = getMarginValues(formState?.selectedSize, labelView);

            const barcodeMargins = {
                pageMarginTopBottomLimit: 3,
                pageMarginLeftRightLimit: 10,
                barcodeBetweenRowsLimit: margins.rows,
                barcodeBetweenColumnsLimit: margins.Columns,
            };

            const barcodeMarginsSize = {
                pageMarginTop: printBarcode?.barcodeSettingData?.roll_margin_top || 0,
                pageMarginBottom: printBarcode?.barcodeSettingData?.roll_margin_bottom || 0,
                pageMarginLeft: printBarcode?.barcodeSettingData?.roll_margin_left || 0,
                pageMarginRight: printBarcode?.barcodeSettingData?.roll_margin_right || 0,
                barcodeBetweenRows: margins.rows,
                barcodeBetweenColumns: margins.Columns,
            };


            setMaxMarginLimit(prev => ({ ...prev, ...barcodeMargins }));
            setBarcodeSize(prev => ({ ...prev, ...barcodeMarginsSize }));

        }
    }, [formState.selectedPrinter, labelView, showModal, printBarcode?.barcodeSettingData]);

    useEffect(() => {
        if (!printLoading) {
            setIsPrint(false);
        }
    }, [printLoading])

    let globalIndex = -1;


    const handleCloseBarcode = () => {
        handleClose();
        setIsBorder(true);
    }

    const allBarcode = tableData.flatMap(item => Array.from({ length: item.labels }, () => item));
    const totalLabels = Number(tableData.reduce((sum, item) => sum + item.labels, 0));

    return (
        <>
            {isPrint && <style>{pageStyle}</style>}

            <Modal show={showModal} onHide={handleCloseBarcode} className="printview-modal" size="lg" centered >
                <Modal.Header closeButton className="position-sticky top-0 bg-white align-items-md-center align-items-start">
                    <Modal.Title className="w-100">
                        <div className="d-flex flex-wrap gap-3 justify-content-between align-items-sm-center align-items-start">
                            <div className='d-flex flex-wrap align-items-center gap-sm-4 gap-1'>
                                <p className="fs-20 fw-6 text-black mb-0 me-sm-0 me-2">Print Preview</p>
                            </div>
                            <div className="d-flex gap-3 align-items-center">
                                <button
                                    className="btn btn-primary d-flex align-items-center gap-2"
                                    onClick={() => {
                                        setIsPrint(true);
                                        setTimeout(() => {
                                            handlePrint();
                                        }, 0);
                                    }}
                                    disabled={printLoading}
                                >
                                    <img src={print} alt="images" />
                                    Print
                                </button>
                                <button
                                    id='downloadBtn'
                                    className="btn btn-primary "
                                    onClick={handleDownload}
                                    disabled={loading}
                                >
                                    Download
                                </button>
                                <button
                                    id='settingBtn'
                                    className="btn btn-primary me-3"
                                    onClick={() => setIsPrintSettings(true)}
                                >
                                    Settings
                                </button>
                            </div>
                        </div>
                    </Modal.Title>
                </Modal.Header>
                <Modal.Body className="h-100 overflow-auto">
                    <div ref={printRef} className={`print-barcode-container ${formState.selectedPrinter === "label_printer" && !isPrint ? "justify-content-center align-items-center" : ""} `}
                        style={{
                            marginLeft: `${barcodeSize?.pageMarginLeft}mm`,
                            marginRight: `${barcodeSize?.pageMarginRight}mm`,
                        }}
                    >
                        {(LabelBarcodeDetails?.isCustomBarcodeSize !== true) ? (
                            <div className={`
                    ${formState.selectedPrinter === "regular_printer"
                                    ? labelView === 65
                                        ? `barcodeContainer64`
                                        : labelView === 48
                                            ? `barcodeContainer48`
                                            : `barcodeContainer24`
                                    : formState.selectedPrinter === "label_printer"
                                        ? [1, 3].includes(formState?.selectedSize)
                                            ? isPrint ? "barcodeContainerlabel13Print" : "barcodeContainerlabel13"
                                            : [2, 4].includes(formState?.selectedSize)
                                                // ? "barcodeContainerlabel24"
                                                ? isPrint ? "barcodeContainerlabel24Print" : "barcodeContainerlabel24"
                                                : isPrint ? "barcodeContainerlabel5Print" : "barcodeContainerlabel5"
                                        : "barcodeContainerlabel"}
                         print_pdf justify-content-lg-center justify-content-start w-fit-content `}

                                style={{
                                    columnGap: `${barcodeSize?.barcodeBetweenColumns}mm`,
                                    rowGap: `${barcodeSize?.barcodeBetweenRows}mm`,
                                }}

                            >
                                {tableData.flatMap(item =>
                                    Array.from({ length: item.labels }).map((_, index) => {
                                        globalIndex++;

                                        const isLastLabel = globalIndex === totalLabels - 1;
                                        const shouldBreak =
                                        !isLastLabel && formState.selectedPrinter === "regular_printer" &&
                                        (
                                            (labelView === 65 && (globalIndex + 1) % 65 === 0) ||
                                            (labelView === 48 && (globalIndex + 1) % 48 === 0) ||
                                            (labelView === 24 && (globalIndex + 1) % 24 === 0)
                                        );
                                        return (
                                            <div className="barcode-sticker-wrapper" style={{
                                                padding: "2mm",
                                                background: "white",
                                                borderRadius: "3px",
                                                boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                                                margin: "1mm",
                                                position: "relative"
                                            }} key={`${item.id}-${index}`}>
                                                <div className={`${isBorder ? "barcode-print-border" : "barcode-card-print-without-border"} ${formState.selectedPrinter === "label_printer" ? "barcodeBreak" : shouldBreak ? "barcodeBreak" : ""} px-0`} style={{
                                                    width:
                                                        formState.selectedPrinter === "regular_printer"
                                                            ? labelView === 65
                                                                ? `38mm`
                                                                : labelView === 48
                                                                    ? `48mm`
                                                                    : `64mm`
                                                            : formState.selectedPrinter === "label_printer"
                                                                ? [1, 2].includes(formState?.selectedSize)
                                                                    ? "189px"
                                                                    : [3, 4].includes(formState?.selectedSize)
                                                                        ? "143.6px"
                                                                        : [5].includes(formState?.selectedSize)
                                                                            ? "238px"
                                                                            : [6].includes(formState?.selectedSize)
                                                                                ? "295px"
                                                                                : "63mm"
                                                                : "auto",
                                                    height:
                                                        formState.selectedPrinter === "regular_printer"
                                                            ? labelView === 65
                                                                ? `21mm`
                                                                : labelView === 48
                                                                    ? `23mm`
                                                                    : `34mm`
                                                            : formState.selectedPrinter === "label_printer"
                                                                ? [1, 2].includes(formState?.selectedSize)
                                                                    ? "91px"
                                                                    : [3, 4].includes(formState?.selectedSize)
                                                                        ? "91px"
                                                                        : [5].includes(formState?.selectedSize)
                                                                            ? "140px"
                                                                            : [6].includes(formState?.selectedSize)
                                                                                ? "94.5px"
                                                                                : "25mm"
                                                                : "auto",
                                                    display: "flex",
                                                    justifyContent: "center",
                                                    alignItems: "flex-start",
                                                    flexDirection: "column",
                                                    boxSizing: "border-box",
                                                    overflow: "visible",
                                                    textAlign: "center",
                                                }}>
                                            <div className={` barcode-card-print d-flex  justify-content-center align-items-start h-100 last:`}>
                                                <div className="barcode-content-print w-100 text-center d-flex align-items-center h-100 justify-content-start" style={{ flexDirection: "column", padding: "1mm" }}>
                                                    {headerCheckboxes?.header && item.header?.length > 0 && <p className={`${formState.selectedPrinter === "regular_printer"
                                                        ? labelView === 65
                                                            ? `barcodeHeader64 barcodeHeaderPDF64 lh-1`
                                                            : labelView === 48
                                                                ? `barcodeHeader48 lh-1 barcodeHeaderPDF48`
                                                                : `barcodeHeader24`
                                                        : formState.selectedPrinter === "label_printer"
                                                            ? [1, 2].includes(formState?.selectedSize)
                                                                ? "barcodeHeader-label"
                                                                : [3, 4].includes(formState?.selectedSize)
                                                                    ? "barcodeHeader-label"
                                                                    : [6].includes(formState?.selectedSize)
                                                                        ? "barcodeHeader-label"
                                                                        : "barcodeHeader-label"
                                                            : "barcodeHeader-label"} text-black mb-1 barcode_header`}

                                                    >{item.header === "custom_text" ? item?.custom_text_header : item.header === "mrp" ? `MRP : ${item[item.header] || ""}` : item.header === "selling_price_with_gst" ? `RATE : ${item[item.header] || ""}` : item[item.header]}</p>}
                                                    <div className="barcode-image-height h-100 w-100 flex-grow-1">
                                                        <img src={item.item_barcode_url}
                                                            alt="Barcode"
                                                            className={` barcode_image w-100 h-100`}
                                                        />
                                                    </div>
                                                    {headerCheckboxes?.sku && <div className={`${formState.selectedPrinter === "regular_printer"
                                                        ? labelView === 65
                                                            ? `barcodesku64 barcodeskuPDF64 lh-1`
                                                            : labelView === 48
                                                                ? `barcodesku48 lh-1 barcodeskuPDF48`
                                                                : `barcodesku24`
                                                        : formState.selectedPrinter === "label_printer"
                                                            ? [1, 2].includes(formState?.selectedSize)
                                                                ? "barcodesku-label"
                                                                : [3, 4].includes(formState?.selectedSize)
                                                                    ? "barcodesku-label34"
                                                                    : [5].includes(formState?.selectedSize)
                                                                        ? "barcodesku-label"
                                                                        : "barcodesku-label"
                                                            : "barcodesku-label"}
                                                                 barcode-number fw-7 text-black barcode_sku mb-1`}
                                                    >{item.sku}</div>}
                                                    {headerCheckboxes?.line1 && item.line1?.length > 0 && <p className={`
                                            ${formState.selectedPrinter === "regular_printer"
                                                            ? labelView === 65
                                                                ? `barcodeline1_64 barcodeline_PDF lh-1`
                                                                : labelView === 48
                                                                    ? `barcodeline1_48 lh-1 barcodeline_PDF`
                                                                    : `barcodeline1_24`
                                                            : formState.selectedPrinter === "label_printer"
                                                                ? [1, 2].includes(formState?.selectedSize)
                                                                    ? "barcodeline1_label"
                                                                    : [3, 4].includes(formState?.selectedSize)
                                                                        ? "barcodeline1_label"
                                                                        : [6].includes(formState?.selectedSize)
                                                                            ? "barcodeline1_label"
                                                                            : "barcodeline1_label lh-1"
                                                                : "barcodeline1_label"}
                                         fw-5 text-black text-center mb-1 barcode_line`}
                                                    >{item.line1 === "custom_text" ? item?.custom_text_line1 : item.line1 === "mrp" ? `MRP : ${item[item.line1] || ""}` : item.line1 === "selling_price_with_gst" ? `RATE : ${item[item.line1] || ""}` : item[item.line1]}</p>}
                                                    {(item.line2?.length > 0 || item.line3?.length > 0) && <div className={`${headerCheckboxes.line2 && headerCheckboxes.line3 ? "justify-content-between print_justify_between" : "justify-content-center print_justify_center"} d-flex  w-100 barcode_lines`}>
                                                        {headerCheckboxes?.line2 && <p className={`
                                                  ${formState.selectedPrinter === "regular_printer"
                                                                ? labelView === 65
                                                                    ? `barcodeline2_64 lh-1 barcodeline_PDF`
                                                                    : labelView === 48
                                                                        ? `barcodeline2_48 lh-1 barcodeline_PDF`
                                                                        : `barcodeline2_24`
                                                                : formState.selectedPrinter === "label_printer"
                                                                    ? [1, 2].includes(formState?.selectedSize)
                                                                        ? "barcodeline2_label lh-1"
                                                                        : [3, 4].includes(formState?.selectedSize)
                                                                            ? "barcodeline2_label lh-1"
                                                                            : [6].includes(formState?.selectedSize)
                                                                                ? "barcodeline2_label lh-1"
                                                                                : "barcodeline2_label"
                                                                    : "barcodeline2_label"}
                                             fw-5 text-black text-center barcode_line mb-1`}
                                                        >{item.line2 === "custom_text" ? item?.custom_text_line2 : item?.line2 === "mrp" ? `MRP : ${item[item.line2] || ""}` : item?.line2 === "selling_price_with_gst" ? `RATE : ${item[item.line2] || ""}` : item[item.line2]}</p>}
                                                        {headerCheckboxes?.line3 && <p className={`
                                                ${formState.selectedPrinter === "regular_printer"
                                                                ? labelView === 65
                                                                    ? `barcodeline2_64 lh-1 barcodeline_PDF`
                                                                    : labelView === 48
                                                                        ? `barcodeline2_48 lh-1 barcodeline_PDF`
                                                                        : `barcodeline2_24`
                                                                : formState.selectedPrinter === "label_printer"
                                                                    ? [1, 2].includes(formState?.selectedSize)
                                                                        ? "barcodeline2_label lh-1"
                                                                        : [3, 4].includes(formState?.selectedSize)
                                                                            ? "barcodeline2_label lh-1"
                                                                            : "barcodeline2_label"
                                                                    : "barcodeline2_label"}
                                             fw-5 text-black text-center barcode_line mb-1`}
                                                        >{item.line3 === "custom_text" ? item?.custom_text_line3 : item?.line3 === "mrp" ? `MRP : ${item[item.line3] || ""}` : item?.line3 === "selling_price_with_gst" ? `RATE : ${item[item.line3] || ""}` : item[item.line3]}</p>}
                                                    </div>}
                                                    {headerCheckboxes?.line4 && item.line4?.length > 0 && <p className={`
                                            ${formState.selectedPrinter === "regular_printer"
                                                            ? labelView === 65
                                                                ? `barcodeline4_64 lh-1 barcodeline_PDF`
                                                                : labelView === 48
                                                                    ? `barcodeline4_48 lh-1 barcodeline_PDF`
                                                                    : `barcodeline4_24`
                                                            : formState.selectedPrinter === "label_printer"
                                                                ? [1, 2].includes(formState?.selectedSize)
                                                                    ? "barcodeline4_label lh-1"
                                                                    : [3, 4].includes(formState?.selectedSize)
                                                                        ? "barcodeline4_label lh-1"
                                                                        : [6].includes(formState?.selectedSize)
                                                                            ? "barcodeline4_label lh-1"
                                                                            : "barcodeline4_label"
                                                                : "barcodeline4_label"}
                                            fw-5 text-black text-center barcode_line mb-0`}
                                                    >{item.line4 === "custom_text" ? item?.custom_text_line4 : item?.line4 === "mrp" ? `MRP : ${item[item.line4] || ""}` : item?.line4 === "selling_price_with_gst" ? `RATE : ${item[item.line4] || ""}` : item[item.line4]}</p>}
                                                </div>
                                            </div>
                                        </div>
                                            </div>
                                        )
                                    })
                                )}
                            </div>
                        ) : (
                            <div
                                className={` print_pdf justify-content-lg-center justify-content-start w-fit-content `}
                                style={{
                                    columnGap: `${barcodeSize?.barcodeBetweenColumns}mm`,
                                    rowGap: `${barcodeSize?.barcodeBetweenRows}mm`,
                                    display: "grid",
                                    gridTemplateColumns: `repeat(${LabelBarcodeDetails?.barcodes_per_row}, 1fr)`,
                                }}
                            >
                                {allBarcode.map((item, index) => {
                                    globalIndex++;
                                    const isLast = labelView > 1 ? (index === allBarcode.length - 1 || index === allBarcode.length - 2) : index === allBarcode.length - 1;

                                    return (
                                        <div className="barcode-sticker-wrapper" style={{
                                            padding: "2mm",
                                            background: "white",
                                            borderRadius: "3px",
                                            boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                                            margin: "1mm",
                                            position: "relative"
                                        }} key={`${item.id}-${index}`}>
                                            <div className={`${isBorder ? "barcode-print-border" : "barcode-card-print-without-border"} ${(formState.selectedPrinter === "label_printer" && !isLast) ? "barcodeBreak" : ""} px-0`} style={{
                                                width: `${LabelBarcodeDetails?.width}mm`,
                                                height: `${LabelBarcodeDetails?.height}mm`,
                                                display: "flex",
                                                justifyContent: "center",
                                                alignItems: "flex-start",
                                                flexDirection: "column",
                                                boxSizing: "border-box",
                                                overflow: "visible",
                                                textAlign: "center",
                                            }}>
                                        <div className={` barcode-card-print d-flex  justify-content-center align-items-start h-100 last:`}>
                                            <div className="barcode-content-print w-100 text-center d-flex align-items-center h-100 justify-content-start" style={{ flexDirection: "column"}}>
                                                {headerCheckboxes?.header && item.header?.length > 0 && (() => {
                                                    let content =
                                                    item.header === "custom_text"
                                                        ? item?.custom_text_header
                                                        : item.header === "mrp"
                                                            ? `MRP : ${item[item.header] || ""}`
                                                            : item.header === "selling_price_with_gst"
                                                                ? `RATE : ${item[item.header] || ""}`
                                                                : item[item.header] || "";
                                                    const baseSize = 12;
                                                    let fontSize = baseSize;
                                                    if (content.length > 56) {
                                                        fontSize -= 5;
                                                    } else if (content.length > 27) {
                                                        fontSize -= 3;
                                                    } else if (content.length > 22) {
                                                        fontSize -= 2;
                                                    }
                                                    return <p className={` barcodeHeader-label text-black mb-1 barcode_header`} style={{ fontSize: `${fontSize}px`, wordBreak: "break-all", lineHeight: "1.1" }}>{content}</p>;
                                                })()}
                                                <div className="barcode-image-height h-100 w-100 flex-grow-1">
                                                    <img src={item.item_barcode_url}
                                                        alt="Barcode"
                                                        className={` barcode_image w-100 h-100`}
                                                    />
                                                </div>
                                                {headerCheckboxes?.sku && <div className={` barcodesku-label barcode-number fw-7 text-black barcode_sku mb-1`}
                                                >{item.sku}</div>}
                                                {headerCheckboxes?.line1 && item.line1?.length > 0 && <p className={` barcodeline1_label fw-5 text-black text-center mb-1 barcode_line`}
                                                >{item.line1 === "custom_text" ? item?.custom_text_line1 : item.line1 === "mrp" ? `MRP : ${item[item.line1] || ""}` : item.line1 === "selling_price_with_gst" ? `RATE : ${item[item.line1] || ""}` : item[item.line1]}</p>}
                                                {(item.line2?.length > 0 || item.line3?.length > 0) && <div className={`${headerCheckboxes.line2 && headerCheckboxes.line3 ? "justify-content-between print_justify_between" : "justify-content-center print_justify_center"} d-flex  w-100 barcode_lines`}>
                                                    {headerCheckboxes?.line2 && <p className={` barcodeline2_label lh-1 fw-5 text-black text-center barcode_line mb-1`}
                                                    >{item.line2 === "custom_text" ? item?.custom_text_line2 : item?.line2 === "mrp" ? `MRP : ${item[item.line2] || ""}` : item?.line2 === "selling_price_with_gst" ? `RATE : ${item[item.line2] || ""}` : item[item.line2]}</p>}
                                                    {headerCheckboxes?.line3 && <p className={` barcodeline2_label lh-1 fw-5 text-black text-center barcode_line mb-1`}
                                                    >{item.line3 === "custom_text" ? item?.custom_text_line3 : item?.line3 === "mrp" ? `MRP : ${item[item.line3] || ""}` : item?.line3 === "selling_price_with_gst" ? `RATE : ${item[item.line3] || ""}` : item[item.line3]}</p>}
                                                </div>}
                                                {headerCheckboxes?.line4 && item.line4?.length > 0 && <p className={`
                                                           barcodeline4_label lh-1 fw-5 text-black text-center barcode_line mb-0`}
                                                >{item.line4 === "custom_text" ? item?.custom_text_line4 : item?.line4 === "mrp" ? `MRP : ${item[item.line4] || ""}` : item?.line4 === "selling_price_with_gst" ? `RATE : ${item[item.line4] || ""}` : item[item.line4]}</p>}
                                            </div>
                                        </div>
                                    </div>
                                        </div>
                                    )
                                })
                                }
                            </div>
                        )}
                    </div>
                </Modal.Body>
            </Modal>
            {isPrintSettings && <PrintBarcodeSettingsModal
                isPrintSettings={isPrintSettings}
                setIsPrintSettings={setIsPrintSettings}
                isBorder={isBorder}
                setIsBorder={setIsBorder}
                setBarcodeSize={setBarcodeSize}
                barcodeSize={barcodeSize}
                maxMarginLimit={maxMarginLimit}
                maxCombinedMarginLimit={maxCombinedMarginLimit}
                setMaxCombinedMarginLimit={setMaxCombinedMarginLimit}
                labelView={labelView}
                LabelBarcodeDetails={LabelBarcodeDetails}
            />}
        </>
    )
}

export default PrintBarcodeModal
