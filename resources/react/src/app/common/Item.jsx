import React, { memo, useCallback, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
    calculatePriceWithoutGst,
    calculateTotal,
    convertToZeroStringWithOne,
    customToFixed,
    formattedDate,
    getCalculatedQuantity,
    updateCustomFieldCalculation,
} from "../../shared/calculation";
import ReactSelect from "../../components/ui/ReactSelect";
import { LedgerType, TABLE_HEADER_TYPE, WARRANTY_FIELD_TYPE } from "../../constants";
import {
    fetchLedgerById,
    fetchLedgerGroupList,
    getLedgerModelDetail,
} from "../../store/ledger/ledgerSlice";
import {
    fetchItemById,
    fetchItemList,
    fetchPriceListOfItems,
    getItemModelDetail,
} from "../../store/item/itemSlice";
import { fetchGstRateList } from "../../store/rate/rateSlice";
import { negativeStock } from "../../store/configuration/configurationSlice";
import PlusCircle from "../../assets/images/svg/PlusCircle";
import CustomFieldDate from "../../components/ui/CustomFieldDate";
import { handleShortcutKeys } from "../../shared/shortcut-keys";
import moment from "moment";

const Item = memo(
    ({
        item,
        index,
        id,
        onUpdate,
        tableHeader,
        itemType,
        openItemModel,
        ledgerList,
        ledgerDefaultOptions,
        handleOpenSecond,
        setLedgerModel,
        itemOption,
        itemDefaultOption,
        gstOptions,
        itemUnitOption,
        changeTax,
        setQuantityId,
        openQuantityModel,
        saleConfiguration,
        setIsEditCalculation,
        setCheckGroupLedgerType,
        openIsNegativeStockModel,
        ledgerId,
        setIsConfirmed,
        classification,
        isPurchase,
        itemRef,
        ledgerRef,
        unitRef,
        quantityRef,
        totalRef,
        mrpRef,
        amountRef,
        priceWithGstRef,
        discountTypeRef,
        discountTypeRef2,
        discountValueRef,
        discountValueRef2,
        gstRef,
        setCreateNewItem,
        userPermission,
        setNotExistItems,
        setisFieldsChanges,
        isRecurring,
        setIsDescription,
        setNotExistParty,
        setPartyTitle,
        setItemIndex,
        recurringInvoiceDetails,
        getCustomFieldTransactionType,
        addNewLineItem,
        isItemRequired=true,
        handleOpenInventoryDetail,
        isInWord,
        setStorePriceListDetail
    }) => {
        const [itemState, setItemState] = useState(item);
        const [descriptionCount, setDescriptionCount] = useState(0);
        const [isFocused, setIsFocused] = useState(false);
        const { company } = useSelector(selector => selector);
        const [typingTimeout, setTypingTimeout] = useState(0);
        const isAdditionalDescriptionUpdate = item?.isAdditionalDescriptionUpdate ?? true;

        useEffect(() => {
            setItemState(item);
        }, [item, changeTax, itemType]);

        useEffect(() => {
            setDescriptionCount(0);
        }, [itemType]);

        const handleFocus = () => setIsFocused(true);
        const handleBlur = () => setIsFocused(false);

        const dispatch = useDispatch();

        const handleKeyDown = (event, nextRef, currentRef, type) => {
            if (event.key === "Enter" && nextRef && nextRef.current) {
                event.preventDefault();
                if (currentRef?.current?.getFocusedOption) {
                    let focusedOption = currentRef?.current?.getFocusedOption();
                    if (type === "item" && focusedOption) {
                        handleItemChange(focusedOption);
                    } else if (type === "ledger" && focusedOption) {
                        handleLedgerChange(focusedOption?.value, focusedOption);
                    } else if (type === "unit" && focusedOption) {
                        handleUnitChange(focusedOption);
                    } else if (type === "amount" && focusedOption) {
                        handlePriceWithoutGstChange(focusedOption);
                    } else if (type === "discount_1" && focusedOption) {
                        handleDiscountTypeChange(focusedOption, "discount_1");
                    } else if (type === "discount_2" && focusedOption) {
                        handleDiscountTypeChange(focusedOption, "discount_2");
                    } else if (type === "gst" && focusedOption) {
                        handleGstChange(focusedOption);
                    }
                }
                nextRef.current.focus();
            }
        };

        const openNegativeModelHandler = (value, data) => {
            if (
                saleConfiguration?.item_table_configuration?.warn_on_negative_stock &&
                data?.selectedLedger
            ) {
                dispatch(
                    negativeStock(data?.selectedItem, data?.quantity, openIsNegativeStockModel)
                );
            }
        };

        const openQuantityModelHandler = id => {
            setQuantityId(id);
            openQuantityModel();
        };

        const handleOpenInventoryModal = useCallback( async () =>{
            handleOpenInventoryDetail(itemState, index);
        }, [itemState, index]);

        const handleItemChange = useCallback(
            async selectedOption => {
                setisFieldsChanges(true);

                const selectedData = parseInt(selectedOption?.value);
                const transaction_item_id = itemState?.selectedItem == selectedData ? itemState?.transaction_item_id : null
                if (selectedOption.__isNew__) {
                    return setCreateNewItem({ name: selectedOption?.value, index: index });
                }
                addNewLineItem(id, selectedData);
                setIsEditCalculation(true);
                // dispatch(fetchSingleItemById(selectedData));
                const priceListDetail = await dispatch(fetchPriceListOfItems([selectedData], ledgerId, null, setIsConfirmed, getCustomFieldTransactionType, transaction_item_id));
                const selectedItemData = itemOption.find(item => item?.value == selectedData);
                if (selectedItemData) {
                    const updatedItem = {
                        ...itemState,
                        selectedItem: selectedData,
                        notExistsItems: false,
                        quantity: 1,
                    };
                    const calculatedTotal = calculateTotal(
                        updatedItem,
                        false,
                        changeTax,
                        itemType === "accounting"
                    );
                    setStorePriceListDetail(null);
                    setItemState(updatedItem);
                    onUpdate(id, updatedItem, index);
                    // if (
                    //     saleConfiguration?.header?.is_change_gst_details &&
                    //     company?.company?.is_gst_applicable
                    // ) {
                        handleOpenSecond(priceListDetail, index);
                    // }
                }
            },
            [itemState, id, saleConfiguration, changeTax, itemOption, ledgerId, company, index]
        );

        useEffect(() => {
            setDescriptionCount(itemState?.additional_description?.length || 0);
        }, [itemState?.additional_description]);

        const handleLedgerChange = useCallback(
            (selectedOption, option) => {
                if(itemType === "accounting" && selectedOption){
                    const selectedData = parseInt(selectedOption);
                    addNewLineItem(id, selectedData);
                }
                const updatedItem = {
                    ...itemState,
                    selectedLedger: selectedOption,
                    notExistsLedger: false,
                    additional_description:
                        itemType === "accounting"
                            ? option?.description || ""
                            : itemState?.additional_description,
                };
                setIsEditCalculation(true);
                if (itemType === "accounting") {
                    updatedItem.gst = option?.gst || 0;
                    updatedItem.gst_id = option?.gst_id;
                    updatedItem.additional_description = updatedItem.additional_description;
                    updatedItem.cessRate = option?.cess_rate ? option?.cess_rate : 0;
                }
                const calculatedTotal = calculateTotal(
                    updatedItem,
                    false,
                    changeTax,
                    itemType === "accounting"
                );
                updatedItem.total = calculatedTotal.total;
                updatedItem.updatedTotal = calculatedTotal.total;
                updatedItem.sgstValue = calculatedTotal.sgst;
                updatedItem.cgstValue = calculatedTotal.sgst;
                updatedItem.cessValue = calculatedTotal.cess;
                setItemState(updatedItem);
                onUpdate(id, updatedItem, index);
                if (
                    saleConfiguration?.header?.is_change_gst_details &&
                    selectedOption &&
                    company?.company?.is_gst_applicable
                ) {
                    handleOpenSecond();
                }
            },
            [itemState, onUpdate, id, saleConfiguration, changeTax, index, itemType, addNewLineItem]
        );

        const handleUnitChange = useCallback(
            selectedUnit => {
                if (!selectedUnit.value) {
                    return;
                }
                const updatedItem = {
                    ...itemState,
                    selectedUnit: selectedUnit.value,
                };
                const conversationRate = itemState?.conversationRate;
                if (itemState.secondaryUnitOfMeasurement == selectedUnit.value) {
                    const updatedPrice = (changeTax ? itemState.rateWithGst : itemState.rateWithoutGst) / conversationRate;
                    updatedItem.oldUpdatedRate = changeTax ? itemState.rateWithGst : itemState.rateWithoutGst;
                    updatedItem.updatedRateWithoutGst = customToFixed(
                        updatedPrice,
                        itemState?.decimal_places_for_rate || 2
                    );
                } else {
                    updatedItem.updatedRateWithoutGst = updatedItem.oldUpdatedRate * 1;
                }
                const calculatedTotal = calculateTotal(
                    updatedItem,
                    false,
                    changeTax,
                    itemType === "accounting"
                );
                updatedItem.total = calculatedTotal.total;
                updatedItem.updatedTotal = calculatedTotal.total;
                updatedItem.sgstValue = calculatedTotal.sgst;
                updatedItem.cgstValue = calculatedTotal.sgst;
                updatedItem.cessValue = calculatedTotal.cess;
                setIsEditCalculation(true);
                setItemState(updatedItem);
                onUpdate(id, updatedItem, index);
            },
            [itemState, onUpdate, id, changeTax, index]
        );

        const handleQuantityChange = useCallback(
            quantity => {
                if (saleConfiguration?.item_table_configuration?.consolidating_items_to_invoice) {
                    return quantityRef?.current?.blur();
                }

                const updatedItem = {
                    ...itemState,
                    quantity: parseFloat(quantity),
                    is_change_quantity: true,
                };
                setIsEditCalculation(true);
                const calculatedTotal = calculateTotal(
                    updatedItem,
                    false,
                    changeTax,
                    itemType === "accounting"
                );
                    (updatedItem.total = calculatedTotal.total),
                    (updatedItem.updatedTotal = calculatedTotal.total),
                    (updatedItem.sgstValue = calculatedTotal.sgst),
                    (updatedItem.cgstValue = calculatedTotal.sgst),
                    (updatedItem.cessValue = calculatedTotal.cess),
                    setItemState(updatedItem),
                    onUpdate(id, updatedItem, index);
            },
            [itemState, onUpdate, id, changeTax, saleConfiguration, index]
        );
        const handleFreeQuantityChange = useCallback(
            quantity => {
                const parsedQuantity = parseFloat(quantity);
                const updatedItem = {
                    ...itemState,
                    free_quantity: isNaN(parsedQuantity) ? 0 : parsedQuantity,
                };
                setIsEditCalculation(true);
                setItemState(updatedItem),
                onUpdate(id, updatedItem, index);
            },
            [itemState, onUpdate, id, changeTax, saleConfiguration, index]
        );

        const handleGstChange = useCallback(
            selectedGst => {
                setisFieldsChanges(true);
                const updatedItem = {
                    ...itemState,
                    gst_id: selectedGst.value,
                    gst: selectedGst.rate,
                };
                setIsEditCalculation(true);
                const calculatedTotal = calculateTotal(
                    updatedItem,
                    false,
                    changeTax,
                    itemType === "accounting",
                    false,
                    classification.rcm_applicable,
                    isPurchase
                );
                updatedItem.total = calculatedTotal.total;
                updatedItem.updatedTotal = calculatedTotal.total;
                updatedItem.sgstValue = calculatedTotal.sgst;
                updatedItem.cgstValue = calculatedTotal.sgst;
                updatedItem.cessValue = calculatedTotal.cess;
                setItemState(updatedItem);
                onUpdate(id, updatedItem, index);
            },
            [itemState, onUpdate, id, changeTax, classification, index]
        );

        const handlePriceWithGstChange = useCallback(
            (price, index) => {
                const parsedPrice = parseFloat(price);
                setIsEditCalculation(true);
                // if (!isNaN(parsedPrice)) {
                const updatedItem = {
                    ...itemState,
                    mrp: isNaN(parsedPrice) ? null : parsedPrice,
                    rateWithGst: isNaN(parsedPrice) ? 0 : parsedPrice,
                    updatedRateWithGst: isNaN(parsedPrice) ? 0 : parsedPrice,
                    amountWithGst: isNaN(parsedPrice) ? 0 : parsedPrice,
                    amountWithoutGst: isNaN(parsedPrice) ? 0 : parsedPrice,
                    rateWithoutGst: calculatePriceWithoutGst(parsedPrice, itemState.gst),
                };
                const calculatedTotal = calculateTotal(
                    updatedItem,
                    false,
                    changeTax,
                    itemType === "accounting"
                );
                updatedItem.total = calculatedTotal.total;
                updatedItem.updatedTotal = calculatedTotal.total;
                updatedItem.sgstValue = calculatedTotal.sgst;
                updatedItem.cgstValue = calculatedTotal.sgst;
                updatedItem.cessValue = calculatedTotal.cess;
                setItemState(updatedItem);
                onUpdate(id, updatedItem, index);
                // }
            },
            [itemState, onUpdate, id, changeTax, index]
        );
        const handleChangeHsnSac = useCallback(
            (value, index) => {
                setIsEditCalculation(true);
                const updatedItem = {
                    ...itemState,
                    hsn_code: value,
                };
                setItemState(updatedItem);
                onUpdate(id, updatedItem, index);
            },
            [itemState, onUpdate, id, changeTax, index]
        );

        const handlePriceWithoutGstChange = useCallback(
            price => {
                const parsedPrice = parseFloat(price);
                setIsEditCalculation(true);
                // if (!isNaN(parsedPrice)) {
                const updatedItem = {
                    ...itemState,
                    ...(changeTax ? { rateWithGst: parsedPrice } : { rateWithoutGst: parsedPrice }),
                    updatedRateWithoutGst: parsedPrice,
                    updatedRateWithGst: parsedPrice,
                    rateWithoutGst: parsedPrice,
                    amountWithoutGst: parsedPrice,
                    updatedAmountWithoutGst: parsedPrice,
                };
                const calculatedTotal = calculateTotal(
                    updatedItem,
                    false,
                    changeTax,
                    itemType === "accounting"
                );
                if(updatedItem?.item_decimal_places){
                    updatedItem.decimal_places = itemState?.item_decimal_places;
                }
                if(updatedItem?.item_decimal_places_for_rate){
                    updatedItem.decimal_places_for_rate = itemState?.item_decimal_places_for_rate;
                }
                updatedItem.total = calculatedTotal.total;
                updatedItem.updatedTotal = calculatedTotal.total;
                updatedItem.sgstValue = calculatedTotal.sgst;
                updatedItem.cgstValue = calculatedTotal.sgst;
                updatedItem.cessValue = calculatedTotal.cess;
                setItemState(updatedItem);
                onUpdate(id, updatedItem, index);
                // }
            },
            [itemState, onUpdate, id, changeTax, index]
        );

        const handleDiscountTypeChange = useCallback(
            (selectedType, type) => {
                setisFieldsChanges(true);
                setIsEditCalculation(true);

                let { updatedRateWithoutGst, with_tax, gst } = itemState;

                // Adjust price if tax is included
                if (with_tax) {
                    updatedRateWithoutGst -= (updatedRateWithoutGst * gst) / (100 + gst);
                }

                let newDiscountValue = parseFloat(itemState.discountValue);
                let newDiscountValue2 = parseFloat(itemState.discountValue_2);

                if (type === "discount_1") {
                    newDiscountValue = 0;
                } else {
                    newDiscountValue2 = 0;
                }
                if (newDiscountValue >= updatedRateWithoutGst) {
                    newDiscountValue = updatedRateWithoutGst;
                    newDiscountValue2 = 0;
                }

                let remainingAmount = updatedRateWithoutGst - newDiscountValue;
                if (newDiscountValue2 > remainingAmount) {
                    newDiscountValue2 = 0;
                }

                const updatedItem = {
                    ...itemState,
                    ...(type === "discount_1"
                        ? {
                              discountType: selectedType.value,
                              discountValue:
                                  selectedType.value === 2 && newDiscountValue > 100
                                      ? 100
                                      : newDiscountValue,
                              discountValue_2: newDiscountValue2,
                          }
                        : {
                              discountType_2: selectedType.value,
                              discountValue_2: newDiscountValue2,
                          }),
                };

                const calculatedTotal = calculateTotal(
                    updatedItem,
                    false,
                    changeTax,
                    itemType === "accounting"
                );
                updatedItem.total = calculatedTotal.total;
                updatedItem.updatedTotal = calculatedTotal.total;
                updatedItem.sgstValue = calculatedTotal.sgst;
                updatedItem.cgstValue = calculatedTotal.sgst;
                updatedItem.cessValue = calculatedTotal.cess;

                setItemState(updatedItem);
                onUpdate(id, updatedItem, index);
            },
            [itemState, onUpdate, id, changeTax, index]
        );

        const handleDiscountValueChange = useCallback(
            (discountValue, type) => {
                setIsEditCalculation(true);

                let sanitizedValue = String(discountValue).replace(/[^0-9.]/g, "");
                discountValue = parseFloat(sanitizedValue);
                if (isNaN(discountValue) || discountValue < 0) discountValue = null;

                let {
                    discountValue: discount1,
                    discountValue_2: discount2,
                    discountType,
                    discountType_2,
                    updatedRateWithoutGst,
                    with_tax,
                    gst,
                } = itemState;
                if (isNaN(discount1)){
                    discount1 = 0;
                }
                if (isNaN(discount2)){
                    discount2 = 0;
                }
                if (with_tax) {
                    updatedRateWithoutGst =
                        updatedRateWithoutGst - (updatedRateWithoutGst * gst) / (100 + gst);
                }

                // Ensure percentage values do not exceed 100
                if (
                    (type === "discount_1" && discountType === 2) ||
                    (type === "discount_2" && discountType_2 === 2)
                ) {
                    if (discountValue > 100) {
                        discountValue = 100;
                    }
                }

                let newDiscount1 =
                    discountType === 2 ? (discount1 * updatedRateWithoutGst) / 100 : discount1;
                let newDiscount2 =
                    discountType_2 === 2
                        ? (discount2 * (updatedRateWithoutGst - newDiscount1)) / 100
                        : discount2;
                if (type === "discount_1") {
                    if (discountType === 2 && discountValue === 100) {
                        discount2 = 0;
                        newDiscount2 = 0;
                    }
                    newDiscount1 =
                        discountType === 2
                            ? (discountValue * updatedRateWithoutGst) / 100
                            : discountValue;
                    // newDiscount1 = discountValue;
                } else {
                    if (discountType_2 === 2 && discountValue > 100) {
                        discountValue = 100;
                    }
                    newDiscount2 =
                        discountType_2 === 2
                            ? (discountValue * (updatedRateWithoutGst - newDiscount1)) / 100
                            : discountValue;
                }

                // Ensure total discount does not exceed the price
                let totalDiscount = newDiscount1 + newDiscount2;
                if (totalDiscount > updatedRateWithoutGst) {
                    if (type === "discount_1") {
                        discountValue =
                            discountType === 2
                                ? ((updatedRateWithoutGst - newDiscount2) * 100) /
                                  updatedRateWithoutGst
                                : updatedRateWithoutGst - newDiscount2;
                        newDiscount1 =
                            discountType === 2
                                ? (discountValue * updatedRateWithoutGst) / 100
                                : discountValue;
                    } else {
                        discountValue =
                            discountType_2 === 2
                                ? ((updatedRateWithoutGst - newDiscount1) * 100) /
                                  updatedRateWithoutGst
                                : updatedRateWithoutGst - newDiscount1;
                        newDiscount2 =
                            discountType_2 === 2
                                ? (discountValue * (updatedRateWithoutGst - newDiscount1)) / 100
                                : discountValue;
                    }
                }

                const updatedItem = {
                    ...itemState,
                    discountValue: type === "discount_1" ? discountValue : discount1,
                    discountValue_2: type === "discount_2" ? discountValue : discount2,
                };
                const calculatedTotal = calculateTotal(
                    updatedItem,
                    false,
                    changeTax,
                    itemType === "accounting"
                );
                updatedItem.total = calculatedTotal.total;
                updatedItem.updatedTotal = calculatedTotal.total;
                updatedItem.sgstValue = calculatedTotal.sgst;
                updatedItem.cgstValue = calculatedTotal.sgst;
                updatedItem.cessValue = calculatedTotal.cess;

                setItemState(updatedItem);
                onUpdate(id, updatedItem, index);
            },
            [itemState, onUpdate, id, changeTax, index]
        );

        const handleQuantityModel = (e, id) => {
            e.target.select();
            if (saleConfiguration?.item_table_configuration?.consolidating_items_to_invoice) {
                quantityRef?.current?.blur();
                openQuantityModelHandler(id);
            }
        };

        const handleOpenItemModal = (id, index) => {
            if (id) {
                dispatch(fetchItemById(id));
            }
            openItemModel(id, index);
            dispatch(getItemModelDetail());
            dispatch(fetchGstRateList());
        };

        const handleOpenLedger = id => {
            setLedgerModel(id, index);
            dispatch(fetchLedgerGroupList());
            dispatch(getLedgerModelDetail(LedgerType.ITEM_LEDGER));
            setCheckGroupLedgerType("item");
            dispatch(fetchLedgerById(id));
        };

        const handleDiscription = async () => {
            if (isRecurring && isAdditionalDescriptionUpdate && recurringInvoiceDetails?.frequency_details?.repeat_frequency !== "Custom") {
                setIsDescription(true);
                setQuantityId(index);
            }
        };

        const handleChangeDescription = e => {
            const updatedItem = {
                ...itemState,
                additional_description: e,
            };

            setDescriptionCount(e.length);
            itemState.additional_description = e;
            onUpdate(id, updatedItem, index);
        };

        const handleChangeCustomField = (value, column, type) => {
        const updatedValue = value;

        // Step 1: Update the changed custom field
        let updatedCustomFields = Array.isArray(itemState?.custom_fields)
            ? itemState.custom_fields.some(
                item => item.custom_field_id == column.custom_field_id
            )
                ? itemState.custom_fields.map(item =>
                    item.custom_field_id == column.custom_field_id
                        ? {
                                ...item,
                                ...(type === 'warranty' ? { field_type: updatedValue || "" } : { value: (type == 'number' ? parseFloat(updatedValue) : updatedValue) || "" }),
                            ...(type === 'select' && { option_id: updatedValue || "" }),
                        }
                      : item
              )
            : [
                  ...itemState.custom_fields,
                  {
                      custom_field_id: column.custom_field_id,
                      ...(type !== 'warranty' && type !== 'select' && {value: (type == 'number' ? parseFloat(updatedValue) : updatedValue) || ""}),
                      ...(type === 'warranty' && { field_type: updatedValue || "" }),
                      ...(type === 'select' && { option_id: updatedValue || "" }),
                  },
              ]
            : [
                {
                    custom_field_id: column.custom_field_id,
                    ...(type !== 'warranty' && type !== 'select' && {value: (type == 'number' ? parseFloat(updatedValue) : updatedValue) || ""}),
                    ...(type === 'warranty' && { field_type: updatedValue || "" }),
                    ...(type === 'select' && { option_id: updatedValue || "" }),
                },
            ];

            // Step 2: Recalculate all formula-based custom fields
            const updatedCustomFieldForCalculations = updateCustomFieldCalculation(updatedCustomFields);
            updatedCustomFields = updatedCustomFieldForCalculations;

            // Step 3: Recalculate quantity from formula (if exists)
            const newQuantity = getCalculatedQuantity(updatedCustomFieldForCalculations);
            const decimalPlaces = itemState?.item_master?.decimal_places ?? itemState?.decimal_places ?? 2;
            // Step 4: Final merged item
            const updatedItem = {
                ...itemState,
                is_change_quantity: false,
                custom_fields: updatedCustomFields,
                quantity:
                    newQuantity != null
                        ? parseFloat(newQuantity || 1).toFixed(decimalPlaces)
                        : itemState.quantity || 1,
            };
            setIsEditCalculation(true);
            const calculatedTotal = calculateTotal(
                updatedItem,
                false,
                changeTax,
                itemType === "accounting"
            );
            (updatedItem.total = calculatedTotal.total),
            (updatedItem.updatedTotal = calculatedTotal.total),
            (updatedItem.sgstValue = calculatedTotal.sgst),
            (updatedItem.cgstValue = calculatedTotal.sgst),
            (updatedItem.cessValue = calculatedTotal.cess),
            setItemState(updatedItem),
            // Step 5: Pass to parent handler
            onUpdate(id, updatedItem, index);
        };

        const handleChangeDate = (date, column, input_type) =>{
            if(input_type !== "date" && input_type !== "datetime") return
            const isValidDate = date && !isNaN(new Date(date).getTime());
            const formattedDateValue = isValidDate ? input_type == "datetime" ? moment(new Date(date)).format("DD-MM-YYYY HH:mm") : formattedDate(new Date(date)) : "";
            const updatedItem = {
                ...itemState,
                custom_fields: Array.isArray(itemState?.custom_fields)
                    ? itemState.custom_fields.some(item => item.custom_field_id == column.custom_field_id)
                        ? itemState.custom_fields.map(item =>
                            item.custom_field_id == column.custom_field_id
                                ? { ...item, value: formattedDateValue }
                                : item
                        )
                        : [...itemState.custom_fields, { custom_field_id: column.custom_field_id, value: formattedDateValue }]
                    : [{ custom_field_id: column.custom_field_id, value: formattedDateValue }]
            };
                onUpdate(id, updatedItem, index);
        }

        const shortcutItem = event => {
            if (event.altKey && event.keyCode == 67) {
                return handleOpenItemModal();
            }
        };
        const shortcutLedger = event => {
            if (event.altKey && event.keyCode == 76) {
                return handleOpenLedger();
            }
        };
        handleShortcutKeys(shortcutItem);
        handleShortcutKeys(shortcutLedger);

        const handleScroll = () => {
            dispatch(fetchItemList({ skip: itemOption.length }));
        };

        const customFilter = searchText => {
            if (!searchText) {
                return;
            }
            if (typingTimeout) {
                clearTimeout(typingTimeout);
            }
            setTypingTimeout(
                setTimeout(() => dispatch(fetchItemList({ search: searchText })), 500)
            );
        };

        const createNotExistsItem = itemState => {
            if (id) {
                dispatch(fetchItemById(id));
            }
            if (setNotExistItems && itemState) {
                setNotExistItems(itemState);
            }
            openItemModel(id, index);
            dispatch(getItemModelDetail());
            dispatch(fetchGstRateList());
        };

        const createNotExistsParty = useCallback((isPartyNameExist) => {
            dispatch(fetchLedgerGroupList());
            // openLedgerModel();
            setPartyTitle({
                name: "Add Ledger",
                id: "",
            });
            setNotExistParty(isPartyNameExist)
            dispatch(getLedgerModelDetail(LedgerType.ITEM_LEDGER));
            setCheckGroupLedgerType("item");
            setItemIndex(index);
            setLedgerModel("", index);
        },
        []
    );

        const getSelectWidth = () => {
            if (typeof window !== 'undefined') {
                if (window.innerWidth <= 600) {
                    return 'calc(100vw - 100px)';
                } else {
                    return "490px";
                }
            }
            return '490px';
        };
        const [selectWidth, setSelectWidth] = useState(getSelectWidth);

        useEffect(() => {
            const handleResize = () => setSelectWidth(getSelectWidth());

            window.addEventListener('resize', handleResize);
            return () => window.removeEventListener('resize', handleResize);
        }, []);
        const checkIsSingleOrMultiInventory = itemState?.model_custom_fields?.[0]?.custom_field_type;

        const renderCell = column => {
            switch (column.header) {
                case TABLE_HEADER_TYPE.ITEM:
                    return (
                        <td key={column.id}>
                            {itemState?.notExistsItems && (
                                <div className="not_exist_item">
                                    <span className="not_exist_item_text">
                                        {itemState?.selectedItem}
                                    </span>
                                    <span
                                        className="item_plus cursor-pointer"
                                        onClick={() => createNotExistsItem(itemState)}
                                    >
                                        <PlusCircle />
                                    </span>
                                    {/* <span className="item_plus" onClick={handleOpenItemModal} ><PlusCircle /></span> */}
                                </div>
                            )}
                            <div id="TransactionForm" className="select-ledger-input mb-2 transaction-form d-flex align-items-center justify-content-between" style={{ minWidth: "160px" }}>
                                <div className="input-group flex-nowrap">
                                    <div
                                        className={`position-relative h-40px w-100 border-transparent focus-shadow ${
                                            userPermission?.add_new_item_masters ? "pe-36px" : ""
                                        }`}
                                    >
                                        <ReactSelect
                                            value={
                                                !itemState?.notExistsItems
                                                    ? itemState?.selectedItem || null
                                                    : null
                                            }
                                            index={index}
                                            onChange={handleItemChange}
                                            customFilter={customFilter}
                                            onMenuScrollToBottom={handleScroll}
                                            options={itemState?.is_status == 0 ? itemDefaultOption : itemOption}
                                            customLabel="item"
                                            styles={{
                                                container: provided => ({
                                                    ...provided,
                                                    minWidth: "230px",
                                                }),
                                            }}
                                            placeholder={"Select item"}
                                            defaultLabel=""
                                            onKeyDown={e =>
                                                handleKeyDown(e, ledgerRef, itemRef, "item")
                                            }
                                            ref={itemRef}
                                            islabel={true}
                                            required={isItemRequired}
                                            portal={true}
                                            radius={true}
                                            showborder={false}
                                            width={getSelectWidth()}
                                            isEdit={userPermission?.edit_item_masters}
                                            handleOpen={handleOpenItemModal}
                                        />
                                    </div>
                                    {userPermission?.add_new_item_masters ? (
                                        <a
                                            onClick={() => handleOpenItemModal("", index)}
                                            className="input-group-text custom-group-text cursor-pointer"
                                            data-ledger-link-id="1"
                                            data-bs-toggle="tooltip"
                                            data-bs-placement="bottom"
                                            title="Shortcut Key : Alt + C"
                                        >
                                            <i className="fas fa-plus text-gray-900"></i>
                                        </a>
                                    ) : (
                                        ""
                                    )}
                                </div>
                            </div>
                            {saleConfiguration?.item_table_configuration
                                ?.is_additional_item_description ? (
                                <div className="position-relative">
                                    <textarea
                                        className="form-control border-transparent overflow-hidden pe-8"
                                        style={{
                                            fontStyle: "italic",
                                            fontSize: "12px",
                                            color: "#333333",
                                        }}
                                        placeholder="Additional Description For Item"
                                        value={itemState?.additional_description ?? ""}
                                        onChange={e => handleChangeDescription(e.target.value)}
                                        maxLength={5000}
                                        onClick={handleDiscription}
                                        onFocus={handleFocus}
                                        onBlur={handleBlur}
                                        rows={1}
                                        disabled={!isAdditionalDescriptionUpdate}
                                    />
                                    {isFocused && (
                                        <p
                                            className="position-absolute bottom-0 right-0 mb-0 end-0 fs-12 pt-1 bootstrap-maxlength badge text-primary limit-textarea"
                                            style={{ whiteSpace: "nowrap" }}
                                        >
                                            {5000 - descriptionCount || 0}
                                        </p>
                                    )}
                                </div>
                            ) : null}
                        </td>
                    );
                case TABLE_HEADER_TYPE.LEDGER:
                    return (
                        <td key={column.id}>
                            {itemState?.notExistsLedger && <div className="not_exist_item">
                                <span className="not_exist_item_text">{itemState?.selectedLedger}</span>
                                <span className="item_plus cursor-pointer" onClick={() => createNotExistsParty(itemState?.selectedLedger)} ><PlusCircle /></span>
                            </div>
                            }
                            <div className="select-ledger-input mb-2" style={{ minWidth: "160px" }}>
                                <div className="input-group flex-nowrap">
                                    <div
                                        className={`position-relative h-40px w-100 border-transparent focus-shadow ${
                                            userPermission?.add_ledger_master ? "pe-36px" : ""
                                        }`}
                                    >
                                        <ReactSelect
                                            value={
                                                !itemState?.notExistsLedger
                                                    ? itemState?.selectedLedger || null
                                                    : null
                                            }
                                            onChange={option => {
                                                setisFieldsChanges(true);
                                                handleLedgerChange(option.value, option);
                                            }}
                                            required={(isItemRequired || itemState?.selectedItem) && !itemState?.selectedLedger}
                                            options={itemState?.is_status == 0 ? ledgerDefaultOptions : ledgerList}
                                            ref={ledgerRef}
                                            onKeyDown={e =>
                                                handleKeyDown(
                                                    e,
                                                    itemType === "accounting" ? amountRef : unitRef,
                                                    ledgerRef,
                                                    "ledger"
                                                )
                                            }
                                            islabel="no"
                                            styles={{
                                                container: provided => ({
                                                    ...provided,
                                                    minWidth: "200px",
                                                }),
                                            }}
                                            placeholder={"Select ledger"}
                                            defaultLabel={"Select ledger"}
                                            radius={true}
                                            showborder={false}
                                            isEdit={userPermission?.edit_ledger_master}
                                            handleOpen={handleOpenLedger}
                                        />
                                    </div>
                                    {userPermission?.add_transport_master ? (
                                        <a
                                            onClick={() => handleOpenLedger("")}
                                            className="input-group-text custom-group-text cursor-pointer"
                                            data-ledger-link-id="1"
                                            data-bs-toggle="tooltip"
                                            data-bs-placement="bottom"
                                            title="Shortcut Key : Alt + L"
                                        >
                                            <i className="fas fa-plus text-gray-900"></i>
                                        </a>
                                    ) : (
                                        ""
                                    )}
                                </div>
                            </div>
                            {itemType === "accounting" &&
                            saleConfiguration?.item_table_configuration
                                ?.is_additional_ledger_description ? (
                                <div className="position-relative">
                                    <textarea
                                        className="form-control border-transparent overflow-hidden pe-8"
                                        style={{
                                            fontStyle: "italic",
                                            fontSize: "12px",
                                            color: "#333333",
                                        }}
                                        placeholder="Additional Description For Ledger"
                                        value={itemState?.additional_description ?? ""}
                                        onChange={e => handleChangeDescription(e.target.value)}
                                        onClick={handleDiscription}
                                        // onInput={(e) => {
                                        //     e.target.style.height = "auto";
                                        //     e.target.style.height = `${e.target.scrollHeight}px`;
                                        // }}
                                        maxLength={5000}
                                        onFocus={handleFocus}
                                        onBlur={handleBlur}
                                        rows={1}
                                        disabled={!isAdditionalDescriptionUpdate}
                                    />
                                    {isFocused && (
                                        <p
                                            className="position-absolute bottom-0 right-0 mb-0 end-0 fs-12 pt-1 bootstrap-maxlength badge text-primary limit-textarea"
                                            style={{ whiteSpace: "nowrap" }}
                                        >
                                            {5000 - descriptionCount || 0}
                                        </p>
                                    )}
                                </div>
                            ) : null}
                        </td>
                    );
                case TABLE_HEADER_TYPE.QUANTITY:
                    return (
                        <td key={column.id} style={{ width: "100px" }}>
                            <div className="border-0 d-flex justify-content-between align-items-center">
                                <input
                                    className="form-control text-end h-40px border-transparent focus-shadow"
                                    required={isItemRequired}
                                    step={`0.${convertToZeroStringWithOne(
                                        itemState?.decimal_places
                                    )}`}
                                    type="number"
                                    min={`0.${convertToZeroStringWithOne(
                                        isItemRequired ? itemState?.decimal_places : 0
                                    )}`}
                                    // defaultValue={1}
                                    value={itemState?.quantity != null ? parseFloat(itemState?.quantity) : null}
                                    onChange={e => handleQuantityChange(e.target.value)}
                                    onClick={e => handleQuantityModel(e, index)}
                                    ref={quantityRef}
                                    onKeyDown={e =>
                                        handleKeyDown(
                                            e,
                                            saleConfiguration?.item_table_configuration
                                                ?.is_enabled_mrp
                                                ? mrpRef
                                                : priceWithGstRef,
                                            quantityRef
                                        )
                                    }
                                    islabel="no"
                                    onBlur={e =>
                                        !saleConfiguration?.item_table_configuration
                                            ?.consolidating_items_to_invoice &&
                                        Number(e.target.value) > 0 &&
                                        openNegativeModelHandler(e, itemState)
                                    }
                                    disabled={checkIsSingleOrMultiInventory === 1
                                    ? isInWord ? (itemState?.model_custom_fields?.length > 0 || itemState?.custom_field_inventory_store?.length > 0) : itemState?.model_select_inventory_custom_fields?.length > 0
                                    : isInWord ? (itemState?.model_custom_fields?.length > 0 || itemState?.custom_field_inventory_store?.length > 0) : itemState?.model_select_inventory_custom_fields?.length > 0}
                                />
                                {(isInWord ? (itemState?.model_custom_fields?.length > 0 || itemState?.custom_field_inventory_store?.length > 0) : itemState?.model_select_inventory_custom_fields?.length > 0) ?
                                    <a
                                        onClick={handleOpenInventoryModal}
                                        className="ms-2 input-group-text cursor-pointer inventory_modal_button px-3"
                                        data-ledger-link-id="1"
                                        data-bs-toggle="tooltip"
                                        data-bs-placement="bottom"
                                        title="Shortcut Key : Alt + C"
                                    >
                                        <i className="fas fa-eye text-gray-900"></i>
                                    </a>
                                    : ''}
                            </div>
                        </td>
                    );
                case TABLE_HEADER_TYPE.FREE_QUANTITY:
                    return (
                        <td key={column.id} style={{ width: "100px" }}>
                            <div className="border-0">
                                <input
                                    className="form-control text-end h-40px border-transparent focus-shadow"
                                    type="number"
                                    value={itemState?.free_quantity || null}
                                    onChange={e => handleFreeQuantityChange(e.target.value)}
                                    onClick={e => e.target.select()}
                                />
                            </div>
                        </td>
                    );
                case TABLE_HEADER_TYPE.UOM:
                    return (
                        <td key={column.id} style={{ width: "124px" }}>
                            <div className="border-transparent h-40px min-w-124px focus-shadow input-group d-block">
                                <ReactSelect
                                    value={item?.selectedUnit || null}
                                    onChange={option => {
                                        setisFieldsChanges(true);
                                        handleUnitChange(option, index);
                                    }}
                                    required={isItemRequired}
                                    placeholder={"Select unit"}
                                    defaultLabel={"Select unit"}
                                    options={item?.itemUnitOption || itemUnitOption}
                                    ref={unitRef}
                                    onKeyDown={e => handleKeyDown(e, quantityRef, unitRef, "unit")}
                                    islabel="no"
                                    showborder={false}
                                />
                            </div>
                        </td>
                    );
                case TABLE_HEADER_TYPE.TAX_RATE:
                    return company?.company?.is_gst_applicable ? (
                        <td
                            key={column.id}
                            className="d-block"
                            style={{ maxWidth: "120px", border: "none" }}
                        >
                            <div className="border-transparent h-40px min-w-100px focus-shadow input-group d-block">
                                <ReactSelect
                                    required={isItemRequired}
                                    value={itemState?.gst_id || null}
                                    onChange={handleGstChange}
                                    options={gstOptions}
                                    placeholder={"Select rate"}
                                    defaultLabel={"Select rate"}
                                    islabel="no"
                                    onKeyDown={e => handleKeyDown(e, totalRef, gstRef)}
                                    ref={gstRef}
                                    showborder={false}
                                />
                            </div>
                        </td>
                    ) : null;
                case TABLE_HEADER_TYPE.MRP:
                    return (
                        <td key={column.id} style={{ width: "100px" }}>
                            <div className="border-0">
                                <input
                                    className="form-control h-40px text-end border-transparent overflow-hidden focus-shadow"
                                    // required
                                    step="0.01"
                                    type="number"
                                    // defaultValue={0}
                                    value={itemState?.mrp ?? null}
                                    onChange={e => handlePriceWithGstChange(e.target.value, index)}
                                    onClick={e => e.target.select()}
                                    ref={mrpRef}
                                    onKeyDown={e => handleKeyDown(e, priceWithGstRef, mrpRef)}
                                />
                            </div>
                        </td>
                    );
                case TABLE_HEADER_TYPE.HSN_SAC:
                    return company?.company?.is_gst_applicable ? (
                        <td key={column.id} style={{ width: "100px" }}>
                            <div className="border-0">
                                <input
                                    className="form-control h-40px text-end border-transparent overflow-hidden focus-shadow"
                                    type="text"
                                    value={itemState?.hsn_code ?? null}
                                    onChange={e => handleChangeHsnSac(e.target.value, index)}
                                    onClick={e => e.target.select()}
                                    ref={mrpRef}
                                    onKeyDown={e => handleKeyDown(e, priceWithGstRef, mrpRef)}
                                />
                            </div>
                        </td>
                    ) : "";
                case TABLE_HEADER_TYPE.AMOUNT:
                    return (
                        <td key={column.id} style={{ width: "100px" }}>
                            <div className="border-0">
                                <input
                                    className="form-control h-40px text-end border-transparent overflow-hidden focus-shadow"
                                    required={isItemRequired}
                                    step="0.01"
                                    type="number"
                                    min="0"
                                    defaultValue={0}
                                    value={itemState?.amountWithoutGst ?? null}
                                    onChange={e =>
                                        handlePriceWithoutGstChange(e.target.value, index)
                                    }
                                    onClick={e => e.target.select()}
                                    ref={amountRef}
                                    onKeyDown={e => handleKeyDown(e, discountTypeRef, amountRef)}
                                />
                            </div>
                        </td>
                    );
                case TABLE_HEADER_TYPE.UNIT_PRICE:
                    return (
                        <td key={column.id} style={{ width: "100px" }}>
                            <div className="border-0">
                                <input
                                    className="form-control text-end h-40px border-transparent overflow-hidden focus-shadow"
                                    step={`0.${convertToZeroStringWithOne(
                                        itemState?.decimal_places_for_rate
                                    )}`}
                                    min="0"
                                    name="discount_value"
                                    type="number"
                                    // value={changeTax ? itemState?.rateWithGst : itemState?.rateWithoutGst}
                                    value={itemState?.updatedRateWithoutGst || null}
                                    onChange={e =>
                                        handlePriceWithoutGstChange(e.target.value, index)
                                    }
                                    onClick={e => e.target.select()}
                                    ref={priceWithGstRef}
                                    onKeyDown={e =>
                                        handleKeyDown(e, discountTypeRef, priceWithGstRef)
                                    }
                                />
                            </div>
                        </td>
                    );
                case TABLE_HEADER_TYPE.DISCOUNT_1:
                    return (
                        <td key={column.id} style={{ width: "100px" }}>
                            <div className="d-flex align-items-center h-40px border-transparent sales-table_discount position-relative">
                                <div className="discount border-0 top-0 input-group d-block">
                                    <ReactSelect
                                        value={itemState?.discountType || null}
                                        defaultValue={1}
                                        onChange={option =>
                                            handleDiscountTypeChange(option, "discount_1")
                                        }
                                        options={[
                                            {
                                                label: `${
                                                    company?.company?.currentCurrencySymbol
                                                        ? company?.company?.currentCurrencySymbol
                                                        : "₹"
                                                }`,
                                                value: 1,
                                            },
                                            { label: "%", value: 2 },
                                        ]}
                                        ref={discountTypeRef}
                                        onKeyDown={e =>
                                            handleKeyDown(
                                                e,
                                                discountValueRef,
                                                discountTypeRef,
                                                "discount_1"
                                            )
                                        }
                                        placeholder=""
                                        showborder={false}
                                        showbg={true}
                                        height="32px"
                                    />
                                </div>
                                <div className="focus-shadow border-0">
                                    <input
                                        className="form-control text-end border-0"
                                        name="discount_value"
                                        type="number"
                                        min="0"
                                        {...(itemState?.discountType == 1 ? {step: "any"} : { step: "0.001" })}
                                        // defaultValue={0}
                                        value={itemState?.discountValue != null ? itemState?.discountValue : ''}
                                        onChange={e => {
                                            handleDiscountValueChange(e.target.value, "discount_1");
                                        }}
                                        onClick={e => e.target.select()}
                                        ref={discountValueRef}
                                        onKeyDown={e =>
                                            handleKeyDown(
                                                e,
                                                saleConfiguration?.item_table_configuration
                                                    ?.is_enabled_discount_2
                                                    ? discountTypeRef2
                                                    : gstRef
                                            )
                                        }
                                    />
                                </div>
                            </div>
                        </td>
                    );
                case TABLE_HEADER_TYPE.DISCOUNT_2:
                    return (
                        <td key={column.id} style={{ width: "100px" }}>
                            <div className="d-flex h-40px border-transparent sales-table_discount  position-relative">
                                <div className="discount border-0 top-0 input-group d-block">
                                    <ReactSelect
                                        value={itemState?.discountType_2 || null}
                                        defaultValue={1}
                                        onChange={option => {
                                            handleDiscountTypeChange(option, "discount_2");
                                        }}
                                        options={[
                                            {
                                                label: `${
                                                    company?.company?.currentCurrencySymbol
                                                        ? company?.company?.currentCurrencySymbol
                                                        : "₹"
                                                }`,
                                                value: 1,
                                            },
                                            { label: "%", value: 2 },
                                        ]}
                                        ref={discountTypeRef2}
                                        onKeyDown={e =>
                                            handleKeyDown(
                                                e,
                                                discountValueRef2,
                                                discountTypeRef2,
                                                "discount_2"
                                            )
                                        }
                                        placeholder=""
                                        showborder={false}
                                        showbg={true}
                                        height="32px"
                                    />
                                </div>
                                <div className="focus-shadow border-0">
                                    <input
                                        className="form-control text-end border-0"
                                        type="number"
                                        min="0"
                                        {...(itemState?.discountType_2 == 1 ? {step: "any"} : { step: "0.001" })}
                                        name="discount_value"
                                        defaultValue={0}
                                        value={itemState?.discountValue_2 != null ? itemState?.discountValue_2 : ''}
                                        onChange={e => {
                                            handleDiscountValueChange(e.target.value, "discount_2");
                                        }}
                                        onClick={e => e.target.select()}
                                        ref={discountValueRef2}
                                        onKeyDown={e => handleKeyDown(e, gstRef, discountValueRef2)}
                                    />
                                </div>
                            </div>
                        </td>
                    );
                case "Total":
                    return (
                        <td key={column.id} style={{ width: "100px" }}>
                            <div>
                                <div className="text-nowrap text-end">
                                    <div className="focus-shadow border-0">
                                        <input
                                            className="form-control h-40px text-end border-transparent overflow-hidden px-1"
                                            value={
                                                company?.company?.currentCurrencySymbol +
                                                itemState?.total
                                            }
                                            disabled
                                        />
                                    </div>
                                </div>
                            </div>
                        </td>
                    );
                default:
                    const matchedField = itemState?.custom_fields?.find(
                        (field) => field.custom_field_id == column.custom_field_id
                    );
                    return column?.input_type === "number" || column?.input_type === "text" && column?.is_enabled ? (
                        column?.custom_field_type == WARRANTY_FIELD_TYPE ? (
                            <td key={column.id} style={{ minWidth: "130px", width: "130px" }}>
                                <div className="d-flex h-40px border-transparent sales-table_discount  position-relative">
                                <div className="discount border-0 top-0 input-group d-block min-w-80px">
                                    <ReactSelect
                                        value={matchedField?.field_type ?? 1}
                                        defaultValue={1}
                                        onChange={(e)=>handleChangeCustomField(e.value, column, "warranty")}
                                        options={[
                                            { label: 'Month', value: 1},
                                            { label: "Year", value: 2 },
                                        ]}
                                        placeholder=""
                                        showborder={false}
                                        showbg={true}
                                        height="32px"
                                        width="80px"
                                        isDisabled={matchedField?.is_able_to_edit ? false : true}
                                    />
                                </div>
                                <div className="focus-shadow border-0">
                                    <input
                                        className="form-control text-end border-0"
                                        type="text"
                                        value={matchedField?.value ?? null}
                                        onChange={(e)=>handleChangeCustomField(e.target.value, column, "text")}
                                        onClick={e => e.target.select()}
                                        disabled={matchedField?.is_able_to_edit ? false : true}
                                    />
                                </div>
                            </div>
                            </td>
                        ) :
                        (<td key={column.id} style={{ width: "100px" }}>
                            <div className={matchedField?.is_able_to_edit ? false : true ? 'react-input-disabled' : ''}>
                            <input
                                className="form-control h-40px text-end border-transparent overflow-hidden"
                                type={column?.input_type}
                                value={matchedField?.is_able_to_edit ? column?.input_type === "number" ? parseFloat(matchedField?.value || null) ?? null : matchedField?.value ?? null : matchedField?.value}
                                onChange={(e)=>handleChangeCustomField(e.target.value, column, column?.input_type)}
                                onClick={e => e.target.select()}
                                disabled={matchedField?.is_able_to_edit ? false : true}
                                {...(column?.input_type === "number" ? { step: "any" } : {})}
                            />
                            </div>
                        </td>
                        )
                    ) : column?.input_type?.includes("date") && column?.is_enabled ? (
                        <td key={column.id} style={{ minWidth: "120px", width: "120px", }}>
                            <div className={matchedField?.is_able_to_edit ? false : true ? 'react-input-disabled' : ''}>
                            <CustomFieldDate
                                value={matchedField?.value ?? null}
                                onChange={(e) => handleChangeDate(e, column, column?.input_type)}
                                // placeholder={column.name}
                                input_type={column?.input_type}
                                onClick={e => e.target.select()}
                                disabled={matchedField?.is_able_to_edit ? false : true}
                            />
                            </div>
                        </td>
                    ) : column?.input_type === "select" && column?.is_enabled ? (
                        <td key={column.id} style={{ minWidth: "150px", width: "150px", }}>
                             <div className={`border-transparent h-40px min-w-100px focus-shadow input-group d-block dropdown-disabled ${
                                    matchedField?.is_able_to_edit ? false : true ? 'react-select-disabled' : ''
                                }`}>
                                <ReactSelect
                                    value={matchedField?.option_id || null}
                                    onChange={(e)=>handleChangeCustomField(e.value, column, "select")}
                                    options={column?.options?.map(opt => ({
                                        value: opt.id,
                                        label: opt.option_label,
                                    }))}
                                    placeholder={column.name}
                                    defaultLabel={column.name}
                                    islabel="no"
                                    onKeyDown={e => handleKeyDown(e, totalRef, gstRef)}
                                    ref={gstRef}
                                    showborder={false}
                                    isDisabled={matchedField?.is_able_to_edit ? false : true}
                                />
                            </div>
                        </td>
                    ): "";
            }
        };

        return (
            <>
                <td>{index + 1}</td>
                {tableHeader?.length > 0 && tableHeader?.map(item => renderCell(item))}
            </>
        );
    }
);

export default Item;
