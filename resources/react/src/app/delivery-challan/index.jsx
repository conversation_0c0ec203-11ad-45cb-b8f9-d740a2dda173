import moment from "moment/moment";
import React, { useContext, useEffect, useRef, useState } from "react";
import { Container } from "react-bootstrap";
import Helmet from "react-helmet";
import { useDispatch, useSelector } from "react-redux";
import Toast from "../../components/ui/Toast";
import { apiBaseURL, TABLE_HEADER_TYPE, SHIPPING_ADDRESS_TYPE_LIST, TRANSACTION_TYPE, CALCULATION_ON_TYPE, toastType, ITEM_STATUS } from "../../constants";
import { StateContext } from "../../context/StateContext";
import useDropdownOption from "../../shared/dropdownList";
import Loader from "../../shared/loader";
import {
    areAllProductsExemptOrNA,
    calculateGSTFlags,
    checkAllAdditionalChargesNaAndExempt,
    convertToFormData,
    findGstRate,
    prepareDeliveryChallanData,
    prepareItemData,
    prepareItemsData,
    prepareSaveAndNewData,
} from "../../shared/prepareData";
import { fetchBrokerList, fetchEntityList } from "../../store/broker/brokerSlice";
import { fetchClassificationList } from "../../store/classification/classificationSlice";
import { fetchCompanyDetails, fetchUserPermission } from "../../store/company/companySlice";
import { fetchConfigurationList, rearrangeItemList, resetConfiguration } from "../../store/configuration/configurationSlice";
import {
    addDeliveryChallan,
    deleteDeliveryChallan,
    updateDeliveryChallan,
} from "../../store/delivery-challan/deliveryChallanSlice";
import { fetchDispatchAddressList } from "../../store/dispatchAddress/dispatchAddressSlice";
import { fetchIncomeDeliveryChallanInvoice } from "../../store/invoice/invoiceSlice";
import { fetchItemList, fetchItemUomList, } from "../../store/item/itemSlice";
import {
    fetchAdditionalLedgerList,
    fetchAddlessLedgerList,
    fetchItemLedgerDetail,
    fetchPartyDetail,
    fetchPartyList,
    fetchPaymentLedgerList,
    fetchPaymentModeList,
} from "../../store/ledger/ledgerSlice";
import { fetchPrevNextUrl } from "../../store/prev-next/prev-nextSlice";
import { fetchGstList, fetchTcsList, fetchTdsList } from "../../store/rate/rateSlice";
import { tableHeader } from "../../store/table/tableSlice";
import { fetchTransportList } from "../../store/transport/transportSlice";
import SaleInvoiceDetail from "../common/InvoiceDetail";
import ConfigurationModal from "../modal/Configuration/ConfigurationModal";
import DeliveryItems from "./DeliveryItems";
import { calculateAdditionalCharges, calculateAdditionalClassification, calculateAddLessCharges, calculateClassification, calculateTotals, customToFixed, formattedDate, gstCalculate, RoundOffMethod, setGSTCalculationType } from "../../shared/calculation";
import Error404Page from "../common/404";
import WarningModal from "../common/WarningModal";
import DeleteWarningModal from "../modal/DeleteWarningModal";
import SaleItems from "../common/Items";
import { correctClassificationNatureType, getEditLedgerFromList } from "../../shared/sharedFunction";
import { errorToast } from "../../store/actions/toastAction";
import { fetchShippingAddressList } from "../../store/shippingAddress/shippingAddressSlice";
import { useTransactionShortcuts } from "../../shared/shortcut-keys";
import CustomHelmet from "../../shared/helmet";

const DeliveryChallanTransaction = ({ id, singleSale, isDuplicate = false, saleInvoiceId }) => {
    const incomeEdit = window.location.pathname.includes("/edit");
    const url = window.location.origin;
    const isInWord = false;
    const shippingAddressType = saleInvoiceId ? SHIPPING_ADDRESS_TYPE_LIST.SALE : SHIPPING_ADDRESS_TYPE_LIST.DELIVERY_CHALLAN

    useEffect(() => {
        document.getElementById("showName").innerHTML = isDuplicate
            ? "Duplicate Delivery Challan"
            : incomeEdit
            ? "Edit Delivery Challan"
            : "Add Delivery Challan";

        if (!incomeEdit && !isDuplicate) {
            setIsEditCalculation(true);
        }
        setConfigurationModalName("Delivery Challan Configuration");
    }, []);

    const withoutTabelHeaderList = [
        { header: TABLE_HEADER_TYPE.ITEM, id: 0 },
        { header: TABLE_HEADER_TYPE.UOM, id: 2 },
        { header: TABLE_HEADER_TYPE.QUANTITY, id: 3 },
    ];

    const dispatch = useDispatch();
    const deliveryRef = useRef(null);
    const formRef = useRef(null);
    const {
        company,
        invoice,
        ledger,
        table,
        dispatchAddress,
        configuration,
        deliveryChallan,
        broker,
        prevNext,
        sale
    } = useSelector(selector => selector);

    const { roundOffOption, gstOptions, classificationOptions, tableHeaderList } = useDropdownOption();

    const {
        items,
        setItems,
        invoiceDetail,
        setInvoiceDetail,
        partyAddress,
        setPartyAddress,
        ewayBillDetail,
        setEwayBillDetail,
        otherDetail,
        setOtherDetail,
        gstCalculation,
        setGstCalculation,
        setPaymentLedgerDetail,
        setClassification,
        brokerDetail,
        setBrokerDetail,
        transporterDetail,
        setTransporterDetail,
        localDispatchAddress,
        setLocalDispatchAddress,
        gstQuote,
        setGstQuote,
        additionalCharges,
        setAdditionalCharges,
        addLessChanges,
        setAddLessChanges,
        selectedAddress,
        sameAsBill,
        setSameAsBill,
        itemType,
        setItemType,
        changeTax,
        setChangeTax,
        setIsEditCalculation,
        invoiceNumber,
        setInvoiceNumber,
        setConfigurationModalName,
        setConfigurationURL,
        setConfigurationHeaderList,
        setConfigurationTableList,
        setConfigurationFooterList,
        loader,
        setLoader,
        dispatchAddressId,
        setDispatchAddressId,
        deleteTransaction,
        openDeleteTransactionModel,
        closeDeleteTransactionModel,
        showDeleteWarningModel,
        setShowDeleteWarningModel,
        isDisable,
        setIsDisable,
        grandTotal,
        setGrandTotal,
        isEditCalculation,
        mainGrandTotal,
        setMainGrandTotal,
        setAdditionalGst,
        finalAmount,
        setFinalAmount,
        roundOffAmount,
        cessValue,
        setCessValue,
        taxableValue,
        setTaxableValue,
        customFieldListTransaction,
        setCustomHeaderListTransaction,
        setHasUnsavedChanges,
        isFieldsChanges,
        setisFieldsChanges,
        isBackButtonClick,
        setIsBackButtonClick,
        setUnsavedBackUrl,
        setInvoiceValue,
        classificationType,
        isIGSTCalculation,
        setIsIGSTCalculation,
        setIsSGSTCalculation,
        isSGSTCalculation,
        isTcsAmountChange,
        tcsRate,
        setTcsRate,
        classification,
        gstValue,
        additionalGst,
        isChangedTcs,
        setGstValue,
        isCheckGstType,
        setIsChangePartyId,
        setCheckGstType,
    } = useContext(StateContext);
    const [isShowGstValue, setIsShowGstValue] = useState(false);
    const [tcsValue, setTCSValue] = useState(0);
    const [tcsRateValue, setTCSRateValue] = useState(0);
    const [shippingValue, setShippingValue] = useState(0);
    const [packingCharge, setPackingCharge] = useState(0);
    const [updatedTableHeader, setUpdatedTableHeader] = useState([]);

    useEffect(() => {
        if (!additionalCharges.terms_and_conditions?.length > 0 && !id) {
            setAdditionalCharges(prev => ({
                ...prev,
                terms_and_conditions: invoice?.incomeDeliveryChallanInvoice?.term_and_condition,
            }));
        }
    }, [invoice]);

    useEffect(() => {
        let updatedHeaders = table?.tableHeader || [];
        if (
            configurationList?.item_table_configuration?.is_enabled_discount_2 === false ||
            configurationList?.item_table_configuration?.is_enabled_discount_2 == 0
        ) {
            updatedHeaders = updatedHeaders?.filter(
                item => item.header !== TABLE_HEADER_TYPE.DISCOUNT_2
            );
        }
        if (configurationList?.item_table_configuration?.is_enabled_mrp === false) {
            updatedHeaders = updatedHeaders?.filter(item => item.header !== TABLE_HEADER_TYPE.MRP);
        }
        const filteredColumns = updatedHeaders.filter(col => col?.is_enabled !== false);
        setUpdatedTableHeader(filteredColumns);
    }, [table?.tableHeader, configurationList]);

    const configurationList = configuration?.configuration;

    useEffect(() => {
        const deliveryChallanInvoice = sale?.getSaleById;
        if (deliveryChallanInvoice?.sale_items) {
            const ids = [];
            const item_ledger_id = [];
            const tcs_type = 1;
            const tds_type = 2;
            const is_call_payment_ledger = false;
            if (deliveryChallanInvoice?.sale_items.length > 0) {
                deliveryChallanInvoice?.sale_items?.forEach(item => {
                    ids.push(item.item_id);
                    item_ledger_id.push(item.ledger_id);
                });
            }

            if (ids.length > 0) {
                dispatch(fetchItemList({ ids }));
            } else {
                dispatch(fetchItemList());
            }
            getEditLedgerFromList({dispatch, item_ledger_id, tcs_type, tds_type, is_call_payment_ledger, singleTransaction: deliveryChallanInvoice});
            setItemType("item");
            const nature_type =
                deliveryChallanInvoice?.sale_items[0]?.classification_nature_type
            const nature_name = classificationOptions.find(item => item.value == nature_type);
            setClassification({
                classification_nature: nature_type,
                classification_nature_name: nature_name?.label,
                rcm_applicable:
                    deliveryChallanInvoice?.sale_items[0]?.classification_is_rcm_applicable ==
                        1
                        ? true
                        : false,
            });
            setGSTCalculationType(
                {
                    classification_nature_name: nature_name?.label,
                },
                setIsIGSTCalculation,
                setIsSGSTCalculation
            );
            // if(deliveryChallanInvoice?.invoice_type == 2){
            dispatch(rearrangeItemList(TRANSACTION_TYPE.DELIVERY_CHALLAN, 1));
            // }else{
            //     dispatch(rearrangeItemList(TRANSACTION_TYPE.DELIVERY_CHALLAN, 2));
            // }
            if (deliveryChallanInvoice?.sale_items?.length > 0) {
                prepareItemData(deliveryChallanInvoice?.sale_items, setItems, setChangeTax);
            } else {
                setItems([
                        {
                            id: "",
                            selectedItem: "",
                            additional_description: "",
                            multiQuantity: [0, 0, 0, 0],
                            quantity: "",
                            selectedUnit: "",
                            isShowDelete: false,
                            decimal_places: "",
                            itemUnitOption: null,
                        }
                    ]
                );
            }
            setPartyAddress({
                billingAddress: {
                    address_1: deliveryChallanInvoice?.billing_address?.address_1,
                    address_2: deliveryChallanInvoice?.billing_address?.address_2,
                    country_id: deliveryChallanInvoice?.billing_address?.country_id,
                    state_id: deliveryChallanInvoice?.billing_address?.state_id,
                    city_id: deliveryChallanInvoice?.billing_address?.city_id,
                    state_name: deliveryChallanInvoice?.billing_address?.state_name,
                    city_name: deliveryChallanInvoice?.billing_address?.city_name,
                    pin_code: deliveryChallanInvoice?.billing_address?.pin_code,
                },
                shippingAddress: {
                    address_1: deliveryChallanInvoice?.shipping_address?.address_1,
                    address_2: deliveryChallanInvoice?.shipping_address?.address_2,
                    country_id: deliveryChallanInvoice?.shipping_address?.country_id,
                    state_id: deliveryChallanInvoice?.shipping_address?.state_id,
                    city_id: deliveryChallanInvoice?.shipping_address?.city_id,
                    state_name: deliveryChallanInvoice?.shipping_address?.state_name,
                    city_name: deliveryChallanInvoice?.shipping_address?.city_name,
                    pin_code: deliveryChallanInvoice?.shipping_address?.pin_code,
                    shipping_name: deliveryChallanInvoice?.shipping_name,
                    shipping_gstin: deliveryChallanInvoice?.shipping_gstin,
                    shipping_address_id: deliveryChallanInvoice?.shipping_address_id
                },
            });
            setIsEditCalculation(true);
            setSameAsBill(deliveryChallanInvoice?.same_as_billing);
            setGstQuote({
                ...gstQuote,
                original_inv_date:
                    deliveryChallanInvoice?.date &&
                    formattedDate(deliveryChallanInvoice?.date),
            });
            setCustomHeaderListTransaction(deliveryChallanInvoice?.custom_values)
            setBrokerDetail({
                broker_id: deliveryChallanInvoice?.broker_id,
                broker_percentage: deliveryChallanInvoice?.brokerage_for_sale ? deliveryChallanInvoice?.brokerage_for_sale : "",
                brokerage_on_value: deliveryChallanInvoice?.brokerage_on_value_type,
            })
            setTransporterDetail({
            transport_id: deliveryChallanInvoice?.transport_id,
            transporter_document_number: deliveryChallanInvoice?.transporter_document_number,
            transporter_document_date: deliveryChallanInvoice?.transporter_document_date
                ? formattedDate(deliveryChallanInvoice?.transporter_document_date)
                : "",
            transporter_vehicle_number: deliveryChallanInvoice?.transporter_vehicle_number,
            });
            setOtherDetail({
                ...otherDetail,
                po_number: deliveryChallanInvoice?.po_no,
                date: deliveryChallanInvoice?.po_date ? formattedDate(deliveryChallanInvoice?.po_date) : null,
            });
        }
    }, [sale?.getSaleById?.sale_items]);

    useEffect(() => {
        if (singleSale?.transaction_items || singleSale?.sale_items) {
            prepareItemData(singleSale?.transaction_items || singleSale?.sale_items, setItems, setChangeTax);
            // if(singleSale?.invoice_type == 2){
            //     dispatch(rearrangeItemList(TRANSACTION_TYPE.DELIVERY_CHALLAN, 1));
            // }else{
            //     dispatch(rearrangeItemList(TRANSACTION_TYPE.DELIVERY_CHALLAN, 2));
            // }
            setAdditionalCharges({
                ...additionalCharges,
                // ...(id &&{upload_document: singleEstimateQuote?.media?.map(media => {
                //     return { original_url: media?.original_url, id: media?.id };
                // })}),
                note: singleSale?.narration,
                terms_and_conditions: singleSale?.term_and_condition,
                additional_detail:
                    !singleSale?.additional_charges ||
                    singleSale?.additional_charges?.length == 0
                        ? [
                              {
                                  ac_ledger_id: null,
                                  ac_type: 1,
                                  ac_value: "",
                                  ac_gst_rate_id: {
                                      label: "",
                                      value: 0,
                                      rate: 0,
                                  },
                                  ac_total: 0,
                                  is_status: ITEM_STATUS.IN_ACTIVE,
                              },
                          ]
                        : singleSale?.additional_charges?.map(charge => {
                              return {
                                  ac_ledger_id: charge?.ledger_id,
                                  ac_type: charge?.charge_type,
                                  ac_value: charge?.value,
                                  ac_gst_rate_id: {
                                      value: charge?.gst_rate_id,
                                      rate: Number(charge?.gst_percentage),
                                      label: `${charge?.gst_percentage}%`,
                                  },
                                  ac_total: customToFixed(charge?.total_without_tax, 2),
                                  is_status: ITEM_STATUS.IN_ACTIVE,
                              };
                          }),
            });
        }
    }, [singleSale?.transaction_items, singleSale?.sale_items]);

        useEffect(() => {
             const newDeliveryChallanData = JSON.parse(localStorage.getItem('saveAndNewData'));

            if (newDeliveryChallanData) {
                setIsChangePartyId(true);
                dispatch(fetchPartyDetail(newDeliveryChallanData?.party_ledger_id));
                dispatch(fetchShippingAddressList(parseFloat(newDeliveryChallanData?.party_ledger_id), shippingAddressType, id));
                if (newDeliveryChallanData?.party_ledger_id || newDeliveryChallanData?.customer_ledger_id) {
                    dispatch(fetchPartyList({ ids: [newDeliveryChallanData?.party_ledger_id || newDeliveryChallanData?.customer_ledger_id] }));
                } else {
                    dispatch(fetchPartyList());
                }
                const newData = newDeliveryChallanData;
                prepareSaveAndNewData(newData, setGstQuote, setPartyAddress, additionalCharges, setAdditionalCharges, setItemType);
            }

        }, []);

    useEffect(() => {
        if (singleSale && singleSale?.payload !== 404) {
            if (saleInvoiceId) {
                setInvoiceNumber(saleInvoiceId);
            }
            dispatch(
                fetchPartyDetail(singleSale?.party_ledger_id || singleSale?.customer_ledger_id)
            );
            if (singleSale?.party_ledger_id || singleSale?.customer_ledger_id) {
                dispatch(
                    fetchPartyList({
                        ids: [singleSale?.party_ledger_id || singleSale?.customer_ledger_id],
                    })
                );
            } else {
                dispatch(fetchPartyList());
            }
            const ids = [];
            const item_ledger_id = [];
            const tcs_type = 1;
            const tds_type = 2;
            const is_call_payment_ledger = false;
            if (singleSale?.transaction_items) {
                singleSale?.transaction_items?.forEach(item => {
                    ids.push(item.item_id);
                    item_ledger_id.push(item.ledger_id);
                });
            } else if (singleSale?.sale_items) {
                singleSale?.sale_items?.forEach(item => {
                    ids.push(item.item_id);
                    item_ledger_id.push(item.ledger_id);
                });
            }

            if (ids.length > 0) {
                dispatch(fetchItemList({ ids }));
            } else {
                dispatch(fetchItemList());
            }
            getEditLedgerFromList({dispatch, item_ledger_id, tcs_type, tds_type, is_call_payment_ledger, singleTransaction: singleSale});
            prepareDeliveryChallanData({
                singleSale,
                id,
                invoice_type: "challan",
                dispatch,
                setStateFunctions: {
                    invoiceDetail,
                    setInvoiceDetail,
                    setGstQuote,
                    setItemType,
                    setClassification,
                    setItems,
                    setAddLessChanges,
                    setAdditionalCharges,
                    setBrokerDetail,
                    setTransporterDetail,
                    setEwayBillDetail,
                    setOtherDetail,
                    setPaymentLedgerDetail,
                    setChangeTax,
                    addLessChanges,
                    additionalCharges,
                    setInvoiceNumber,
                    setPartyAddress,
                    setDispatchAddressId,
                    setSameAsBill,
                    setTaxableValue,
                    setIsIGSTCalculation,
                    setIsSGSTCalculation,
                    setAddLessChanges,
                    setGstCalculation,
                    setGstValue,
                    setTcsRate,
                    setGrandTotal,
                    setMainGrandTotal,
                    setFinalAmount,
                    setCustomHeaderListTransaction,
                    setCessValue
                },
                withoutTabelHeaderList,
                classificationOptions,
                isSetItems: false,
                saleInvoiceId,
                invoice
            });
            setTimeout(() => {
                setLoader(false);
            }, 700);
        }
    }, [singleSale, classificationOptions]);

    useEffect(() => {
        setLoader(true);
        dispatch(tableHeader(""));
        dispatch(fetchIncomeDeliveryChallanInvoice());
        dispatch(fetchUserPermission());
        dispatch(fetchPartyDetail(""));
        dispatch(fetchClassificationList());
        dispatch(fetchCompanyDetails());
        dispatch(fetchConfigurationList(apiBaseURL.DELIVERY_CHALLAN_CONFIGURATION));
        setConfigurationURL(apiBaseURL.DELIVERY_CHALLAN_CONFIGURATION);
        setTimeout(() => {
            if (!incomeEdit) {
                dispatch(fetchItemList());
                dispatch(fetchPartyList());
                // dispatch(rearrangeItemList(TRANSACTION_TYPE.DELIVERY_CHALLAN, 2));
            }
            dispatch(fetchPrevNextUrl({ type: "4", id: id }));
            dispatch(fetchPaymentLedgerList());
            dispatch(fetchTcsList({id:1}));
            dispatch(fetchTdsList({id:2}));
            dispatch(fetchDispatchAddressList());
            // dispatch(fetchPaymentModeList());
            dispatch(fetchAddlessLedgerList());
            dispatch(fetchBrokerList());
            dispatch(fetchTransportList());
            dispatch(fetchAdditionalLedgerList());
            dispatch(fetchItemLedgerDetail());
        }, 1500)
    }, [dispatch]);

    useEffect(() => {
        dispatch(resetConfiguration());
    }, []);

    useEffect(() => {
        setTimeout(() => {
            if (configurationList) {
                setLoader(false);
            }
        }, 300)
    }, [configurationList])

    useEffect(() => {
        setLocalDispatchAddress(dispatchAddress?.dispatchAddress);
    }, [dispatchAddress?.dispatchAddress]);

    useEffect(() => {
        setConfigurationHeaderList(
            [
                {
                    name: "Dispatch Details",
                    key: "is_enabled_dispatch_details",
                    value: configurationList?.header?.is_enabled_dispatch_details,
                    position: 0,
                },
                {
                    name: "Mobile Number",
                    key: "is_enabled_phone_number",
                    value: configurationList?.header?.is_enabled_phone_number,
                    position: 1,
                },
                {
                    name: "PO Details",
                    key: "is_enabled_po_details_of_buyer",
                    value: configurationList?.header?.is_enabled_po_details_of_buyer,
                    position: 8,
                },
            ].filter(Boolean)
        );

        setConfigurationTableList([
            company?.company?.is_gst_applicable && {
                name: "Change GST Details",
                key: "is_change_gst_details",
                value: configurationList?.header?.is_change_gst_details,
                position: 1,
            },
            {
                name: "Discount 2",
                key: "is_enabled_discount_2",
                value: configurationList?.item_table_configuration?.is_enabled_discount_2,
                position: 7,
            },
            {
                name: "MRP",
                key: "is_enabled_mrp",
                value: configurationList?.item_table_configuration?.is_enabled_mrp,
                position: 6,
            },
            {
                name: "Warn On Negative Stock",
                key: "warn_on_negative_stock",
                value: configurationList?.item_table_configuration?.warn_on_negative_stock,
                position: 8,
            },
        ].filter(Boolean));
        setConfigurationFooterList([
            {
                name: "TCS",
                key: "is_enabled_tcs_details",
                value: configurationList?.footer?.is_enabled_tcs_details,
                position: 1,
            },
            {
                name: "Terms & Conditions",
                key: "is_enabled_terms_and_conditions",
                value: configurationList?.footer?.is_enabled_terms_and_conditions,
                position: 4,
            },
        ].filter(Boolean));
    }, [configurationList, company]);

    useEffect(() => {
        if (
            !configurationList?.header?.is_change_gst_details &&
            // !classification?.classification_nature_name &&
            localDispatchAddress &&
            company?.company?.is_gst_applicable &&
            ((!id && !isDuplicate && !saleInvoiceId) || isCheckGstType) &&
            itemType == "item"
        ) {
            if (
                partyAddress?.billingAddress?.state_id ===
                localDispatchAddress[selectedAddress]?.state_id
            ) {
                setIsSGSTCalculation(true);
                setIsIGSTCalculation(false);
            } else {
                setIsSGSTCalculation(false);
                setIsIGSTCalculation(true);
            }
        }
    }, [
        ledger?.partyDetail?.billingAddress?.state_id,
        localDispatchAddress,
        gstQuote.original_inv_no,
        isCheckGstType,
        partyAddress?.billingAddress,
        itemType
    ]);

    useEffect(() => {
        if (
            !configurationList?.header?.is_change_gst_details &&
            company?.company?.is_gst_applicable && itemType == "item" && ((!id && !isDuplicate && !saleInvoiceId) || isCheckGstType)
        ) {
            setClassification({
                rcm_applicable: false,
                classification_nature_name: "",
                classification_nature: 0,
            });
            if (
                partyAddress?.billingAddress?.state_id ===
                localDispatchAddress[selectedAddress]?.state_id
            ) {
                setIsSGSTCalculation(true);
                setIsIGSTCalculation(false);
            } else {
                setIsSGSTCalculation(false);
                setIsIGSTCalculation(true);
            }
        }
    }, [configurationList?.header?.is_change_gst_details, itemType]);

    useEffect(() => {
        if(itemType == "item"){
        let updatedHeaders = table?.tableHeader || [];
        if (
            configurationList?.item_table_configuration?.is_enabled_discount_2 === false ||
            configurationList?.item_table_configuration?.is_enabled_discount_2 == 0
        ) {
            updatedHeaders = updatedHeaders?.filter(item => item.header !== TABLE_HEADER_TYPE.DISCOUNT_2);
        }
        if (configurationList?.item_table_configuration?.is_enabled_mrp === false) {
            updatedHeaders = updatedHeaders?.filter(item => item.header !== TABLE_HEADER_TYPE.MRP);
        }
        if (!configurationList?.item_table_configuration?.is_enabled_hsn_code) {
            updatedHeaders = updatedHeaders?.filter(item => item.header !== TABLE_HEADER_TYPE.HSN_SAC);
        }
        const filteredColumns = updatedHeaders.filter(col => col?.is_enabled !== false);
        setUpdatedTableHeader(filteredColumns);
    }
    }, [table?.tableHeader, configurationList, itemType]);

    useEffect(() => {
        if (configurationList?.header?.is_change_gst_details) {
            const gstData = calculateClassification(
                classification,
                itemType === "item"
                    ? findGstRate(items, gstOptions)
                    : ""
            );
            if (isEditCalculation) {
                setGstValue({
                    cgstValue: gstData.totalCgstTax !== NaN ? gstData?.totalCgstTax : 0.0,
                    sgstValue: gstData?.totalSgstTax || 0,
                    igstValue: gstData?.totalIgstTax || 0,
                });
            }
        } else {
            const matchState =
                ledger?.partyDetail?.billingAddress?.state_id ===
                localDispatchAddress[selectedAddress]?.state_id;
            const gstData = gstCalculate(
                itemType === "item"
                    ? findGstRate(items, gstOptions)
                    : "",
                grandTotal,
                matchState
            );
            if (isEditCalculation) {
                setGstValue({
                    cgstValue:
                        gstData.totalCgstTax !== NaN ? parseFloat(gstData.totalCgstTax) : 0.0,
                    sgstValue: parseFloat(gstData.totalSgstTax) || 0,
                    igstValue: parseFloat(gstData.totalIgstTax) || 0,
                });
            }
        }
    }, [
        classification,
        grandTotal,
        company,
        ledger?.partyDetail,
        items,
        additionalCharges,
        addLessChanges,
        tcsRate.tcs_amount,
        configurationList?.footer?.round_off_method,
    ]);

    useEffect(() => {
        if (ledger?.partyDetail?.billingAddress?.state_id) {
            const GstType =
                localDispatchAddress[selectedAddress]?.state_id ==
                ledger?.partyDetail?.billingAddress?.state_id
                    ? true
                    : false;
            setIsShowGstValue(GstType);
        }
    }, [company, ledger?.partyDetail]);

    useEffect(() => {
        if (!id && !isDuplicate) {
            const maxDate = moment(company?.company?.currentFinancialYear?.yearEndDate).format(
                "YYYY-MM-DD"
            );
            let newDate = "";
            const date1 = new Date();
            const date2 = new Date(maxDate);

            if (date1 < date2) {
                newDate = moment(date1).format("DD-MM-YYYY");
            } else {
                newDate = moment(date2).format("DD-MM-YYYY");
            }
            setInvoiceDetail({
                ...invoiceDetail,
                challan_date: newDate,
                challan_number: invoice?.incomeDeliveryChallanInvoice?.invoice_number,
                sell_challan_number: invoice?.incomeDeliveryChallanInvoice?.invoice_number,
            });
            setTimeout(() => {
                setLoader(false);
            }, 700);
        }
    }, [invoice, company?.company?.currentFinancialYear]);

    useEffect(() => {
        if(invoice?.incomeDeliveryChallanInvoice?.invoice_type){
            dispatch(rearrangeItemList(TRANSACTION_TYPE.DELIVERY_CHALLAN, invoice?.incomeDeliveryChallanInvoice?.invoice_type == 2 ? 1 : 2));
            setItemType(invoice?.incomeDeliveryChallanInvoice?.invoice_type == 2 ? "item" : "without_amount");
        }
    }, [invoice])

    useEffect(() => {
        if (isEditCalculation) {
            const { itemTotal, cessTotal } = calculateTotals(
                items,
                classificationType
            );
            setGrandTotal(itemTotal);
            const addition_charges = calculateAdditionalCharges(
                additionalCharges,
                itemTotal,
                isIGSTCalculation
            );
            setTaxableValue(parseFloat(addition_charges?.addition_charges) + itemTotal);
            // setCessValue(cessTotal);
            const newInvoiceValue = isIGSTCalculation
                ? parseFloat(taxableValue || 0) +
                  parseFloat(cessValue || 0) +
                  parseFloat(gstValue?.igstValue || 0) +
                  parseFloat(additionalGst || 0)
                : parseFloat(taxableValue || 0) +
                  parseFloat(cessValue || 0) +
                  parseFloat(gstValue?.sgstValue || 0) +
                  parseFloat(additionalGst || 0) +
                  parseFloat(additionalGst || 0) +
                  parseFloat(gstValue?.cgstValue || 0);

            // Update the invoice value
            setInvoiceValue(newInvoiceValue);
            if (taxableValue > 0 && tcsRate?.tcs_rate && !isTcsAmountChange && isChangedTcs) {
                const tcsRateAmount =
                    tcsRate.tcs_calculated_on == CALCULATION_ON_TYPE.INVOICE_VALUE
                        ? parseFloat(newInvoiceValue * ((tcsRate?.tcs_rate || 0) / 100))
                        : parseFloat(taxableValue * ((tcsRate?.tcs_rate || 0) / 100));
                setTcsRate({
                    ...tcsRate,
                    tcs_amount: parseFloat(tcsRateAmount || 0).toFixed(2),
                });
            }
            const cgst =
                !classification.rcm_applicable && !isIGSTCalculation && isSGSTCalculation
                    ? customToFixed(
                          parseFloat(gstValue?.cgstValue) + parseFloat(additionalGst || 0),
                          2
                      )
                    : 0;
            const sgst =
                !classification.rcm_applicable && !isIGSTCalculation && isSGSTCalculation
                    ? customToFixed(
                          parseFloat(gstValue?.sgstValue) + parseFloat(additionalGst || 0),
                          2
                      )
                    : 0;
            const igst =
                !classification.rcm_applicable && isIGSTCalculation && classification.classification_nature_name !== "Export Sales Taxable" && classification.classification_nature_name !== "Sales to SEZ Taxable"
                    ? customToFixed(
                          parseFloat(gstValue?.igstValue) + parseFloat(additionalGst || 0),
                          2
                      )
                    : 0;
            const totalWithoutAddLess =
                parseFloat(itemTotal) +
                parseFloat(addition_charges?.addition_charges || 0) +
                parseFloat(configurationList?.footer?.is_enabled_tcs_details ? tcsRate.tcs_tax_id ? isNaN(tcsRate.tcs_amount) ? 0 : tcsRate.tcs_amount : 0 || 0 : 0) +
                // (parseFloat(totalAddLessAmount) || 0) +
                parseFloat(company?.company?.is_gst_applicable ? cessValue || 0 : 0) +
                parseFloat(company?.company?.is_gst_applicable ? cgst : 0) +
                parseFloat(company?.company?.is_gst_applicable ? sgst : 0) +
                parseFloat(company?.company?.is_gst_applicable ? igst : 0);

            const updatedAddLessCharges = calculateAddLessCharges(
                addLessChanges,
                totalWithoutAddLess
            );
            const totalAddLessAmount = updatedAddLessCharges?.reduce((sum, change) => {
                if (change.al_type === 1) {
                    return sum + (parseFloat(change.al_value) || 0);
                } else if (change.al_type === 2) {
                    return sum + (parseFloat(change.al_total) || 0);
                }
                return sum;
            }, 0);
            setMainGrandTotal(parseFloat(totalWithoutAddLess + (totalAddLessAmount || 0)));
            const gstData = calculateAdditionalClassification(classification, addition_charges?.acTotal);
            setAdditionalGst(gstData);
        }
    }, [
        taxableValue,
        tcsRate.tcs_amount,
        grandTotal,
        mainGrandTotal,
        cessValue,
        items,
        addLessChanges,
        gstValue,
        additionalGst,
        additionalCharges,
        configurationList?.footer?.round_off_method,
        configurationList?.footer?.is_enabled_tcs_details
    ]);

    const calculateTotal = () => {
        const { grandFinalAmount, roundOffAmount } = RoundOffMethod(
            mainGrandTotal,
            id && !isCheckGstType && gstCalculation?.round_off_method ? gstCalculation?.round_off_method : configurationList?.footer?.round_off_method
        );
        setGstCalculation({
            ...gstCalculation,
            is_gst_enabled: company?.company?.is_gst_applicable,
            round_of_amount: roundOffAmount,
        });
        setFinalAmount(grandFinalAmount);
    };

    useEffect(() => {
        if (isEditCalculation && itemType == "item") {
            calculateTotal();
        }
    }, [mainGrandTotal, configurationList?.footer?.round_off_method, company, additionalCharges, itemType, isCheckGstType]);

    useTransactionShortcuts(formRef);

    const handleSubmit = e => {
        const submitType = e.nativeEvent.submitter.value;
        const submit_button = submitType === "save" ? 1 : submitType == "saveAndNew" ? 2 : 3;
        const submit_button_type = submitType == "saveAndNew" ? "saveAndNew" : "";

        e.preventDefault();
        const dispatch_address = localDispatchAddress[selectedAddress];

        const isFirstDetailInvalid = detail => {
            return (
                (detail.ac_ledger_id === null && !detail.ac_value) ||
                detail.ac_value === "" ||
                (detail.ac_value == NaN && detail.ac_gst_rate_id === null) ||
                (detail.ac_gst_rate_id?.value === null && detail.ac_total === 0)
            );
        };

        const {
            itemList: item,
            is_na,
            hasNegativeTotal,
        } = prepareItemsData(
            items,
            gstCalculation,
            setGstCalculation,
            company,
            gstOptions,
            configurationList,
            changeTax,
            isInWord
        );

        const { isExempt, isNa } = areAllProductsExemptOrNA(
            items
        );
        const { isAdditionalChargesExempt, isAdditionalChargesNa } =
            checkAllAdditionalChargesNaAndExempt(additionalCharges);

        const { is_cgst_sgst_igst_calculated, is_gst_na } = calculateGSTFlags(
            company,
            isNa,
            isExempt,
            isAdditionalChargesNa,
            isAdditionalChargesExempt
        );

        if (submitType) {
            setIsDisable(true);
            if (hasNegativeTotal && itemType !== "without_amount") {
                setIsDisable(false);
                return dispatch(
                    errorToast({
                        text: "Negative Item Amount Not Accepted.",
                        type: toastType.ERROR,
                    })
                );
            }

            const submitData = {
                challan_number: invoiceDetail.challan_number || invoiceDetail.sell_challan_number,
                challan_date: invoiceDetail?.challan_date,
                invoice_number: invoiceNumber,
                invoice_type: itemType == "without_amount" ? 1 : 2,
                invoice_date: invoiceDetail.invoice_date,
                dispatch_address_id: configurationList?.header?.is_enabled_dispatch_details
                    ? dispatchAddressId
                    : null,
                party_ledger_id: gstQuote.party_ledger_id,
                ...(company?.company?.is_gst_applicable && {
                    gstin: gstQuote.gstin,
                }),
                region_iso: gstQuote?.mobile?.region_iso,
                region_code: gstQuote?.mobile?.region_code,
                ...(configurationList?.header?.is_enabled_phone_number && {
                    party_phone_number: gstQuote?.mobile?.party_phone_number,
                }),
                original_inv_no: invoiceNumber ?? gstQuote.original_inv_no,
                original_inv_date: gstQuote.original_inv_date,
                invoice_date: gstQuote.original_inv_date || invoiceDetail.invoice_date,
                billing_address: {
                    address_1: partyAddress.billingAddress.address_1,
                    address_2: partyAddress.billingAddress.address_2,
                    country_id: partyAddress.billingAddress.country_id,
                    state_id: partyAddress.billingAddress.state_id,
                    city_id: partyAddress.billingAddress.city_id,
                    pin_code: partyAddress.billingAddress.pin_code,
                },
                same_as_billing: sameAsBill ? 1 : 0,
                shipping_gstin: partyAddress?.shippingAddress?.shipping_gstin,
                shipping_name: partyAddress?.shippingAddress?.shipping_name,
                address_name: partyAddress?.shippingAddress?.address_name,
                party_name_same_as_address_name: partyAddress?.shippingAddress?.party_name_same_as_address_name ? 1 : 0,
                ...(partyAddress.shippingAddress?.shipping_address_id ?
                {shipping_address_id: partyAddress?.shippingAddress?.shipping_address_id} :
                {shipping_address: configurationList?.header?.is_enabled_shipping_address
                    ? {
                          address_name: partyAddress?.shippingAddress?.address_name,
                          shipping_gstin: partyAddress?.shippingAddress?.shipping_gstin,
                          shipping_name: partyAddress?.shippingAddress?.shipping_name,
                          address_1: partyAddress?.shippingAddress?.address_1,
                          address_2: partyAddress?.shippingAddress?.address_2,
                          country_id: partyAddress?.shippingAddress?.country_id,
                          state_id: partyAddress?.shippingAddress?.state_id,
                          city_id: partyAddress?.shippingAddress?.city_id,
                          pin_code: partyAddress?.shippingAddress?.pin_code,
                          party_name_same_as_address_name: partyAddress?.shippingAddress?.party_name_same_as_address_name ? 1 : 0
                      }
                    : null}),
                broker_details:
                    configurationList?.header?.is_enabled_broker_details && brokerDetail?.broker_id
                        ? {
                              broker_id: brokerDetail.broker_id,
                              brokerage_for_sale: brokerDetail.broker_percentage,
                              brokerage_on_value_type: brokerDetail.brokerage_on_value,
                          }
                        : null,
                transport_details: configurationList?.header?.is_enabled_transport_details
                    ? {
                          transport_id: transporterDetail.transport_id,
                          transporter_document_number:
                              transporterDetail.transporter_document_number,
                          transporter_document_date: transporterDetail.transporter_document_date,
                          transporter_vehicle_number: transporterDetail.transporter_vehicle_number,
                      }
                    : null,
                eway_bill_details: configurationList?.header?.is_enabled_eway_details
                    ? {
                          eway_bill_number: ewayBillDetail.eway_bill_number,
                          eway_bill_date: ewayBillDetail.eway_bill_date,
                      }
                    : null,
                other_details: {
                    po_no: otherDetail.po_number,
                    po_date: otherDetail.date,
                    credit_period: otherDetail.creditPeriod,
                    credit_period_type: otherDetail.creditPeriod
                        ? otherDetail.creditPeriodType
                        : null,
                    // shipping_name: null,
                    // shipping_gstin: null,
                },
                items: item,
                main_classification_nature_type: correctClassificationNatureType(
                    isCheckGstType,
                    classification.classification_nature_name,
                    configurationList?.header?.is_change_gst_details,
                    partyAddress.billingAddress.state_id,
                    dispatch_address?.state_id,
                    isNa,
                    isExempt,
                    isAdditionalChargesNa,
                    isAdditionalChargesExempt
                ),
                is_rcm_applicable: classification.rcm_applicable ? 1 : 0,
                ...(additionalCharges.additional_detail.length === 1 &&
                    isFirstDetailInvalid(additionalCharges.additional_detail[0])
                        ? { additional_charges: " " }
                        : {
                              additional_charges: additionalCharges.additional_detail?.map(detail => ({
                                  ...detail,
                                  ...(company?.company?.is_gst_applicable && {
                                      ac_gst_rate_id: detail.ac_gst_rate_id?.value || null,
                                  }),
                                  //   add_less_total: detail?.ac_total,
                                  ac_total_without_tax:
                                      detail?.ac_type == 2 ? detail?.ac_total : detail.ac_value,
                              })),
                          }),
                    taxable_value: customToFixed(taxableValue, 2),
                    gross_value: customToFixed(grandTotal, 2),
                    total: grandTotal,
                    ...(company?.company?.is_gst_applicable && {
                        cgst: classification.rcm_applicable
                            ? 0
                            : customToFixed(
                                  !isIGSTCalculation && isSGSTCalculation
                                      ? !isEditCalculation
                                          ? gstValue?.cgstValue
                                          : gstValue?.cgstValue + parseFloat(additionalGst || 0)
                                      : 0,
                                  2
                              ) || 0,
                    }),
                    ...(company?.company?.is_gst_applicable && {
                        sgst: classification.rcm_applicable
                            ? 0
                            : customToFixed(
                                  !isIGSTCalculation && isSGSTCalculation
                                      ? !isEditCalculation
                                          ? gstValue?.cgstValue
                                          : gstValue?.cgstValue + parseFloat(additionalGst || 0)
                                      : 0,
                                  2
                              ) || 0,
                    }),
                    ...(company?.company?.is_gst_applicable && {
                        igst: classification.rcm_applicable
                            ? 0
                            : customToFixed(
                                  isIGSTCalculation
                                      ? !isEditCalculation
                                          ? gstValue?.igstValue
                                          : gstValue?.igstValue + parseFloat(additionalGst || 0)
                                      : 0,
                                  2
                              ),
                    }),
                    ...(configurationList?.footer?.is_enabled_tcs_details ? {tcs_details: tcsRate.tcs_tax_id ? tcsRate : []} : {}),
                    add_less: addLessChanges[0]?.al_ledger_id ? addLessChanges : [],
                    grand_total: customToFixed(finalAmount, 2),
                    cess: customToFixed(cessValue, 2),
                round_off_amount: customToFixed(gstCalculation.round_of_amount, 2),
                is_gst_enabled: !company?.company?.is_gst_applicable
                    ? 0
                    : gstCalculation.is_gst_enabled || 1,
                rounding_amount: customToFixed(gstCalculation.round_of_amount, 2),
                is_cgst_sgst_igst_calculated,
                is_gst_na,
                is_round_off_not_changed: gstCalculation.is_round_off_not_changed ?? 1,
                submit_button_value: submit_button,
                narration: configurationList?.footer?.is_enable_narration
                    ? additionalCharges?.note || null
                    : null,
                term_and_condition: configurationList?.footer?.is_enabled_terms_and_conditions
                    ? additionalCharges?.terms_and_conditions
                    : null,
                submit_button_value: submit_button,
                // custom_fields: customFieldListTransaction?.flatMap((customField) =>
                //     customField.value ? [{ ...(customField?.id ? {id: customField?.id} : {}), custom_field_id: customField.custom_field_id, value: customField.value }] : []
                // ),
                custom_fields: customFieldListTransaction?.flatMap(customField =>
                    customField.value &&
                    (customField.is_enabled === undefined || customField.is_enabled)
                        ? [
                              {
                                  ...(customField?.id ? { id: customField.id } : {}),
                                  custom_field_id: customField.custom_field_id,
                                  value: customField.value,
                              },
                          ]
                        : []
                ),
                round_off_method: (id || isDuplicate) && !isCheckGstType && gstCalculation?.round_off_method ? gstCalculation?.round_off_method : configurationList?.footer?.round_off_method
            };
            const formData = convertToFormData(submitData);
            if (additionalCharges?.upload_document) {
                additionalCharges.upload_document.forEach((doc, index) => {
                    if (doc.file) {
                        formData.append(`delivery_challan_document[${index}]`, doc.file ? doc.file : " ");
                    }
                });
            }
            if (id && !isDuplicate) {
                dispatch(updateDeliveryChallan(id, formData, submitType, submit_button, setIsDisable));
            } else {
                dispatch(
                    addDeliveryChallan(
                        formData,
                        submitType === "saveAndNew" ? "duplicate" : submitType,
                        submit_button,
                        setIsDisable,
                        submitData,
                        submit_button_type
                    )
                );
            };
            setisFieldsChanges(false);
            setHasUnsavedChanges(false);
        }
    };

    useEffect(() => {
        setBrokerDetail({
            ...brokerDetail,
            broker_percentage: broker?.getBrokerDetailsById?.brokerage_for_sale || "",
            brokerage_on_value: broker?.getBrokerDetailsById?.brokerage_for_sale_type || "",
        });
    }, [broker?.getBrokerDetailsById]);

    const salesUrl = "/company/delivery-challan";
    const deliveryPrevUrl = `${url}${salesUrl}/${
        prevNext?.fetchPrevNextUrl?.previousBillId + "/edit"
    }`;
    const deliveryNextUrl = `${url}${salesUrl}/${
        prevNext?.fetchPrevNextUrl?.nextBillId
            ? prevNext.fetchPrevNextUrl.nextBillId + "/edit"
            : "create"
    }`;

    const handleDeleteTransaction = () => {
        if (id) {
            openDeleteTransactionModel()
        }
    };

    const handleConfirmDelete = () => {
        if (id) {
            dispatch(deleteDeliveryChallan(id, setShowDeleteWarningModel));
            closeDeleteTransactionModel();
        }
    }

    const handleInput = (e) => {
        setisFieldsChanges(true);
    };

    const handleBack = () => {
        if(isFieldsChanges) {
            setHasUnsavedChanges(true);
        };
    };

    const handleBeforeUnload = (e) => {
        if (isFieldsChanges && isBackButtonClick) {
            e.preventDefault();
            e.returnValue = '';
        }
    };

    useEffect(() => {
        if (isBackButtonClick) {
                window.addEventListener('beforeunload', handleBeforeUnload);
            return () => {
                window.removeEventListener('beforeunload', handleBeforeUnload);
            };
        };
    }, [isFieldsChanges, isBackButtonClick]);

    return (
        <>
            <CustomHelmet title={isDuplicate
                        ? "Duplicate Delivery Challan"
                        : id
                        ? "Edit Delivery Challan "
                        : "Add Delivery Challan "}/>
            {loader ? (
                <Loader />
            ) : loader && !configurationList?.document_prefix ? (
                <Loader />
            ) : deliveryChallan?.status === 404 && !window.location.pathname.includes("/create") ? (
                <Error404Page />
            ) : (
                <div className="ms-3 mt-12" defaultActiveKey={"edit"} id="uncontrolled-tab-example">
                    {/* <Tab eventKey={"edit"} title="Edit"> */}
                    <form ref={formRef} onSubmit={handleSubmit}  onInput={handleInput}>
                        <Container fluid className="p-0">
                            <div className="content-wrapper-invoice py-6 px-lg-10 px-sm-8 px-6">
                                <SaleInvoiceDetail
                                    isShowChallanPage
                                    isShowChallanPageComponent
                                    isShowInvoiceDetails={false}
                                    isShowPartyInvoiceNumber
                                    id={id}
                                    deliveryRef={deliveryRef}
                                    ledgerModalName={"Add Customer"}
                                    ledgerModalEditName={"Update Customer"}
                                    saleInvoiceId={saleInvoiceId}
                                    isDuplicate={isDuplicate}
                                    shipping_address_type={shippingAddressType}
                                />
                            </div>
                            <div className="custom-nav-tabs nav-tabs d-flex">
                                <a
                                    href={ !isFieldsChanges ? deliveryPrevUrl : undefined}
                                    onClick={(e) => {
                                        if (isFieldsChanges) {
                                            e.preventDefault();
                                            setUnsavedBackUrl(deliveryPrevUrl);
                                            setIsBackButtonClick(false);
                                            handleBack();
                                        }
                                    }}
                                    className={`arrow-btn prev-btn d-flex justify-content-center ${
                                        !prevNext?.fetchPrevNextUrl?.previousBillId && "disabled"
                                    } align-items-center cursor_pointer`}
                                >
                                    <i className="fa-solid fa-angle-left"></i>
                                </a>
                                <a
                                    href={ !isFieldsChanges ? deliveryNextUrl : undefined}
                                    onClick={(e) => {
                                        if (isFieldsChanges) {
                                            e.preventDefault();
                                            setUnsavedBackUrl(deliveryNextUrl);
                                            setIsBackButtonClick(false);
                                            handleBack();
                                        }
                                    }}
                                    className={`arrow-btn next-btn d-flex justify-content-center align-items-center  ${
                                        !prevNext?.fetchPrevNextUrl?.nextBillId && !id && "disabled"
                                    } me-3 cursor_pointer`}
                                >
                                    <i className="fa-solid fa-angle-right"></i>
                                </a>
                                <ConfigurationModal roundOffOption={roundOffOption} />
                            </div>
                        </Container>
                        {itemType == "item" ? (
                            <SaleItems
                                items={items}
                                setItems={setItems}
                                setOnlyItems={setItems}
                                grandTotal={grandTotal}
                                setGrandTotal={setGrandTotal}
                                mainGrandTotal={mainGrandTotal}
                                shippingValue={shippingValue}
                                setShippingValue={setShippingValue}
                                tcsValue={tcsValue}
                                setTCSValue={setTCSValue}
                                tcsRateValue={tcsRateValue}
                                setTCSRateValue={setTCSRateValue}
                                packingCharge={packingCharge}
                                setPackingCharge={setPackingCharge}
                                tableHeader={updatedTableHeader}
                                tabelHeaderList={withoutTabelHeaderList}
                                accountingTabelHeader={tableHeaderList}
                                isShowGstValue={isShowGstValue}
                                taxableValue={taxableValue}
                                setFinalAmount={setFinalAmount}
                                finalAmount={finalAmount}
                                // isShowPaymentDetails={true}
                                // showCreditLimit={!saleCreateOrReturn}
                                // settlePaymentType={saleCreateOrReturn ? "sale-return" : "sale"}
                                shipping_address_type={shippingAddressType}
                                isDeliveryChallan
                                isInWord={isInWord}
                        />
                        ) :
                        <DeliveryItems
                            items={items}
                            setItems={setItems}
                            tableHeader={updatedTableHeader}
                            shipping_address_type={shippingAddressType}
                        />
                        }
                        <Container fluid className="p-0 mt-10 fixed-bottom-section">
                            <div className="d-flex flex-wrap gap-sm-4 gap-3 fixed-buttons px-lg-10 px-sm-8 px-6">
                                <button
                                    type="submit"
                                    name="submitType"
                                    value="save"
                                    className="btn btn-primary"
                                    disabled={isDisable}
                                >
                                    Save
                                </button>
                                {(!id || isDuplicate) && (
                                    <button
                                        type="submit"
                                        name="submitType"
                                        value="saveAndNew"
                                        className="btn btn-primary"
                                        disabled={isDisable}
                                    >
                                        Save & New
                                    </button>
                                )}
                                <button
                                    type="submit"
                                    name="submitType"
                                    value="saveAndPrint"
                                    className="btn btn-primary"
                                    disabled={isDisable}
                                >
                                    Save & Print
                                </button>
                                <button
                                    type="button"
                                    className="btn btn-secondary"
                                    onClick={() => {
                                        if(isFieldsChanges) {
                                            setUnsavedBackUrl(`${window.location.origin}${salesUrl}`);
                                            setIsBackButtonClick(false);
                                            handleBack();
                                        } else {
                                            (window.location.href = `${window.location.origin}${salesUrl}`)
                                        };
                                    }
                                    }
                                >
                                    Back
                                </button>
                                {id && (
                                    <button
                                        onClick={handleDeleteTransaction}
                                        className="btn btn-danger"
                                    >
                                        Delete
                                    </button>
                                )}
                            </div>
                        </Container>
                    </form>

                </div>
            )}
            <Toast />
            {deleteTransaction && <WarningModal
                show={deleteTransaction}
                title="Delete!"
                message='Are you sure want to delete this transaction?'
                showCancelButton
                showConfirmButton
                confirmText="Yes, Delete"
                cancelText="No, Cancel"
                handleClose={closeDeleteTransactionModel}
                handleSubmit={() => handleConfirmDelete()}
            />}
            {showDeleteWarningModel &&
                <DeleteWarningModal show={showDeleteWarningModel?.show} handleClose={() => setShowDeleteWarningModel({ show: false })} transactions={showDeleteWarningModel?.transactions} />}
        </>
    );
};

export default DeliveryChallanTransaction;
