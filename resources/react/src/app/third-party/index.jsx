import React, { useState } from 'react'
import { Card, Container, Tab, Tabs } from 'react-bootstrap'
import { Helm<PERSON> } from 'react-helmet'
import { Link } from 'react-router-dom'
import CustomHelmet from '../../shared/helmet'

const ThirdParty = () => {
    const [searchData, setSearchData] = useState("")

    return (
        <>
            <CustomHelmet title={"Third Parties"} />
            <div className="position-relative vastra-details">
                <Card className="bg-transparent">
                    {/* <div>
                        <div className='d-flex gap-2 align-items-center search-icon-box bg-white w-100 py-2 mb-5'>
                            <div className='search-icon-svg d-flex align-items-center justify-content-center'>
                                <svg xmlns="http://www.w3.org/2000/svg" className='h-100 w-100' viewBox="0 0 18 18" fill="none">
                                    <path d="M16.5 16.5L11.5001 11.5M13.1667 7.33333C13.1667 10.555 10.555 13.1667 7.33333 13.1667C4.11167 13.1667 1.5 10.555 1.5 7.33333C1.5 4.11167 4.11167 1.5 7.33333 1.5C10.555 1.5 13.1667 4.11167 13.1667 7.33333Z" stroke="black" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                </svg>
                            </div>
                            <input type='search' className='p-0 w-100 search search-box remove-close' placeholder='Search' value={searchData} onChange={(e) => setSearchData(e.target.value)} />
                        </div>
                    </div> */}
                    <Container fluid className="vastra-details-container bg-white scroll-color d-flex flex-column gap-8">
                            <div className="d-flex flex-wrap gap-4 justify-content-center justify-content-lg-start">
                            <div className="third-party-img flex-fill d-flex">
                                <Link to={`/company/third-party/vastra`} className=" d-flex flex-column h-100 w-100">
                                <img src="/images/vastraIcon.png" alt="vastra" className="party_img" />
                                <div className="d-flex flex-column align-items-start mt-4 flex-grow-1">
                                    <h3 className="third_party_title">Vastra App</h3>
                                    <p className="third_party_description mb-0">
                                    Easily manage garment billing, stock, and orders — sync data with hisabkitab to automate your accounting.
                                    </p>
                                </div>
                                </Link>
                            </div>

                            <div className="third-party-img flex-fill d-flex">
                                <Link to={`/company/third-party/11za`} className=" d-flex flex-column h-100 w-100">
                                <img src="/images/11za_logo.jpeg" alt="11za" className="party_img" />
                                <div className="d-flex flex-column align-items-start mt-4 flex-grow-1">
                                    <h3 className="third_party_title">11za App</h3>
                                    <p className="third_party_description mb-0">
                                    Users can connect their own 11za WhatsApp API account, select approved message templates, and send automated WhatsApp messages transaction-wise.
                                    </p>
                                </div>
                                </Link>
                            </div>
                            </div>
                    </Container>
                </Card>
            </div>
        </>
    )
}

export default ThirdParty
