import { useFormik } from "formik";
import { useContext, useEffect, useMemo, useRef, useState } from "react";
import { Col, Container, Form, OverlayTrigger, Row, Tooltip } from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import { FormInput } from "../../components/ui/Input";
import ReactSelect from "../../components/ui/ReactSelect";
import Toast from "../../components/ui/Toast";
import { toastType, WARRANTY_FIELD_TYPE } from "../../constants";
import { StateContext } from "../../context/StateContext";
import useDropdownOption from "../../shared/dropdownList";
import CustomHelmet from "../../shared/helmet";
import Loader from "../../shared/loader";
import { convertToFormData } from "../../shared/prepareData";
import { useTransactionShortcuts } from "../../shared/shortcut-keys";
import { errorToast } from "../../store/actions/toastAction";
import { fetchCompanyDetails } from "../../store/company/companySlice";
import {
    addItem,
    fetchIncomeExpenseById,
    getItemConfiguration,
    getItemModelDetail,
    updateItem
} from "../../store/item/itemSlice";
import { fetchGstRateList } from "../../store/rate/rateSlice";
import Error404Page from "../common/404";
import AddIncomeModal from "../modal/Item/IncomeModal";
import AddItemCategoryModal from "../modal/Item/ItemCategoryModal";
import AddUomModal from "../modal/Uom/UomModal";
import CessRateModal from "../modal/Item/CessRateModal";
import ItemMasterConfigurationModal from "../modal/CustomFieldItemsMaster/ItemMasterConfiguration";
import { getAllItemMasterCustomField } from "../../store/configuration/configurationSlice";
import CustomFieldDate from "../../components/ui/CustomFieldDate";
import moment from "moment";
import { formattedDate, replaceIdsWithLabels } from "../../shared/calculation";
import CustomFieldCalculation from "../modal/CustomFieldItemsMaster/CustomFieldCalculation";
import ItemMasterCustomFieldWithOneQty from "./ItemCustomFieldOpeningStock";

const ItemTransaction = ({
    fetchItemDetail,
    id,
    name,
    index,
    items,
    setItems,
    setIndex,
    isPurchase,
}) => {
    const {
        isIncomeModel,
        openIncomeModel,
        closeIncomeModel,
        isUomModel,
        openUomModel,
        closeUomModel,
        isItemCatModel,
        openItemCatModel,
        closeItemCatModel,
        loader,
        setLoader,
        isDisable,
        setIsDisable,
        setHasUnsavedChanges,
        isFieldsChanges,
        setisFieldsChanges,
        isBackButtonClick,
        setIsBackButtonClick,
        setUnsavedBackUrl,
        customItemConfigurationList,
        setCustomItemConfigurationList,
        isCustomFieldCalculation,
        setIsCustomFieldCalculation,
        setCustomFieldItemMaster,
        setDisplayFormula,
        setBackendFormula,
        isEditModel,
        setIsEditModel
    } = useContext(StateContext);
    const {
        companyItemMasterGroups,
        unitOfMeasurement,
        incomeLedgers,
        expenseLedgers,
        gstOptions,
        gstCessOptions,
        discountOption,
        CustomFieldFormulaType,
        CustomFieldFormulaTypeForQty
    } = useDropdownOption();
    const formRef = useRef(null);
    const { company, item } = useSelector(selector => selector);
    const configuration = useSelector(state => state.configuration);
    const [isInitialRender, setIsInitialRender] = useState(true);
    const [selectedCustomFieldFormula, setSelectedCustomFieldFormula] = useState("")
    const [customFieldInventory, setCustomFieldInventory] = useState(false)
    const [addItemData, setAddItemData] = useState({
        itemName: "",
        itemCategory: "",
        itemType: "1",
        primaryUOM: 29,
        primaryUOMName: "PCS-PIECES",
        secondaryUOM: "",
        conversionRate: "",
        decimalPlaces: "",
        sku: "",
        isGstApplicable: "0",
        gstRate: "",
        purchasePriceWithoutGst: "",
        purchasePriceWithGst: "",
        sellingPriceWithoutGst: "",
        sellingPriceWithGst: "",
        isRcmApplicable: "",
        gstCessRate: "",
        hsnCode: "",
        description: "",
        sameAsItem: "",
        incomeLedger: "",
        expenseLedger: "",
        quantityUnit: "",
        quantity: "",
        rate: "",
        stockValuationMethod: "",
        id: "",
        mrp: "",
        prevDescription: "",
    });
    const initialValue = {
        item_name: "",
        group_id: "",
        is_use_in_other_companies: 0,
        item_type: 1,
        primary_unit_of_measurement: "",
        primary_unit_of_measurement_name: "",
        secondary_unit_of_measurement: null,
        secondary_unit_of_measurement_name: "",
        conversion_rate: 1,
        decimal_places: 2,
        sku: null,
        description: null,
        same_description: "",
        is_description_same_as_item_name: 0,
        is_gst_applicable: 0,
        gst_tax_id: null,
        hsn_sac_code: "",
        gst_cess_rate: null,
        is_rcm_applicable: 0,
        mrp: "",
        sale_price: "",
        sale_price_type: 2,
        discount_value: "",
        discount_type: 1,
        income_ledger_id: "",
        purchase_price: "",
        purchase_discount_type: 1,
        purchase_discount_value: "",
        purchase_price_type: 2,
        expense_ledger_id: "",
        decimal_places_for_rate: 2,
        quantity_unit: "",
        quantity: null,
        rate: null,
        opening_balance_type: 2,
        item_image: null,
        is_re_order: 0,
        re_order_level: "",
        re_order_uom: "",
    };
    const formik = useFormik({
        initialValues: initialValue,
        onSubmit: (values, action, e) => handleSubmit(values, action, e),
    });
    const dispatch = useDispatch();
    const [isChangeUnit, setIsChangeUnit] = useState(false);
    const [incomeTitle, setIncomeTitle] = useState("");
    const [unitmodel, setUnitmodel] = useState("");
    const [descriptionCount, setDescriptionCount] = useState(0);
    const [itemDescriptionCount, setItemDescriptionCount] = useState(0);
    const [skuCount, setSkuCount] = useState(0);
    const [isFocusedItem, setIsFocusedItem] = useState(false);
    const [isFocused, setIsFocused] = useState(false);
    const [isFocusedSku, setIsFocusedSku] = useState(false);
    const [scan, setScan] = useState([]);
    const [error, setError] = useState({
        hsn_code: "",
        isError: false,
    });
    const [reOrderLevelOptions, setReOrderLevelOptions] = useState([]);
    const [isAddCessRate, setIsAddCessRate] = useState(false);
    const [cessRateId, setCessRateId] = useState("");
    const inputRef = useRef();
    const location = useLocation();

    const queryParams = new URLSearchParams(location.search);
    const section = queryParams.get('section');

    //shortcut-keys
    useTransactionShortcuts(formRef)

    useEffect(() => {
        if (inputRef.current) {
            inputRef.current.focus();
        }
        dispatch(getAllItemMasterCustomField())
        dispatch(getItemModelDetail());
        dispatch(fetchGstRateList());
        dispatch(fetchCompanyDetails());
        dispatch(getItemConfiguration());
    }, [dispatch]);

    const defaultSelectedGroup = item?.itemModelDetail?.defaultSelectedGroup;

    useEffect(() => {
        formik.setFieldValue("group_id", defaultSelectedGroup);
    }, [defaultSelectedGroup]);

    useEffect(() => {
    if (!configuration?.getItemMasterCustomFieldList?.length) return;

    const list = configuration.getItemMasterCustomFieldList;

    // Separate out all Quantity fields
    const quantityFields = list.filter(
        field => field.is_system_field && field.system_field_name === "Quantity"
    );

    // Remove Quantity fields from main processing
    const customFields = list.filter(
        field => !(field.is_system_field && field.system_field_name === "Quantity")
    );

    const customFieldValues = fetchItemDetail?.custom_fields_value || [];

    const updatedCustomFields = customFields.map(field => {
        const updatedField = { ...field };

        const matchValue = customFieldValues.find(cf => cf.custom_field_id === field.id);
        if (matchValue) {
        updatedField.item_cf_value = matchValue.value;
        updatedField.status = matchValue.status;
        updatedField.able_to_status_change = matchValue.able_to_status_change;
        updatedField.local_status = matchValue.local_status;
        updatedField.select_default_value_or_formula = matchValue.select_default_value_or_formula;
        if (matchValue?.default_formula?.formula) {
            updatedField.default_formula = { formula: matchValue.default_formula.formula };
        }
        }

        if (field.input_type === "select") {
        updatedField.item_cf_value =
            matchValue ? matchValue.option_id : field?.item_custom_field_default_value?.value_select_option_id;
        }

        if (field.custom_field_type === WARRANTY_FIELD_TYPE) {
        updatedField.field_type = matchValue ? matchValue.field_type : field.field_type;
        }

        updatedField.local_status = !id
        ? field.enable_for_all
            ? true
            : false
        : matchValue?.local_status || field.enable_for_all ? matchValue?.local_status || field.local_status : false;
        if(!matchValue){
            updatedField.able_to_status_change = true
        }
        return updatedField;
    });

    // ✅ Handle Quantity merging logic
    const fetchedQuantity = customFieldValues.find(
        cf => cf?.is_system_field && cf?.system_field_name === "Quantity"
    );

    let mergedFormulaStr = "";
    let default_formula_id = "";
    let mergedUsedIds = [];

    // Merge formulas from quantity fields in configuration
    quantityFields?.length > 0 && quantityFields?.forEach(q => {
        const defaultFormula = q.default_formula?.formula;
        const itemFormula = q.item_custom_field_default_formula?.formula;
        const usedIdsDefault = q.default_formula?.used_cf_ids_for_formula || [];
        const usedIdsItem = q.item_custom_field_default_formula?.used_cf_ids_for_formula || [];

        if (defaultFormula) {
        if (mergedFormulaStr) mergedFormulaStr += " + ";
        mergedFormulaStr += defaultFormula;
        }

        if (itemFormula) {
        if (mergedFormulaStr) mergedFormulaStr += " + ";
        mergedFormulaStr += itemFormula;
        }
        default_formula_id = q.default_formula?.id;
        mergedUsedIds = [
        ...mergedUsedIds,
        ...(Array.isArray(usedIdsDefault) ? usedIdsDefault : []),
        ...(Array.isArray(usedIdsItem) ? usedIdsItem : [])
        ];

    });

    // If we have any Quantity-related data, push merged Quantity object
    if (quantityFields.length) {
        const finalQuantity = {
        ...(fetchedQuantity || quantityFields[0]),
        default_formula: {
            id: default_formula_id,
            formula: mergedFormulaStr,
            used_cf_ids_for_formula: [...new Set(mergedUsedIds)]
        },
        item_custom_field_default_formula: {
            formula: fetchedQuantity?.default_formula?.formula,
            used_cf_ids_for_formula: fetchedQuantity?.default_formula?.used_cf_ids_for_formula
        }
        };
        updatedCustomFields.push(finalQuantity);
    }
    setCustomItemConfigurationList(updatedCustomFields);
    }, [configuration?.getItemMasterCustomFieldList, fetchItemDetail?.custom_fields_value]);

    useEffect(() => {
        if (unitOfMeasurement?.length > 0 && !formik.values.primary_unit_of_measurement) {
            const defaultPrimaryUnit = unitOfMeasurement?.filter(
                unit => unit.label === "PCS-PIECES"
            );
            formik.setFieldValue("primary_unit_of_measurement", defaultPrimaryUnit[0].value);
            formik.setFieldValue("primary_unit_of_measurement_name", defaultPrimaryUnit[0].label);
            formik.setFieldValue("quantity_unit", defaultPrimaryUnit[0].value);
        }
    }, [unitOfMeasurement, formik?.values?.primary_unit_of_measurement]);

    const generateSKUID = () => {
        let alphanumericChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        let result = "";
        for (let i = 0; i < 18; i++) {
            let randomData = Math.floor(Math.random() * alphanumericChars.length);
            result += alphanumericChars.charAt(randomData);
        }
        formik.setFieldValue("sku", result);
        setSkuCount(result.length);
    };
    const handleCloseModal = id => {
        formik.resetForm();

    };
    const handleOpenIncomeModel = name => {
        openIncomeModel();
        setIncomeTitle(name);
        dispatch(fetchIncomeExpenseById(name === "Income" ? 1 : 2));
    };
    const handleFocus = () => setIsFocused(true);
    const handleBlur = () => setIsFocused(false);
    const handleFocusSku = () => setIsFocusedSku(true);
    const handleBlurSku = () => setIsFocusedSku(false);
    const handleFocusItem = () => setIsFocusedItem(true);
    const handleBlurItem = () => setIsFocusedItem(false);

    useEffect(() => {
        if (!id) {
        formik.setFieldValue("primary_unit_of_measurement", item?.itemConfigurationData?.primary_uom_id),
        formik.setFieldValue("primary_unit_of_measurement_name", item?.itemConfigurationData?.primary_uom?.full_name);
        formik.setFieldValue("secondary_unit_of_measurement", item?.itemConfigurationData?.secondary_uom_id);
        formik.setFieldValue("secondary_unit_of_measurement_name", item?.itemConfigurationData?.secondary_uom?.full_name);
        formik.setFieldValue("conversion_rate", item?.itemConfigurationData?.conversion_rate);
        formik.setFieldValue("re_order_uom", item?.itemConfigurationData?.primary_uom_id);
        }
    }, [item.itemConfigurationData]);

    useEffect(() => {
        const { primary_unit_of_measurement, secondary_unit_of_measurement } = formik.values;

        if (primary_unit_of_measurement || secondary_unit_of_measurement) {
            const pcsUnit = unitOfMeasurement?.find(unit => unit.value == primary_unit_of_measurement);
            const dozUnit = unitOfMeasurement?.find(unit => unit.value == secondary_unit_of_measurement);

            if(!formik.values.re_order_uom) {
                formik.setFieldValue("re_order_uom", pcsUnit?.value);
            }

            const uniqueUnits = [];
            if (pcsUnit) uniqueUnits.push(pcsUnit);
            if (dozUnit && dozUnit.value !== pcsUnit?.value) uniqueUnits.push(dozUnit);

            setReOrderLevelOptions(uniqueUnits);
        }
    }, [formik.values.primary_unit_of_measurement, formik.values.secondary_unit_of_measurement, unitOfMeasurement]);

    const handleKeydown = e => {
        // Check if the focus is on the button
        const isFocus = document.activeElement.classList.contains("barcode-print-item-icon");
        if (!isFocus) return;

        // Handle Enter key
        if (e.which === 10 || e.which === 13) {
            e.preventDefault();
        }

        // Handle scan logic
        if (scan.length > 0 && e.timeStamp - scan[scan.length - 1].timeStamp > 10) {
            setScan([]);
        }

        if (e.key === "Enter" && scan.length > 0) {
            const scannedString = scan.reduce(
                (scannedString, entry) => scannedString + entry.key,
                ""
            );

            setScan([]);
            document.dispatchEvent(new CustomEvent("scanBarcode", { detail: scannedString }));
            return;
        }

        if (e.key !== "Shift") {
            const data = {
                key: e.key,
                timeStamp: e.timeStamp,
                timeStampDiff: scan.length > 0 ? e.timeStamp - scan[scan.length - 1].timeStamp : 0,
            };
            setScan(prevScan => [...prevScan, data]);
        }
    };

    const handleScanBarcode = async e => {
        const itemSku = e.detail;
        formik.setFieldValue("sku", itemSku);
        setSkuCount(itemSku?.length);
        const focusButton = document?.querySelector(".barcode-print-item-icon");
        if (focusButton) focusButton.focus();
    };

    useEffect(() => {
        // Add event listeners
        document.addEventListener("keydown", handleKeydown);
        document.addEventListener("scanBarcode", handleScanBarcode);

        // Clean up event listeners on unmount
        return () => {
            document.removeEventListener("keydown", handleKeydown);
            document.removeEventListener("scanBarcode", handleScanBarcode);
        };
    }, [scan]);

    useEffect(() => {
        if (
            isInitialRender &&
            incomeLedgers?.length > 0 &&
            expenseLedgers?.length > 0 &&
            gstOptions?.length > 0
        ) {
            const defaultIncomeOption = incomeLedgers?.find(option => option?.label == "Sale");
            if (defaultIncomeOption) {
                formik.setFieldValue("income_ledger_id", defaultIncomeOption?.value);
            }
            const defaultExpenseOption = expenseLedgers?.find(
                option => option?.label == "Purchase"
            );
            if (defaultExpenseOption) {
                formik.setFieldValue("expense_ledger_id", defaultExpenseOption?.value);
            }
            const defaultGSTOption = gstOptions?.find(option => option?.label == "NA");
            if (defaultGSTOption) {
                formik.setFieldValue("gst_tax_id", defaultGSTOption?.value);
            }
            setIsInitialRender(false);
        }
    }, [incomeLedgers, expenseLedgers, gstOptions]);
    useEffect(() => {
        // when change item type to render this
        if (

            unitOfMeasurement?.length > 0 &&

            !isChangeUnit &&

            !formik.values.primary_unit_of_measurement

        ) {
            const defaultPrimaryUnit = unitOfMeasurement?.filter(
                unit => unit.label === "PCS-PIECES"
            );
            formik.setFieldValue("primary_unit_of_measurement", defaultPrimaryUnit[0].value);
            formik.setFieldValue("primary_unit_of_measurement_name", defaultPrimaryUnit[0].label);
            formik.setFieldValue("quantity_unit", defaultPrimaryUnit[0].value);
        }
    }, [unitOfMeasurement, formik.values.item_type]);
    useEffect(() => {
        const getItem = fetchItemDetail?.item_master;
        const openingDetail = fetchItemDetail?.opening_balance_details;
        const getItemModel = fetchItemDetail?.item_master?.model;
        if (!isInitialRender && getItem) {
            formik.setFieldValue("item_name", getItem?.item_name);
            formik.setFieldValue("group_id", getItem?.group_id);
            formik.setFieldValue("is_use_in_other_companies", getItem?.is_use_in_other_companies);
            formik.setFieldValue("item_type", getItem?.item_type);
            formik.setFieldValue(
                "primary_unit_of_measurement",
                getItemModel?.unit_of_measurement?.id
            );
            formik.setFieldValue(
                "primary_unit_of_measurement_name",
                getItemModel?.unit_of_measurement?.full_name
            );
            const secondary = unitOfMeasurement?.find(
                unit => unit.value == getItemModel?.secondary_unit_of_measurement
            );
            formik.setFieldValue(
                "secondary_unit_of_measurement",
                getItemModel?.secondary_unit_of_measurement
            );
            formik.setFieldValue("secondary_unit_of_measurement_name", secondary?.label);
            formik.setFieldValue("conversion_rate", getItemModel?.conversion_rate);
            formik.setFieldValue("decimal_places", getItemModel?.decimal_places);
            formik.setFieldValue("decimal_places_for_rate", getItemModel?.decimal_places_for_rate);
            formik.setFieldValue("sku", getItem?.sku);
            setSkuCount(getItemModel?.sku?.length || 0);
            formik.setFieldValue(
                "sale_price",
                getItemModel?.sale_price_type == 1
                    ? getItemModel?.selling_price_with_gst
                    : getItemModel?.selling_price_without_gst
            );
            formik.setFieldValue("sale_price_type", getItemModel?.sale_price_type);
            formik.setFieldValue("is_gst_applicable", getItemModel?.is_gst_applicable ? 1 : 0);
            formik.setFieldValue("gst_tax_id", getItemModel?.gst_tax_id);
            formik.setFieldValue(
                "purchase_price",
                getItemModel?.purchase_price_type == 1
                    ? getItemModel?.purchase_price_with_gst
                    : getItemModel?.purchase_price_without_gst
            );
            formik.setFieldValue("purchase_price_type", getItemModel?.purchase_price_type);
            formik.setFieldValue("is_rcm_applicable", getItemModel?.is_rcm_applicable ? 1 : 0);
            formik.setFieldValue("gst_cess_rate", getItemModel?.gst_cess_rate);
            formik.setFieldValue("hsn_sac_code", getItemModel?.hsn_sac_code);
            formik.setFieldValue("description", getItemModel?.description);
            setDescriptionCount(getItemModel?.description?.length);
            setItemDescriptionCount(getItem?.item_name?.length);
            formik.setFieldValue("income_ledger_id", getItemModel?.income_ledger_id);
            formik.setFieldValue("expense_ledger_id", getItemModel?.expense_ledger_id);
            formik.setFieldValue("quantity_unit", getItemModel?.quantity_unit);
            formik.setFieldValue("quantity", openingDetail?.opening_balance_qty);
            formik.setFieldValue("rate", openingDetail?.opening_balance_rate);
            formik.setFieldValue("opening_balance_type", openingDetail?.opening_balance_type);
            formik.setFieldValue("id", getItem?.id);
            formik.setFieldValue("mrp", getItemModel?.mrp);
            formik.setFieldValue("discount_type", getItemModel?.discount_type);
            formik.setFieldValue("discount_value", getItemModel?.discount_value);
            formik.setFieldValue("purchase_discount_type", getItemModel?.purchase_discount_type);
            formik.setFieldValue("purchase_discount_value", getItemModel?.purchase_discount_value);
            formik.setFieldValue(
                "is_description_same_as_item_name",
                getItemModel?.is_description_same_as_item_name ? 1 : 0
            );
            formik.setFieldValue("is_re_order", getItemModel?.is_re_order ? 1 : 0);
            formik.setFieldValue("re_order_level", getItemModel?.re_order_level);
            formik.setFieldValue("re_order_uom", getItemModel?.re_order_uom);
            if (getItem?.item_image) {
                setPreviewImage(getItem?.item_image);
            }

            // Transform custom_field_inventory from edit response format to add format
            const customFieldInventory = fetchItemDetail?.custom_field_inventory;
            if (customFieldInventory && customFieldInventory.length > 0) {
                const transformedInventory = customFieldInventory.map(inventoryItem => {
                    // Each inventory item becomes a row (array of objects)
                    return inventoryItem.fields.map(field => ({
                        custom_field_id: field.custom_field_id,
                        quantity: inventoryItem.quantity,
                        value: field.value,
                        purchase_rate: field.purchase_rate || inventoryItem.purchase_rate,
                        purchase_date: field.purchase_date || inventoryItem.purchase_date
                    }));
                });

                formik.setFieldValue('custom_field_inventory', transformedInventory);
            }
        }
    }, [fetchItemDetail, isInitialRender]);

    const handleSubmit = (values, action, e) => {
        const submitType = values.submitType;
        const custom_fields_formula = customItemConfigurationList
        ?.filter(
            item =>
            item.local_status &&
            item.select_default_value_or_formula === 2 &&
            (item?.default_formula?.formula || item?.item_cf_value || item?.item_custom_field_default_formula?.formula)
        )
        ?.map(item => {
            return {
            custom_field_id: item.id,
            is_system_field: 0,
            formula: item?.item_custom_field_default_formula?.formula || item.default_formula?.formula,
            };
        });
        const custom_fields_formula_quantity = customItemConfigurationList
        ?.filter(item => (item?.default_formula?.formula || item?.item_custom_field_default_formula?.formula) && item?.is_system_field)
        ?.map(item => {
            const formula = item?.item_custom_field_default_formula?.formula || item?.default_formula?.formula;
            return {
            is_system_field: 1,
            system_field_name: item?.label_name,
            formula,
            };
        });
        const custom_fields_values = customItemConfigurationList
            ?.filter(
                item =>
                    item.local_status &&
                    item.select_default_value_or_formula == 1 &&
                    (item?.default_value || item?.item_cf_value)
            )
            ?.map(item => {
                return {
                    custom_field_id: item.id,
                    value: item?.item_cf_value || item.default_value,
                    field_type: item.field_type,
                };
            });
        const custom_fields = customItemConfigurationList
            ?.filter(item => item.local_status)
            ?.map(item => item.id);
        const {
            primary_unit_of_measurement_name,
            secondary_unit_of_measurement_name,
            same_description,
            id,
            ...data
        } = values;
        const submit_values = {
            ...values,
            custom_fields,
            custom_fields_values,
            custom_fields_formula: [...custom_fields_formula, ...custom_fields_formula_quantity],
        };

        const formData = convertToFormData(submit_values);
        if (values?.item_image) {
            formData.append("item_image", values?.item_image);
        }

        if (!error.isError) {
            setIsDisable(true);
            if (values.id) {
                dispatch(
                    updateItem(
                        id,
                        formData,
                        handleCloseModal,
                        items,
                        "",
                        index,
                        isPurchase,
                        submitType,
                        "",
                        "",
                        setIsDisable,
                        "",
                        "",
                        section
                    )
                );
            } else {
                dispatch(
                    addItem(
                        formData,
                        handleCloseModal,
                        items,
                        "",
                        index,
                        setIndex,
                        "",
                        "",
                        "",
                        "",
                        "",
                        submitType,
                        "",
                        "",
                        "",
                        setIsDisable
                    )
                );
            }
        }
        setisFieldsChanges(false);
        setHasUnsavedChanges(false);
        setIndex(0);
    };
    const changePrimarydetail = e => {
        if (formik.values.secondary_unit_of_measurement == e.value) {
            return dispatch(
                errorToast({
                    text: "Secondary and Primary Unit can't be same",
                    type: toastType.ERROR,
                })
            );
        }
        formik.setFieldValue("primary_unit_of_measurement", e.value),
        formik.setFieldValue("re_order_uom", e.value);
        formik.setFieldValue("primary_unit_of_measurement_name", e.label);
        formik.setFieldValue("quantity_unit", e.value);
    };
    const changeSecondarydetail = e => {
        if (formik.values.primary_unit_of_measurement == e.value) {
            return dispatch(
                errorToast({
                    text: "Secondary and Primary Unit can't be same",
                    type: toastType.ERROR,
                })
            );
        }
        formik.setFieldValue("secondary_unit_of_measurement", e.value);
        formik.setFieldValue("re_order_uom", e.value);
        formik.setFieldValue("secondary_unit_of_measurement_name", e.label);
    };
    const changeSameAsItemName = e => {
        const { checked } = e.target;
        formik.setFieldValue("is_description_same_as_item_name", checked ? 1 : 0);
        if (checked) {
            formik.setFieldValue("description", formik.values.item_name);
            setDescriptionCount(formik?.values?.item_name?.length);
        } else {
            setDescriptionCount(0);
            formik.setFieldValue("description", formik.values.same_description);
        }
    };
    const changeItemName = e => {
        const { value } = e.target;
        const currentValue = value.substring(0, 1000);
        formik.setFieldValue("item_name", value);
        setItemDescriptionCount(value?.length);
        if (formik.values.is_description_same_as_item_name) {
            formik.setFieldValue("description", currentValue);
            if (formik.values.is_description_same_as_item_name) {
                setDescriptionCount(currentValue?.length);
            }
        }
    };
    const changeDiscountValue = (e, type) => {
        const { value } = e.target;
        if (type == 1) {
            if (formik.values.discount_type == 2 && value > 100) {
                return;
            } else {
                if(parseFloat(formik.values.sale_price) < parseFloat(value) && formik.values.discount_type == 1){
                    return
                }
                formik.setFieldValue("discount_value", value);
            }
        } else {
            if (formik.values.purchase_discount_type == 2 && value > 100) {
                return;
            } else {
                if(parseFloat(formik.values.purchase_price) < parseFloat(value) && formik.values.purchase_discount_type == 1){
                    return
                }
                formik.setFieldValue("purchase_discount_value", value);
            }
        }
    };

    const handleDecimalChange = (e, name) => {
        const { value } = e.target;
        if (name === "decimal_places_for_rate" && (value < 0 || value > 5)) {
            return;
        }
        if (name === "decimal_places" && (value < 0 || value > 4)) {
            return;
        }
        formik.setFieldValue(name, value);
    };

    const handleChangePrice = (e, name) => {
        const { value } = e.target;
        const decimalPlacesForRate = formik.values.decimal_places_for_rate; // Get the max allowed decimal places
        const regex = new RegExp(`^-?\\d+(\\.\\d{0,${decimalPlacesForRate}})?$`); // Regex to enforce decimal limit

        if (regex.test(value) || value === "") {
            // If value is valid or empty, update the field
            formik.setFieldValue(name, value);
        } else {
            dispatch(
                errorToast({
                    text: `Only up to ${decimalPlacesForRate} decimal places are allowed.`,
                    type: toastType.ERROR,
                })
            );
        }
    };

    const handleChangeDiscountType = (e, type) => {
        if (type == 1) {
            formik.setFieldValue("discount_type", e.value);
            if (e.value == 2 && formik.values.discount_value > 100) {
                formik.setFieldValue("discount_value", 0);
            }
        } else {
            formik.setFieldValue("purchase_discount_type", e.value);
            if (e.value == 2 && formik.values.purchase_discount_value > 100) {
                formik.setFieldValue("purchase_discount_value", 0);
            }
        }
    };

    const options =
        formik?.values?.primary_unit_of_measurement || formik?.values?.secondary_unit_of_measurement
            ? [
                  formik?.values?.primary_unit_of_measurement && {
                      value: formik.values.primary_unit_of_measurement,
                      label: formik.values.primary_unit_of_measurement_name,
                  },
                  formik?.values?.secondary_unit_of_measurement && {
                      value: formik.values.secondary_unit_of_measurement,
                      label: formik.values.secondary_unit_of_measurement_name,
                  },
              ].filter(Boolean)
            : [
                  {
                      value: "Select Quantity unit",
                      label: "Select Quantity unit",
                  },
              ];
    const [previewImage, setPreviewImage] = useState("/assets/images/company-logo.png");

    const handleImageChange = event => {
        const maxFileSize = 2 * 1024 * 1024;
        const file = event.target.files[0];

        if (file?.size >= maxFileSize) {
            dispatch(
                errorToast({
                    text: "Please Select a file less than 2MB",
                    type: toastType.ERROR,
                })
            );
            event.target.files = "";
            return;
        }
        if (file) {
            const reader = new FileReader();
            reader.onload = e => {
                setPreviewImage(e.target.result);
            };
            reader.readAsDataURL(file);
            formik.setFieldValue(`item_image`, file);
        }
    };

    const handleHSNCode = e => {
        const { value } = e.target;
        if (formik.values.item_type == 1 && !/^\d*$/.test(value)) {
            return;
        }
        if (value.length > 8) {
            return;
        }
        if (value.length !== 4 && value.length !== WARRANTY_FIELD_TYPE && value.length !== 8 && value.length !== 0) {
            setError({
                hsn_code: `Please Enter valid ${formik.values.item_type == 1 ? "HSN" : "SAC"} Code`,
                isError: true,
            });
        } else {
            setError({ hsn_code: "", isError: false });
        }
        formik.setFieldValue("hsn_sac_code", value);
    };
    useEffect(() => {
        const tooltipTriggerList = [].slice.call(
            document?.querySelectorAll('[data-bs-toggle="tooltip"]')
        );
        tooltipTriggerList?.forEach(tooltipTriggerEl => {
            new bootstrap.Tooltip(tooltipTriggerEl);
        });
        document.getElementById("showName").innerHTML = id ? "Edit Item" : "Add Item";
    }, []);

    useEffect(() => {
        if (window.location.pathname.includes("item-masters/create")) {
            setTimeout(() => {
                setLoader(false);
            }, 1000);
        }
    }, []);


    const handleInput = (e) => {
        setisFieldsChanges(true);
    };

    const handleBack = () => {
        if(isFieldsChanges) {
            setHasUnsavedChanges(true);
        };
    };

    const handleBeforeUnload = (e) => {
        if (isFieldsChanges && isBackButtonClick) {
            e.preventDefault();
            e.returnValue = '';
        }
    };

    useEffect(() => {
        if (isBackButtonClick) {
                window.addEventListener('beforeunload', handleBeforeUnload);
            return () => {
                window.removeEventListener('beforeunload', handleBeforeUnload);
            };
        };
    }, [isFieldsChanges, isBackButtonClick]);


    const handleOpenCessRateModal = (id) => {
        setIsAddCessRate(true);
        setCessRateId(id);
    }

    const handleOpenCustomFieldCalculation = (item) => {
        setIsCustomFieldCalculation(true);
        setSelectedCustomFieldFormula(item);
        setCustomFieldItemMaster(item);
        setBackendFormula(item?.item_custom_field_default_formula?.formula ? item?.item_custom_field_default_formula?.formula : item?.default_formula?.formula);
        const DisplayFormula = replaceIdsWithLabels(item?.item_custom_field_default_formula?.formula ? item?.item_custom_field_default_formula?.formula : item?.default_formula?.formula, customItemConfigurationList);
        setDisplayFormula(DisplayFormula);
    }

    const handleChangeCustomField = (value, data) =>{
        if(data?.default_formula){
            return handleOpenCustomFieldCalculation(data);
        }
        const customList = customItemConfigurationList.map((item, i) =>
            item?.id === data?.id ? data?.input_type == "number" ? { ...item, item_cf_value: value?.length < 13 ? value : item?.item_cf_value, default_value:  value?.length < 13 ? value : item?.default_value, default_formula: null} : { ...item, item_cf_value: value, default_value: value, default_formula: null} : item
        );
        setCustomItemConfigurationList(customList);
    }

    const handleChangeWarrantyType = (value, data) =>{
        const customList = customItemConfigurationList.map((item, i) =>
            item?.id === data?.id ? { ...item, field_type: value } : item
        );
        setCustomItemConfigurationList(customList);
    }

    const handleCloseCalculationModel = () => {
        setIsCustomFieldCalculation(false);
        setSelectedCustomFieldFormula(null);
        // setCustomFieldItemMaster(null);
        // setBackendFormula("");
        // setDisplayFormula("");
    }

    const handleChangeCustomFieldFormulaType = (e, data) =>{
        const customList = customItemConfigurationList.map((item, i) =>
            item?.id === data?.id ? { ...item, select_default_value_or_formula: e.value, item_cf_value: null,default_value: null, ...(e.label === "Set Default Value" ? {default_formula: null, item_custom_field_default_formula: null} : {}) } : item
        );
        setCustomItemConfigurationList(customList);
    }

    const onUpdateCustomField = (type, data) => {
        const customField = customItemConfigurationList?.map((item, i) =>
            item?.id === data?.id ? {...item, local_status: type} : item
        );
        setCustomItemConfigurationList(customField);
    }

    const handleChangeDate = (date, data, input_type) =>{
        const isValidDate = date && !isNaN(new Date(date).getTime());
        const formattedDateValue = isValidDate ? input_type == "datetime" ? moment(new Date(date)).format("DD-MM-YYYY HH:mm") : formattedDate(new Date(date)) : "";
        const customList = customItemConfigurationList.map((item, i) =>
            item?.id === data?.id ? { ...item, item_cf_value: formattedDateValue, default_formula: null } : item
        );
        setCustomItemConfigurationList(customList);
    }

    const checkIsExistInventory = useMemo(() => {
        return customItemConfigurationList?.filter(field => field.custom_field_type === 2 && (field.local_status && field.status ? true : field?.local_status && !field?.status ? false : false )) || [];
    }, [customItemConfigurationList]);

    const handleOpenInventoryModal = () => {
        if(checkIsExistInventory?.length === 0) return;
        setCustomFieldInventory(true);
    }

    const handleCloseCustomFieldModal = () => {
        setCustomFieldInventory(false);
    }

    return (
        <>
            <CustomHelmet title={id ? "Edit Item" : "Add Item"} />
            {loader ? (
                <Loader />
            ) : item.status === 404 && !window.location.pathname.includes("/create") ? (
                <Error404Page />
            ) : (
                <form ref={formRef} onSubmit={formik.handleSubmit} onInput={handleInput}>
                    <div className="item-master-label mt-12">
                        <Container fluid className="p-0">
                            <div className="content-wrapper py-lg-8 py-6 px-lg-9 px-6">
                                <div>
                                    <div>
                                        <div className="item-transaction add-item-transactions">
                                            <Row className="mb-3">
                                                <Col xl={10} lg={8} sm={12}>
                                                    <div>
                                                        <Row className="align-items-center mb-1">
                                                            <Col
                                                                xl={4}
                                                                md={6}
                                                                sm={12}
                                                                className="mb-1"
                                                            >
                                                                <Form.Group className="position-relative form-floating-group h-40">
                                                                    <FormInput
                                                                        ref={inputRef}
                                                                        className="floating-label-input required capitalize h-40 pe-6"
                                                                        type="text"
                                                                        required
                                                                        placeholder=""
                                                                        name="item_name"
                                                                        value={
                                                                            formik.values.item_name
                                                                        }
                                                                        onChange={changeItemName}
                                                                        onBlur={handleBlurItem}
                                                                        onFocus={handleFocusItem}
                                                                        maxLength={100}
                                                                        autoFocus={true}
                                                                    />
                                                                    <Form.Label className="required">
                                                                        {formik.values.item_type ==
                                                                        1
                                                                            ? "Item Name"
                                                                            : "Service Name"}
                                                                    </Form.Label>
                                                                    <p
                                                                        className={`position-absolute bottom-0 right-0 mb-0 end-0 fs-12 pt-1 bootstrap-maxlength badge text-primary limit-textarea`}
                                                                    >
                                                                        {isFocusedItem
                                                                            ? 100 -
                                                                              (itemDescriptionCount ||
                                                                                  0)
                                                                            : ""}
                                                                    </p>
                                                                </Form.Group>
                                                            </Col>
                                                            <Col
                                                                xl={4}
                                                                md={6}
                                                                sm={12}
                                                                className="mb-1"
                                                            >
                                                                <Form.Group>
                                                                    <div className="input-group flex-nowrap form-group-select mb-0">
                                                                        <div className="position-relative h-40 w-100 pe-36px focus-shadow">
                                                                            <ReactSelect
                                                                                placeholder="Item Category"
                                                                                value={
                                                                                    formik.values
                                                                                        .group_id
                                                                                }
                                                                                onChange={e =>
                                                                                    formik.setFieldValue(
                                                                                        "group_id",
                                                                                        e.value
                                                                                    )
                                                                                }
                                                                                required={true}
                                                                                options={
                                                                                    companyItemMasterGroups
                                                                                }
                                                                                defaultValue={
                                                                                    defaultSelectedGroup
                                                                                }
                                                                                name="itemCategory"
                                                                                radius={true}
                                                                                height="40px"
                                                                            />
                                                                        </div>
                                                                        <button
                                                                            type="button"
                                                                            className="input-group-text custom-group-text"
                                                                            onClick={
                                                                                openItemCatModel
                                                                            }
                                                                        >
                                                                            <i className="fas fa-plus text-gray-900"></i>
                                                                        </button>
                                                                    </div>
                                                                </Form.Group>
                                                            </Col>
                                                            <Col
                                                                xl={4}
                                                                lg={6}
                                                                sm={12}
                                                                className="mb-1"
                                                            >
                                                                <label
                                                                    htmlFor="itemType"
                                                                    className="form-label fs-6 fw-bolder text-gray-900 mb-0"
                                                                >
                                                                    Item Type
                                                                </label>
                                                                <div className="d-flex align-items-center mt-0">
                                                                    <div className="form-check mx-2">
                                                                        <label
                                                                            className="form-label mb-0 text-gray-900"
                                                                            htmlFor="itemTypeGood"
                                                                        >
                                                                            Goods
                                                                        </label>
                                                                        <input
                                                                            className="form-check-input item-type valid-item-type"
                                                                            id="itemTypeGood"
                                                                            name="item_type_1"
                                                                            type="radio"
                                                                            checked={
                                                                                formik.values
                                                                                    .item_type == 1
                                                                            }
                                                                            value={1}
                                                                            onChange={() => {
                                                                                formik.setFieldValue(
                                                                                    "item_type",
                                                                                    1
                                                                                );
                                                                                formik.setFieldValue(
                                                                                    "quantity_unit",
                                                                                    formik.values
                                                                                        .primary_unit_of_measurement
                                                                                );
                                                                            }}
                                                                        />
                                                                    </div>
                                                                    <div className="form-check mx-2">
                                                                        <label
                                                                            className="form-label mb-0 text-gray-900"
                                                                            htmlFor="itemService"
                                                                        >
                                                                            Service
                                                                        </label>
                                                                        <input
                                                                            className="form-check-input item-type valid-item-type"
                                                                            id="itemService"
                                                                            name="item_type_2"
                                                                            type="radio"
                                                                            checked={
                                                                                formik.values
                                                                                    .item_type == 2
                                                                            }
                                                                            value={2}
                                                                            onChange={() => {
                                                                                formik.setFieldValue(
                                                                                    "item_type",
                                                                                    2
                                                                                );
                                                                                formik.setFieldValue(
                                                                                    "quantity_unit",
                                                                                    formik.values
                                                                                        .primary_unit_of_measurement
                                                                                );
                                                                            }}
                                                                        />
                                                                    </div>
                                                                </div>
                                                            </Col>
                                                        </Row>
                                                        <div className="form-check form-check-custom mb-5 w-100">
                                                            <input
                                                                className="form-check-input me-2"
                                                                type="checkbox"
                                                                name="is_use_in_other_companies"
                                                                id="isUseInOtherCompanies"
                                                                checked={formik.values.is_use_in_other_companies == 1 ? true : false}
                                                                onChange={e =>
                                                                    formik.setFieldValue(
                                                                        "is_use_in_other_companies",
                                                                        e.target.checked ? 1 : 0
                                                                    )
                                                                }
                                                            />
                                                            <label
                                                                htmlFor="isUseInOtherCompanies"
                                                                className="form-label fs-6 fw-bolder text-gray-900 mb-0"
                                                            >
                                                                Use in other Companies
                                                            </label>
                                                        </div>
                                                        <Row className="mb-2">
                                                            <Col
                                                                xl={4}
                                                                md={6}
                                                                sm={12}
                                                                className="mb-5"
                                                            >
                                                                <Form.Group>
                                                                    <div className="input-group flex-nowrap form-group-select mb-0">
                                                                        <div className="position-relative h-40 w-100 pe-36px focus-shadow">
                                                                            <ReactSelect
                                                                                name="primary_unit_of_measurement"
                                                                                required={true}
                                                                                value={
                                                                                    formik.values
                                                                                        .primary_unit_of_measurement
                                                                                }
                                                                                options={
                                                                                    unitOfMeasurement
                                                                                }
                                                                                onChange={
                                                                                    changePrimarydetail
                                                                                }
                                                                                radius={true}
                                                                                height="40px"
                                                                                islabel={true}
                                                                                placeholder="Primary Unit of Measurement"
                                                                                defaultLabel="Select Primary Unit"
                                                                            />
                                                                        </div>
                                                                        <button
                                                                            type="button"
                                                                            className="input-group-text custom-group-text"
                                                                            onClick={() => {
                                                                                openUomModel();
                                                                                setUnitmodel(
                                                                                    "primary"
                                                                                );
                                                                            }}
                                                                        >
                                                                            <i className="fas fa-plus text-gray-900"></i>
                                                                        </button>
                                                                    </div>
                                                                </Form.Group>
                                                            </Col>
                                                            <Col
                                                                xl={4}
                                                                md={6}
                                                                sm={12}
                                                                className="mb-5"
                                                            >
                                                                <Form.Group>
                                                                    <div className="input-group flex-nowrap form-group-select mb-0">
                                                                        <div className="position-relative h-40 w-100 pe-36px focus-shadow">
                                                                            <ReactSelect
                                                                                name="secondaryUOM"
                                                                                value={
                                                                                    formik.values
                                                                                        .secondary_unit_of_measurement
                                                                                }
                                                                                onChange={
                                                                                    changeSecondarydetail
                                                                                }
                                                                                options={
                                                                                    unitOfMeasurement
                                                                                }
                                                                                radius={true}
                                                                                height="40px"
                                                                                islabel={true}
                                                                                placeholder="Secondary Unit of Measurement"
                                                                                defaultLabel="Select Secondary Unit"
                                                                            />
                                                                        </div>
                                                                        <button
                                                                            type="button"
                                                                            className="input-group-text custom-group-text"
                                                                            onClick={() => {
                                                                                openUomModel();
                                                                                setUnitmodel(
                                                                                    "secondary"
                                                                                );
                                                                            }}
                                                                        >
                                                                            <i className="fas fa-plus text-gray-900"></i>
                                                                        </button>
                                                                    </div>
                                                                </Form.Group>
                                                            </Col>
                                                            <Col
                                                                xl={4}
                                                                lg={6}
                                                                sm={12}
                                                                className="mb-5"
                                                            >
                                                                {formik.values
                                                                    .secondary_unit_of_measurement &&
                                                                    formik.values
                                                                        .primary_unit_of_measurement && (
                                                                        <>
                                                                            <label className="form-label fs-6 fw-medium text-gray-900 mb-0">
                                                                                Conversion Rate
                                                                            </label>
                                                                            <div className="d-flex gap-2 align-items-center">
                                                                                <p className="mb-0">
                                                                                    1 (
                                                                                    {
                                                                                        formik
                                                                                            .values
                                                                                            .primary_unit_of_measurement_name
                                                                                    }
                                                                                    ) =
                                                                                </p>
                                                                                <input
                                                                                    type="text"
                                                                                    name="conversion_rate"
                                                                                    required
                                                                                    value={
                                                                                        formik
                                                                                            .values
                                                                                            .conversion_rate
                                                                                    }
                                                                                    onChange={
                                                                                        formik.handleChange
                                                                                    }
                                                                                    className="form-control w-25 h-40px"
                                                                                    style={{
                                                                                        maxWidth:
                                                                                            "76px",
                                                                                    }}
                                                                                />
                                                                                <p className="mb-0">
                                                                                    (
                                                                                    {
                                                                                        formik
                                                                                            .values
                                                                                            .secondary_unit_of_measurement_name
                                                                                    }
                                                                                    )
                                                                                </p>
                                                                            </div>
                                                                        </>
                                                                    )}
                                                            </Col>
                                                        </Row>
                                                        <Row>
                                                            {addItemData.itemType == 1 && (
                                                                <Col
                                                                    xl={4}
                                                                    md={6}
                                                                    sm={12}
                                                                    className="mb-5"
                                                                >
                                                                    <Form.Group className="position-relative form-floating-group">
                                                                        <FormInput
                                                                            className="floating-label-input h-40"
                                                                            type="number"
                                                                            placeholder=""
                                                                            max={4}
                                                                            min={0}
                                                                            name="decimal_places"
                                                                            value={
                                                                                formik.values
                                                                                    .decimal_places
                                                                            }
                                                                            onChange={e =>
                                                                                handleDecimalChange(
                                                                                    e,
                                                                                    "decimal_places"
                                                                                )
                                                                            }
                                                                        />
                                                                        <Form.Label>
                                                                            Decimal Places
                                                                        </Form.Label>
                                                                    </Form.Group>
                                                                </Col>
                                                            )}
                                                            {formik.values.item_type == 1 ? (
                                                                <Col
                                                                    xl={4}
                                                                    md={6}
                                                                    sm={12}
                                                                    className="mb-5"
                                                                >
                                                                    <Form.Group className="position-relative form-floating-group">
                                                                        <FormInput
                                                                            maxLength={30}
                                                                            className="floating-label-input h-40"
                                                                            type="text"
                                                                            placeholder=""
                                                                            name="sku"
                                                                            value={
                                                                                formik.values.sku
                                                                            }
                                                                            onChange={e => {
                                                                                formik.setFieldValue(
                                                                                    "sku",
                                                                                    e.target.value
                                                                                );
                                                                                setSkuCount(
                                                                                    e.target.value
                                                                                        .length
                                                                                );
                                                                            }}
                                                                            onFocus={handleFocusSku}
                                                                            onBlur={handleBlurSku}
                                                                        />
                                                                        <span
                                                                            className="bootstrap-maxlength badge text-primary"
                                                                            style={{
                                                                                display: "block",
                                                                                position:
                                                                                    "absolute",
                                                                                whiteSpace:
                                                                                    "nowrap",
                                                                                zIndex: 1099,
                                                                                top: "40px",
                                                                                left: "124.875px",
                                                                                paddingRight:
                                                                                    "60px",
                                                                            }}
                                                                        >
                                                                            {isFocusedSku
                                                                                ? 30 - skuCount || 0
                                                                                : ""}
                                                                        </span>
                                                                        <button
                                                                            type="button"
                                                                            className="input-group-text barcode-print-item-icon position-absolute top-0 h-100"
                                                                            style={{
                                                                                width: "40px",
                                                                                right: "36px",
                                                                                borderRadius: "0",
                                                                            }}
                                                                        >
                                                                            <i
                                                                                className="fa-solid fa-barcode text-primary"
                                                                                style={{
                                                                                    marginLeft:
                                                                                        "7px",
                                                                                }}
                                                                            ></i>
                                                                        </button>
                                                                        <button
                                                                            type="button"
                                                                            className="input-group-text custom-group-text position-absolute right-0 top-0"
                                                                            onClick={generateSKUID}
                                                                        >
                                                                            <i
                                                                                className="text-gray-900 fa-solid fa-wand-magic-sparkles me-1"
                                                                                style={{
                                                                                    marginLeft:
                                                                                        "-3px",
                                                                                }}
                                                                            ></i>
                                                                        </button>
                                                                        <Form.Label>
                                                                            Barcode / SKU
                                                                        </Form.Label>
                                                                    </Form.Group>
                                                                </Col>
                                                            ) : (
                                                                ""
                                                            )}
                                                        </Row>
                                                    </div>
                                                    <>
                                                        <div>
                                                            <Row>
                                                                <Col xl={8} className="mb-3">
                                                                    <h4 className="mb-3 text-primary">
                                                                        Item Description
                                                                    </h4>
                                                                    <div className="d-flex mb-6">
                                                                        <div className="d-flex align-items-center">
                                                                            <h6 className="mb-0 text-gray-700">
                                                                                Description
                                                                            </h6>
                                                                        </div>
                                                                        <div className="form-check form-check-custom mx-2 d-flex align-items-center">
                                                                            <input
                                                                                className="form-check-input"
                                                                                type="checkbox"
                                                                                id="sameAsItemName"
                                                                                onChange={
                                                                                    changeSameAsItemName
                                                                                }
                                                                                checked={
                                                                                    formik?.values
                                                                                        ?.is_description_same_as_item_name ==
                                                                                    1
                                                                                }
                                                                            />
                                                                            <label
                                                                                className="form-label fw-bolder text-gray-700 mb-0 ms-2"
                                                                                htmlFor="sameAsItemName"
                                                                            >
                                                                                Same as{" "}
                                                                                {formik.values
                                                                                    .item_type == 1
                                                                                    ? "Item Name"
                                                                                    : "Service Name"}
                                                                            </label>
                                                                        </div>
                                                                    </div>
                                                                    <Form.Group className="position-relative form-floating-group">
                                                                        <textarea
                                                                            className="floating-label-input required form-control"
                                                                            name="description"
                                                                            rows="3"
                                                                            maxLength={5000}
                                                                            value={
                                                                                formik.values
                                                                                    .description
                                                                            }
                                                                            onChange={e => {
                                                                                formik.setFieldValue(
                                                                                    "description",
                                                                                    e.target.value
                                                                                );
                                                                                formik.setFieldValue(
                                                                                    "same_description",
                                                                                    e.target.value
                                                                                );
                                                                                setDescriptionCount(
                                                                                    e.target.value
                                                                                        .length
                                                                                );
                                                                            }}
                                                                            onFocus={handleFocus}
                                                                            onBlur={handleBlur}
                                                                        ></textarea>
                                                                        <p
                                                                            className={`position-absolute bottom-0 right-0 mb-0 end-0 fs-12 pt-1 bootstrap-maxlength badge text-primary limit-textarea`}
                                                                        >
                                                                            {isFocused
                                                                                ? 5000 -
                                                                                      descriptionCount ||
                                                                                  0
                                                                                : ""}
                                                                        </p>
                                                                        <Form.Label>
                                                                            Description
                                                                        </Form.Label>
                                                                    </Form.Group>
                                                                </Col>
                                                            </Row>
                                                        </div>
                                                        {formik.values.item_type == 1 && (
                                                            <div>
                                                                <Row>
                                                                    <Col sm={12}>
                                                                        <div className="form-check form-switch form-check-custom mt-2 mb-4">
                                                                            <label
                                                                                htmlFor="ReOrderLevel"
                                                                                className="mb-0 text-primary reorder_level"
                                                                            >
                                                                                Re-order Level
                                                                            </label>
                                                                            <input
                                                                                className="form-check-input mx-2"
                                                                                type="checkbox"
                                                                                name="Re-order-level"
                                                                                id="ReOrderLevel"
                                                                                checked={
                                                                                    formik.values
                                                                                        .is_re_order ==
                                                                                    1
                                                                                        ? true
                                                                                        : false
                                                                                }
                                                                                value={
                                                                                    formik.values
                                                                                        .is_re_order
                                                                                }
                                                                                onChange={e =>
                                                                                    formik.setFieldValue(
                                                                                        "is_re_order",
                                                                                        e.target
                                                                                            .checked
                                                                                            ? 1
                                                                                            : 0
                                                                                    )
                                                                                }
                                                                            />
                                                                        </div>
                                                                    </Col>
                                                                    {formik.values.is_re_order ===
                                                                        1 && (
                                                                        <Col
                                                                            xl={4}
                                                                            md={6}
                                                                            className={`mb-6 `}
                                                                        >
                                                                            <Form.Group className="position-relative form-floating-group">
                                                                                <FormInput
                                                                                    className="floating-label-input-2 h-40"
                                                                                    type="number"
                                                                                    step="0.01"
                                                                                    placeholder=""
                                                                                    name="re_order_level"
                                                                                    onClick={e => {
                                                                                        e.target.select();
                                                                                    }}
                                                                                    value={
                                                                                        formik
                                                                                            .values
                                                                                            .re_order_level
                                                                                    }
                                                                                    onChange={e =>
                                                                                        handleChangePrice(
                                                                                            e,
                                                                                            "re_order_level"
                                                                                        )
                                                                                    }
                                                                                />
                                                                                <Form.Label>
                                                                                    Re-order Level
                                                                                </Form.Label>

                                                                                <div className="w-fit-content bg-light position-absolute credit-dropdown-select focus-shadow input-group d-block">
                                                                                    <ReactSelect
                                                                                        options={
                                                                                            reOrderLevelOptions
                                                                                        }
                                                                                        value={
                                                                                            formik
                                                                                                .values
                                                                                                .re_order_uom
                                                                                        }
                                                                                        onChange={e =>
                                                                                            formik.setFieldValue(
                                                                                                "re_order_uom",
                                                                                                e.value
                                                                                            )
                                                                                        }
                                                                                        placeholder=""
                                                                                        isCreatable={
                                                                                            false
                                                                                        }
                                                                                        showborder={
                                                                                            false
                                                                                        }
                                                                                        showbg={
                                                                                            true
                                                                                        }
                                                                                        height="38px"
                                                                                        width="180px"
                                                                                    />
                                                                                </div>
                                                                            </Form.Group>
                                                                        </Col>
                                                                    )}
                                                                </Row>
                                                            </div>
                                                        )}
                                                        <div>
                                                            {customItemConfigurationList?.some(
                                                                item => item?.status
                                                            ) && (
                                                                <h4 className="mb-3 text-primary">
                                                                    Custom Fields
                                                                </h4>
                                                            )}

                                                            {customItemConfigurationList?.length >
                                                                0 &&
                                                                customItemConfigurationList
                                                                ?.filter(item => item?.status)
                                                                ?.map((item, index) => (
                                                                    <Row key={index}>
                                                                            <Col xxl={8}>
                                                                                <div>
                                                                                    <Row className="mb-4 gap-row-16px">
                                                                                        <Col sm={4}>
                                                                                            <div className="d-flex gap-4 align-items-center justify-content-between pe-7 h-100">
                                                                                                <h6 className="mb-0">
                                                                                                    {item?.label_name}
                                                                                                </h6>
                                                                                            {!item?.is_system_field ?
                                                                                                <div className="form-check form-switch form-check-custom">
                                                                                                    <input
                                                                                                        className="form-check-input"
                                                                                                        type="checkbox"
                                                                                                        name="is_gst_applicable"
                                                                                                        checked={item?.local_status}
                                                                                                        onChange={e =>onUpdateCustomField(e.target.checked, item)}
                                                                                                        disabled={formik.values?.id ? !item?.able_to_status_change : false}
                                                                                                    />
                                                                                                </div>
                                                                                            : "" }
                                                                                            {item?.is_system_field ? (
                                                                                                <>
                                                                                                    <i class="fa-solid fs-4 fa-calculator my-3 text-black cursor-pointer ps-1" onClick={() => handleOpenCustomFieldCalculation(item)}></i>
                                                                                                </>
                                                                                            ): ""}
                                                                                            </div>
                                                                                        </Col>
                                                                                        {item?.open_in_popup ===
                                                                                        false || item?.is_system_field ? (
                                                                                            <>
                                                                                                {item?.input_type == "number" ? (
                                                                                                    <Col sm={4}>
                                                                                                        <div className="position-relative h-40 w-100 focus-shadow">
                                                                                                            <ReactSelect
                                                                                                                placeholder="Select Type"
                                                                                                                radius={true}
                                                                                                                height="40px"
                                                                                                                options={CustomFieldFormulaType}
                                                                                                                value={item?.select_default_value_or_formula}
                                                                                                                onChange={e => handleChangeCustomFieldFormulaType(e, item)}
                                                                                                                onClick={e => {
                                                                                                                    e.target.select();
                                                                                                                }}
                                                                                                            />
                                                                                                        </div>
                                                                                                    </Col>
                                                                                                ) : (
                                                                                                    ""
                                                                                                )}
                                                                                                {item?.is_system_field ? (
                                                                                                    <Col sm={6}>
                                                                                                    <div className="mt-3">
                                                                                                        <h6>{replaceIdsWithLabels(item?.item_custom_field_default_formula?.formula ? item?.item_custom_field_default_formula?.formula : item?.default_formula?.formula, customItemConfigurationList) ?? ""}</h6>
                                                                                                    </div>
                                                                                                </Col>
                                                                                                ) : ""}
                                                                                                {item?.input_type == "number" || item?.input_type == "text" ? (
                                                                                                <>
                                                                                                    {item?.custom_field_type == WARRANTY_FIELD_TYPE ? (
                                                                                                    <Col sm={4}>
                                                                                                     <Form.Group className="position-relative form-floating-group">
                                                                                                        <FormInput
                                                                                                            className="floating-label-input-2 h-40"
                                                                                                            type="number"
                                                                                                            step="0.01"
                                                                                                            placeholder=""
                                                                                                            onClick={e => {
                                                                                                                e.target.select();
                                                                                                            }}
                                                                                                            value={item?.item_cf_value ? item?.item_cf_value : (item?.default_value || '')}
                                                                                                            onChange={e => handleChangeCustomField(e.target.value, item)}
                                                                                                        />
                                                                                                        <Form.Label>Warranty</Form.Label>
                                                                                                            <div className="w-fit-content bg-light position-absolute credit-dropdown-select focus-shadow input-group d-block">
                                                                                                                <ReactSelect
                                                                                                                    defaultValue={1}
                                                                                                                    options={[
                                                                                                                        {
                                                                                                                            label: "Month",
                                                                                                                            value: 1,
                                                                                                                        },
                                                                                                                        {
                                                                                                                            label: "Year",
                                                                                                                            value: 2,
                                                                                                                        },
                                                                                                                    ]}
                                                                                                                    value={item?.field_type}
                                                                                                                    onChange={e => handleChangeWarrantyType(e.value, item)}
                                                                                                                    placeholder=""
                                                                                                                    isCreatable={false}
                                                                                                                    showborder={false}
                                                                                                                    showbg={true}
                                                                                                                    height="38px"
                                                                                                                />
                                                                                                            </div>
                                                                                                    </Form.Group>
                                                                                                  </Col>
                                                                                                ) :
                                                                                                <Col sm={4}>
                                                                                                    <div onClick={() =>item?.input_type == "number" && item?.select_default_value_or_formula == 2 ? handleOpenCustomFieldCalculation(item): ""}
                                                                                                    >
                                                                                                        <Form.Group className="position-relative form-floating-group calculator-input h-40">
                                                                                                                <FormInput
                                                                                                                    className="h-100 floating-label-input capitalize pe-28px"
                                                                                                                    type={item?.input_type == "number" ? "text": item?.input_type}
                                                                                                                    placeholder=""
                                                                                                                    value={item?.item_cf_value ? item?.item_cf_value ?? "" : item?.default_value ?? "" ? item?.default_value ?? "" : replaceIdsWithLabels(item?.item_custom_field_default_formula?.formula ? item?.item_custom_field_default_formula?.formula : item?.default_formula?.formula, customItemConfigurationList) ?? ""}
                                                                                                                    onChange={e => handleChangeCustomField(e.target.value, item)}
                                                                                                                    disabled={(item?.input_type == "number" && item?.select_default_value_or_formula == 2) || item?.is_system_field}
                                                                                                                />
                                                                                                            {(item?.input_type == "number" && item?.select_default_value_or_formula == 2) || item?.is_system_field  ? (
                                                                                                                <OverlayTrigger
                                                                                                                    placement="top"
                                                                                                                    overlay={<Tooltip id={`tooltip-${item?.id || item?.system_field_name}`}>{replaceIdsWithLabels(item?.item_custom_field_default_formula?.formula ? item?.item_custom_field_default_formula?.formula : item?.default_formula?.formula, customItemConfigurationList)}</Tooltip>}
                                                                                                                >
                                                                                                                <i
                                                                                                                className="fa-solid fs-4 fa-calculator mx-auto my-3 text-black cursor-pointer ps-1"
                                                                                                                onClick={() => handleOpenCustomFieldCalculation(item)}
                                                                                                                ></i>
                                                                                                                </OverlayTrigger>
                                                                                                            ) :""}
                                                                                                            <Form.Label>{item?.select_default_value_or_formula == 1 ? "Default Value" : "Default Formula"}
                                                                                                            </Form.Label>
                                                                                                        </Form.Group>
                                                                                                    </div>
                                                                                                </Col>
                                                                                                }
                                                                                                </>
                                                                                                ) : ""}
                                                                                                {item?.input_type == "date" || item?.input_type == "datetime" ? (
                                                                                                    <Col sm={4}>
                                                                                                    <CustomFieldDate
                                                                                                        value={item?.item_cf_value ? item?.item_cf_value : item?.default_value || ""}
                                                                                                        onChange={e => handleChangeDate(e, item, item?.input_type)}
                                                                                                        placeholder={item?.label_name}
                                                                                                        input_type={item?.input_type}
                                                                                                    />
                                                                                                    </Col>
                                                                                                ) : ""}
                                                                                               {item?.input_type === "select" && (() => {
                                                                                                    const selectedOption = item?.options?.map(option => ({
                                                                                                    value: option.id,
                                                                                                    label: option.option_label
                                                                                                    })) || [];

                                                                                                return (
                                                                                                <Col sm={4}>
                                                                                                    <div className="input-group flex-nowrap">
                                                                                                    <div className="position-relative h-40px w-100 focus-shadow">
                                                                                                        <ReactSelect
                                                                                                            placeholder={item?.label_name}
                                                                                                            options={selectedOption}
                                                                                                            value={item?.item_cf_value ? item?.item_cf_value : item?.option_id || null}
                                                                                                            onChange={selectedOption => {
                                                                                                                const customList = customItemConfigurationList.map((item_list, i) =>
                                                                                                                    item_list?.id === item?.id ? { ...item_list, default_value: selectedOption?.value, item_cf_value: selectedOption?.value } : item_list
                                                                                                                );
                                                                                                                setCustomItemConfigurationList(customList);
                                                                                                            }}
                                                                                                        />
                                                                                                    </div>
                                                                                                    </div>
                                                                                                </Col>
                                                                                                );
                                                                                            })()
                                                                                            }
                                                                                            </>
                                                                                        ) : (
                                                                                            ""
                                                                                        )}
                                                                                    </Row>
                                                                                </div>
                                                                            </Col>
                                                                        </Row>
                                                                    ))}
                                                        </div>
                                                    </>
                                                </Col>
                                                {formik.values.item_type == 1 ? (
                                                    <Col xl={2} sm={12} id="itemImage">
                                                        <div className="justify-content-center">
                                                            <label className="me-3 text-gray-900 form-label fs-6 fw-bolder">
                                                                Item Image:
                                                            </label>
                                                            <span
                                                                data-bs-toggle="tooltip"
                                                                data-bs-placement="top"
                                                                title="The image must be of pixel 90 x 60"
                                                            >
                                                                <i className="bi bi-question-circle fs-6"></i>
                                                            </span>
                                                        </div>
                                                        <div
                                                            className="image-input image-input-outline"
                                                            data-kt-image-input="true"
                                                        >
                                                            <div
                                                                className="shadow-none image-input-wrapper w-125px h-125px"
                                                                id="previewImage"
                                                                style={{
                                                                    backgroundImage: `url('${previewImage}')`,
                                                                    backgroundSize: "contain",
                                                                    backgroundPosition: "center",
                                                                    border: "1px solid #dee2e6",
                                                                    backgroundColor: "whitesmoke",
                                                                }}
                                                            ></div>
                                                            <label
                                                                className="shadow btn btn-icon btn-circle w-25px h-25px bg-orange change-profile-btn"
                                                                data-kt-image-input-action="change"
                                                                data-bs-toggle="tooltip"
                                                                title="Change Profile"
                                                            >
                                                                <i className="text-white bi bi-pencil-fill fs-7"></i>
                                                                <input
                                                                    type="file"
                                                                    name="item_image"
                                                                    accept=".png, .jpg, .jpeg"
                                                                    onChange={handleImageChange}
                                                                />
                                                            </label>
                                                        </div>
                                                        <div className="form-text-900">
                                                            Allowed file types: png, jpg, jpeg.
                                                        </div>
                                                    </Col>
                                                ) : (
                                                    ""
                                                )}
                                            </Row>

                                            {/* <div className="d-flex mt-7">
                                            <button
                                                type="submit"
                                                className="btn btn-primary me-2"
                                            >
                                                Save
                                            </button>
                                        </div> */}
                                        </div>
                                    </div>
                                    {isItemCatModel && (
                                        <AddItemCategoryModal
                                            show={isItemCatModel}
                                            handleClose={closeItemCatModel}
                                            itemFormik={formik}
                                            isPurchase={isPurchase}
                                            isItem={true}
                                        />
                                    )}
                                    {isUomModel && (
                                        <AddUomModal
                                            show={isUomModel}
                                            handleClose={closeUomModel}
                                            formikValue={formik}
                                            unitmodel={unitmodel}
                                            setIsChangeUnit={setIsChangeUnit}
                                        />
                                    )}
                                    {isIncomeModel && (
                                        <AddIncomeModal
                                            show={isIncomeModel}
                                            handleClose={closeIncomeModel}
                                            name={incomeTitle}
                                            formikValue={formik}
                                            isPurchase={isPurchase}
                                            // editIncome={editIncome}
                                            // setEditIncome={setEditIncome}
                                        />
                                    )}
                                </div>
                            </div>
                            <div className="custom-nav-tabs nav-tabs d-flex px-3">
                                <ItemMasterConfigurationModal formik={formik} id={id} />
                            </div>
                            <div className="content-wrapper py-lg-8 py-6 px-lg-9 px-6 mt-7">
                                {company?.company?.is_gst_applicable ? (
                                    <div>
                                        <h4 className="text-primary">GST Details</h4>
                                        <div>
                                            <Row>
                                                <Col sm={12}>
                                                    <div className="form-check form-switch form-check-custom my-4">
                                                        <label
                                                            htmlFor="gstApplicable"
                                                            className="form-label fs-6 fw-bolder text-gray-900 mb-0"
                                                        >
                                                            Is GST Applicable?
                                                        </label>
                                                        <input
                                                            className="form-check-input mx-2"
                                                            type="checkbox"
                                                            name="is_gst_applicable"
                                                            id="gstApplicable"
                                                            checked={
                                                                formik.values.is_gst_applicable == 1
                                                                    ? true
                                                                    : false
                                                            }
                                                            defaultChecked={false}
                                                            value={formik.values.is_gst_applicable}
                                                            onChange={e =>
                                                                formik.setFieldValue(
                                                                    "is_gst_applicable",
                                                                    e.target.checked ? 1 : 0
                                                                )
                                                            }
                                                        />
                                                    </div>
                                                </Col>
                                            </Row>
                                        </div>
                                    </div>
                                ) : (
                                    ""
                                )}
                                {company?.company?.is_gst_applicable &&
                                formik.values.is_gst_applicable == 1 ? (
                                    <Row className="align-items-center">
                                        <Col xl={3} lg={4} md={6} sm={12} className="mb-4">
                                            <Form.Group>
                                                <div className="input-group flex-nowrap">
                                                    <div className="position-relative h-40 w-100 focus-shadow">
                                                        <ReactSelect
                                                            placeholder="GST Rate"
                                                            value={formik.values.gst_tax_id}
                                                            required={true}
                                                            onChange={e =>
                                                                formik.setFieldValue(
                                                                    "gst_tax_id",
                                                                    e.value
                                                                )
                                                            }
                                                            options={gstOptions}
                                                            height="40px"
                                                        />
                                                    </div>
                                                </div>
                                            </Form.Group>
                                        </Col>
                                        <Col xl={3} lg={4} md={6} sm={12} className="mb-4">
                                            <Form.Group className="position-relative form-floating-group">
                                                <FormInput
                                                    className="floating-label-input h-40"
                                                    type={
                                                        formik.values.item_type == 1
                                                            ? "number"
                                                            : "text"
                                                    }
                                                    placeholder=""
                                                    name="hsn_sac_code"
                                                    value={formik.values.hsn_sac_code}
                                                    onChange={e => handleHSNCode(e)}
                                                    maxLength={8}
                                                />
                                                <Form.Label>
                                                    {formik.values.item_type == 1
                                                        ? "HSN Code"
                                                        : "SAC Code"}
                                                </Form.Label>
                                                <span className="text-danger position-absolute">
                                                    {error?.hsn_code}
                                                </span>
                                            </Form.Group>
                                        </Col>
                                        <Col xl={3} lg={4} md={6} sm={12} className="mb-4">
                                            <Form.Group>
                                                <div className="input-group flex-nowrap">
                                                    <div className="position-relative h-40 w-100 focus-shadow">
                                                        <ReactSelect
                                                            placeholder="GST CESS Rate"
                                                            defaultLabel="Select GST CESS Rate"
                                                            name="gst_cess_rate"
                                                            value={formik.values.gst_cess_rate}
                                                            onChange={e =>
                                                                formik.setFieldValue(
                                                                    "gst_cess_rate",
                                                                    e.value
                                                                )
                                                            }
                                                            isEdit={true}
                                                            options={gstCessOptions}
                                                            height="40px"
                                                            handleOpen={handleOpenCessRateModal}
                                                        />
                                                    </div>
                                                    <button
                                                        type="button"
                                                        className="input-group-text custom-group-text"
                                                        onClick={() => handleOpenCessRateModal("")}
                                                    >
                                                        <i className="fas fa-plus text-gray-900"></i>
                                                    </button>
                                                </div>
                                            </Form.Group>
                                        </Col>
                                        <Col xl={3} lg={4} md={6} sm={12} className="mb-4">
                                            <label
                                                htmlFor="rcmApplicable"
                                                className="form-label mx-2 fs-6 fw-bolder text-gray-900 mb-0"
                                            >
                                                RCM Applicable
                                            </label>
                                            <div className="d-flex align-items-center mt-2">
                                                <div className="form-check mx-2">
                                                    <label
                                                        className="form-label mb-0 text-gray-900"
                                                        htmlFor="isRcmApplicableYes"
                                                    >
                                                        Yes
                                                    </label>
                                                    <input
                                                        className="form-check-input"
                                                        onChange={e =>
                                                            formik.setFieldValue(
                                                                "is_rcm_applicable",
                                                                1
                                                            )
                                                        }
                                                        checked={
                                                            formik.values.is_rcm_applicable == 1
                                                        }
                                                        name="isRcmApplicableYes"
                                                        type="radio"
                                                        value={1}
                                                    />
                                                </div>
                                                <div className="form-check mx-2">
                                                    <label
                                                        className="form-label mb-0 text-gray-900"
                                                        htmlFor="isRcmApplicableNo"
                                                    >
                                                        No
                                                    </label>
                                                    <input
                                                        className="form-check-input"
                                                        onChange={e =>
                                                            formik.setFieldValue(
                                                                "is_rcm_applicable",
                                                                0
                                                            )
                                                        }
                                                        checked={
                                                            formik.values.is_rcm_applicable == 0
                                                        }
                                                        name="isRcmApplicableNo"
                                                        type="radio"
                                                        value={0}
                                                    />
                                                </div>
                                            </div>
                                        </Col>
                                    </Row>
                                ) : (
                                    ""
                                )}
                                <Row>
                                    <Col sm={12}>
                                        <h4 className="mt-4 mb-3 text-primary">Pricing Details</h4>
                                    </Col>
                                    <Col xl={6} className="mb-xl-0 mb-6">
                                        <div className="bg-light p-4 rounded-3">
                                            <h5 className="mb-5">Sale Information</h5>
                                            <Row>
                                                {formik.values.item_type == 1 && (
                                                    <Col md={6} className="mb-6 order-1">
                                                        <Form.Group className="position-relative form-floating-group">
                                                            <FormInput
                                                                className="floating-label-input-2 h-40"
                                                                type="number"
                                                                placeholder=""
                                                                step="0.01"
                                                                name="mrp"
                                                                onClick={e => {
                                                                    e.target.select();
                                                                }}
                                                                value={formik.values.mrp}
                                                                onChange={formik.handleChange}
                                                            />
                                                            <Form.Label>MRP</Form.Label>
                                                        </Form.Group>
                                                    </Col>
                                                )}
                                                <Col
                                                    md={6}
                                                    className={`mb-6 ${
                                                        formik.values.item_type == 1
                                                            ? "order-2"
                                                            : "order-1"
                                                    }`}
                                                >
                                                    <Form.Group className="position-relative form-floating-group">
                                                        <FormInput
                                                            className="floating-label-input-2 h-40"
                                                            type="number"
                                                            step="0.01"
                                                            placeholder=""
                                                            name="sale_price"
                                                            onClick={e => {
                                                                e.target.select();
                                                            }}
                                                            value={formik.values.sale_price}
                                                            onChange={e =>
                                                                handleChangePrice(e, "sale_price")
                                                            }
                                                        />
                                                        <Form.Label>Sale Price</Form.Label>

                                                        {company?.company?.is_gst_applicable ? (
                                                            <div className="w-fit-content bg-light position-absolute credit-dropdown-select focus-shadow input-group d-block">
                                                                <ReactSelect
                                                                    defaultValue={2}
                                                                    options={[
                                                                        {
                                                                            label: "With GST",
                                                                            value: 1,
                                                                        },
                                                                        {
                                                                            label: "Without GST",
                                                                            value: 2,
                                                                        },
                                                                    ]}
                                                                    value={
                                                                        formik.values
                                                                            .sale_price_type
                                                                    }
                                                                    onChange={e =>
                                                                        formik.setFieldValue(
                                                                            "sale_price_type",
                                                                            e.value
                                                                        )
                                                                    }
                                                                    placeholder=""
                                                                    isCreatable={false}
                                                                    showborder={false}
                                                                    showbg={true}
                                                                    height="38px"
                                                                />
                                                            </div>
                                                        ) : null}
                                                    </Form.Group>
                                                </Col>
                                                <Col
                                                    md={6}
                                                    className={`mb-6 ${
                                                        formik.values.item_type == 1
                                                            ? "order-3"
                                                            : "order-3"
                                                    }`}
                                                >
                                                    <Form.Group className="position-relative form-floating-group">
                                                        <FormInput
                                                            className="floating-label-input-2 h-40"
                                                            type="number"
                                                            step="0.01"
                                                            placeholder=""
                                                            name="discount_value"
                                                            value={formik.values.discount_value}
                                                            onChange={e =>
                                                                changeDiscountValue(e, 1)
                                                            }
                                                            onClick={e => {
                                                                e.target.select();
                                                            }}
                                                        />
                                                        <Form.Label>Discount</Form.Label>
                                                        <div
                                                            className="w-fit-content bg-light position-absolute credit-dropdown focus-shadow input-group d-block"
                                                            style={{ minWidth: "60px" }}
                                                        >
                                                            <ReactSelect
                                                                defaultValue={1}
                                                                options={discountOption}
                                                                value={formik.values.discount_type}
                                                                onChange={e =>
                                                                    handleChangeDiscountType(e, 1)
                                                                }
                                                                placeholder=""
                                                                isCreatable={false}
                                                                showborder={false}
                                                                showbg={true}
                                                                height="38px"
                                                            />
                                                        </div>
                                                    </Form.Group>
                                                </Col>
                                                <Col
                                                    md={6}
                                                    className={`mb-6 ${
                                                        formik.values.item_type == 1
                                                            ? "order-4"
                                                            : "order-2"
                                                    }`}
                                                >
                                                    <Form.Group>
                                                        <div className="input-group mb-0 flex-nowrap form-group-select">
                                                            <div className="position-relative h-40 w-100 pe-36px bg-white focus-shadow">
                                                                <ReactSelect
                                                                    value={
                                                                        formik.values
                                                                            .income_ledger_id
                                                                    }
                                                                    onChange={e =>
                                                                        formik.setFieldValue(
                                                                            "income_ledger_id",
                                                                            e.value
                                                                        )
                                                                    }
                                                                    options={incomeLedgers}
                                                                    radius={true}
                                                                    height="40px"
                                                                    islabel={true}
                                                                    position="top"
                                                                    placeholder="Income Ledger"
                                                                    bgColor="linear-gradient(to bottom, #f5f8fa 50%, white 50%)"
                                                                />
                                                            </div>
                                                            <button
                                                                type="button"
                                                                onClick={() =>
                                                                    handleOpenIncomeModel("Income")
                                                                }
                                                                className="input-group-text custom-group-text"
                                                            >
                                                                <i className="fas fa-plus text-gray-900"></i>
                                                            </button>
                                                        </div>
                                                    </Form.Group>
                                                </Col>
                                            </Row>
                                        </div>
                                    </Col>
                                    <Col xl={6}>
                                        <div className="bg-light p-4 rounded-3">
                                            <h5 className="mb-5">Purchase Information</h5>
                                            <Row>
                                                <Col md={6} className="mb-6">
                                                    <Form.Group className="position-relative form-floating-group">
                                                        <FormInput
                                                            className="floating-label-input-2 h-40"
                                                            type="number"
                                                            step="0.01"
                                                            placeholder=""
                                                            name="purchase_price"
                                                            value={formik.values.purchase_price}
                                                            onChange={e =>
                                                                handleChangePrice(
                                                                    e,
                                                                    "purchase_price"
                                                                )
                                                            }
                                                            onClick={e => {
                                                                e.target.select();
                                                            }}
                                                        />
                                                        <Form.Label
                                                            style={{
                                                                maxWidth: "calc(100% - 128px)",
                                                            }}
                                                        >
                                                            Purchase Price
                                                        </Form.Label>

                                                        {company?.company?.is_gst_applicable ? (
                                                            <div className="w-fit-content bg-light position-absolute credit-dropdown-select focus-shadow input-group d-block">
                                                                <ReactSelect
                                                                    defaultValue={2}
                                                                    options={[
                                                                        {
                                                                            label: "With GST",
                                                                            value: 1,
                                                                        },
                                                                        {
                                                                            label: "Without GST",
                                                                            value: 2,
                                                                        },
                                                                    ]}
                                                                    value={
                                                                        formik.values
                                                                            .purchase_price_type
                                                                    }
                                                                    onChange={e =>
                                                                        formik.setFieldValue(
                                                                            "purchase_price_type",
                                                                            e.value
                                                                        )
                                                                    }
                                                                    placeholder=""
                                                                    isCreatable={false}
                                                                    showborder={false}
                                                                    showbg={true}
                                                                    height="38px"
                                                                />
                                                            </div>
                                                        ) : null}
                                                    </Form.Group>
                                                </Col>
                                                <Col md={6} className="mb-6">
                                                    <Form.Group>
                                                        <div className="input-group flex-nowrap form-group-select mb-0">
                                                            <div className="position-relative h-40 w-100 pe-36px bg-white focus-shadow">
                                                                <ReactSelect
                                                                    value={
                                                                        formik.values
                                                                            .expense_ledger_id
                                                                    }
                                                                    onChange={e =>
                                                                        formik.setFieldValue(
                                                                            "expense_ledger_id",
                                                                            e.value
                                                                        )
                                                                    }
                                                                    options={expenseLedgers}
                                                                    radius={true}
                                                                    height="40px"
                                                                    islabel={true}
                                                                    position="top"
                                                                    placeholder="Expense Ledger"
                                                                    bgColor="linear-gradient(to bottom, #f5f8fa 50%, white 50%)"
                                                                />
                                                            </div>
                                                            <button
                                                                type="button"
                                                                onClick={() =>
                                                                    handleOpenIncomeModel("Expense")
                                                                }
                                                                className="input-group-text custom-group-text"
                                                            >
                                                                <i className="fas fa-plus text-gray-900"></i>
                                                            </button>
                                                        </div>
                                                    </Form.Group>
                                                </Col>
                                                <Col md={6} className="mb-6">
                                                    <Form.Group className="position-relative form-floating-group">
                                                        <FormInput
                                                            className="floating-label-input-2 h-40"
                                                            type="number"
                                                            step="0.01"
                                                            placeholder=""
                                                            name="purchase_discount_value"
                                                            value={
                                                                formik.values
                                                                    .purchase_discount_value
                                                            }
                                                            onChange={e =>
                                                                changeDiscountValue(e, 2)
                                                            }
                                                            onClick={e => {
                                                                e.target.select();
                                                            }}
                                                        />
                                                        <Form.Label>Discount</Form.Label>
                                                        <div
                                                            className="w-fit-content bg-light position-absolute credit-dropdown focus-shadow input-group d-block"
                                                            style={{ minWidth: "60px" }}
                                                        >
                                                            <ReactSelect
                                                                defaultValue={1}
                                                                options={discountOption}
                                                                value={
                                                                    formik.values
                                                                        .purchase_discount_type
                                                                }
                                                                onChange={e =>
                                                                    handleChangeDiscountType(e, 2)
                                                                }
                                                                placeholder=""
                                                                isCreatable={false}
                                                                showborder={false}
                                                                showbg={true}
                                                                height="38px"
                                                            />
                                                        </div>
                                                    </Form.Group>
                                                </Col>
                                            </Row>
                                        </div>
                                    </Col>
                                </Row>
                                <Row className="mt-6">
                                    <Col md={6} xl={3} xxl={3} className="px-4">
                                        <Form.Group className="position-relative form-floating-group">
                                            <FormInput
                                                className="floating-label-input h-40"
                                                type="number"
                                                step="0.01"
                                                placeholder=""
                                                name="decimal_places_for_rate"
                                                onClick={e => {
                                                    e.target.select();
                                                }}
                                                max={5}
                                                min={2}
                                                value={formik.values.decimal_places_for_rate}
                                                onChange={e =>
                                                    handleDecimalChange(
                                                        e,
                                                        "decimal_places_for_rate"
                                                    )
                                                }
                                            />
                                            <Form.Label>Decimal Places For Rate</Form.Label>
                                        </Form.Group>
                                    </Col>
                                </Row>
                            </div>

                            {formik.values.item_type == 1 && (
                                <>
                                    <div className="content-wrapper overflow-visible py-lg-8 py-6 px-lg-9 px-6 mt-7">
                                        <Row>
                                            <Col sm={12}>
                                                <h4 className="mb-4 text-primary">
                                                    Opening Stock Details
                                                </h4>
                                            </Col>
                                            <Col xl={3} md={6} sm={12} className="mb-4">
                                                <Form.Group>
                                                    <div className="input-group flex-nowrap">
                                                        <div className="position-relative h-40 w-100 focus-shadow">
                                                            <ReactSelect
                                                                customLabel="modal"
                                                                value={
                                                                    formik.values.quantity_unit ||
                                                                    null
                                                                }
                                                                options={options}
                                                                onChange={e =>
                                                                    formik.setFieldValue(
                                                                        "quantity_unit",
                                                                        e.value
                                                                    )
                                                                }
                                                                placeholder="Quantity Unit"
                                                                isCreatable={false}
                                                                height="40px"
                                                            />
                                                        </div>
                                                    </div>
                                                </Form.Group>
                                            </Col>
                                            <Col xl={3} md={6} sm={12} className="mb-4">
                                                <Form.Group className="position-relative form-floating-group d-flex align-items-center justify-content-between">
                                                    <FormInput
                                                        className={`floating-label-input h-40 form-control ${checkIsExistInventory?.length ? 'cursor-pointer' : ''}`}
                                                        type="number"
                                                        step="0.01"
                                                        min="0.00"
                                                        placeholder=""
                                                        name="quantity"
                                                        value={checkIsExistInventory?.length !== 0 ? formik.values.quantity || 0 : formik.values.quantity}
                                                        onChange={formik.handleChange}
                                                        onClick={handleOpenInventoryModal}
                                                        readOnly={checkIsExistInventory?.length !== 0}
                                                    />
                                                    <Form.Label>Quantity</Form.Label>
                                                    {checkIsExistInventory?.length !== 0 ? (
                                                        <a
                                                            onClick={handleOpenInventoryModal}
                                                            className="input-group-text custom-group-text cursor-pointer"
                                                            data-ledger-link-id="1"
                                                            data-bs-toggle="tooltip"
                                                            data-bs-placement="bottom"
                                                            title="Shortcut Key : Alt + C"
                                                        >
                                                           <i className="fas fa-eye text-gray-900"></i>
                                                        </a>
                                                    ) : ""}
                                                </Form.Group>
                                            </Col>
                                            <Col xl={3} md={6} sm={12} className="mb-4">
                                                <div
                                                    className="input-group flex-nowrap position-relative"
                                                    style={{ gap: 0 }}
                                                >
                                                    <div
                                                        className="input-group-text w-fit-content h-100 position-relative rate-input"
                                                        style={{
                                                            borderRadius: "8px 0 0 8px",
                                                            zIndex: "2",
                                                            minHeight: "40px",
                                                        }}
                                                    >
                                                        {company?.company?.currentCurrencySymbol
                                                            ? company?.company
                                                                  ?.currentCurrencySymbol
                                                            : "₹"}
                                                    </div>
                                                    <Form.Group
                                                        className="position-relative w-100 form-floating-group"
                                                        style={{ marginLeft: "-6px" }}
                                                    >
                                                        <FormInput
                                                            className="floating-label-input h-40"
                                                            type="text"
                                                            placeholder=""
                                                            name="rate"
                                                            value={formik.values.rate}
                                                            onChange={e => {
                                                                const value = e.target.value;
                                                                const regex = /^[0-9]*\.?[0-9]*$/;
                                                                if (regex.test(value)) {
                                                                    formik.setFieldValue(
                                                                        "rate",
                                                                        value
                                                                    );
                                                                }
                                                            }}
                                                        />
                                                        <Form.Label>Rate</Form.Label>
                                                        {company?.company?.is_gst_applicable ? (
                                                            <div className="w-fit-content bg-light position-absolute credit-dropdown-select focus-shadow">
                                                                <ReactSelect
                                                                    defaultValue={2}
                                                                    options={[
                                                                        {
                                                                            label: "With GST",
                                                                            value: 1,
                                                                        },
                                                                        {
                                                                            label: "Without GST",
                                                                            value: 2,
                                                                        },
                                                                    ]}
                                                                    value={
                                                                        formik.values
                                                                            .opening_balance_type
                                                                    }
                                                                    onChange={e =>
                                                                        formik.setFieldValue(
                                                                            "opening_balance_type",
                                                                            e.value
                                                                        )
                                                                    }
                                                                    placeholder=""
                                                                    isCreatable={false}
                                                                    showborder={false}
                                                                    showbg={true}
                                                                    height="38px"
                                                                />
                                                            </div>
                                                        ) : null}
                                                    </Form.Group>
                                                </div>
                                            </Col>
                                        </Row>
                                    </div>
                                </>
                            )}
                        </Container>
                    </div>
                    <Container fluid className="p-0 mt-10 fixed-bottom-section">
                        <div className="d-flex flex-wrap gap-sm-4 gap-3 fixed-buttons px-lg-10 px-sm-8 px-6">
                            <button
                                type="submit"
                                name="submitType"
                                value="save"
                                className="btn btn-primary"
                                onClick={() => formik.setFieldValue("submitType", "save")}
                                disabled={isDisable}
                            >
                                {id ? "Update" : "Save"}
                            </button>
                            {!id && (
                                <button
                                    type="submit"
                                    name="submitType"
                                    value="saveAndNew"
                                    className="btn btn-primary"
                                    onClick={() => formik.setFieldValue("submitType", "saveAndNew")}
                                    disabled={isDisable}
                                >
                                    Save & New
                                </button>
                            )}
                            <button
                                type="button"
                                className="btn btn-secondary"
                                onClick={() => {
                                    if (isFieldsChanges) {
                                        setIsBackButtonClick(false);
                                        setUnsavedBackUrl("/company/item-masters");
                                        handleBack();
                                    } else {
                                        window.location.href = "/company/item-masters";
                                    }
                                }}
                            >
                                Back
                            </button>
                        </div>
                    </Container>
                </form>
            )}
            <Toast />
            {isAddCessRate && <CessRateModal isAddCessRate={isAddCessRate} handleClose={() => setIsAddCessRate(false)} cessRateId={cessRateId} setCessRateId={setCessRateId} formik={formik} />}
            {isCustomFieldCalculation && (
                <CustomFieldCalculation
                    show={isCustomFieldCalculation}
                    handleCloseModel={handleCloseCalculationModel}
                    isEditModel={isEditModel}
                    selectedCustomFieldFormula={selectedCustomFieldFormula}
                    is_default_configuration={false}
                />
            )}
            {customFieldInventory && (
                <ItemMasterCustomFieldWithOneQty
                    show={customFieldInventory}
                    handleCloseModel={handleCloseCustomFieldModal}
                    customFieldDetail={customItemConfigurationList}
                    formik={formik}
                />
            )}
        </>
    );
};

export default ItemTransaction;
