import { useContext, useEffect, useState } from "react";
import { Col, Container, Row } from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import ReactSelect from "../../components/ui/ReactSelect";
import { StateContext } from "../../context/StateContext";
import useDropdownOption from "../../shared/dropdownList";
import Loader from "../../shared/loader";
import { currencyList, fetchGeneralSetting, updateGeneralSetting } from "../../store/setting/settingSlice";
const GeneralSettingsTab = () => {
    const dispatch = useDispatch();
    const { currencyOptions } = useDropdownOption();
    const { loader, setLoader, userPermission } = useContext(StateContext);
    const { setting } = useSelector(selector => selector);
    const generalSetting = setting?.generalSetting;
    const [selectedOption, setSelectedOption] = useState("");
    const [currency, setCurrency] = useState("");
    const [inventory, setInventory] = useState("");
    const [companyId, setCompanyId] = useState("");
    const [isRecurringInvoice, setIsRecurringInvoice] = useState(false);
    const [isRecycleBin, setIsRecycleBin] = useState(false);
    useEffect(() => {
        dispatch(fetchGeneralSetting());
        dispatch(currencyList());
        setTimeout(() => {
            setLoader(false);
        }, 1000);
    }, []);

    useEffect(() => {
        setSelectedOption(generalSetting?.default_currency);
        setCurrency(generalSetting?.show_currency);
        setInventory(generalSetting?.inventory_with_account);
        setCompanyId(generalSetting?.company_id);
        setIsRecurringInvoice(+generalSetting?.generate_recurring_invoice);
        setIsRecycleBin(+generalSetting?.recycle_bin);
    }, [generalSetting]);

    const handleChange = selectedOption => {
        setSelectedOption(selectedOption.value);
    };

    const handleCurrencyChange = e => {
        setCurrency(e.target.checked ? 1 : 0);
    };

    const handleInventoryChange = e => {
        setInventory(e.target.checked ? 1 : 0);
    };

    const handleSubmit = e => {
        e.preventDefault();
        const data = {
            show_currency: currency,
            default_currency: selectedOption,
            inventory_with_account: inventory,
            company_id: companyId,
            generate_recurring_invoice: isRecurringInvoice ? 1 : 0,
            recycle_bin: isRecycleBin ? 1 : 0
        };
        dispatch(updateGeneralSetting(data));
    };

    return (
        <>
            {loader ? (
                <Loader />
            ) : (
                <form onSubmit={handleSubmit} className="h-100">
                    <div className="min-h-100vh-fixed-button">
                        <Container fluid className="print-settings-container mt-2 bg-white scroll-color p-0 h-100">
                            <div className="py-6 px-lg-10 px-sm-8 px-6">
                                <Row className="  general-settings-container ">
                                        <div className="d-flex align-items-center justify-content-between ">
                                            <label
                                                htmlFor="show_currency"
                                                className="form-label fs-16px fw-bold text-gray-900 mb-0"
                                            >
                                                Show Currency:
                                            </label>
                                            <div
                                                className="form-check form-switch d-flex gap-2"
                                                style={{
                                                    paddingLeft: "0",
                                                }}
                                            >
                                                <input
                                                    className="form-check-input mb-0"
                                                    style={{
                                                        float: "right",
                                                        marginLeft: "0",
                                                    }}
                                                    type="checkbox"
                                                    id="show_currency"
                                                    checked={currency == 1}
                                                    onChange={handleCurrencyChange}
                                                    disabled={!userPermission?.edit_general_setting}
                                                />
                                            </div>
                                        </div>
                                    {currency == 1 ?
                                            <div className="d-flex align-items-center justify-content-between ">
                                                <label
                                                    htmlFor=""
                                                    className="form-label fs-16px fw-bold text-gray-900 "
                                                >
                                                    Currency:
                                                </label>
                                                <div className="focus-shadow w-150px ">
                                                    <ReactSelect
                                                        value={selectedOption}
                                                        onChange={handleChange}
                                                        options={currencyOptions}
                                                        placeholder=""
                                                        isDisabled={!userPermission?.edit_general_setting}
                                                    />
                                                </div>
                                            </div>
                                        : ""}
                                    <label
                                        htmlFor="other_setting"
                                        className="form-label fw-bolder text-gray-900 mt-4 mb-4 other-general-settings "
                                    >
                                        Other Settings
                                    </label>
                                        <div className="d-flex align-items-center justify-content-between ">
                                            <label
                                                htmlFor="inventory_with_account"
                                                className="form-label mb-0 fs-16px fw-bold text-gray-900"
                                            >
                                                Integrate Inventory with Accounts:
                                            </label>
                                            <div
                                                className="form-check form-switch d-flex gap-2"
                                                style={{
                                                    paddingLeft: "0",
                                                }}
                                            >
                                                <input
                                                    className="form-check-input mb-0"
                                                    style={{
                                                        float: "right",
                                                        marginLeft: "0",
                                                    }}
                                                    type="checkbox"
                                                    id="inventory_with_account"
                                                    checked={inventory == 1}
                                                    onChange={handleInventoryChange}
                                                    disabled={!userPermission?.edit_general_setting}
                                                />
                                            </div>
                                        </div>

                                        <div className="d-flex align-items-center justify-content-between ">
                                            <label
                                                htmlFor="recurring_invoice"
                                                className="form-label mb-0 fs-16px fw-bold text-gray-900"
                                            >
                                                Recurring Invoice:
                                            </label>

                                            <div
                                                className="form-check form-switch d-flex gap-2"
                                                style={{
                                                    paddingLeft: "0",
                                                }}
                                            >
                                                <input
                                                    className="form-check-input mb-0 "
                                                    style={{
                                                        float: "right",
                                                        marginLeft: "0",
                                                    }}
                                                    type="checkbox"
                                                    id="recurring_invoice"
                                                    checked={isRecurringInvoice}
                                                    onChange={() => setIsRecurringInvoice(!isRecurringInvoice)}
                                                    disabled={!userPermission?.edit_general_setting}
                                                />
                                            </div>
                                        </div>

                                        <div className="d-flex align-items-center justify-content-between ">
                                            <label
                                                htmlFor="recycle_bin"
                                                className="form-label mb-0 fs-16px fw-bold text-gray-900"
                                            >
                                                Recycle Bin:
                                            </label>

                                            <div
                                                className="form-check form-switch d-flex gap-2"
                                                style={{
                                                    paddingLeft: "0",
                                                }}
                                            >
                                                <input
                                                    className="form-check-input mb-0 "
                                                    style={{
                                                        float: "right",
                                                        marginLeft: "0",
                                                    }}
                                                    type="checkbox"
                                                    id="recycle_bin"
                                                    checked={isRecycleBin}
                                                    onChange={() => setIsRecycleBin(!isRecycleBin)}
                                                    disabled={!userPermission?.edit_general_setting}
                                                />
                                            </div>
                                        </div>
                                </Row>
                            </div>
                        </Container>
                    </div>
                    <Container fluid className="p-0 mt-10 fixed-bottom-section">
                        <div className="d-flex flex-wrap gap-sm-4 gap-3 fixed-buttons px-lg-10 px-sm-8 px-6">
                            <div className="d-flex">
                                <button
                                    type="submit"
                                    name="submit_button"
                                    className="btn btn-primary me-2 w-100"
                                    disabled={!userPermission?.edit_general_setting}
                                >
                                    Save
                                </button>
                            </div>
                        </div>
                    </Container>
                </form>
            )}
        </>
    );
};

export default GeneralSettingsTab;
