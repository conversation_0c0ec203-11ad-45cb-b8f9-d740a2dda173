<?php

use App\Actions\CustomFieldsItemMaster\Inventory\GetItemCFInventoryListForSale;
use App\Actions\v1\Transactions\Purchase\GetPurchaseTransactionAction;
use App\Models\ItemCustomField;
use App\Models\ItemCustomFieldCombinationInventory;
use App\Models\ItemCustomFieldSetting;
use App\Models\Master\ItemMaster;
use App\Models\PurchaseItemTransaction;
use Carbon\Carbon;
use Database\Factories\Api\CustomFieldInventory\EstimateToSaleCreateWithCFInventoryFactory;
use Database\Factories\Api\CustomFieldInventory\EstimateTransactionWithCFInventoryFactory;
use Database\Factories\Api\CustomFieldInventory\PreparePurchaseEditResponseFactory;
use Database\Factories\Api\CustomFieldInventory\PurchaseTransactionWithCFInventoryFactory;
use Database\Factories\Api\CustomFieldInventory\SaleTransactionWithCFInventoryFactory;
use Database\Seeders\tests\TestCustomFieldsInventorySeeder;

test('purchase: 1 Item : inventory 10 qty', function () {
    // arrange
    setCompany(1);

    $this->seed(TestCustomFieldsInventorySeeder::class);

    $faker = \Faker\Factory::create();

    $purchaseInput = PurchaseTransactionWithCFInventoryFactory::new()->make()->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    foreach ($purchaseInput['items'] as $key => &$item) {
        $cf = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
            $query->where('item_id', $item['item_id']);
        })->where('open_in_popup', true)->orderBy('id', 'ASC')->get()->toArray();

        $response = [];

        foreach ($cf as $i => $c) {
            $response[] = [
                'custom_field_id' => $c['id'],
                'value' => $faker->word(),
                'quantity' => 10,
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    // act
    $response = $this->postJson(route('purchase-transaction'), $purchaseInput);

    $purchaseResponse = GetPurchaseTransactionAction::run($response->json()['data']['purchaseTransaction']['id']);
    $purchaseItemsIds = collect($purchaseResponse['purchaseTransactionItems'])->pluck('id')->toArray();

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // assert
    $response->assertStatus(200);

    // This assertion is the check of inventory available quantity
    foreach ($purchaseInput['items'] as $key => $item) {
        foreach ($item['custom_field_inventory'] as $key => $cf) {
            $customFields = collect($cf)->take(5)->mapWithKeys(function ($field, $i) {
                return [
                    'cf_id_'.($i + 1) => $field['custom_field_id'],
                    'cf_value_'.($i + 1) => $field['value'],
                ];
            })->all();

            $expectedCombinationData = array_merge([
                'item_id' => $item['item_id'],
                'available_quantity' => 10,
                'purchase_rate' => $item['rpu_without_gst'],
                'purchase_date' => Carbon::parse($purchaseInput['date_of_invoice'])->format('Y-m-d'),
            ], $customFields);

            $this->assertDatabaseHas('item_custom_field_combinations', $expectedCombinationData);
        }
    }

    // This assertion is the check of total row of item inventory
    foreach ($purchaseItemsIds as $key => $id) {
        $query = ItemCustomFieldCombinationInventory::where('model_id', $id)->where('model_type', PurchaseItemTransaction::class);
        $count = $query->count();
        $this->assertEquals($count, $query->count());
    }
});

test('purchase: 10 Items(same item) : each inventory 10 qty', function () {
    // arrange
    setCompany(1);

    $this->seed(TestCustomFieldsInventorySeeder::class);

    $purchaseInput = PurchaseTransactionWithCFInventoryFactory::new()->make()->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    $items = collect($purchaseInput['items'])->first();
    $finalItems = [];
    for ($i = 0; $i < 10; $i++) {
        $finalItems[] = $items;
    }
    $purchaseInput['items'] = $finalItems;

    foreach ($purchaseInput['items'] as $key => &$item) {
        $cf = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
            $query->where('item_id', $item['item_id']);
        })->where('open_in_popup', true)->orderBy('id', 'ASC')->get()->toArray();

        $response = [];

        foreach ($cf as $i => $c) {
            $response[] = [
                'custom_field_id' => $c['id'],
                'value' => $i == 0 ? 'B123' : 'BLACK',
                'quantity' => 10,
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    // act
    $response = $this->postJson(route('purchase-transaction'), $purchaseInput);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    $response->assertStatus(200);

    $customFields = null;
    foreach ($purchaseInput['items'] as $key => $item) {
        foreach ($item['custom_field_inventory'] as $key => $cf) {
            $customFields = collect($cf)->take(5)->mapWithKeys(function ($field, $i) {
                return [
                    'cf_id_'.($i + 1) => $field['custom_field_id'],
                    'cf_value_'.($i + 1) => $field['value'],
                ];
            })->all();
        }
    }

    // assert
    $expectedCombinationData = array_merge([
        'item_id' => $items['item_id'],
        'available_quantity' => 100,
        'purchase_rate' => $items['rpu_without_gst'],
        'purchase_date' => Carbon::parse($purchaseInput['date_of_invoice'])->format('Y-m-d'),
    ], $customFields);

    // check for one combination of all items with available quantity 100
    $this->assertDatabaseHas('item_custom_field_combinations', $expectedCombinationData);

    $this->assertDatabaseCount('item_custom_field_combinations', 1);

    $this->assertDatabaseCount('item_custom_field_combination_inventory', 10);
});

test('purchase: 1 Item : inventory 25 qty & sale: 5 Items(same item) : each inventory 10 qty', function () {
    // arrange
    setCompany(1);

    $this->seed(TestCustomFieldsInventorySeeder::class);

    // First Purchase with 25 qty inventory of single item & total inventory 25
    $purchaseInput = PurchaseTransactionWithCFInventoryFactory::new()->make()->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    foreach ($purchaseInput['items'] as $key => &$item) {
        $cf = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
            $query->where('item_id', $item['item_id']);
        })->where('open_in_popup', true)->orderBy('id', 'ASC')->get()->toArray();

        $response = [];

        foreach ($cf as $i => $c) {
            $response[] = [
                'custom_field_id' => $c['id'],
                'value' => $i == 0 ? 'B123' : 'BLACK',
                'quantity' => 25,
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    $purchaseResponse = $this->postJson(route('purchase-transaction'), $purchaseInput);

    // assert
    $purchaseResponse->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // Second Sale with 5 same items with 10 qty total inventory is 50
    $saleInput = SaleTransactionWithCFInventoryFactory::new()->make()->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');

    $saleItems = collect($saleInput['items'])->first();
    $finalSaleItems = [];
    for ($i = 0; $i < 5; $i++) {
        $finalSaleItems[] = $saleItems;
    }
    $saleInput['items'] = $finalSaleItems;

    foreach ($saleInput['items'] as $key => &$item) {
        $cf = GetItemCFInventoryListForSale::run($item['item_id'], 1);
        $cf = $cf[0]['fields'];
        $response = [];

        foreach ($cf as $i => $c) {
            $response[] = [
                'custom_field_id' => $c['custom_field_id'],
                'value' => $c['value'],
                'quantity' => 10,
                'purchase_rate' => $c['purchase_rate'],
                'purchase_date' => $c['purchase_date'],
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    $saleResponse = $this->postJson(route('sale-transaction'), $saleInput);

    // assert
    $saleResponse->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    $customFields = null;
    $purchaseRate = null;
    $purchaseDate = null;
    foreach ($saleInput['items'] as $key => $item) {
        foreach ($item['custom_field_inventory'] as $key => $cf) {
            $customFields = collect($cf)->take(5)->mapWithKeys(function ($field, $i) {
                return [
                    'cf_id_'.($i + 1) => $field['custom_field_id'],
                    'cf_value_'.($i + 1) => $field['value'],
                ];
            })->all();

            $purchaseRate = $purchaseRate ?? $cf[0]['purchase_rate'];
            $purchaseDate = $purchaseDate ?? $cf[0]['purchase_date'];
        }
    }

    // assert
    $expectedCombinationData = array_merge([
        'item_id' => $saleItems['item_id'],
        'available_quantity' => -25,
        'purchase_rate' => $purchaseRate,
        'purchase_date' => Carbon::parse($purchaseDate)->format('Y-m-d'),
    ], $customFields);

    // check for one combination of with nagative quantity
    $this->assertDatabaseHas('item_custom_field_combinations', $expectedCombinationData);

});

test('purchase: 1 Item : inventory 10 qty & sale: 1 Item : inventory 10 qty', function () {
    // arrange
    setCompany(1);

    $this->seed(TestCustomFieldsInventorySeeder::class);

    // First Purchase with 10 qty inventory of single item & total inventory 10
    $purchaseInput = PurchaseTransactionWithCFInventoryFactory::new()->make()->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    foreach ($purchaseInput['items'] as $key => &$item) {
        $cf = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
            $query->where('item_id', $item['item_id']);
        })->where('open_in_popup', true)->orderBy('id', 'ASC')->get()->toArray();

        $response = [];

        foreach ($cf as $i => $c) {
            $response[] = [
                'custom_field_id' => $c['id'],
                'value' => $i == 0 ? 'B123' : 'BLACK',
                'quantity' => 10,
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    $purchaseResponse = $this->postJson(route('purchase-transaction'), $purchaseInput);

    // assert
    $purchaseResponse->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // Second Sale with 10 qty inventory of single item & total inventory 10
    $saleInput = SaleTransactionWithCFInventoryFactory::new()->make()->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');

    foreach ($saleInput['items'] as $key => &$item) {
        $cf = GetItemCFInventoryListForSale::run($item['item_id'], 1);
        $cf = $cf[0]['fields'];
        $response = [];

        foreach ($cf as $i => $c) {
            $response[] = [
                'custom_field_id' => $c['custom_field_id'],
                'value' => $c['value'],
                'quantity' => 10,
                'purchase_rate' => $c['purchase_rate'],
                'purchase_date' => $c['purchase_date'],
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    $saleResponse = $this->postJson(route('sale-transaction'), $saleInput);

    // assert
    $saleResponse->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    $customFields = null;
    $purchaseRate = null;
    $purchaseDate = null;
    $itemId = $saleInput['items'][0]['item_id'];

    foreach ($saleInput['items'] as $key => $item) {
        foreach ($item['custom_field_inventory'] as $key => $cf) {
            $customFields = collect($cf)->take(5)->mapWithKeys(function ($field, $i) {
                return [
                    'cf_id_'.($i + 1) => $field['custom_field_id'],
                    'cf_value_'.($i + 1) => $field['value'],
                ];
            })->all();

            $purchaseRate = $purchaseRate ?? $cf[0]['purchase_rate'];
            $purchaseDate = $purchaseDate ?? $cf[0]['purchase_date'];
        }
    }

    // assert
    $expectedCombinationData = array_merge([
        'item_id' => $itemId,
        'available_quantity' => 0,
        'purchase_rate' => $purchaseRate,
        'purchase_date' => Carbon::parse($purchaseDate)->format('Y-m-d'),
    ], $customFields);

    // check for one combination of all items with available quantity 100
    $this->assertDatabaseHas('item_custom_field_combinations', $expectedCombinationData);

    $this->assertDatabaseCount('item_custom_field_combinations', 1);

    $this->assertDatabaseCount('item_custom_field_combination_inventory', 2);
});

test('purchase: 1 Item : inventory 10 qty & sale: 1 Item : inventory 10 qty & purchase: 1 Item : inventory 10 qty', function () {
    // arrange
    setCompany(1);

    $this->seed(TestCustomFieldsInventorySeeder::class);

    // First Purchase with 10 qty inventory of single item & total inventory 10
    $purchaseInput = PurchaseTransactionWithCFInventoryFactory::new()->make()->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    foreach ($purchaseInput['items'] as $key => &$item) {
        $cf = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
            $query->where('item_id', $item['item_id']);
        })->where('open_in_popup', true)->orderBy('id', 'ASC')->get()->toArray();

        $response = [];

        foreach ($cf as $i => $c) {
            $response[] = [
                'custom_field_id' => $c['id'],
                'value' => $i == 0 ? 'B123' : 'BLACK',
                'quantity' => 10,
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    $purchaseResponse = $this->postJson(route('purchase-transaction'), $purchaseInput);

    // assert
    $purchaseResponse->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // Second Sale with 10 qty inventory of single item & total inventory 10
    $saleInput = SaleTransactionWithCFInventoryFactory::new()->make()->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');

    foreach ($saleInput['items'] as $key => &$item) {
        $cf = GetItemCFInventoryListForSale::run($item['item_id'], 1);
        $cf = $cf[0]['fields'];
        $response = [];

        foreach ($cf as $i => $c) {
            $response[] = [
                'custom_field_id' => $c['custom_field_id'],
                'value' => $c['value'],
                'quantity' => 10,
                'purchase_rate' => $c['purchase_rate'],
                'purchase_date' => $c['purchase_date'],
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    $saleResponse = $this->postJson(route('sale-transaction'), $saleInput);

    // assert
    $saleResponse->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // Third Purchase with 10 qty inventory of single item & total inventory 10
    $purchaseInput2 = PurchaseTransactionWithCFInventoryFactory::new()->make()->toArray();
    $purchaseInput2['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput2['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    foreach ($purchaseInput2['items'] as $key => &$item) {
        $cf = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
            $query->where('item_id', $item['item_id']);
        })->where('open_in_popup', true)->orderBy('id', 'ASC')->get()->toArray();

        $response = [];

        foreach ($cf as $i => $c) {
            $response[] = [
                'custom_field_id' => $c['id'],
                'value' => $i == 0 ? 'B123' : 'BLACK',
                'quantity' => 10,
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    $purchaseResponse2 = $this->postJson(route('purchase-transaction'), $purchaseInput2);

    // assert
    $purchaseResponse2->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    $customFields = null;
    $purchaseRate = null;
    $purchaseDate = null;
    $itemId = $saleInput['items'][0]['item_id'];

    foreach ($saleInput['items'] as $key => $item) {
        foreach ($item['custom_field_inventory'] as $key => $cf) {
            $customFields = collect($cf)->take(5)->mapWithKeys(function ($field, $i) {
                return [
                    'cf_id_'.($i + 1) => $field['custom_field_id'],
                    'cf_value_'.($i + 1) => $field['value'],
                ];
            })->all();
            $purchaseRate = $purchaseRate ?? $cf[0]['purchase_rate'];
            $purchaseDate = $purchaseDate ?? $cf[0]['purchase_date'];
        }
    }

    // assert
    $expectedCombinationData = array_merge([
        'item_id' => $itemId,
        'available_quantity' => 10,
        'purchase_rate' => $purchaseRate,
        'purchase_date' => Carbon::parse($purchaseDate)->format('Y-m-d'),
    ], $customFields);

    // check for one combination of all items with available quantity 100
    $this->assertDatabaseHas('item_custom_field_combinations', $expectedCombinationData);

    $this->assertDatabaseCount('item_custom_field_combinations', 1);

    $this->assertDatabaseCount('item_custom_field_combination_inventory', 3);
});

test('purchase: 1 Item : inventory 10 qty & purchase edit: 1 Item : inventory 20 qty', function () {
    // arrange
    setCompany(1);

    $this->seed(TestCustomFieldsInventorySeeder::class);

    // First Sale with 10 qty inventory of single item & total inventory 10
    $purchaseInput = PurchaseTransactionWithCFInventoryFactory::new()->make()->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    foreach ($purchaseInput['items'] as $key => &$item) {
        $cf = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
            $query->where('item_id', $item['item_id']);
        })->where('open_in_popup', true)->orderBy('id', 'ASC')->get()->toArray();

        $response = [];

        foreach ($cf as $i => $c) {
            $response[] = [
                'custom_field_id' => $c['id'],
                'value' => $i == 0 ? 'B123' : 'BLACK',
                'quantity' => 10,
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    // act
    $response = $this->postJson(route('purchase-transaction'), $purchaseInput);

    $purchaseId = $response->json()['data']['purchaseTransaction']['id'];

    // assert
    $response->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */
    // update purchase with single item inv 20.Q
    $purchaseEditInput = PreparePurchaseEditResponseFactory::new()->make()->toArray();
    $purchaseEditInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseEditInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    foreach ($purchaseEditInput['items'] as $key => &$item) {
        $cf = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
            $query->where('item_id', $item['item_id']);
        })->where('open_in_popup', true)->orderBy('id', 'ASC')->get()->toArray();

        $response = [];

        foreach ($cf as $i => $c) {
            $response[] = [
                'custom_field_id' => $c['id'],
                'value' => $i == 0 ? 'B123' : 'BLACK',
                'quantity' => 20,
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    $purchaseEditResponse = $this->postJson(route('purchase-transaction-update', $purchaseId), $purchaseEditInput);

    // assert
    $purchaseEditResponse->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    $customFields = null;
    foreach ($purchaseEditInput['items'] as $key => $item) {
        foreach ($item['custom_field_inventory'] as $key => $cf) {
            $customFields = collect($cf)->take(5)->mapWithKeys(function ($field, $i) {
                return [
                    'cf_id_'.($i + 1) => $field['custom_field_id'],
                    'cf_value_'.($i + 1) => $field['value'],
                ];
            })->all();
        }
    }

    // assert
    $expectedCombinationData = array_merge([
        'item_id' => $purchaseEditInput['items'][0]['item_id'],
        'available_quantity' => 20,
        'purchase_rate' => 100,
        'purchase_date' => Carbon::parse($purchaseEditInput['date_of_invoice'])->format('Y-m-d'),
    ], $customFields);

    $this->assertDatabaseHas('item_custom_field_combinations', $expectedCombinationData);

    $this->assertDatabaseCount('item_custom_field_combinations', 1);

    $this->assertDatabaseCount('item_custom_field_combination_inventory', 1);
});

test('purchase: 1 Item : inventory 10 qty & purchase edit (different item): 1 Item : inventory 10 qty', function () {
    // arrange
    setCompany(1);

    $this->seed(TestCustomFieldsInventorySeeder::class);

    // First Sale with 10 qty inventory of single item & total inventory 10
    $purchaseInput = PurchaseTransactionWithCFInventoryFactory::new()->make()->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    foreach ($purchaseInput['items'] as $key => &$item) {
        $cf = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
            $query->where('item_id', $item['item_id']);
        })->where('open_in_popup', true)->orderBy('id', 'ASC')->get()->toArray();

        $response = [];

        foreach ($cf as $i => $c) {
            $response[] = [
                'custom_field_id' => $c['id'],
                'value' => $i == 0 ? 'B123' : 'BLACK',
                'quantity' => 10,
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    // act
    $response = $this->postJson(route('purchase-transaction'), $purchaseInput);

    $purchaseId = $response->json()['data']['purchaseTransaction']['id'];

    // assert
    $response->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // update purchase with different item inv 10.Q
    $purchaseEditInput = PreparePurchaseEditResponseFactory::new()->make()->toArray();
    $purchaseEditInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseEditInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    foreach ($purchaseEditInput['items'] as $key => &$item) {
        $newItemSelect = ItemMaster::where('id', '!=', $item['item_id'])->first();

        $item['item_id'] = $newItemSelect->id; // change item with same purchase_item_id
        $item['unit_id'] = $newItemSelect->model->unit_of_measurement;

        $cf = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
            $query->where('item_id', $item['item_id']);
        })->where('open_in_popup', true)->orderBy('id', 'ASC')->get()->toArray();

        $response = [];

        foreach ($cf as $i => $c) {
            $response[] = [
                'custom_field_id' => $c['id'],
                'value' => $i == 0 ? 'B123' : 'BLACK',
                'quantity' => 10,
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    $purchaseEditResponse = $this->postJson(route('purchase-transaction-update', $purchaseId), $purchaseEditInput);

    // assert
    $purchaseEditResponse->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    $customFields = null;
    foreach ($purchaseEditInput['items'] as $key => $item) {
        foreach ($item['custom_field_inventory'] as $key => $cf) {
            $customFields = collect($cf)->take(5)->mapWithKeys(function ($field, $i) {
                return [
                    'cf_id_'.($i + 1) => $field['custom_field_id'],
                    'cf_value_'.($i + 1) => $field['value'],
                ];
            })->all();
        }
    }

    // assert
    $expectedCombinationData = array_merge([
        'item_id' => $purchaseEditInput['items'][0]['item_id'],
        'available_quantity' => 10,
        'purchase_rate' => 100,
        'purchase_date' => Carbon::parse($purchaseEditInput['date_of_invoice'])->format('Y-m-d'),
    ], $customFields);

    $this->assertDatabaseHas('item_custom_field_combinations', $expectedCombinationData);

    $this->assertDatabaseCount('item_custom_field_combinations', 1);

    $this->assertDatabaseCount('item_custom_field_combination_inventory', 1);
});

test('purchase: 1 Item : inventory 10 qty & purchase edit (new item): 1 Item : inventory 20 qty', function () {
    // arrange
    setCompany(1);

    $this->seed(TestCustomFieldsInventorySeeder::class);

    // First purchase with 10 qty inventory of single item & total inventory 10
    $purchaseInput = PurchaseTransactionWithCFInventoryFactory::new()->make()->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    foreach ($purchaseInput['items'] as $key => &$item) {
        $cf = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
            $query->where('item_id', $item['item_id']);
        })->where('open_in_popup', true)->orderBy('id', 'ASC')->get()->toArray();

        $response = [];

        foreach ($cf as $i => $c) {
            $response[] = [
                'custom_field_id' => $c['id'],
                'value' => $i == 0 ? 'B123' : 'BLACK',
                'quantity' => 10,
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    // act
    $response = $this->postJson(route('purchase-transaction'), $purchaseInput);

    $purchaseId = $response->json()['data']['purchaseTransaction']['id'];

    // assert
    $response->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // update with remove existing item and add new item inv 20.Q
    $purchaseEditInput = PreparePurchaseEditResponseFactory::new()->make()->toArray();
    $purchaseEditInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseEditInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    foreach ($purchaseEditInput['items'] as $key => &$item) {
        $cf = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
            $query->where('item_id', $item['item_id']);
        })->where('open_in_popup', true)->orderBy('id', 'ASC')->get()->toArray();

        $response = [];

        foreach ($cf as $i => $c) {
            $response[] = [
                'custom_field_id' => $c['id'],
                'value' => $i == 0 ? 'B123' : 'BLACK',
                'quantity' => 20,
            ];
        }

        $item['custom_field_inventory'] = [$response];
        unset($item['id']); // unset existing purchase item id so it means add new item
    }

    $purchaseEditResponse = $this->postJson(route('purchase-transaction-update', $purchaseId), $purchaseEditInput);

    // assert
    $purchaseEditResponse->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    $customFields = null;
    foreach ($purchaseEditInput['items'] as $key => $item) {
        foreach ($item['custom_field_inventory'] as $key => $cf) {
            $customFields = collect($cf)->take(5)->mapWithKeys(function ($field, $i) {
                return [
                    'cf_id_'.($i + 1) => $field['custom_field_id'],
                    'cf_value_'.($i + 1) => $field['value'],
                ];
            })->all();
        }
    }

    // assert
    $expectedCombinationData = array_merge([
        'item_id' => $purchaseEditInput['items'][0]['item_id'],
        'available_quantity' => 20,
        'purchase_rate' => 100,
        'purchase_date' => Carbon::parse($purchaseEditInput['date_of_invoice'])->format('Y-m-d'),
    ], $customFields);

    $this->assertDatabaseHas('item_custom_field_combinations', $expectedCombinationData);

    $this->assertDatabaseCount('item_custom_field_combination_inventory', 1);

    $purchaseResponse = GetPurchaseTransactionAction::run($purchaseId);
    $purchaseItemsIds = collect($purchaseResponse['purchaseTransactionItems'])->pluck('id')->toArray();

    foreach ($purchaseItemsIds as $key => $id) {
        $query = ItemCustomFieldCombinationInventory::where('model_id', $id)->where('model_type', PurchaseItemTransaction::class);
        $inventory = $query->get();

        $this->assertEquals($inventory->count(), $query->count());

        foreach ($inventory as $key => $inv) {
            $this->assertDatabaseHas('item_custom_field_combination_inventory', [
                'model_id' => $inv['model_id'],
                'model_type' => $inv['model_type'],
                'quantity' => $inv['quantity'],
                'item_custom_field_combination_id' => $inv['item_custom_field_combination_id'],
            ]);
        }
    }
});

test('purchase: 1 Item : inventory 10 qty & purchase edit (remove item & add new item): 1 Item : inventory 10 qty', function () {
    // arrange
    setCompany(1);

    $this->seed(TestCustomFieldsInventorySeeder::class);

    // First Sale with 10 qty inventory of single item & total inventory 10
    $purchaseInput = PurchaseTransactionWithCFInventoryFactory::new()->make()->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    foreach ($purchaseInput['items'] as $key => &$item) {
        $cf = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
            $query->where('item_id', $item['item_id']);
        })->where('open_in_popup', true)->orderBy('id', 'ASC')->get()->toArray();

        $response = [];

        foreach ($cf as $i => $c) {
            $response[] = [
                'custom_field_id' => $c['id'],
                'value' => $i == 0 ? 'B123' : 'BLACK',
                'quantity' => 10,
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    // act
    $response = $this->postJson(route('purchase-transaction'), $purchaseInput);

    $purchaseId = $response->json()['data']['purchaseTransaction']['id'];

    // assert
    $response->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // Second update purchase with different item & remove existing purchase_item_id & inv 10.Q
    $purchaseEditInput = PreparePurchaseEditResponseFactory::new()->make()->toArray();
    $purchaseEditInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseEditInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    foreach ($purchaseEditInput['items'] as $key => &$item) {
        $newItemSelect = ItemMaster::where('id', '!=', $item['item_id'])->first();

        $item['item_id'] = $newItemSelect->id; // change item with same purchase_item_id
        $item['unit_id'] = $newItemSelect->model->unit_of_measurement;

        $cf = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
            $query->where('item_id', $item['item_id']);
        })->where('open_in_popup', true)->orderBy('id', 'ASC')->get()->toArray();

        $response = [];

        foreach ($cf as $i => $c) {
            $response[] = [
                'custom_field_id' => $c['id'],
                'value' => $i == 0 ? 'B123' : 'BLACK',
                'quantity' => 10,
            ];
        }

        $item['custom_field_inventory'] = [$response];
        unset($item['id']); // unset existing purchase item id so it means add new item
    }

    $purchaseEditResponse = $this->postJson(route('purchase-transaction-update', $purchaseId), $purchaseEditInput);

    // assert
    $purchaseEditResponse->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    $customFields = null;
    foreach ($purchaseEditInput['items'] as $key => $item) {
        foreach ($item['custom_field_inventory'] as $key => $cf) {
            $customFields = collect($cf)->take(5)->mapWithKeys(function ($field, $i) {
                return [
                    'cf_id_'.($i + 1) => $field['custom_field_id'],
                    'cf_value_'.($i + 1) => $field['value'],
                ];
            })->all();
        }
    }

    // assert
    $expectedCombinationData = array_merge([
        'item_id' => $purchaseEditInput['items'][0]['item_id'],
        'available_quantity' => 10,
        'purchase_rate' => 100,
        'purchase_date' => Carbon::parse($purchaseEditInput['date_of_invoice'])->format('Y-m-d'),
    ], $customFields);

    $this->assertDatabaseHas('item_custom_field_combinations', $expectedCombinationData);

    $this->assertDatabaseCount('item_custom_field_combinations', 1);

    $this->assertDatabaseCount('item_custom_field_combination_inventory', 1);
});

test('purchase: 1 Item : inventory 10 qty & purchase edit : 1 Item : update inventory value in same item inv 10 qty', function () {
    // arrange
    setCompany(1);

    $this->seed(TestCustomFieldsInventorySeeder::class);

    // First: Purchase with single item & total inventory 10 (row line 1)
    $purchaseInput = PurchaseTransactionWithCFInventoryFactory::new()->make()->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    foreach ($purchaseInput['items'] as $key => &$item) {
        $cf = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
            $query->where('item_id', $item['item_id']);
        })->where('open_in_popup', true)->orderBy('id', 'ASC')->get()->toArray();

        $response = [];

        foreach ($cf as $i => $c) {
            $response[] = [
                'custom_field_id' => $c['id'],
                'value' => $i == 0 ? 'B123' : 'BLACK',
                'quantity' => 10,
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    // act
    $response = $this->postJson(route('purchase-transaction'), $purchaseInput);

    $purchaseId = $response->json()['data']['purchaseTransaction']['id'];

    // assert
    $response->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // Second update purchase single item & total inventory 10 but update custom field inv. value
    $purchaseEditInput = PreparePurchaseEditResponseFactory::new()->make()->toArray();
    $purchaseEditInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseEditInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    foreach ($purchaseEditInput['items'] as $key => &$item) {
        $cf = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
            $query->where('item_id', $item['item_id']);
        })->where('open_in_popup', true)->orderBy('id', 'ASC')->get()->toArray();

        $response = [];

        foreach ($cf as $i => $c) {
            $response[] = [
                'custom_field_id' => $c['id'],
                'value' => $i == 0 ? 'B111' : 'BLACK',
                'quantity' => 10,
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    $purchaseEditResponse = $this->postJson(route('purchase-transaction-update', $purchaseId), $purchaseEditInput);

    // assert
    $purchaseEditResponse->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    $customFields = null;
    foreach ($purchaseEditInput['items'] as $key => $item) {
        foreach ($item['custom_field_inventory'] as $key => $cf) {
            $customFields = collect($cf)->take(5)->mapWithKeys(function ($field, $i) {
                return [
                    'cf_id_'.($i + 1) => $field['custom_field_id'],
                    'cf_value_'.($i + 1) => $field['value'],
                ];
            })->all();
        }
    }

    // assert
    $expectedCombinationData = array_merge([
        'item_id' => $purchaseEditInput['items'][0]['item_id'],
        'available_quantity' => 10,
        'purchase_rate' => 100,
        'purchase_date' => Carbon::parse($purchaseEditInput['date_of_invoice'])->format('Y-m-d'),
    ], $customFields);

    $this->assertDatabaseHas('item_custom_field_combinations', $expectedCombinationData);

    $this->assertDatabaseCount('item_custom_field_combinations', 1);

    $this->assertDatabaseCount('item_custom_field_combination_inventory', 1);
});

test('purchase: 1 Item : inventory 10 qty & sale: 1 Item : inventory 10 qty & purchase edit : 1 Item : update inventory value in same item inv 10 qty', function () {
    // arrange
    setCompany(1);

    $this->seed(TestCustomFieldsInventorySeeder::class);

    // First: Purchase with single item & total inventory 10 (row line 1)
    $purchaseInput = PurchaseTransactionWithCFInventoryFactory::new()->make()->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    foreach ($purchaseInput['items'] as $key => &$item) {
        $cf = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
            $query->where('item_id', $item['item_id']);
        })->where('open_in_popup', true)->orderBy('id', 'ASC')->get()->toArray();

        $response = [];

        foreach ($cf as $i => $c) {
            $response[] = [
                'custom_field_id' => $c['id'],
                'value' => $i == 0 ? 'B123' : 'BLACK',
                'quantity' => 10,
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    // act
    $response = $this->postJson(route('purchase-transaction'), $purchaseInput);

    $purchaseId = $response->json()['data']['purchaseTransaction']['id'];

    // assert
    $response->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // Second Sale with 10 qty inventory of single item & total inventory 10
    $saleInput = SaleTransactionWithCFInventoryFactory::new()->make()->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');

    foreach ($saleInput['items'] as $key => &$item) {
        $cf = GetItemCFInventoryListForSale::run($item['item_id'], 1);
        $cf = $cf[0]['fields'];
        $response = [];

        foreach ($cf as $i => $c) {
            $response[] = [
                'custom_field_id' => $c['custom_field_id'],
                'value' => $c['value'],
                'quantity' => 5,
                'purchase_rate' => $c['purchase_rate'],
                'purchase_date' => $c['purchase_date'],
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    $saleResponse = $this->postJson(route('sale-transaction'), $saleInput);

    // assert
    $saleResponse->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // Third update purchase single item & total inventory 10 but update custom field inv. value
    $purchaseEditInput = PreparePurchaseEditResponseFactory::new()->make()->toArray();
    $purchaseEditInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseEditInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    foreach ($purchaseEditInput['items'] as $key => &$item) {
        $cf = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
            $query->where('item_id', $item['item_id']);
        })->where('open_in_popup', true)->orderBy('id', 'ASC')->get()->toArray();

        $response = [];

        foreach ($cf as $i => $c) {
            $response[] = [
                'custom_field_id' => $c['id'],
                'value' => $i == 0 ? 'B111' : 'BLACK',
                'quantity' => 10,
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    $purchaseEditResponse = $this->postJson(route('purchase-transaction-update', $purchaseId), $purchaseEditInput);

    // assert
    $purchaseEditResponse->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // check 2nd combination
    $secondCustomFieldsCombination = null;
    foreach ($purchaseEditInput['items'] as $key => $item) {
        foreach ($item['custom_field_inventory'] as $key => $cf) {
            $secondCustomFieldsCombination = collect($cf)->take(5)->mapWithKeys(function ($field, $i) {
                return [
                    'cf_id_'.($i + 1) => $field['custom_field_id'],
                    'cf_value_'.($i + 1) => $field['value'],
                ];
            })->all();
        }
    }

    // assert
    $expectedSecondCombinationData = array_merge([
        'item_id' => $purchaseEditInput['items'][0]['item_id'],
        'available_quantity' => 10,
        'purchase_rate' => 100,
        'purchase_date' => Carbon::parse($purchaseEditInput['date_of_invoice'])->format('Y-m-d'),
    ], $secondCustomFieldsCombination);

    $this->assertDatabaseHas('item_custom_field_combinations', $expectedSecondCombinationData);

    // check 1nd combination
    $firstCustomFieldsCombination = null;
    foreach ($saleInput['items'] as $key => $item) {
        foreach ($item['custom_field_inventory'] as $key => $cf) {
            $firstCustomFieldsCombination = collect($cf)->take(5)->mapWithKeys(function ($field, $i) {
                return [
                    'cf_id_'.($i + 1) => $field['custom_field_id'],
                    'cf_value_'.($i + 1) => $field['value'],
                ];
            })->all();
        }
    }

    // assert
    $expectedFirstCombinationData = array_merge([
        'item_id' => $saleInput['items'][0]['item_id'],
        'available_quantity' => -5,
        'purchase_rate' => 100,
        'purchase_date' => Carbon::parse($saleInput['date'])->format('Y-m-d'),
    ], $firstCustomFieldsCombination);

    $this->assertDatabaseHas('item_custom_field_combinations', $expectedFirstCombinationData);

    $this->assertDatabaseCount('item_custom_field_combinations', 2);

    $this->assertDatabaseCount('item_custom_field_combination_inventory', 2);
});

test('purchase: 1 Item : inventory 10 qty & sale: 1 Item : inventory 5 qty & purchase edit : 1 Item : update inventory 7 qty', function () {
    // arrange
    setCompany(1);

    $this->seed(TestCustomFieldsInventorySeeder::class);

    // First: Purchase with single item & total inventory 10 (row line 1)
    $purchaseInput = PurchaseTransactionWithCFInventoryFactory::new()->make()->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    foreach ($purchaseInput['items'] as $key => &$item) {
        $cf = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
            $query->where('item_id', $item['item_id']);
        })->where('open_in_popup', true)->orderBy('id', 'ASC')->get()->toArray();

        $response = [];

        foreach ($cf as $i => $c) {
            $response[] = [
                'custom_field_id' => $c['id'],
                'value' => $i == 0 ? 'B123' : 'BLACK',
                'quantity' => 10,
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    // act
    $response = $this->postJson(route('purchase-transaction'), $purchaseInput);

    $purchaseId = $response->json()['data']['purchaseTransaction']['id'];

    // assert
    $response->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // Second Sale with 10 qty inventory of single item & total inventory 10
    $saleInput = SaleTransactionWithCFInventoryFactory::new()->make()->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');

    foreach ($saleInput['items'] as $key => &$item) {
        $cf = GetItemCFInventoryListForSale::run($item['item_id'], 1);
        $cf = $cf[0]['fields'];
        $response = [];

        foreach ($cf as $i => $c) {
            $response[] = [
                'custom_field_id' => $c['custom_field_id'],
                'value' => $c['value'],
                'quantity' => 5,
                'purchase_rate' => $c['purchase_rate'],
                'purchase_date' => $c['purchase_date'],
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    $saleResponse = $this->postJson(route('sale-transaction'), $saleInput);

    // assert
    $saleResponse->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // Third update purchase single item & total inventory 10 but update custom field inv. value
    $purchaseEditInput = PreparePurchaseEditResponseFactory::new()->make()->toArray();
    $purchaseEditInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseEditInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    foreach ($purchaseEditInput['items'] as $key => &$item) {
        $cf = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
            $query->where('item_id', $item['item_id']);
        })->where('open_in_popup', true)->orderBy('id', 'ASC')->get()->toArray();

        $response = [];

        foreach ($cf as $i => $c) {
            $response[] = [
                'custom_field_id' => $c['id'],
                'value' => $i == 0 ? 'B123' : 'BLACK',
                'quantity' => 7,
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    $purchaseEditResponse = $this->postJson(route('purchase-transaction-update', $purchaseId), $purchaseEditInput);

    // assert
    $purchaseEditResponse->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // check 2nd combination
    $secondCustomFieldsCombination = null;
    foreach ($purchaseEditInput['items'] as $key => $item) {
        foreach ($item['custom_field_inventory'] as $key => $cf) {
            $secondCustomFieldsCombination = collect($cf)->take(5)->mapWithKeys(function ($field, $i) {
                return [
                    'cf_id_'.($i + 1) => $field['custom_field_id'],
                    'cf_value_'.($i + 1) => $field['value'],
                ];
            })->all();
        }
    }

    // assert
    $expectedSecondCombinationData = array_merge([
        'item_id' => $purchaseEditInput['items'][0]['item_id'],
        'available_quantity' => 2,
        'purchase_rate' => 100,
        'purchase_date' => Carbon::parse($purchaseEditInput['date_of_invoice'])->format('Y-m-d'),
    ], $secondCustomFieldsCombination);

    $this->assertDatabaseHas('item_custom_field_combinations', $expectedSecondCombinationData);

    $this->assertDatabaseCount('item_custom_field_combinations', 1);

    $this->assertDatabaseCount('item_custom_field_combination_inventory', 2);
});

test('purchase: 1 Item : inventory 10 qty & sale: 1 Item : inventory 5 qty & purchase edit : 1 Item : update inventory 3 qty', function () {
    // arrange
    setCompany(1);

    $this->seed(TestCustomFieldsInventorySeeder::class);

    // First: Purchase with single item & total inventory 10 (row line 1)
    $purchaseInput = PurchaseTransactionWithCFInventoryFactory::new()->make()->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    foreach ($purchaseInput['items'] as $key => &$item) {
        $cf = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
            $query->where('item_id', $item['item_id']);
        })->where('open_in_popup', true)->orderBy('id', 'ASC')->get()->toArray();

        $response = [];

        foreach ($cf as $i => $c) {
            $response[] = [
                'custom_field_id' => $c['id'],
                'value' => $i == 0 ? 'B123' : 'BLACK',
                'quantity' => 10,
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    // act
    $response = $this->postJson(route('purchase-transaction'), $purchaseInput);

    $purchaseId = $response->json()['data']['purchaseTransaction']['id'];

    // assert
    $response->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // Second Sale with 10 qty inventory of single item & total inventory 10
    $saleInput = SaleTransactionWithCFInventoryFactory::new()->make()->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');

    foreach ($saleInput['items'] as $key => &$item) {
        $cf = GetItemCFInventoryListForSale::run($item['item_id'], 1);
        $cf = $cf[0]['fields'];
        $response = [];

        foreach ($cf as $i => $c) {
            $response[] = [
                'custom_field_id' => $c['custom_field_id'],
                'value' => $c['value'],
                'quantity' => 5,
                'purchase_rate' => $c['purchase_rate'],
                'purchase_date' => $c['purchase_date'],
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    $saleResponse = $this->postJson(route('sale-transaction'), $saleInput);

    // assert
    $saleResponse->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // Third update purchase single item & total inventory 10 but update custom field inv. value
    $purchaseEditInput = PreparePurchaseEditResponseFactory::new()->make()->toArray();
    $purchaseEditInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseEditInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    foreach ($purchaseEditInput['items'] as $key => &$item) {
        $cf = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
            $query->where('item_id', $item['item_id']);
        })->where('open_in_popup', true)->orderBy('id', 'ASC')->get()->toArray();

        $response = [];

        foreach ($cf as $i => $c) {
            $response[] = [
                'custom_field_id' => $c['id'],
                'value' => $i == 0 ? 'B123' : 'BLACK',
                'quantity' => 3,
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    $purchaseEditResponse = $this->postJson(route('purchase-transaction-update', $purchaseId), $purchaseEditInput);

    // assert
    $purchaseEditResponse->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // check 2nd combination
    $secondCustomFieldsCombination = null;
    foreach ($purchaseEditInput['items'] as $key => $item) {
        foreach ($item['custom_field_inventory'] as $key => $cf) {
            $secondCustomFieldsCombination = collect($cf)->take(5)->mapWithKeys(function ($field, $i) {
                return [
                    'cf_id_'.($i + 1) => $field['custom_field_id'],
                    'cf_value_'.($i + 1) => $field['value'],
                ];
            })->all();
        }
    }

    // assert
    $expectedSecondCombinationData = array_merge([
        'item_id' => $purchaseEditInput['items'][0]['item_id'],
        'available_quantity' => -2,
        'purchase_rate' => 100,
        'purchase_date' => Carbon::parse($purchaseEditInput['date_of_invoice'])->format('Y-m-d'),
    ], $secondCustomFieldsCombination);

    $this->assertDatabaseHas('item_custom_field_combinations', $expectedSecondCombinationData);

    $this->assertDatabaseCount('item_custom_field_combinations', 1);

    $this->assertDatabaseCount('item_custom_field_combination_inventory', 2);
});

test('2 Purchase create with different CF inv. & 1 sale using 1st combination, 1 sale using 1st & 2nd combination', function () {
    // arrange
    setCompany(1);

    $this->seed(TestCustomFieldsInventorySeeder::class);

    // First: Purchase with single item & total inventory 10 (row line 1)
    $purchaseInput = PurchaseTransactionWithCFInventoryFactory::new()->make()->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    foreach ($purchaseInput['items'] as $key => &$item) {
        $cf = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
            $query->where('item_id', $item['item_id']);
        })->where('open_in_popup', true)->orderBy('id', 'ASC')->get()->toArray();

        $response = [];

        foreach ($cf as $i => $c) {
            $response[] = [
                'custom_field_id' => $c['id'],
                'value' => $i == 0 ? 'B123' : 'BLACK',
                'quantity' => 10,
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    // act
    $response = $this->postJson(route('purchase-transaction'), $purchaseInput);

    // assert
    $response->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // Second Sale with 10 qty inventory of single item & total inventory 10
    $saleInput = SaleTransactionWithCFInventoryFactory::new()->make()->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');

    foreach ($saleInput['items'] as $key => &$item) {
        $cf = GetItemCFInventoryListForSale::run($item['item_id'], 1);
        $cf = $cf[0]['fields'];
        $response = [];

        foreach ($cf as $i => $c) {
            $response[] = [
                'custom_field_id' => $c['custom_field_id'],
                'value' => $c['value'],
                'quantity' => 5,
                'purchase_rate' => $c['purchase_rate'],
                'purchase_date' => $c['purchase_date'],
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    $saleResponse = $this->postJson(route('sale-transaction'), $saleInput);

    // assert
    $saleResponse->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // Third: Purchase again with new combination
    $purchaseInput2 = PurchaseTransactionWithCFInventoryFactory::new()->make()->toArray();
    $purchaseInput2['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput2['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    foreach ($purchaseInput2['items'] as $key => &$item) {
        $cf = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
            $query->where('item_id', $item['item_id']);
        })->where('open_in_popup', true)->orderBy('id', 'ASC')->get()->toArray();

        $response = [];

        foreach ($cf as $i => $c) {
            $response[] = [
                'custom_field_id' => $c['id'],
                'value' => $i == 0 ? 'B121' : 'BLACK',
                'quantity' => 5,
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    // act
    $purchase2 = $this->postJson(route('purchase-transaction'), $purchaseInput2);

    // assert
    $purchase2->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // Fourth: Sale with new combination
    $saleInput2 = SaleTransactionWithCFInventoryFactory::new()->make()->toArray();
    $saleInput2['date'] = Carbon::now()->format('d-m-Y');

    foreach ($saleInput2['items'] as $key => &$item) {
        $cf = GetItemCFInventoryListForSale::run($item['item_id'], 1);

        $response = [];

        foreach ($cf as $i => $c) {
            $group = [];
            foreach ($c['fields'] as $f) {
                $group[] = [
                    'custom_field_id' => $f['custom_field_id'],
                    'value' => $f['value'],
                    'quantity' => $i == 0 ? 5 : 2,
                    'purchase_rate' => $c['purchase_rate'],
                    'purchase_date' => $c['purchase_date'],
                ];
            }

            $response[] = $group;
        }

        $item['custom_field_inventory'] = $response;
    }

    $saleResponse2 = $this->postJson(route('sale-transaction'), $saleInput2);

    // assert
    $saleResponse2->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    $secondCustomFieldsCombination = [];
    foreach ($saleInput2['items'] as $key => $item) {
        foreach ($item['custom_field_inventory'] as $key => $cf) {
            $secondCustomFieldsCombination[] = collect($cf)->take(5)->values()->mapWithKeys(function ($field, $i) {
                return [
                    'cf_id_'.($i + 1) => $field['custom_field_id'],
                    'cf_value_'.($i + 1) => $field['value'],
                ];
            })->all();
        }
    }

    $this->assertDatabaseHas('item_custom_field_combinations', array_merge([
        'item_id' => $saleInput2['items'][0]['item_id'],
        'available_quantity' => 0,
        'purchase_rate' => 100,
        'purchase_date' => Carbon::parse($saleInput2['date'])->format('Y-m-d'),
    ], $secondCustomFieldsCombination[0]));

    $this->assertDatabaseHas('item_custom_field_combinations', array_merge([
        'item_id' => $saleInput2['items'][0]['item_id'],
        'available_quantity' => 3,
        'purchase_rate' => 100,
        'purchase_date' => Carbon::parse($saleInput2['date'])->format('Y-m-d'),
    ], $secondCustomFieldsCombination[1]));

    $this->assertDatabaseCount('item_custom_field_combinations', 2);

    $this->assertDatabaseCount('item_custom_field_combination_inventory', 5);
});

test('purchase: 1 Item : inv(10)qty | sale: 1 Item : inv(5)qty | purchase 1 Item : inv(5)qty | purchase 1st edit CF inv value', function () {
    // arrange
    setCompany(1);

    $this->seed(TestCustomFieldsInventorySeeder::class);

    // First: Purchase with single item & total inventory 10 (row line 1)
    $purchaseInput = PurchaseTransactionWithCFInventoryFactory::new()->make()->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    foreach ($purchaseInput['items'] as $key => &$item) {
        $cf = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
            $query->where('item_id', $item['item_id']);
        })->where('open_in_popup', true)->orderBy('id', 'ASC')->get()->toArray();

        $response = [];

        foreach ($cf as $i => $c) {
            $response[] = [
                'custom_field_id' => $c['id'],
                'value' => $i == 0 ? 'B123' : 'BLACK',
                'quantity' => 10,
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    // act
    $response = $this->postJson(route('purchase-transaction'), $purchaseInput);

    $purchase1Id = $response->json()['data']['purchaseTransaction']['id'];

    // assert
    $response->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // Second Sale with 10 qty inventory of single item & total inventory 10
    $saleInput = SaleTransactionWithCFInventoryFactory::new()->make()->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');

    foreach ($saleInput['items'] as $key => &$item) {
        $cf = GetItemCFInventoryListForSale::run($item['item_id'], 1);
        $cf = $cf[0]['fields'];
        $response = [];

        foreach ($cf as $i => $c) {
            $response[] = [
                'custom_field_id' => $c['custom_field_id'],
                'value' => $c['value'],
                'quantity' => 5,
                'purchase_rate' => $c['purchase_rate'],
                'purchase_date' => $c['purchase_date'],
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    $saleResponse = $this->postJson(route('sale-transaction'), $saleInput);

    // assert
    $saleResponse->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // Third: Purchase again with new combination
    $purchaseInput2 = PurchaseTransactionWithCFInventoryFactory::new()->make()->toArray();
    $purchaseInput2['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput2['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    foreach ($purchaseInput2['items'] as $key => &$item) {
        $cf = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
            $query->where('item_id', $item['item_id']);
        })->where('open_in_popup', true)->orderBy('id', 'ASC')->get()->toArray();

        $response = [];

        foreach ($cf as $i => $c) {
            $response[] = [
                'custom_field_id' => $c['id'],
                'value' => $i == 0 ? 'B123' : 'BLACK',
                'quantity' => 5,
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    // act
    $purchase2 = $this->postJson(route('purchase-transaction'), $purchaseInput2);

    // assert
    $purchase2->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // Fourth: Purchase 1st edit CF inv value
    $purchase1stEditInput = PreparePurchaseEditResponseFactory::new()->make([
        'id' => $purchase1Id,
    ])->toArray();
    $purchase1stEditInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchase1stEditInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    foreach ($purchase1stEditInput['items'] as $key => &$item) {
        $cf = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
            $query->where('item_id', $item['item_id']);
        })->where('open_in_popup', true)->orderBy('id', 'ASC')->get()->toArray();

        $response = [];

        foreach ($cf as $i => $c) {
            $response[] = [
                'custom_field_id' => $c['id'],
                'value' => $i == 0 ? 'B121' : 'BLACK',
                'quantity' => 10,
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    $purchase1stEditResponse = $this->postJson(route('purchase-transaction-update', $purchase1Id), $purchase1stEditInput);

    // assert
    $purchase1stEditResponse->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // check 2nd combination
    $secondCustomFieldsCombination = null;
    foreach ($purchase1stEditInput['items'] as $key => $item) {
        foreach ($item['custom_field_inventory'] as $key => $cf) {
            $secondCustomFieldsCombination = collect($cf)->take(5)->mapWithKeys(function ($field, $i) {
                return [
                    'cf_id_'.($i + 1) => $field['custom_field_id'],
                    'cf_value_'.($i + 1) => $field['value'],
                ];
            })->all();
        }
    }

    // assert
    $expectedSecondCombinationData = array_merge([
        'item_id' => $purchase1stEditInput['items'][0]['item_id'],
        'available_quantity' => 10,
        'purchase_rate' => 100,
        'purchase_date' => Carbon::parse($purchase1stEditInput['date_of_invoice'])->format('Y-m-d'),
    ], $secondCustomFieldsCombination);

    $this->assertDatabaseHas('item_custom_field_combinations', $expectedSecondCombinationData);

    // check 1nd combination
    $firstCustomFieldsCombination = null;
    foreach ($saleInput['items'] as $key => $item) {
        foreach ($item['custom_field_inventory'] as $key => $cf) {
            $firstCustomFieldsCombination = collect($cf)->take(5)->mapWithKeys(function ($field, $i) {
                return [
                    'cf_id_'.($i + 1) => $field['custom_field_id'],
                    'cf_value_'.($i + 1) => $field['value'],
                ];
            })->all();
        }
    }

    // assert
    $expectedFirstCombinationData = array_merge([
        'item_id' => $saleInput['items'][0]['item_id'],
        'available_quantity' => 0,
        'purchase_rate' => 100,
        'purchase_date' => Carbon::parse($saleInput['date'])->format('Y-m-d'),
    ], $firstCustomFieldsCombination);

    $this->assertDatabaseHas('item_custom_field_combinations', $expectedFirstCombinationData);

    $this->assertDatabaseCount('item_custom_field_combinations', 2);

    $this->assertDatabaseCount('item_custom_field_combination_inventory', 3);
});

test('2 Purchase create with different CF inv. & 2nd purchase with new extra custom field', function () {
    // arrange
    setCompany(1);

    $this->seed(TestCustomFieldsInventorySeeder::class);

    // First: Purchase with single item & total inventory 10 (row line 1)
    $purchaseInput = PurchaseTransactionWithCFInventoryFactory::new()->make()->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    foreach ($purchaseInput['items'] as $key => &$item) {
        $cf = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
            $query->where('item_id', $item['item_id']);
        })->where('open_in_popup', true)->orderBy('id', 'ASC')->get()->toArray();

        $response = [];

        foreach ($cf as $i => $c) {
            $response[] = [
                'custom_field_id' => $c['id'],
                'value' => $i == 0 ? 'B123' : 'BLACK',
                'quantity' => 10,
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    // act
    $response = $this->postJson(route('purchase-transaction'), $purchaseInput);

    // assert
    $response->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // Second: add new custom field for inventory
    $newCustomFieldForInventory = ItemCustomField::create([
        'company_id' => getCurrentCompany()->id,
        'label_name' => 'Storage',
        'custom_field_type' => 2,
        'enable_for_all' => false,
        'open_in_popup' => true,
    ]);

    $ids = array_keys(getItemMasters());

    foreach ($ids as $id) {
        ItemCustomFieldSetting::create([
            'item_id' => $id,
            'custom_field_id' => $newCustomFieldForInventory->id,
            'is_show_in_print' => true,
        ]);
    }

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // Third: Purchase again with new combination
    $purchaseInput2 = PurchaseTransactionWithCFInventoryFactory::new()->make()->toArray();
    $purchaseInput2['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput2['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    foreach ($purchaseInput2['items'] as $key => &$item) {
        $cf = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
            $query->where('item_id', $item['item_id']);
        })->where('open_in_popup', true)->orderBy('id', 'ASC')->get()->toArray();

        $response = [];

        foreach ($cf as $i => $c) {
            $value = $i == 0 ? 'B123' : ($i == 1 ? 'BLACK' : '64GB');
            $response[] = [
                'custom_field_id' => $c['id'],
                'value' => $value,
                'quantity' => 5,
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    // act
    $purchase2 = $this->postJson(route('purchase-transaction'), $purchaseInput2);

    // assert
    $purchase2->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // check 2nd combination
    $secondCustomFieldsCombination = null;
    foreach ($purchaseInput2['items'] as $key => $item) {
        foreach ($item['custom_field_inventory'] as $key => $cf) {
            $secondCustomFieldsCombination = collect($cf)->take(5)->mapWithKeys(function ($field, $i) {
                return [
                    'cf_id_'.($i + 1) => $field['custom_field_id'],
                    'cf_value_'.($i + 1) => $field['value'],
                ];
            })->all();
        }
    }

    // assert
    $expectedSecondCombinationData = array_merge([
        'item_id' => $purchaseInput2['items'][0]['item_id'],
        'available_quantity' => 5,
        'purchase_rate' => 100,
        'purchase_date' => Carbon::parse($purchaseInput2['date_of_invoice'])->format('Y-m-d'),
    ], $secondCustomFieldsCombination);

    $this->assertDatabaseHas('item_custom_field_combinations', $expectedSecondCombinationData);

    // check 1nd combination
    $firstCustomFieldsCombination = null;
    foreach ($purchaseInput['items'] as $key => $item) {
        foreach ($item['custom_field_inventory'] as $key => $cf) {
            $firstCustomFieldsCombination = collect($cf)->take(5)->mapWithKeys(function ($field, $i) {
                return [
                    'cf_id_'.($i + 1) => $field['custom_field_id'],
                    'cf_value_'.($i + 1) => $field['value'],
                ];
            })->all();
        }
    }

    // assert
    $expectedFirstCombinationData = array_merge([
        'item_id' => $purchaseInput['items'][0]['item_id'],
        'available_quantity' => 10,
        'purchase_rate' => 100,
        'purchase_date' => Carbon::parse($purchaseInput['date_of_invoice'])->format('Y-m-d'),
    ], $firstCustomFieldsCombination);

    $this->assertDatabaseHas('item_custom_field_combinations', $expectedFirstCombinationData);

    $this->assertDatabaseCount('item_custom_field_combinations', 2);

    $this->assertDatabaseCount('item_custom_field_combination_inventory', 2);
});

test('2 Purchase create with different CF inv. & 2nd purchase with new extra custom field & sale both purchase combination inv.', function () {
    // arrange
    setCompany(1);

    $this->seed(TestCustomFieldsInventorySeeder::class);

    // First: Purchase with single item & total inventory 10 (row line 1)
    $purchaseInput = PurchaseTransactionWithCFInventoryFactory::new()->make()->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    foreach ($purchaseInput['items'] as $key => &$item) {
        $cf = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
            $query->where('item_id', $item['item_id']);
        })->where('open_in_popup', true)->orderBy('id', 'ASC')->get()->toArray();

        $response = [];

        foreach ($cf as $i => $c) {
            $response[] = [
                'custom_field_id' => $c['id'],
                'value' => $i == 0 ? 'B123' : 'BLACK',
                'quantity' => 10,
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    // act
    $response = $this->postJson(route('purchase-transaction'), $purchaseInput);

    // assert
    $response->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // Second: add new custom field for inventory
    $newCustomFieldForInventory = ItemCustomField::create([
        'company_id' => getCurrentCompany()->id,
        'label_name' => 'Storage',
        'custom_field_type' => 2,
        'enable_for_all' => false,
        'open_in_popup' => true,
    ]);

    $ids = array_keys(getItemMasters());

    foreach ($ids as $id) {
        ItemCustomFieldSetting::create([
            'item_id' => $id,
            'custom_field_id' => $newCustomFieldForInventory->id,
            'is_show_in_print' => true,
        ]);
    }

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // Third: Purchase again with new combination
    $purchaseInput2 = PurchaseTransactionWithCFInventoryFactory::new()->make()->toArray();
    $purchaseInput2['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput2['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    foreach ($purchaseInput2['items'] as $key => &$item) {
        $cf = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
            $query->where('item_id', $item['item_id']);
        })->where('open_in_popup', true)->orderBy('id', 'ASC')->get()->toArray();

        $response = [];

        foreach ($cf as $i => $c) {
            $value = $i == 0 ? 'B123' : ($i == 1 ? 'BLACK' : '64GB');
            $response[] = [
                'custom_field_id' => $c['id'],
                'value' => $value,
                'quantity' => 10,
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    // act
    $purchase2 = $this->postJson(route('purchase-transaction'), $purchaseInput2);

    // assert
    $purchase2->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // Fourth: Sale with use all existing combination
    $saleInput2 = SaleTransactionWithCFInventoryFactory::new()->make()->toArray();
    $saleInput2['date'] = Carbon::now()->format('d-m-Y');

    foreach ($saleInput2['items'] as $key => &$item) {
        $cf = GetItemCFInventoryListForSale::run($item['item_id'], 1);

        $response = [];

        foreach ($cf as $i => $c) {
            $group = [];
            foreach ($c['fields'] as $f) {
                $group[] = [
                    'custom_field_id' => $f['custom_field_id'],
                    'value' => $f['value'],
                    'quantity' => $i == 0 ? 10 : 3,
                    'purchase_rate' => $c['purchase_rate'],
                    'purchase_date' => $c['purchase_date'],
                ];
            }

            $response[] = $group;
        }

        $item['custom_field_inventory'] = $response;
    }

    $saleResponse2 = $this->postJson(route('sale-transaction'), $saleInput2);

    // assert
    $saleResponse2->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // check 2nd combination
    $secondCustomFieldsCombination = null;
    foreach ($purchaseInput2['items'] as $key => $item) {
        foreach ($item['custom_field_inventory'] as $key => $cf) {
            $secondCustomFieldsCombination = collect($cf)->take(5)->mapWithKeys(function ($field, $i) {
                return [
                    'cf_id_'.($i + 1) => $field['custom_field_id'],
                    'cf_value_'.($i + 1) => $field['value'],
                ];
            })->all();
        }
    }

    // assert
    $expectedSecondCombinationData = array_merge([
        'item_id' => $purchaseInput2['items'][0]['item_id'],
        'available_quantity' => 7,
        'purchase_rate' => 100,
        'purchase_date' => Carbon::parse($purchaseInput2['date_of_invoice'])->format('Y-m-d'),
    ], $secondCustomFieldsCombination);

    $this->assertDatabaseHas('item_custom_field_combinations', $expectedSecondCombinationData);

    // check 1nd combination
    $firstCustomFieldsCombination = null;
    foreach ($purchaseInput['items'] as $key => $item) {
        foreach ($item['custom_field_inventory'] as $key => $cf) {
            $firstCustomFieldsCombination = collect($cf)->take(5)->mapWithKeys(function ($field, $i) {
                return [
                    'cf_id_'.($i + 1) => $field['custom_field_id'],
                    'cf_value_'.($i + 1) => $field['value'],
                ];
            })->all();
        }
    }

    // assert
    $expectedFirstCombinationData = array_merge([
        'item_id' => $purchaseInput['items'][0]['item_id'],
        'available_quantity' => 0,
        'purchase_rate' => 100,
        'purchase_date' => Carbon::parse($purchaseInput['date_of_invoice'])->format('Y-m-d'),
    ], $firstCustomFieldsCombination);

    $this->assertDatabaseHas('item_custom_field_combinations', $expectedFirstCombinationData);

    $this->assertDatabaseCount('item_custom_field_combinations', 2);

    $this->assertDatabaseCount('item_custom_field_combination_inventory', 4);
});

test('2 Purchase create with different CF inv. & sale with both purchase combination inv. & 2nd purchase edit inv. value', function () {
    // arrange
    setCompany(1);

    $this->seed(TestCustomFieldsInventorySeeder::class);

    // First: Purchase with single item & total inventory 10 (row line 1)
    $purchaseInput = PurchaseTransactionWithCFInventoryFactory::new()->make()->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    foreach ($purchaseInput['items'] as $key => &$item) {
        $cf = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
            $query->where('item_id', $item['item_id']);
        })->where('open_in_popup', true)->orderBy('id', 'ASC')->get()->toArray();

        $response = [];

        foreach ($cf as $i => $c) {
            $response[] = [
                'custom_field_id' => $c['id'],
                'value' => $i == 0 ? 'B123' : 'BLACK',
                'quantity' => 10,
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    // act
    $response = $this->postJson(route('purchase-transaction'), $purchaseInput);

    // assert
    $response->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // Second: add new custom field for inventory
    $newCustomFieldForInventory = ItemCustomField::create([
        'company_id' => getCurrentCompany()->id,
        'label_name' => 'Storage',
        'custom_field_type' => 2,
        'enable_for_all' => false,
        'open_in_popup' => true,
    ]);

    $ids = array_keys(getItemMasters());

    foreach ($ids as $id) {
        ItemCustomFieldSetting::create([
            'item_id' => $id,
            'custom_field_id' => $newCustomFieldForInventory->id,
            'is_show_in_print' => true,
        ]);
    }

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // Third: Purchase again with new combination
    $purchaseInput2 = PurchaseTransactionWithCFInventoryFactory::new()->make()->toArray();
    $purchaseInput2['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput2['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    foreach ($purchaseInput2['items'] as $key => &$item) {
        $cf = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
            $query->where('item_id', $item['item_id']);
        })->where('open_in_popup', true)->orderBy('id', 'ASC')->get()->toArray();

        $response = [];

        foreach ($cf as $i => $c) {
            $value = $i == 0 ? 'B123' : ($i == 1 ? 'BLACK' : '64GB');
            $response[] = [
                'custom_field_id' => $c['id'],
                'value' => $value,
                'quantity' => 10,
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    // act
    $purchase2 = $this->postJson(route('purchase-transaction'), $purchaseInput2);

    // assert
    $purchase2->assertStatus(200);

    $purchase2Id = $purchase2->json()['data']['purchaseTransaction']['id'];

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // Fourth: Sale with use all existing combination
    $saleInput2 = SaleTransactionWithCFInventoryFactory::new()->make()->toArray();
    $saleInput2['date'] = Carbon::now()->format('d-m-Y');

    foreach ($saleInput2['items'] as $key => &$item) {
        $cf = GetItemCFInventoryListForSale::run($item['item_id'], 1);

        $response = [];

        foreach ($cf as $i => $c) {
            $group = [];
            foreach ($c['fields'] as $f) {
                $group[] = [
                    'custom_field_id' => $f['custom_field_id'],
                    'value' => $f['value'],
                    'quantity' => $i == 0 ? 10 : 3,
                    'purchase_rate' => $c['purchase_rate'],
                    'purchase_date' => $c['purchase_date'],
                ];
            }

            $response[] = $group;
        }

        $item['custom_field_inventory'] = $response;
    }

    $saleResponse2 = $this->postJson(route('sale-transaction'), $saleInput2);

    // assert
    $saleResponse2->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // Fifth: 2nd Purchase edit inventory value
    $purchaseInput2EditInput = PreparePurchaseEditResponseFactory::new()->make([
        'id' => $purchase2Id,
    ])->toArray();
    $purchaseInput2EditInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput2EditInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    foreach ($purchaseInput2EditInput['items'] as $key => &$item) {
        $cf = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
            $query->where('item_id', $item['item_id']);
        })->where('open_in_popup', true)->orderBy('id', 'ASC')->get()->toArray();

        $response = [];

        foreach ($cf as $i => $c) {
            $value = $i == 0 ? 'B123' : ($i == 1 ? 'BLACK' : '128GB');
            $response[] = [
                'custom_field_id' => $c['id'],
                'value' => $value,
                'quantity' => 10,
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    // act
    $purchase2EditResponse = $this->postJson(route('purchase-transaction-update', $purchase2Id), $purchaseInput2EditInput);

    // assert
    $purchase2EditResponse->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // Check 3rd combination
    $thirdCustomFieldsCombination = null;
    foreach ($purchaseInput2EditInput['items'] as $key => $item) {
        foreach ($item['custom_field_inventory'] as $key => $cf) {
            $thirdCustomFieldsCombination = collect($cf)->take(5)->mapWithKeys(function ($field, $i) {
                return [
                    'cf_id_'.($i + 1) => $field['custom_field_id'],
                    'cf_value_'.($i + 1) => $field['value'],
                ];
            })->all();
        }
    }

    $expectedThirdCombinationData = array_merge([
        'item_id' => $purchaseInput2EditInput['items'][0]['item_id'],
        'available_quantity' => 10,
        'purchase_rate' => 100,
        'purchase_date' => Carbon::parse($purchaseInput2EditInput['date_of_invoice'])->format('Y-m-d'),
    ], $thirdCustomFieldsCombination);

    $this->assertDatabaseHas('item_custom_field_combinations', $expectedThirdCombinationData);

    // check 2nd combination
    $secondCustomFieldsCombination = null;
    foreach ($purchaseInput2['items'] as $key => $item) {
        foreach ($item['custom_field_inventory'] as $key => $cf) {
            $secondCustomFieldsCombination = collect($cf)->take(5)->mapWithKeys(function ($field, $i) {
                return [
                    'cf_id_'.($i + 1) => $field['custom_field_id'],
                    'cf_value_'.($i + 1) => $field['value'],
                ];
            })->all();
        }
    }

    // assert
    $expectedSecondCombinationData = array_merge([
        'item_id' => $purchaseInput2['items'][0]['item_id'],
        'available_quantity' => -3,
        'purchase_rate' => 100,
        'purchase_date' => Carbon::parse($purchaseInput2['date_of_invoice'])->format('Y-m-d'),
    ], $secondCustomFieldsCombination);

    $this->assertDatabaseHas('item_custom_field_combinations', $expectedSecondCombinationData);

    // check 1nd combination
    $firstCustomFieldsCombination = null;
    foreach ($purchaseInput['items'] as $key => $item) {
        foreach ($item['custom_field_inventory'] as $key => $cf) {
            $firstCustomFieldsCombination = collect($cf)->take(5)->mapWithKeys(function ($field, $i) {
                return [
                    'cf_id_'.($i + 1) => $field['custom_field_id'],
                    'cf_value_'.($i + 1) => $field['value'],
                ];
            })->all();
        }
    }

    // assert
    $expectedFirstCombinationData = array_merge([
        'item_id' => $purchaseInput['items'][0]['item_id'],
        'available_quantity' => 0,
        'purchase_rate' => 100,
        'purchase_date' => Carbon::parse($purchaseInput['date_of_invoice'])->format('Y-m-d'),
    ], $firstCustomFieldsCombination);

    $this->assertDatabaseHas('item_custom_field_combinations', $expectedFirstCombinationData);

    $this->assertDatabaseCount('item_custom_field_combinations', 3);

    $this->assertDatabaseCount('item_custom_field_combination_inventory', 4);
});

test('2 Purchase create with different CF inv. & 1 sale with both purchase combination inv. & both purchase edit inv. value', function () {
    // arrange
    setCompany(1);

    $this->seed(TestCustomFieldsInventorySeeder::class);

    // First: Purchase with single item & total inventory 10 (row line 1)
    $purchaseInput = PurchaseTransactionWithCFInventoryFactory::new()->make()->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    foreach ($purchaseInput['items'] as $key => &$item) {
        $cf = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
            $query->where('item_id', $item['item_id']);
        })->where('open_in_popup', true)->orderBy('id', 'ASC')->get()->toArray();

        $response = [];

        foreach ($cf as $i => $c) {
            $response[] = [
                'custom_field_id' => $c['id'],
                'value' => $i == 0 ? 'B123' : 'BLACK',
                'quantity' => 10,
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    // act
    $response = $this->postJson(route('purchase-transaction'), $purchaseInput);

    // assert
    $response->assertStatus(200);

    $purchase1Id = $response->json()['data']['purchaseTransaction']['id'];

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // Second: add new custom field for inventory
    $newCustomFieldForInventory = ItemCustomField::create([
        'company_id' => getCurrentCompany()->id,
        'label_name' => 'Storage',
        'custom_field_type' => 2,
        'enable_for_all' => false,
        'open_in_popup' => true,
    ]);

    $ids = array_keys(getItemMasters());

    foreach ($ids as $id) {
        ItemCustomFieldSetting::create([
            'item_id' => $id,
            'custom_field_id' => $newCustomFieldForInventory->id,
            'is_show_in_print' => true,
        ]);
    }

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // Third: Purchase again with new combination
    $purchaseInput2 = PurchaseTransactionWithCFInventoryFactory::new()->make()->toArray();
    $purchaseInput2['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput2['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    foreach ($purchaseInput2['items'] as $key => &$item) {
        $cf = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
            $query->where('item_id', $item['item_id']);
        })->where('open_in_popup', true)->orderBy('id', 'ASC')->get()->toArray();

        $response = [];

        foreach ($cf as $i => $c) {
            $value = $i == 0 ? 'B123' : ($i == 1 ? 'BLACK' : '64GB');
            $response[] = [
                'custom_field_id' => $c['id'],
                'value' => $value,
                'quantity' => 10,
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    // act
    $purchase2 = $this->postJson(route('purchase-transaction'), $purchaseInput2);

    // assert
    $purchase2->assertStatus(200);

    $purchase2Id = $purchase2->json()['data']['purchaseTransaction']['id'];

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // Fourth: Sale with use all existing combination
    $saleInput2 = SaleTransactionWithCFInventoryFactory::new()->make()->toArray();
    $saleInput2['date'] = Carbon::now()->format('d-m-Y');

    foreach ($saleInput2['items'] as $key => &$item) {
        $cf = GetItemCFInventoryListForSale::run($item['item_id'], 1);

        $response = [];

        foreach ($cf as $i => $c) {
            $group = [];
            foreach ($c['fields'] as $f) {
                $group[] = [
                    'custom_field_id' => $f['custom_field_id'],
                    'value' => $f['value'],
                    'quantity' => $i == 0 ? 10 : 3,
                    'purchase_rate' => $c['purchase_rate'],
                    'purchase_date' => $c['purchase_date'],
                ];
            }

            $response[] = $group;
        }

        $item['custom_field_inventory'] = $response;
    }

    $saleResponse2 = $this->postJson(route('sale-transaction'), $saleInput2);

    // assert
    $saleResponse2->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // Fifth: 1st Purchase edit inventory value
    $purchaseInput1EditInput = PreparePurchaseEditResponseFactory::new()->make([
        'id' => $purchase1Id,
    ])->toArray();
    $purchaseInput1EditInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput1EditInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    foreach ($purchaseInput1EditInput['items'] as $key => &$item) {
        $cf = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
            $query->where('item_id', $item['item_id']);
        })->where('open_in_popup', true)->orderBy('id', 'ASC')->take(2)->get()->toArray();

        $response = [];

        foreach ($cf as $i => $c) {
            $response[] = [
                'custom_field_id' => $c['id'],
                'value' => $i == 0 ? 'B121' : 'BLACK',
                'quantity' => 10,
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    // act
    $purchase1EditResponse = $this->postJson(route('purchase-transaction-update', $purchase1Id), $purchaseInput1EditInput);

    // assert
    $purchase1EditResponse->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // Sixth: 2nd Purchase edit inventory value
    $purchaseInput2EditInput = PreparePurchaseEditResponseFactory::new()->make([
        'id' => $purchase2Id,
    ])->toArray();
    $purchaseInput2EditInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput2EditInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    foreach ($purchaseInput2EditInput['items'] as $key => &$item) {
        $cf = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
            $query->where('item_id', $item['item_id']);
        })->where('open_in_popup', true)->orderBy('id', 'ASC')->get()->toArray();

        $response = [];

        foreach ($cf as $i => $c) {
            $value = $i == 0 ? 'B123' : ($i == 1 ? 'BLACK' : '128GB');
            $response[] = [
                'custom_field_id' => $c['id'],
                'value' => $value,
                'quantity' => 10,
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    // act
    $purchase2EditResponse = $this->postJson(route('purchase-transaction-update', $purchase2Id), $purchaseInput2EditInput);

    // assert
    $purchase2EditResponse->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // Check 4th combination
    $forthCustomFieldsCombination = null;
    foreach ($purchaseInput2EditInput['items'] as $key => $item) {
        foreach ($item['custom_field_inventory'] as $key => $cf) {
            $forthCustomFieldsCombination = collect($cf)->take(5)->mapWithKeys(function ($field, $i) {
                return [
                    'cf_id_'.($i + 1) => $field['custom_field_id'],
                    'cf_value_'.($i + 1) => $field['value'],
                ];
            })->all();
        }
    }

    $expectedForthCombinationData = array_merge([
        'item_id' => $purchaseInput2EditInput['items'][0]['item_id'],
        'available_quantity' => 10,
        'purchase_rate' => 100,
        'purchase_date' => Carbon::parse($purchaseInput2EditInput['date_of_invoice'])->format('Y-m-d'),
    ], $forthCustomFieldsCombination);

    $this->assertDatabaseHas('item_custom_field_combinations', $expectedForthCombinationData);

    // Check 4th combination
    $thirdCustomFieldsCombination = null;
    foreach ($purchaseInput1EditInput['items'] as $key => $item) {
        foreach ($item['custom_field_inventory'] as $key => $cf) {
            $thirdCustomFieldsCombination = collect($cf)->take(5)->mapWithKeys(function ($field, $i) {
                return [
                    'cf_id_'.($i + 1) => $field['custom_field_id'],
                    'cf_value_'.($i + 1) => $field['value'],
                ];
            })->all();
        }
    }

    $expectedThirdCombinationData = array_merge([
        'item_id' => $purchaseInput1EditInput['items'][0]['item_id'],
        'available_quantity' => 10,
        'purchase_rate' => 100,
        'purchase_date' => Carbon::parse($purchaseInput1EditInput['date_of_invoice'])->format('Y-m-d'),
    ], $thirdCustomFieldsCombination);

    $this->assertDatabaseHas('item_custom_field_combinations', $expectedThirdCombinationData);

    // check 2nd combination
    $secondCustomFieldsCombination = null;
    foreach ($purchaseInput2['items'] as $key => $item) {
        foreach ($item['custom_field_inventory'] as $key => $cf) {
            $secondCustomFieldsCombination = collect($cf)->take(5)->mapWithKeys(function ($field, $i) {
                return [
                    'cf_id_'.($i + 1) => $field['custom_field_id'],
                    'cf_value_'.($i + 1) => $field['value'],
                ];
            })->all();
        }
    }

    // assert
    $expectedSecondCombinationData = array_merge([
        'item_id' => $purchaseInput2['items'][0]['item_id'],
        'available_quantity' => -3,
        'purchase_rate' => 100,
        'purchase_date' => Carbon::parse($purchaseInput2['date_of_invoice'])->format('Y-m-d'),
    ], $secondCustomFieldsCombination);

    $this->assertDatabaseHas('item_custom_field_combinations', $expectedSecondCombinationData);

    // check 1nd combination
    $firstCustomFieldsCombination = null;
    foreach ($purchaseInput['items'] as $key => $item) {
        foreach ($item['custom_field_inventory'] as $key => $cf) {
            $firstCustomFieldsCombination = collect($cf)->take(5)->mapWithKeys(function ($field, $i) {
                return [
                    'cf_id_'.($i + 1) => $field['custom_field_id'],
                    'cf_value_'.($i + 1) => $field['value'],
                ];
            })->all();
        }
    }

    // assert
    $expectedFirstCombinationData = array_merge([
        'item_id' => $purchaseInput['items'][0]['item_id'],
        'available_quantity' => -10,
        'purchase_rate' => 100,
        'purchase_date' => Carbon::parse($purchaseInput['date_of_invoice'])->format('Y-m-d'),
    ], $firstCustomFieldsCombination);

    $this->assertDatabaseHas('item_custom_field_combinations', $expectedFirstCombinationData);

    $this->assertDatabaseCount('item_custom_field_combinations', 4);

    $this->assertDatabaseCount('item_custom_field_combination_inventory', 4);
});

test('1 purchase & 1 estimate & 1 sale & all custom field inv. with same combination', function () {
    // arrange
    setCompany(1);

    $this->seed(TestCustomFieldsInventorySeeder::class);

    // First: Purchase with single item & total inventory 10 (row line 1)
    $purchaseInput = PurchaseTransactionWithCFInventoryFactory::new()->make()->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    foreach ($purchaseInput['items'] as $key => &$item) {
        $cf = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
            $query->where('item_id', $item['item_id']);
        })->where('open_in_popup', true)->orderBy('id', 'ASC')->get()->toArray();

        $response = [];

        foreach ($cf as $i => $c) {
            $response[] = [
                'custom_field_id' => $c['id'],
                'value' => $i == 0 ? 'B123' : 'BLACK',
                'quantity' => 10,
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    // act
    $purchaseResponse = $this->postJson(route('purchase-transaction'), $purchaseInput);

    // assert
    $purchaseResponse->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // Second: Estimate with single item & total inventory 10 (row line 1)
    $estimateInput = EstimateTransactionWithCFInventoryFactory::new()->make()->toArray();
    $estimateInput['date'] = Carbon::now()->format('d-m-Y');

    foreach ($estimateInput['items'] as $key => &$item) {
        $cf = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
            $query->where('item_id', $item['item_id']);
        })->where('open_in_popup', true)->orderBy('id', 'ASC')->get()->toArray();

        $response = [];

        foreach ($cf as $i => $c) {
            $response[] = [
                'custom_field_id' => $c['id'],
                'value' => $i == 0 ? 'B123' : 'BLACK',
                'quantity' => 5,
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    // act
    $estimateResponse = $this->postJson(route('income-estimate-quote-transaction'), $estimateInput);

    // assert
    $estimateResponse->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // third: Sale with single item & total inventory 10 (row line 1)
    $saleInput = SaleTransactionWithCFInventoryFactory::new()->make()->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');

    foreach ($saleInput['items'] as $key => &$item) {
        $cf = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
            $query->where('item_id', $item['item_id']);
        })->where('open_in_popup', true)->orderBy('id', 'ASC')->get()->toArray();

        $response = [];

        foreach ($cf as $i => $c) {
            $response[] = [
                'custom_field_id' => $c['id'],
                'value' => $i == 0 ? 'B123' : 'BLACK',
                'quantity' => 5,
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    // act
    $saleResponse = $this->postJson(route('sale-transaction'), $saleInput);

    // assert
    $saleResponse->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    $combination = null;
    foreach ($purchaseInput['items'] as $key => $item) {
        foreach ($item['custom_field_inventory'] as $key => $cf) {
            $combination = collect($cf)->take(5)->mapWithKeys(function ($field, $i) {
                return [
                    'cf_id_'.($i + 1) => $field['custom_field_id'],
                    'cf_value_'.($i + 1) => $field['value'],
                ];
            })->all();
        }
    }

    // assert
    $expectedCombinationData = array_merge([
        'item_id' => $purchaseInput['items'][0]['item_id'],
        'available_quantity' => 5,
        'purchase_rate' => 100,
        'purchase_date' => Carbon::parse($purchaseInput['date_of_invoice'])->format('Y-m-d'),
    ], $combination);

    $this->assertDatabaseHas('item_custom_field_combinations', $expectedCombinationData);

    $this->assertDatabaseCount('item_custom_field_combinations', 1);

    $this->assertDatabaseCount('item_custom_field_combination_inventory', 3);
});

// on working
test('1 purchase & 1 estimate & 1 sale & 1st estimate to sale create & all custom field inv. with same combination', function () {
    // arrange
    setCompany(1);

    $this->seed(TestCustomFieldsInventorySeeder::class);

    // First: Purchase with single item & total inventory 10 (row line 1)
    $purchaseInput = PurchaseTransactionWithCFInventoryFactory::new()->make()->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    foreach ($purchaseInput['items'] as $key => &$item) {
        $cf = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
            $query->where('item_id', $item['item_id']);
        })->where('open_in_popup', true)->orderBy('id', 'ASC')->get()->toArray();

        $response = [];

        foreach ($cf as $i => $c) {
            $response[] = [
                'custom_field_id' => $c['id'],
                'value' => $i == 0 ? 'B123' : 'BLACK',
                'quantity' => 10,
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    // act
    $purchaseResponse = $this->postJson(route('purchase-transaction'), $purchaseInput);

    // assert
    $purchaseResponse->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // Second: Estimate with single item & total inventory 10 (row line 1)
    $estimateInput = EstimateTransactionWithCFInventoryFactory::new()->make()->toArray();
    $estimateInput['date'] = Carbon::now()->format('d-m-Y');

    foreach ($estimateInput['items'] as $key => &$item) {
        $cf = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
            $query->where('item_id', $item['item_id']);
        })->where('open_in_popup', true)->orderBy('id', 'ASC')->get()->toArray();

        $response = [];

        foreach ($cf as $i => $c) {
            $response[] = [
                'custom_field_id' => $c['id'],
                'value' => $i == 0 ? 'B123' : 'BLACK',
                'quantity' => 5,
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    // act
    $estimateResponse = $this->postJson(route('income-estimate-quote-transaction'), $estimateInput);

    // assert
    $estimateResponse->assertStatus(200);

    $estimateId = $estimateResponse->json()['data']['estimate_quote_transaction']['id'];

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // third: Sale with single item & total inventory 10 (row line 1)
    $saleInput = SaleTransactionWithCFInventoryFactory::new()->make()->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');

    foreach ($saleInput['items'] as $key => &$item) {
        $cf = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
            $query->where('item_id', $item['item_id']);
        })->where('open_in_popup', true)->orderBy('id', 'ASC')->get()->toArray();

        $response = [];

        foreach ($cf as $i => $c) {
            $response[] = [
                'custom_field_id' => $c['id'],
                'value' => $i == 0 ? 'B123' : 'BLACK',
                'quantity' => 10,
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    // act
    $saleResponse = $this->postJson(route('sale-transaction'), $saleInput);

    // assert
    $saleResponse->assertStatus(200);

    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    // fourth: Estimate to Sale with single item & total inventory 10 (row line 1)
    $estimateToSaleInput = EstimateToSaleCreateWithCFInventoryFactory::new()->make([
        'estimate_id' => $estimateId,
    ])->toArray();
    $estimateToSaleInput['date'] = Carbon::now()->format('d-m-Y');

    foreach ($estimateToSaleInput['items'] as $key => &$item) {
        $cf = ItemCustomField::whereHas('customFieldItemSettings', function ($query) use ($item) {
            $query->where('item_id', $item['item_id']);
        })->where('open_in_popup', true)->orderBy('id', 'ASC')->get()->toArray();

        $response = [];

        foreach ($cf as $i => $c) {
            $response[] = [
                'custom_field_id' => $c['id'],
                'value' => $i == 0 ? 'B123' : 'BLACK',
                'quantity' => 5,
            ];
        }

        $item['custom_field_inventory'] = [$response];
    }

    // act
    $estimateToSaleResponse = $this->postJson(route('sale-transaction'), $estimateToSaleInput);

    // assert
    $estimateToSaleResponse->assertStatus(200);
    /*--------------------------------------------------------------------------------------------------------------------------------------- */

    $combination = null;
    foreach ($purchaseInput['items'] as $key => $item) {
        foreach ($item['custom_field_inventory'] as $key => $cf) {
            $combination = collect($cf)->take(5)->mapWithKeys(function ($field, $i) {
                return [
                    'cf_id_'.($i + 1) => $field['custom_field_id'],
                    'cf_value_'.($i + 1) => $field['value'],
                ];
            })->all();
        }
    }

    // assert
    $expectedCombinationData = array_merge([
        'item_id' => $purchaseInput['items'][0]['item_id'],
        'available_quantity' => -5,
        'purchase_rate' => 100,
        'purchase_date' => Carbon::parse($purchaseInput['date_of_invoice'])->format('Y-m-d'),
    ], $combination);

    $this->assertDatabaseHas('item_custom_field_combinations', $expectedCombinationData);

    $this->assertDatabaseCount('item_custom_field_combinations', 1);

    $this->assertDatabaseCount('item_custom_field_combination_inventory', 4);
});
